apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: onpre-lite
  namespace: ape-pms-lite
  annotations:
    kubernetes.io/ingress.class: 'nginx'
    nginx.ingress.kubernetes.io/proxy-body-size: '100m'
spec:
  rules:
    # Tên miền truy cập lite onPre
    - host: ape-pms-lite-admin.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-pms-lite-admin
                port:
                  number: 80
    - host: ape-pms-lite-client.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-pms-lite-client
                port:
                  number: 80
    - host: ape-pms-lite-api.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-pms-lite-api
                port:
                  number: 80
