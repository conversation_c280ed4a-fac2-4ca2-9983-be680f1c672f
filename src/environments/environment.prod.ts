/** Thêm 1 trang SAAS thì add vào SetSaas này */
const setSaas = new Set()
setSaas.add('pms-saas-lite-admin.apetechs.co')

export const environment = {
  production: true,
  backEnd: setSaas.has(window.location.host) ? 'https://pms-saas-lite-api.apetechs.co' : 'https://ape-pms-lite-api.apetechs.co',
  backEndFnb: 'https://fnb-api-dev.apetechs.co',
  backEndNTSS: 'https://ntss-api.apetechs.co',
  repair: 'https://ntss-sc-api.apetechs.co',
}
