import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { AuthGuard } from './_helpers/auth.guard'
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component'
import { ConfirmLayoutComponent } from './layouts/confirm-layout/confirm-layout.component'

const routes: Routes = [
  { path: '', pathMatch: 'full', redirectTo: '/welcome' },
  {
    path: 'user',
    loadChildren: () => import('./user/user.module').then((m) => m.UserModule),
  },
  {
    path: 'confirm',
    component: ConfirmLayoutComponent,
    canActivate: [AuthGuard],
    loadChildren: () => import('./confirm/confirm.module').then((m) => m.ConfirmModule),
  },
  {
    path: '',
    component: AdminLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'welcome',
        loadChildren: () => import('./pages/welcome/welcome.module').then((m) => m.WelcomeModule),
      },
      {
        path: 'bid',
        loadChildren: () => import('./pages/bid/bid.module').then((m) => m.BidModule),
      },
      {
        path: 'setting',
        loadChildren: () => import('./pages/setting/setting.module').then((m) => m.SettingModule),
      },
      {
        path: 'repair',
        loadChildren: () => import('./pages/repair/repair.module').then((m) => m.RepairModule),
      },
      {
        path: 'property',
        loadChildren: () => import('./pages/property/property.module').then((m) => m.PropertyModule),
      },
      {
        path: 'manufacture',
        loadChildren: () => import('./pages/manufacturing/manufacturing.module').then((m) => m.ManufacturingModule),
      },

      {
        path: 'change-password',
        loadChildren: () => import('./pages/change-password/change-password.module').then((m) => m.ChangePasswordModule),
      },
      {
        path: 'contract',
        loadChildren: () => import('./pages/contract/contract.module').then((m) => m.ContractModule),
      },
      {
        path: 'asn',
        loadChildren: () => import('./pages/asn/asn.module').then((m) => m.AsnModule),
      },
      {
        path: 'pr',
        loadChildren: () => import('./pages/pr/pr.module').then((m) => m.PrModule),
      },
      {
        path: 'price-quote',
        loadChildren: () => import('./pages/price-quote/price-quote.module').then((m) => m.PriceQuoteModule),
      },
      {
        path: 'payment',
        loadChildren: () => import('./pages/payment/payment.module').then((m) => m.PaymentModule),
      },
      {
        path: 'inbound',
        loadChildren: () => import('./pages/inbound/inbound.module').then((m) => m.InboundModule),
      },
    ],
  },

  { path: '**', redirectTo: 'error/403', pathMatch: 'full' },
]

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
