import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BehaviorSubject, lastValueFrom } from 'rxjs'
import { environment } from 'src/environments/environment'
import { CoreService } from './core.service'
@Injectable()
export class ApiService {
  host = environment.backEnd + '/'
  eventCloseModal = new BehaviorSubject<boolean>(false)
  eventCloseModalPO = new BehaviorSubject<boolean>(false)
  eventReloadPrPlan = new BehaviorSubject<boolean>(false)
  eventReloadPrArise = new BehaviorSubject<boolean>(false)
  constructor(public coreService: CoreService, private http: HttpClient) {}

  //#region API Construct
  UploadUrl = `${this.host}uploadFiles/upload_single`

  NOTIFY = {
    LOAD: 'employeeNotify/load',
    READ: 'employeeNotify/read',
  }
  WARNING = {
    LOAD: 'employeeWarning/load',
    READ: 'employeeWarning/read',
  }

  DEPARTMENT = {
    FIND: 'departments/find',
    PAGINATION: 'departments/pagination',
    CREATE: 'departments/create_data',
    UPDATE: 'departments/update_data',
    DELETE: 'departments/update_active',
  }

  SETTING_STRING = {
    FIND: 'settingStrings/find',
    PAGINATION: 'settingStrings/pagination',
    CREATE: 'settingStrings/create_data',
    UPDATE: 'settingStrings/update_data',
    DELETE: 'settingStrings/update_active',
  }

  BID_TYPE = {
    FIND: 'bidTypes/find',
    PAGINATION: 'bidTypes/pagination',
    CREATE: 'bidTypes/create_data',
    UPDATE: 'bidTypes/update_data',
    DELETE: 'bidTypes/update_active',
  }

  SERVICE = {
    FIND: 'services/find',
    PAGINATION: 'services/pagination',
    MANAGER_PAGINATION: 'services/manager_pagination',
    CREATE: 'services/create_data',
    UPDATE: 'services/update_data',
    DELETE: 'services/update_active',
    IMPORT: 'services/import',
    GET_ALL_SERVICE: `services/get_all_service`,
  }

  SERVICE_SETTING = {
    // capacity
    CAPACITY_SUMPERCENT: 'serviceSettings/capacity_getsumpercent',
    CAPACITY_FIND: 'serviceSettings/capacity_find',
    CAPACITY_PAGINATION: 'serviceSettings/capacity_pagination',
    CAPACITY_CREATE: 'serviceSettings/capacity_create_data',
    CAPACITY_UPDATE: 'serviceSettings/capacity_update_data',
    CAPACITY_DELETE: 'serviceSettings/capacity_update_active',
    CAPACITY_DELETEALL: 'serviceSettings/capacity_deleteall',
    CAPACITY_IMPORT: (serviceId: string) => {
      return `service_settings/capacity_import/${serviceId}`
    },
    CAPACITY_SEND_APPROVE: 'serviceSettings/capacity_send_approve',
    CAPACITY_APPROVE: 'serviceSettings/capacity_approve',
    CAPACITY_RECHECK: 'serviceSettings/capacity_recheck',

    // capacitylistdetail
    CAPACITYLISTDETAIL_PAGINATION: 'serviceSettings/capacitylistdetail_pagination',
    CAPACITYLISTDETAIL_CREATE: 'serviceSettings/capacitylistdetail_create_data',
    CAPACITYLISTDETAIL_UPDATE: 'serviceSettings/capacitylistdetail_update_data',
    CAPACITYLISTDETAIL_DELETE: 'serviceSettings/capacitylistdetail_update_active',

    // tech
    TECH_SUMPERCENT: 'serviceSettings/tech_getsumpercent',
    TECH_FIND: 'serviceSettings/tech_find',
    TECH_PAGINATION: 'serviceSettings/tech_pagination',
    TECH_CREATE: 'serviceSettings/tech_create_data',
    TECH_UPDATE: 'serviceSettings/tech_update_data',
    TECH_DELETE: 'serviceSettings/tech_update_active',
    TECH_DELETEALL: 'serviceSettings/tech_deleteall',
    TECH_IMPORT: (serviceId: string) => {
      return `service_settings/tech_import/${serviceId}`
    },

    // techlistdetail
    TECHLISTDETAIL_PAGINATION: 'serviceSettings/techlistdetail_pagination',
    TECHLISTDETAIL_CREATE: 'serviceSettings/techlistdetail_create_data',
    TECHLISTDETAIL_UPDATE: 'serviceSettings/techlistdetail_update_data',
    TECHLISTDETAIL_DELETE: 'serviceSettings/techlistdetail_update_active',

    // trade
    TRADE_SUMPERCENT: 'serviceSettings/trade_getsumpercent',
    TRADE_FIND: 'serviceSettings/trade_find',
    TRADE_PAGINATION: 'serviceSettings/trade_pagination',
    TRADE_CREATE: 'serviceSettings/trade_create_data',
    TRADE_UPDATE: 'serviceSettings/trade_update_data',
    TRADE_DELETE: 'serviceSettings/trade_update_active',
    TRADE_DELETEALL: 'serviceSettings/trade_deleteall',
    TRADE_IMPORT: (serviceId: string) => {
      return `service_settings/trade_import/${serviceId}`
    },

    // tradelistdetail
    TRADELISTDETAIL_PAGINATION: 'serviceSettings/tradelistdetail_pagination',
    TRADELISTDETAIL_CREATE: 'serviceSettings/tradelistdetail_create_data',
    TRADELISTDETAIL_UPDATE: 'serviceSettings/tradelistdetail_update_data',
    TRADELISTDETAIL_DELETE: 'serviceSettings/tradelistdetail_update_active',

    // price
    PRICE_FIND: 'serviceSettings/price_find',
    PRICE_PAGINATION: 'serviceSettings/price_pagination',
    PRICE_CREATE: 'serviceSettings/price_create_data',
    PRICE_UPDATE: 'serviceSettings/price_update_data',
    PRICE_DELETE: 'serviceSettings/price_update_active',
    PRICE_DELETEALL: 'serviceSettings/price_deleteall',
    SETTING_FOMULAR: 'serviceSettings/setting_fomular',
    SETTING_WAY_CAL_SCORE_PRICE: 'serviceSettings/setting_way_cal_score_price',
    PRICE_IMPORT: (serviceId: string) => {
      return `service_settings/price_import/${serviceId}`
    },

    // pricelistdetail
    PRICELISTDETAIL_PAGINATION: 'serviceSettings/pricelistdetail_pagination',
    PRICELISTDETAIL_CREATE: 'serviceSettings/pricelistdetail_create_data',
    PRICELISTDETAIL_UPDATE: 'serviceSettings/pricelistdetail_update_data',
    PRICELISTDETAIL_DELETE: 'serviceSettings/pricelistdetail_update_active',

    // pricecol
    PRICECOL_PAGINATION: 'serviceSettings/pricecol_pagination',
    PRICECOL_CREATE: 'serviceSettings/pricecol_create_data',
    PRICECOL_UPDATE: 'serviceSettings/pricecol_update_data',
    PRICECOL_DELETE: 'serviceSettings/pricecol_update_active',

    // customprice
    CUSTOMPRICE_PAGINATION: 'serviceSettings/customprice_pagination',
    CUSTOMPRICE_CREATE: 'serviceSettings/customprice_create_data',
    CUSTOMPRICE_UPDATE: 'serviceSettings/customprice_update_data',
    CUSTOMPRICE_DELETE: 'serviceSettings/customprice_update_active',
    CUSTOMPRICE_DELETEALL: 'serviceSettings/customprice_deleteall',
    CUSTOMPRICE_IMPORT: (serviceId: string) => {
      return `service_settings/customprice_import/${serviceId}`
    },

    // rate
    RATE_UPDATE: 'serviceSettings/rate_update',
  }

  EMPLOYEE = {
    FIND: 'employee/find',
    PAGINATION: 'employee/pagination',
    CREATE: 'employee/create_data',
    UPDATE: 'employee/update_data',
    DELETE: 'employee/update_active',
    CREATE_BY_EXCEL: 'employee/create_data_by_excel',
    UPDATE_PASSWORD: 'employee/update_password',
  }

  AUTH = {
    LOAD_PERMISSION_USER: 'auth/load_permission_user',
    LOAD_PERMISSION_EMPLOYEE: 'auth/load_permission_employee',
    SAVE_PERMISSION_EMPLOYEE: 'auth/save_permission_employee',
  }

  BANNER_CLIENT = {
    PAGINATION: 'bannerClients/pagination',
    CREATE: 'bannerClients/create_data',
    UPDATE: 'bannerClients/update_data',
    DELETE: 'bannerClients/update_active',
  }

  LINK_CLIENT = {
    PAGINATION: 'linkClients/pagination',
    CREATE: 'linkClients/create_data',
    UPDATE: 'linkClients/update_data',
    DELETE: 'linkClients/update_active',
  }

  SETTING_STRING_CLIENT = {
    PAGINATION_FOOTER: 'settingStringClients/pagination_footer',
    PAGINATION_HEADER: 'settingStringClients/pagination_header',
    CREATE: 'settingStringClients/create_data',
    UPDATE: 'settingStringClients/update_data',
  }

  COMMENT_HISTORY = {
    PAGINATION: 'comment_history/pagination',
    CREATE: 'comment_history/create_data',
    UPDATE: 'comment_history/update_data',
  }

  EMAIL_TEMPLATE = {
    PAGINATION: 'emailTemplate/pagination',
    CREATE: 'emailTemplate/create_data',
    UPDATE: 'emailTemplate/update_data',
    DELETE: 'emailTemplate/update_active',
  }

  OFFER_DEAL = {
    LOAD_SUPPLIER_DATA: `offer_deal/load_supplier_data`,
    LOAD_SUPPLIER_DEAL: `offer_deal/load_supplier_deal`,
    LOAD_SUPPLIER_OFFER: `offer_deal/load_supplier_offer`,
    GET_PRICE: (bidId: string) => {
      return `offer_deal/get_price/${bidId}`
    },
    SAVE_OFFER_DEAL: `offer_deal/save_offer_deal`,
    SAVE_OFFER_SERVICE: `offer_deal/offer_service`,
  }

  OFFER_DETAIL = {
    GET_SUPPLIER_TECH_BY_BID_TECH_ID: (bidTechId: string) => {
      return `offer_details/get_supplier_tech_by_bid_tech_id/${bidTechId}`
    },
    GET_SUPPLIER_TRADE_BY_BID_TRADE_ID: (bidTradeId: string) => {
      return `offer_details/get_supplier_trade_by_bid_trade_id/${bidTradeId}`
    },
    GET_SUPPLIER_PRICE_BY_BID_PRICE_ID: (bidPriceId: string) => {
      return `offer_details/get_supplier_price_by_bid_price_id/${bidPriceId}`
    },
    FIND_DETAIL: `offer_details/find_detail`,
    GET_CHECK_DEAL_AND_AUCTION: (bidId: string) => {
      return `offer_details/get_check_deal_and_auction/${bidId}`
    },
    PAGINATION_HISTORY: `offer_details/pagination_history`,
    LOAD_BID_RESULT: `offer_details/load_bid_result`,
    GET_BID_RESULT_CAPACITY: (bidId: string) => {
      return `offer_details/get_bid_result_capacity/${bidId}`
    },
    GET_BID_RESULT_TECH: (bidId: string) => {
      return `offer_details/get_bid_result_tech/${bidId}`
    },
    GET_BID_RESULT_TRADE: (bidId: string) => {
      return `offer_details/get_bid_result_trade/${bidId}`
    },
    GET_BID_RESULT_PRICE: (bidId: string) => {
      return `offer_details/get_bid_result_price/${bidId}`
    },
    GET_BID_HISTORY_PRICE: (bidId: string) => {
      return `offer_details/get_bid_history_price/${bidId}`
    },
    GET_BID_RESULT_CUSTOM_PRICE: (bidId: string) => {
      return `offer_details/get_bid_result_custom_price/${bidId}`
    },

    GET_LIST_BID_RESULT_DEAL: `offer_details/get_list_bid_result_deal`,
    GET_BID_RESULT_DEAL: (bidDealId: string) => {
      return `offer_details/get_bid_result_deal/${bidDealId}`
    },
    GET_BID_RESULT_DEAL_SUPPLIER_DETAIL: (bidDealSupplierId: string) => {
      return `offer_details/get_bid_result_deal_supplier_detail/${bidDealSupplierId}`
    },

    GET_LIST_BID_RESULT_AUCTION: `offer_details/get_list_bid_result_auction`,
    GET_BID_RESULT_AUCTION: (bidAuctionId: string) => {
      return `offer_details/get_bid_result_auction/${bidAuctionId}`
    },
    GET_BID_RESULT_AUCTION_SUPPLIER_DETAIL: (bidAuctionSupplierId: string) => {
      return `offer_details/get_bid_result_auction_supplier_detail/${bidAuctionSupplierId}`
    },
  }

  OFFER = {
    FIND_WITH_EX: 'offer/find_with_ex',
    FIND: 'offer/find',
    FIND_ONE: 'offer/find_one',
    FIND_PR_ITEMS: 'offer/find_item',
    GET_BID_RESULT_DEAL: (bidDealId: string) => {
      return `offer/get_bid_result_deal/${bidDealId}`
    },
    LOAD_SUPPLIER_INVITE: `offer/load_supplier_invite`,
    BID_CHOOSE_SUPPLIER: `offer/bid_choose_supplier`,
    ITEMTECHLISTDETAIL_CREATE: 'offer/list_detail_create_data',
    ITEMTECHLISTDETAIL_UPDATE: 'offer/list_detail_update_data',
    ITEMTECHLISTDETAIL_DELETE: 'offer/list_detail_delete_data',
    FIND_ALL: 'offer/find_all',
    // GET_PRICE: 'offer/config_price_pagination',
    PAGINATION: 'offer/pagination',
    UPDATE_TIME: 'offer/update_time',
    SEND_APPROVE: 'offer/send_approve',
    APPROVE: 'offer/approve',
    PUBLIC: 'offer/public',
    CREATE: 'offer/create_data',
    UPDATE: 'offer/update_data',
    DELETE: 'offer/update_active',
    FIND_DETAIL: 'offer/delete_data',
    LOAD_OFFER_BY_SUPPLIER: 'offer/load_offer_by_supplier',
    ITEMTECHLISTDETAIL_LIST: (itemTechId: string) => {
      return `offer/listdetail_list/${itemTechId}`
    },
    GET_LIST_OFFER_RESULT_DEAL: `offer/get_list_bid_result_deal`,
    /* Trade */
    LOADTRADE: (offerId: any) => {
      return `offer/load_trade/${offerId}`
    },
    CREATE_TRADE: (offerId: any) => {
      return `offer/create_trade/${offerId}`
    },

    SEND_ACCEPT: (bidId: string) => {
      return `bids/send_trade/${bidId}`
    },

    TRADE_ACCEPT: (offerId: any) => {
      return `offer/trade_accept/${offerId}`
    },
    GET_TRADE: (offerId: any) => {
      return `offer/get_trade/${offerId}`
    },
    OFFERTRADE_GET: (offerId: any) => {
      return `offer/trade_get_data/${offerId}`
    },
    OFFERTRADE_CREATE: 'offer/trade_create_data',
    OFFERTRADE_UPDATE: 'offer/trade_update_data',
    OFFERTRADE_DELETE: 'offer/trade_delete_data',
    OFFERTRADE_DELETEALL: 'offer/trade_deleteall_data',
    OFFERTRADE_IMPORT: (offerId: any) => {
      return `offer/trade_import/${offerId}`
    },
    OFFERTRADELISTDETAIL_LIST: (bidTradeId: any) => {
      return `offer/trade_listdetail_list/${bidTradeId}`
    },
    OFFERTRADELISTDETAIL_CREATE: 'offer/trade_listdetail_create_data',
    OFFERTRADELISTDETAIL_UPDATE: 'offer/trade_listdetail_update_data',
    OFFERTRADELISTDETAIL_DELETE: 'offer/trade_listdetail_delete_data',

    //#region a
    LOAD_PRICE: (offer: any) => {
      return `offer/load_price/${offer}`
    },
    LOAD_PRICE_SERVICE: (offer: any) => {
      return `offer/load_price_service/${offer}`
    },

    LOAD_CUSTOMPRICE: (offer: any) => {
      return `offer/load_customprice/${offer}`
    },
    CREATE_PRICE: (offer: any) => {
      return `offer/create_price/${offer}`
    },
    PRICE_ACCEPT: (offer: any) => {
      return `offer/price_accept/${offer}`
    },
    GET_PRICE: (offer: any) => {
      return `offer/get_price/${offer}`
    },
    RESET_PRICE: (offer: any) => {
      return `offer/reset_price/${offer}`
    },
    SAVE_RESET_PRICE: (offer: any) => {
      return `offer/save_reset_price/${offer}`
    },
    BID_SUPPLIER_JOIN_RESET_PRICE: (offer: any) => {
      return `offer/bid_supplier_join_reset_price/${offer}`
    },
    END_RESET_PRICE: (offer: any) => {
      return `offer/end_reset_price/${offer}`
    },
    GET_CUSTOMPRICE: (offer: any) => {
      return `offer/get_customprice/${offer}`
    },
    OFFERPRICE_FIND: 'offer/price_find',
    OFFERPRICE_CREATE: 'offer/price_create_data',
    OFFERPRICE_UPDATE: 'offer/price_update_data',
    OFFERPRICE_DELETE: 'offer/price_delete_data',
    OFFERPRICE_DELETEALL: 'offer/price_deleteall_data',
    SETTING_FOMULAR: 'offer/setting_fomular',
    SETTING_WAY_CAL_SCORE_PRICE: 'offer/setting_way_cal_score_price',
    OFFERPRICE_IMPORT: (offer: any) => {
      return `offer/price_import/${offer}`
    },

    OFFERCUSTOMPRICE_CREATE: 'offer/customprice_create_data',
    OFFERCUSTOMPRICE_UPDATE: 'offer/customprice_update_data',
    OFFERCUSTOMPRICE_DELETE: 'offer/customprice_delete_data',
    OFFERCUSTOMPRICE_DELETEALL: 'offer/customprice_deleteall_data',
    OFFERCUSTOMPRICE_IMPORT: (offer: any) => {
      return `offer/customprice_import/${offer}`
    },

    OFFERPRICECOL_LIST: (offer: any) => {
      return `offer/price_col_list/${offer}`
    },
    OFFERPRICECOL_CREATE: 'offer/price_col_create_data',
    OFFERPRICECOL_UPDATE: 'offer/price_col_update_data',
    OFFERPRICECOL_DELETE: 'offer/price_col_delete_data',
    OFFERPRICECOL_DELETEALL: 'offer/price_col_delete_all_data',

    OFFERPRICELISTDETAIL_LIST: (bidPriceId: any) => {
      return `offer/price_listdetail_list/${bidPriceId}`
    },
    OFFERPRICELISTDETAIL_CREATE: 'offer/price_listdetail_create_data',
    OFFERPRICELISTDETAIL_UPDATE: 'offer/price_listdetail_update_data',
    OFFERPRICELISTDETAIL_DELETE: 'offer/price_listdetail_delete_data',
    FIND_OFFER_PR: `offer/find_offer_pr`,

    GET_BID_RESULT_TECH: (bidId: string) => {
      return `offer/get_bid_result_tech/${bidId}`
    },
    GET_BID_RESULT_TRADE: (bidId: string) => {
      return `offer/get_bid_result_trade/${bidId}`
    },
    GET_BID_RESULT_PRICE: (bidId: string) => {
      return `offer/get_bid_result_price/${bidId}`
    },

    OFFER_SHIPMENT_FIND: 'offer/find_with_shipment',
    OFFER_SUPPLIER_SHIPMENT: 'offer/load_offer_supplier_from_offer',
    OFFER_SUPPLIER_SHIPMENT_VALUE: 'offer/load_supplier_shipment_value',
    //#endregion a
  }

  OFFER_EVALUATION = {
    AUTO_OFFER: `offer_evaluation/auto_offer`,
    LOAD_SUPPLIER_DATA: `offer_evaluation/load_supplier_data`,
    EVALUATION_OFFER_SUPPLIER: `offer_evaluation/evaluation_offer_supplier`,
    APPROVE_SUPPLIER_WIN_OFFER: `offer_evaluation/approve_supplier_win_offer`,
    REJECT_SUPPLIER_WIN_OFFER: `offer_evaluation/reject_supplier_win_offer`,
  }
  OFFER_RATE = {
    // Danh sách gói thầu cần đánh giá
    PAGINATION: 'offerRates/pagination',

    // Lấy ds ncc tham gia thầu và tính điểm
    LOAD_TECH_RATE: (bidId: string) => `offerRates/load_tech_rate/${bidId}`,
    // Hàm lấy danh sách sách bidTech và điểm cao nhất tương ứng
    LOAD_BEST_TECH_VALUE: `offerRates/load_best_tech_value`,
    // Tạo đánh giá kỹ thuật
    CREATE_TECH_RATE: `offerRates/create_tech_rate`,
    // Duyệt đánh giá kỹ thuật
    APPROVE_TECH_RATE: `offerRates/approve_tech_rate`,
    // Từ chối đánh giá kỹ thuật
    REJECT_TECH_RATE: `offerRates/reject_tech_rate`,

    // Lấy ds ncc tham gia thầu và tính điểm
    LOAD_TRADE_RATE: (bidId: string) => `offerRates/load_trade_rate/${bidId}`,
    // Hàm lấy danh sách sách bidTrade và điểm cao nhất tương ứng
    LOAD_BEST_TRADE_VALUE: `offerRates/load_best_trade_value`,
    // Tạo đánh giá thương mại
    CREATE_TRADE_RATE: `offerRates/create_trade_rate`,
    // Duyệt đánh giá thương mại
    APPROVE_TRADE_RATE: `offerRates/approve_trade_rate`,
    // Từ chối đánh giá thương mại
    REJECT_TRADE_RATE: `offerRates/reject_trade_rate`,

    // Lấy ds ncc tham gia thầu và tính điểm
    LOAD_PRICE_RATE: (bidId: string) => `offerRates/load_price_rate/${bidId}`,
    // Hàm lấy danh sách sách bidPrice và điểm cao nhất tương ứng
    LOAD_BEST_PRICE_VALUE: `offerRates/load_best_price_value`,
    // Tạo đánh giá giá
    CREATE_PRICE_RATE: `offerRates/create_price_rate`,

    // Hàm lấy xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)
    LOAD_RANK_BY_MIN_PRICE: 'offerRates/load_rank_by_min_price',
    // Hàm lấy xếp hạng theo tổng giá (Giá theo từng NCC)
    LOAD_RANK_BY_SUM_PRICE: 'offerRates/load_rank_by_sum_price',
    // Hàm lấy xếp hạng theo tổng giá (Giá theo từng NCC) mỗi dòng là 1 ncc
    LOAD_SUPPLIER_RANK_BY_SUM_PRICE: 'offerRates/load_supplier_rank_by_sum_price',

    // Báo cáo kết quả đánh giá gói thầu
    GET_DATA_REPORT: (bidId: string) => {
      return `offerRates/get_data_report_rate_bid/${bidId}`
    },
    // In kết quả đánh giá gói thầu
    GET_DATA_PRINT: (bidId: string) => {
      return `offerRates/get_data_print_rate_bid/${bidId}`
    },

    SEND_REQUEST_FINISH_BID: `offerRates/send_request_finish_bid`,
    APPROVE_FINISH_BID: `offerRates/approve_finish_bid`,

    // Danh sách gói thầu hoàn tất
    RESULT_PAGINATION: 'offerRates/result_pagination',

    // Danh sách Item khi đàm phán/ đấu giá
    ITEM_PAGINATION: 'offerRates/item_pagination',
  }

  FAQ_CATEGORY = {
    FIND: 'faq_category/find',
    PAGINATION: 'faq_category/pagination',
    CREATE: 'faq_category/create_data',
    UPDATE: 'faq_category/update_data',
    DELETE: 'faq_category/update_active',
  }

  FAQ = {
    PAGINATION: 'faq/pagination',
    CREATE: 'faq/create_data',
    UPDATE: 'faq/update_data',
    DELETE: 'faq/update_active',
  }
  /** View Duyệt hồ sơ năng lực NCC */
  SUPPLIER_REVIEWAL = {
    PAGINATION: 'supplier_reviewal/pagination',
    APPROVE_SUPPLIER: `supplier_reviewal/approve_supplier`,
    REJECT_SUPPLIER: `supplier_reviewal/reject_supplier`,
    EMPLOYEE_APPROVE_SUPPLIER: `supplier_reviewal/employee_approve`,
    EMPLOYEE_REJECT_SUPPLIER: `supplier_reviewal/employee_reject`,
    CHECK_PERMISSION_APPROVE: (serviceId: string) => {
      return `supplier_reviewal/check_permission_approve/${serviceId}`
    },
    CHECK_PERMISSION_ACCESS: (serviceId: string) => {
      return `supplier_reviewal/check_permission_access/${serviceId}`
    },
    GET_HISTORY_CAPACITIES: (supplierServiceId: string) => {
      return `supplier_reviewal/get_supplier_capacity_history/${supplierServiceId}`
    },
    GET_HISTORY_LAW: (supplierId: string) => {
      return `supplier_reviewal/get_supplier_law_history/${supplierId}`
    },
    /** Xóa thông tin đăng ký */
    DELETE_SUPPLIER: (supplierServiceId: string) => {
      return `supplier_reviewal/delete_supplier/${supplierServiceId}`
    },
    DEACTIVE_SUPPLIER: (supplierServiceId: string) => {
      return `supplier_reviewal/deactive_supplier/${supplierServiceId}`
    },
  }

  /** View Danh sách NCC */
  SUPPLIER = {
    FIND_DETAIL: `suppliers/find_detail`,
    CREATE_SUPPLIER: `suppliers/create_supplier`,
    UPDATE_SUPPLIER: `suppliers/update_supplier`,
    GET_SUPPLIER_SERVICE_CAPACITY: (supplierServiceId: string) => {
      return `suppliers/get_supplier_service_capacity/${supplierServiceId}`
    },
    LOAD_OFFER: 'suppliers/find_offer',
    SUPPLIER_SERVICE_PAGINATION: 'suppliers/supplier_service_pagination',
    SUPPLIER_SERVICE_TO_EXPERTISE: 'suppliers/supplier_service_to_expertise',
    SUPPLIER_PAGINATION: 'suppliers/supplier_pagination',
    UPDATE_PASSWORD: 'suppliers/update_password',
    IMPORT: 'suppliers/import',
    DELETE: 'suppliers/delete',
    FIND: 'suppliers/find',

    GET_SERVICES_CAN_ADD: 'suppliers/get_services_can_add',
    LOAD_SUPPLIER_SERVICE: 'suppliers/load_supplier_service',
    SAVE_SUPPLIER_SERVICE: 'suppliers/save_supplier_service',
    ADD_SUPPLIER_SERVICE: 'suppliers/add_supplier_service',

    LOAD_CAPACITY: `suppliers/load_capacity`,
    SAVE_SUPPLIER_SERVICE_CAPACITY: `suppliers/save_supplier_service_capacity`,
    DELETE_ALL_CAPACITY: `suppliers/delete_all_capacity`,

    PAGINATION_BID_HISTORY: `suppliers/pagination_bid_history`,
  }

  /** View thẩm định NCC */
  SUPPLIER_EXPERTISE = {
    PAGINATION: 'supplier_expertise/pagination',
    GET_LAW: (supplierExpertiseId: string) => {
      return `supplier_expertise/get_law/${supplierExpertiseId}`
    },
    GET_CAPACITY: (supplierExpertiseId: string) => {
      return `supplier_expertise/get_capacity/${supplierExpertiseId}`
    },
    GET_PERMISSION: (supplierExpertiseId: string) => {
      return `supplier_expertise/get_permission/${supplierExpertiseId}`
    },
    SAVE_LAW: `supplier_expertise/save_law`,
    SAVE_CAPACITY: `supplier_expertise/save_capacity`,
    FINISH: `supplier_expertise/finish`,
    CANCEL: `supplier_expertise/cancel`,
  }

  /** Chức năng admin nộp hồ sơ thầu giúp ncc */
  BIDDING = {
    CREATE_BID_SUPPLIER: 'bids/bidding_from_admin_createBidSupplier',
    LOAD_DATA_BID_TECH: 'bids/bidding_from_admin_loadDataBidTech',
    LOAD_DATA_BID_TRADE: 'bids/bidding_from_admin_loadDataBidTrade',
    LOAD_DATA_BID_PRICE: 'bids/bidding_from_admin_loadDataBidPrice',
    LOAD_DATA_BID_CUSTOMPRICE: 'bids/bidding_from_admin_loadDataBidCustomPrice',
  }

  /** View danh sách gói thầu */
  BID = {
    FIND: `bids/find`,
    FIND_DETAIL_EDIT: `bids/find_detail_edit`,
    // common
    PAGINATION: `bids/pagination`,
    FIND_BID_SUPPLIER: `bids/find_bid_supplier`,
    // load ds NCC để nộp thầu trên site admin
    LOAD_BID_SUPPLIER: `bids/load_bid_supplier`,
    SEND_EMAIL_BID: `bids/send_email_bid`,

    // create bid
    CREATE_BID: `bids/create_bid`,
    COPY_BID: `bids/copy_bid`,
    UPDATE_BID: `bids/update_bid`,
    IMPORT_BID: `bids/import_bid`,
    REQUEST_DELETE_BID: (bidId: string) => {
      return `bids/request_delete_bid/${bidId}`
    },
    DELETE_BID: (bidId: string) => {
      return `bids/delete_bid/${bidId}`
    },
    RATE_UPDATE: `bids/update_setting_rate`,
    GET_BID_STATUS: (id: string) => {
      return `bids/get_bid_status/${id}`
    },
    SEND_MPOLEADER_CHECK_BID: (bidId: string) => {
      return `bids/send_mpoleader_check_bid/${bidId}`
    },
    MPOLEADER_ACCEPT_BID: (bidId: string) => {
      return `bids/mpoleader_accept_bid/${bidId}`
    },
    MPOLEADER_ACCEPT_BID_QUICK: (bidId: string) => {
      return `bids/mpoleader_accept_bid_quick/${bidId}`
    },
    MPOLEADER_REJECT_BID: (bidId: string) => {
      return `bids/mpoleader_reject_bid/${bidId}`
    },

    // bidTech
    LOADTECH: (bidId: string) => {
      return `bids/load_tech/${bidId}`
    },
    CREATE_TECH: (bidId: string) => {
      return `bids/create_tech/${bidId}`
    },
    GET_TECH: (bidId: string) => {
      return `bids/get_tech/${bidId}`
    },
    TECH_ACCEPT: (bidId: string) => {
      return `bids/tech_accept/${bidId}`
    },
    TECH_REJECT: (bidId: string) => {
      return `bids/tech_reject/${bidId}`
    },
    BIDTECH_GET: (bidId: string) => {
      return `bids/tech_get_data/${bidId}`
    },
    BIDITEM_GET: (bidId: string) => {
      return `bids/item_get_data/${bidId}`
    },
    BIDTECH_CREATE: 'bids/tech_create_data',
    BIDTECH_UPDATE: 'bids/tech_update_data',
    BIDTECH_DELETE: 'bids/tech_delete_data',
    BIDTECH_DELETEALL: 'bids/tech_deleteall_data',
    BIDTECH_IMPORT: (bidId: string) => {
      return `bids/tech_import/${bidId}`
    },
    BIDTECHLISTDETAIL_LIST: (bidTechId: string) => {
      return `bids/tech_listdetail_list/${bidTechId}`
    },
    BIDTECHLISTDETAIL_CREATE: 'bids/tech_listdetail_create_data',
    BIDTECHLISTDETAIL_UPDATE: 'bids/tech_listdetail_update_data',
    BIDTECHLISTDETAIL_DELETE: 'bids/tech_listdetail_delete_data',

    // bidTrade
    LOADTRADE: (bidId: string) => {
      return `bids/load_trade/${bidId}`
    },
    CREATE_TRADE: (bidId: string) => {
      return `bids/create_trade/${bidId}`
    },
    GET_TRADE: (bidId: string) => {
      return `bids/get_trade/${bidId}`
    },
    BIDTRADE_GET: (bidId: string) => {
      return `bids/trade_get_data/${bidId}`
    },
    BIDTRADE_CREATE: 'bids/trade_create_data',
    BIDTRADE_UPDATE: 'bids/trade_update_data',
    BIDTRADE_DELETE: 'bids/trade_delete_data',
    BIDTRADE_DELETEALL: 'bids/trade_deleteall_data',
    BIDTRADE_IMPORT: (bidId: string) => {
      return `bids/trade_import/${bidId}`
    },
    BIDTRADELISTDETAIL_LIST: (bidTradeId: string) => {
      return `bids/trade_listdetail_list/${bidTradeId}`
    },
    BIDTRADELISTDETAIL_CREATE: 'bids/trade_listdetail_create_data',
    BIDTRADELISTDETAIL_UPDATE: 'bids/trade_listdetail_update_data',
    BIDTRADELISTDETAIL_DELETE: 'bids/trade_listdetail_delete_data',

    // bidPrice
    LOAD_PRICE: (bidId: string) => {
      return `bids/load_price/${bidId}`
    },
    LOAD_CUSTOMPRICE: (bidId: string) => {
      return `bids/load_customprice/${bidId}`
    },
    CREATE_PRICE: (bidId: string) => {
      return `bids/create_price/${bidId}`
    },
    GET_PRICE: (bidId: string) => {
      return `bids/get_price/${bidId}`
    },
    RESET_PRICE: (bidId: string) => {
      return `bids/reset_price/${bidId}`
    },
    SAVE_RESET_PRICE: (bidId: string) => {
      return `bids/save_reset_price/${bidId}`
    },
    BID_SUPPLIER_JOIN_RESET_PRICE: (bidId: string) => {
      return `bids/bid_supplier_join_reset_price/${bidId}`
    },
    END_RESET_PRICE: (bidId: string) => {
      return `bids/end_reset_price/${bidId}`
    },
    GET_CUSTOMPRICE: (bidId: string) => {
      return `bids/get_customprice/${bidId}`
    },
    BIDPRICE_FIND: 'bids/price_find',
    BIDPRICE_CREATE: 'bids/price_create_data',
    BIDPRICE_UPDATE: 'bids/price_update_data',
    BIDPRICE_DELETE: 'bids/price_delete_data',
    BIDPRICE_DELETEALL: 'bids/price_deleteall_data',
    SETTING_FOMULAR: 'bids/setting_fomular',
    SETTING_WAY_CAL_SCORE_PRICE: 'bids/setting_way_cal_score_price',
    BIDPRICE_IMPORT: (bidId: string) => {
      return `bids/price_import/${bidId}`
    },

    BIDCUSTOMPRICE_CREATE: 'bids/customprice_create_data',
    BIDCUSTOMPRICE_UPDATE: 'bids/customprice_update_data',
    BIDCUSTOMPRICE_DELETE: 'bids/customprice_delete_data',
    BIDCUSTOMPRICE_DELETEALL: 'bids/customprice_deleteall_data',
    BIDCUSTOMPRICE_IMPORT: (bidId: string) => {
      return `bids/customprice_import/${bidId}`
    },

    BIDPRICECOL_LIST: (bidId: string) => {
      return `bids/price_col_list/${bidId}`
    },
    BIDPRICECOL_CREATE: 'bids/price_col_create_data',
    BIDPRICECOL_UPDATE: 'bids/price_col_update_data',
    BIDPRICECOL_DELETE: 'bids/price_col_delete_data',
    BIDPRICECOL_DELETEALL: 'bids/price_col_delete_all_data',

    BIDPRICELISTDETAIL_LIST: (bidPriceId: string) => {
      return `bids/price_listdetail_list/${bidPriceId}`
    },
    BIDPRICELISTDETAIL_CREATE: 'bids/price_listdetail_create_data',
    BIDPRICELISTDETAIL_UPDATE: 'bids/price_listdetail_update_data',
    BIDPRICELISTDETAIL_DELETE: 'bids/price_listdetail_delete_data',

    // bidChooseSupplier
    LOAD_SUPPLIER_INVITE: `bids/load_supplier_invite`,
    BID_CHOOSE_SUPPLIER: `bids/bid_choose_supplier`,
    BID_RECHOOSE_SUPPLIER: (bidId: string) => {
      return `bids/bid_rechoose_supplier/${bidId}`
    },
    BID_SEND_MPOLEADER_CHECK: (bidId: string) => {
      return `bids/bid_send_mpoleader_check/${bidId}`
    },

    // acceptAll
    ACCEPT_ALL: `bids/accept_all`,
    REJECT_ALL: `bids/rejectall`,

    // openBid
    LOAD_BID_SUPPLIER_OPEN_BID: `bids/load_bid_supplier_open_bid`,
    OPEN_BID: `bids/open_bid`,
  }

  /** Module đánh giá hồ sơ NCC tham gia thầu */
  BID_RATE = {
    // Danh sách gói thầu cần đánh giá
    PAGINATION: 'bidRates/pagination',

    // Lấy ds ncc tham gia thầu và tính điểm
    LOAD_TECH_RATE: (bidId: string) => `bidRates/load_tech_rate/${bidId}`,
    // Hàm lấy danh sách sách bidTech và điểm cao nhất tương ứng
    LOAD_BEST_TECH_VALUE: `bidRates/load_best_tech_value`,
    // Tạo đánh giá kỹ thuật
    CREATE_TECH_RATE: `bidRates/create_tech_rate`,
    // Duyệt đánh giá kỹ thuật
    APPROVE_TECH_RATE: `bidRates/approve_tech_rate`,
    // Từ chối đánh giá kỹ thuật
    REJECT_TECH_RATE: `bidRates/reject_tech_rate`,

    // Lấy ds ncc tham gia thầu và tính điểm
    LOAD_TRADE_RATE: (bidId: string) => `bidRates/load_trade_rate/${bidId}`,
    // Hàm lấy danh sách sách bidTrade và điểm cao nhất tương ứng
    LOAD_BEST_TRADE_VALUE: `bidRates/load_best_trade_value`,
    // Tạo đánh giá thương mại
    CREATE_TRADE_RATE: `bidRates/create_trade_rate`,
    // Duyệt đánh giá thương mại
    APPROVE_TRADE_RATE: `bidRates/approve_trade_rate`,
    // Từ chối đánh giá thương mại
    REJECT_TRADE_RATE: `bidRates/reject_trade_rate`,

    // Lấy ds ncc tham gia thầu và tính điểm
    LOAD_PRICE_RATE: (bidId: string) => `bidRates/load_price_rate/${bidId}`,
    // Hàm lấy danh sách sách bidPrice và điểm cao nhất tương ứng
    LOAD_BEST_PRICE_VALUE: `bidRates/load_best_price_value`,
    // Tạo đánh giá giá
    CREATE_PRICE_RATE: `bidRates/create_price_rate`,

    // Hàm lấy xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)
    LOAD_RANK_BY_MIN_PRICE: 'bidRates/load_rank_by_min_price',
    // Hàm lấy xếp hạng theo tổng giá (Giá theo từng NCC)
    LOAD_RANK_BY_SUM_PRICE: 'bidRates/load_rank_by_sum_price',
    // Hàm lấy xếp hạng theo tổng giá (Giá theo từng NCC) mỗi dòng là 1 ncc
    LOAD_SUPPLIER_RANK_BY_SUM_PRICE: 'bidRates/load_supplier_rank_by_sum_price',

    // Báo cáo kết quả đánh giá gói thầu
    GET_DATA_REPORT: (bidId: string) => {
      return `bidRates/get_data_report_rate_bid/${bidId}`
    },
    // In kết quả đánh giá gói thầu
    GET_DATA_PRINT: (bidId: string) => {
      return `bidRates/get_data_print_rate_bid/${bidId}`
    },

    SEND_REQUEST_FINISH_BID: `bidRates/send_request_finish_bid`,
    APPROVE_FINISH_BID: `bidRates/approve_finish_bid`,

    // Danh sách gói thầu hoàn tất
    RESULT_PAGINATION: 'bidRates/result_pagination',

    // Danh sách Item khi đàm phán/ đấu giá
    ITEM_PAGINATION: 'bidRates/item_pagination',
  }

  /** View đàm phán giá */
  BID_DEAL = {
    LOAD_SUPPLIER_DATA: `bid_deal/load_supplier_data`,
    GET_PRICE: (bidId: string) => {
      return `bid_deal/get_price/${bidId}`
    },
    SAVE_BID_DEAL: `bid_deal/save_bid_deal`,
    CALL_AI: `bid_deal/call_ai`,
    GET_PRICE_FROM_AI: `bid_deal/get_price_ai`,
  }

  BID_AUCTION = {
    LOAD_SUPPLIER_DATA: `bid_auction/load_supplier_data`,
    GET_PRICE: (bidId: string) => {
      return `bid_auction/get_price/${bidId}`
    },
    SAVE_BID_AUCTION: `bid_auction/save_bid_auction`,
  }

  BID_DETAIL = {
    GET_SUPPLIER_TECH_BY_BID_TECH_ID: (bidTechId: string) => {
      return `bid_details/get_supplier_tech_by_bid_tech_id/${bidTechId}`
    },
    GET_SUPPLIER_TRADE_BY_BID_TRADE_ID: (bidTradeId: string) => {
      return `bid_details/get_supplier_trade_by_bid_trade_id/${bidTradeId}`
    },
    GET_SUPPLIER_PRICE_BY_BID_PRICE_ID: (bidPriceId: string) => {
      return `bid_details/get_supplier_price_by_bid_price_id/${bidPriceId}`
    },
    FIND_DETAIL: `bid_details/find_detail`,
    GET_CHECK_DEAL_AND_AUCTION: (bidId: string) => {
      return `bid_details/get_check_deal_and_auction/${bidId}`
    },
    PAGINATION_HISTORY: `bid_details/pagination_history`,
    LOAD_BID_RESULT: `bid_details/load_bid_result`,
    GET_BID_RESULT_CAPACITY: (bidId: string) => {
      return `bid_details/get_bid_result_capacity/${bidId}`
    },
    GET_BID_RESULT_TECH: (bidId: string) => {
      return `bid_details/get_bid_result_tech/${bidId}`
    },
    GET_BID_RESULT_TRADE: (bidId: string) => {
      return `bid_details/get_bid_result_trade/${bidId}`
    },
    GET_BID_RESULT_PRICE: (bidId: string) => {
      return `bid_details/get_bid_result_price/${bidId}`
    },
    GET_BID_HISTORY_PRICE: (bidId: string) => {
      return `bid_details/get_bid_history_price/${bidId}`
    },
    GET_BID_RESULT_CUSTOM_PRICE: (bidId: string) => {
      return `bid_details/get_bid_result_custom_price/${bidId}`
    },

    GET_LIST_BID_RESULT_DEAL: `bid_details/get_list_bid_result_deal`,
    GET_BID_RESULT_DEAL: (bidDealId: string) => {
      return `bid_details/get_bid_result_deal/${bidDealId}`
    },
    GET_BID_RESULT_DEAL_SUPPLIER_DETAIL: (bidDealSupplierId: string) => {
      return `bid_details/get_bid_result_deal_supplier_detail/${bidDealSupplierId}`
    },

    GET_LIST_BID_RESULT_AUCTION: `bid_details/get_list_bid_result_auction`,
    GET_BID_RESULT_AUCTION: (bidAuctionId: string) => {
      return `bid_details/get_bid_result_auction/${bidAuctionId}`
    },
    GET_BID_RESULT_AUCTION_SUPPLIER_DETAIL: (bidAuctionSupplierId: string) => {
      return `bid_details/get_bid_result_auction_supplier_detail/${bidAuctionSupplierId}`
    },
  }

  BID_EVALUATION = {
    AUTO_BID: `bid_evaluation/auto_bid`,
    LOAD_SUPPLIER_DATA: `bid_evaluation/load_supplier_data`,
    EVALUATION_BID_SUPPLIER: `bid_evaluation/evaluation_bid_supplier`,

    APPROVE_SUPPLIER_WIN_BID: `bid_evaluation/approve_supplier_win_bid`,
    REJECT_SUPPLIER_WIN_BID: `bid_evaluation/reject_supplier_win_bid`,
  }

  /** Đấu giá nhanh */
  AUCTION = {
    DETAIL: 'auction/find_detail',
    CREATE: 'auction/create_data',
    UPDATE: 'auction/update_data',
    CANCEL: 'auction/cancel_data',
    ADD_SUPPLIER: 'auction/add_supplier',
    PAGINATION: 'auction/pagination',
  }

  UPLOAD_FILE = {
    UPLOAD_SINGLE: 'uploadFiles/upload_single',
  }

  REPORT = {
    GET_REPORT_SUPPLIER: 'reports/report_suppliers',
    GET_REPORT_EXPERTISE: 'reports/report_expertise',
    GET_REPORT_BID: 'reports/report_bid',
    GET_REPORT_HISTORY_PRICE_SUPPLIER: 'reports/report_history_price_supplier',
    GET_REPORT_HISTORY_PRICE_SERVICE: 'reports/report_history_price_service',
    GET_REPORT_HISTORY_PRICE_CATEGORY: 'reports/report_history_price_category',
    GET_REPORT_HISTORY_BID_SUPPLIER: 'reports/report_history_bid_supplier',
    GET_REPORT_HISTORY_BUYER: 'reports/report_history_buyer',
  }

  CONTRACT = {
    DETAIL: 'contract/find_detail',
    CREATE: 'contract/create_data',
    PAGINATION: 'contract/pagination',
    UPDATE_PROCESSING: 'contract/update_status_processing',
    UPDATE_COMPLETE: 'contract/update_status_complete',
    UPDATE_CANCEL: 'contract/update_status_cancel',
    FIND: 'contract/find',
    FIND_MIRROR: 'contract/find_mirror',
    UPDATE: 'contract/update_data',
  }
  CONTRACT_APPENDIX = {
    CREATE: 'contract_appendix/create_data',
    PAGINATION: 'contract_appendix/pagination',
    UPDATE: 'contract_appendix/update_data',
  }
  CONTRACT_HISTORY = {
    PAGINATION: 'contract_history/pagination',
  }

  OBJECT = {
    FIND: 'object/find',
    PAGINATION: 'object/pagination',
    CREATE: 'object/create_data',
    UPDATE: 'object/update_data',
    DELETE: 'object/update_active',
  }

  MATERIAL = {
    FIND: 'material/find',
    PAGINATION: 'material/pagination',
    CREATE: 'material/create_data',
    UPDATE: 'material/update_data',
    DELETE: 'material/update_active',
  }

  /** Tiến độ thanh toán */
  PAYMENT_PROGRESS = {
    FIND: 'payment_progress/find',
  }
  INVOICE = {
    CREATE: 'invoice/create_data',
  }
  INVOICE_SUGGEST = {
    FIND_DETAIL_FILE: 'invoice_suggest/pagination_file',
    FIND_DETAIL: 'invoice_suggest/find_detail',
    CREATE: 'invoice_suggest/create_data',
    UPDATE: 'invoice_suggest/update_data',
    DELETE: 'invoice_suggest/delete_data',
    PAGINATION: 'invoice_suggest/pagination',
  }
  PO = {
    CREATE: 'po/create_data',
    UPDATE: 'po/update_data',
    UPDATE_DELIVERY_DATE: 'po/update_delivery_date',
    UPDATE_STATUS: 'po/update_status',
    CREATE_EXCEL: 'po/create_data_excel',
    DETAIL: 'po/find_detail',
    PAGINATION: 'po/pagination',
    FIND: 'po/find',
    APPROVED: 'po/update_status_approved',
    COMPLETE: 'po/update_status_complete',
    REFUSE: 'po/update_status_refuse',
    CANCEL: 'po/update_status_cancel',
    LOAD_PO_PRODUCT: 'po/load_po_product',
    FIND_CONTRACT_PO: 'po/find_contract_po',
    FIND_LIST_PO_CONTRACT: 'po/load_list_po_contract',
  }
  PO_HISTORY = {
    PAGINATION: 'poHistory/pagination',
  }

  ASN = {
    FIND: 'asn/find',
    PAGINATION: 'asn/pagination',
    PAGINATION_DETAIL: 'asn/pagination_detail',
    CREATE: 'asn/create_data',
    UPDATE: 'asn/update_data',
    DELETE: 'asn/update_active',
    CREATEEXCEL: 'asn/create_data_excel',
    FINDASNPO: 'asn/find_asn_po',
  }

  BRANCH = {
    FIND: 'branch/find',
    FIND_ALL: 'branch/find_all',
    PAGINATION: 'branch/pagination',
    CREATE: 'branch/create_data',
    UPDATE: 'branch/update_data',
    DELETE: 'branch/update_active',
  }

  BRANCH_MEMBER = {
    PAGINATION: 'branch_member/pagination',
    CREATE: 'branch_member/create_data',
    UPDATE: 'branch_member/update_data',
    UPDATE_ACTIVE: 'branch_member/update_active',
  }

  PURCHASE_PLAN = {
    FIND: 'purchasePlan/find',
    FIND_DETAIL_EDIT: 'purchasePlan/find_detail_edit',
    FIND_DETAIL: 'purchasePlan/find_detail',
    PAGINATION: 'purchasePlan/pagination',
    CREATELIST: 'purchasePlan/create_data_list',
    CREATE: 'purchasePlan/create_data',
    UPDATE: 'purchasePlan/update_data',
    DELETE: 'purchasePlan/update_active',
  }

  WAREHOUSE = {
    FIND: 'warehouses/find',
    PAGINATION: 'warehouses/pagination',
    CREATE: 'warehouses/create_data',
    UPDATE: 'warehouses/update_data',
    DELETE: 'warehouses/update_active',
  }

  ASSET = {
    FIND: 'asset/find',
    PAGINATION: 'asset/pagination',
    CREATE: 'asset/create_data',
    UPDATE: 'asset/update_data',
    DELETE: 'asset/update_active',
  }

  PR = {
    FIND: 'pr/find',
    // load ds pr có thể tạo bid
    LOAD_PR_CREATE_BID: 'pr/load_pr_create_bid',
    FINDDETAIL: 'pr/find_detail',
    FIND_ITEM_DETAIL: 'pr/find_item_detail',
    PAGINATION: 'pr/pagination',
    CREATELIST: 'pr/create_data_list',
    PLANCREATE: 'pr/create_data_plan',
    PLANUPDATE: 'pr/update_data_plan',
    ARISECREATE: 'pr/create_data_arise',
    ARISEUPDATE: 'pr/update_data_arise',
    APPROVE: 'pr/approve_pr',
    CANCEL: 'pr/cancel_pr',
    PROCESS: 'pr/process_pr',
    CLOSE: 'pr/close_pr',
    ITEM: {
      GET_TECH: (prItemId: string) => {
        return `pr/item/get_tech/${prItemId}`
      },
      LOAD_TECH: (prItemId: string) => {
        return `pr/item/load_tech/${prItemId}`
      },
      DELETE_TECH: 'pr/item/tech_delete_data',
      DELETEALL_TECH: 'pr/item/tech_deleteall_data',
      IMPORT_TECH: (prItemId: string) => {
        return `pr/item/tech_import/${prItemId}`
      },

      ITEMTECH_GET: (prItemId: string) => {
        return `pr/item/tech_get_data/${prItemId}`
      },
      ITEMTECH_CREATE: 'pr/item/tech_create_data',
      ITEMTECH_UPDATE: 'pr/item/tech_update_data',

      ITEMTECHLISTDETAIL_LIST: (itemTechId: string) => {
        return `pr/item/tech_listdetail_list/${itemTechId}`
      },
      ITEMTECHLISTDETAIL_CREATE: 'pr/item/tech_listdetail_create_data',
      ITEMTECHLISTDETAIL_UPDATE: 'pr/item/tech_listdetail_update_data',
      ITEMTECHLISTDETAIL_DELETE: 'pr/item/tech_listdetail_delete_data',
    },

    RECHECK: 'pr/update_recheck_pr',
  }

  DASHBOARD = {
    DATA: 'dashboard/load_data_dashboard',
    SUPPLIER: 'dashboard/load_dashboard_supplier',
    SUPPLIER_TOP10: 'dashboard/load_dashboard_top10_supplier',
  }

  LANGUAGE = {
    FIND: 'language/find',
    PAGINATION: 'language/pagination',
    CREATE: 'language/create_data',
    UPDATE: 'language/update_data',
    DELETE: 'language/update_active',
  }

  LANGUAGE_KEY = {
    LOAD_DATA: `language_key/load_data`,
    UPDATE: `language_key/update_data`,
    REFRESH_DATA: `language_key/refresh_data`,
    IMPORT: `language_key/import_data`,
    LOAD_DATA_BY_TENANT: 'language_key/load_data_by_tenant',
  }

  LANGUAGE_CONFIG = {
    FIND_LANGUAGE_CONFIG: 'languageConfig/find_language_config',
    PAGINATION: 'languageConfig/pagination',
    CREATE: 'languageConfig/create_data',
    UPDATE: 'languageConfig/update_data',
    DELETE: 'languageConfig/update_active',
    CREATE_EXCEL: 'languageConfig/create_data_excel',
  }

  BILL = {
    FIND: `bill/find`,
    PAGINATION: 'bill/pagination',
    CREATE: 'bill/create_data',
    UPDATE: 'bill/update_data',
    UPDATE_ACTIVE: 'bill/update_active',
    IMPORT: 'bill/import_data',
    CANCEL: 'bill/update_cancel',
    SEND_BILL: 'bill/update_send',
    CONFIRMED: 'bill/update_confirmed',
    FIND_DETAIL: 'bill/find_detail',
    FIND_PO: 'bill/find_po',
    FIND_CONTRACT: 'bill/find_contract',
    FIND_BILL: 'bill/find_bill',
    GET_COMPANY: 'bill/load_companies_from_bizzi',
  }

  BILL_LOOKUP = {
    LOAD_DETAIL: `bill_lookup/load_detail`,
    FIND: `bill_lookup/find`,
    CREATE_DATA: `bill_lookup/create_data`,
    IMPORT_EXCEL: `bill_lookup/import_data`,
    UPDATE_DATA: `bill_lookup/update_data`,
    UPDATE_ACTIVE_STATUS: `bill_lookup/update_active_status`,
    PAGINATION: `bill_lookup/pagination`,
  }

  PAYMENT = {
    PAGINATION: 'payment/pagination',
    CREATE: 'payment/create_data',
    FIND_DETAIL: 'payment/find_detail',
    CHECKING: 'payment/update_checking',
    VALID_CONFIRM: 'payment/update_valid_confirm',
    RECHECK: 'payment/update_recheck',
    REQUEST_APPROVE: 'payment/request_approve',
    APPROVED: 'payment/approve_payment',
    REQUEST_CONFIRM: 'payment/update_request_confirm',

    FIND: `payment/find`,
    UPDATE: 'payment/update_data',
    UPDATE_ACTIVE: 'payment/update_active',
    IMPORT: 'payment/importdata',
    LOAD_PO: 'po/load_po_by_supplier',
    LOAD_CONTACT: 'contract/load_contract_by_supplier',
    LOAD_CURRENCY: 'currency/data_select_box',
    CANCEL: 'payment/update_cancel',
    SEND: 'payment/update_send',
    FIND_PO: 'payment/find_po',
    FIND_CONTRACT: 'payment/find_contract',
  }

  /** Enum inbound */
  INBOUND = {
    FIND: `inbound/find`,
    LOAD_DETAIL: `inbound/load_detail`,
    LOAD_LIST_ITEM: `inbound/load_list_item`,
    LOAD_LIST_CONTAINER: `inbound/load_list_container`,
    LOAD_DATA_BY_MONTH: `inbound/load_data_by_month`,
    LOAD_PAGINATION_BY_DATE: `inbound/load_pagination_by_date`,
    CREATE_DATA: `inbound/create_data`,
    UPDATE_APPROVED: `inbound/update_approved`,
    UPDATE_DATA: `inbound/update_data`,
    UPDATE_STATUS: `inbound/update_status`,
    PAGINATION: `inbound/pagination`,
    IMPORT_EXCEL: `inbound/import_excel`,
    UPDATE_CANCEL: `inbound/update_cancel`,
    LOAD_INBOUND_PO: `inbound/load_inbound_po`,
  }

  //#endregion

  //#region Handle

  objToQueryString = (obj: any) =>
    Object.keys(obj)
      .map((k) => {
        if (Array.isArray(obj[k])) {
          return `${k}=${JSON.stringify(obj[k])}`
        }
        return `${k}=${obj[k]}`
      })
      .join('&')

  post(url: string, data: any) {
    const request = this.http.post(this.host + url, data)
    return lastValueFrom(request) as Promise<any>
  }

  put(url: string, data: any) {
    const request = this.http.put(this.host + url, data)
    return lastValueFrom(request) as Promise<any>
  }

  get(url: string, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.host + url}?${query}`
    const request = this.http.get(newUrl)
    return lastValueFrom(request) as Promise<any>
  }
  //#endregion
}
