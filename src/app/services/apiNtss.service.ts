import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BehaviorSubject, lastValueFrom } from 'rxjs'
import { environment } from '../../environments/environment'
import { CoreService } from './core.service'
@Injectable()
export class ApiNtssService {
  hostRepair = `${environment.repair}/`
  eventCloseModal = new BehaviorSubject<boolean>(false)
  eventCloseModalPO = new BehaviorSubject<boolean>(false)
  eventUpdateCollectEmployee = new BehaviorSubject<boolean>(false)
  eventUpdateReadNotify = new BehaviorSubject<boolean>(false)
  eventChangeLocal = new BehaviorSubject<boolean>(false)
  constructor(public coreService: CoreService, private http: HttpClient) {}

  isLoggedIn() {
    return this.eventChangeLocal.asObservable()
  }

  //#region API Construct

  DEPARTMENT = {
    FIND: `${environment.backEndNTSS}/departments/find`,
    LOAD_DATA: `${environment.backEndNTSS}/departments/load_data`,
    LOAD_DETAIL: `${environment.backEndNTSS}/departments/load_detail`,
    PAGINATION: `${environment.backEndNTSS}/departments/pagination`,
    CREATE: `${environment.backEndNTSS}/departments/create_data`,
    UPDATE: `${environment.backEndNTSS}/departments/update_data`,
    DELETE: `${environment.backEndNTSS}/departments/update_delete`,
    ACTIVE: `${environment.backEndNTSS}/departments/update_active`,
  }

  WARNING = {
    WARNING_REFRESH_TOKEN_ZALO: `${environment.backEndNTSS}/setting_string/warning_refresh_token_zalo`,
  }

  WAREHOUSE = {
    FIND: `${environment.backEndNTSS}/warehouse/find`,
    LOAD_DATA: `${environment.backEndNTSS}/warehouse/load_data`,
    PAGINATION: `${environment.backEndNTSS}/warehouse/pagination`,
    CREATE: `${environment.backEndNTSS}/warehouse/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/warehouse/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/warehouse/update_data`,
    DELETE: `${environment.backEndNTSS}/warehouse/update_delete`,
    ACTIVE: `${environment.backEndNTSS}/warehouse/update_active`,
  }

  INBOUND = {
    FIND_DETAIL: `${environment.backEndNTSS}/inbound/find_detail`,
    FIND_FOR_EDIT: `${environment.backEndNTSS}/inbound/find_for_edit`,
    PAGINATION: `${environment.backEndNTSS}/inbound/pagination`,
    CREATE: `${environment.backEndNTSS}/inbound/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/inbound/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/inbound/update_data`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/inbound/update_cancel`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/inbound/update_approve`,
  }

  CITY = {
    LOAD_DATA: `${environment.backEndNTSS}/cities/load_data`,
    PAGINATION: `${environment.backEndNTSS}/cities/pagination`,
    CREATE: `${environment.backEndNTSS}/cities/create_data`,
    UPDATE: `${environment.backEndNTSS}/cities/update_data`,
    DELETE: `${environment.backEndNTSS}/cities/update_active`,
  }

  DISTRICT = {
    LOAD_DISTRICT_BY_CITY: (cityId: string) => {
      return `districts/LOAD_DISTRICT_BY_CITY/${cityId}`
    },
    LOAD_DATA: `${environment.backEndNTSS}/districts/load_data`,
    PAGINATION: `${environment.backEndNTSS}/districts/pagination`,
    CREATE: `${environment.backEndNTSS}/districts/create_data`,
    UPDATE: `${environment.backEndNTSS}/districts/update_data`,
    DELETE: `${environment.backEndNTSS}/districts/update_active`,
  }

  WARD = {
    LOAD_WARD_BY_DISTRICT: (districtId: string) => {
      return `wards/load_ward_by_district/${districtId}`
    },
    LOAD_DATA: `${environment.backEndNTSS}/wards/load_data`,
    PAGINATION: `${environment.backEndNTSS}/wards/pagination`,
    CREATE: `${environment.backEndNTSS}/wards/create_data`,
    UPDATE: `${environment.backEndNTSS}/wards/update_data`,
    DELETE: `${environment.backEndNTSS}/wards/update_active`,
  }

  SETTING_STRING = {
    FIND: `${environment.backEndNTSS}/setting_string/find`,
    FIND_KEY: `${environment.backEndNTSS}/setting_string/find_by_key`,
    PAGINATION: `${environment.backEndNTSS}/setting_string/pagination`,
    CREATE: `${environment.backEndNTSS}/setting_string/create_data`,
    CREATE_LIST: `${environment.backEndNTSS}/setting_string/create_data_list`,
    UPDATE: `${environment.backEndNTSS}/setting_string/update_data`,
    DELETE: `${environment.backEndNTSS}/setting_string/update_delete`,
  }

  EMPLOYEE = {
    PAGINATION: `${environment.backEndNTSS}/employee/pagination`,
    PAGINATION_FOR_REPAIR_STAFF: `${environment.backEndNTSS}/employee/pagination_for_repair_staff`,
    PAGINATION_FOR_MANAGER: `${environment.backEndNTSS}/employee/pagination_for_apartment_manager`,
    CREATE: `${environment.backEndNTSS}/employee/create_data`,
    UPDATE: `${environment.backEndNTSS}/employee/update_data`,
    DELETE: `${environment.backEndNTSS}/employee/update_active`,
    UPDATE_PASSWORD: `${environment.backEndNTSS}/employee/update_password`,
    LOAD_DATA: `${environment.backEndNTSS}/employee/load_data`,
    LOAD_BY_APARTMENT: `${environment.backEndNTSS}/employee/load_by_apartment`,
    LOAD_APARTMENT_FOR_EMPLOYEE: `${environment.backEndNTSS}/employee/load_apartment_for_employee`,
    LOAD_EMPLOYEE_BY_USER_TYPE: `${environment.backEndNTSS}/employee/load_employee_by_user_type`,
    LOAD_DETAIL: `${environment.backEndNTSS}/employee/load_employee_detail`,
    LOAD_DATA_BY_DEPARTMENT: `${environment.backEndNTSS}/employee/load_data_by_department`,
    UPDATE_LIST_SALE_EMPLOYEE: `${environment.backEndNTSS}/employee/update_list_sale_employee`,
    GET_LIST_REPAIR_EMPLOYEE: `${environment.backEndNTSS}/employee/get_list_repair_employee`,
  }

  UTILITY = {
    PAGINATION: `${environment.backEndNTSS}/utility/pagination`,
    PAGINATION_BY_APARTMENT_UTILITY: `${environment.backEndNTSS}/utility/pagination_by_apartment_utility`,
    CREATE: `${environment.backEndNTSS}/utility/create_data`,
    CREATE_APARTMENT_UTILITY: `${environment.backEndNTSS}/utility/create_apartment_utility`,
    CREATE_ROOM_UTILITY: `${environment.backEndNTSS}/utility/create_room_utility`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/utility/create_data_list`,
    CREATE_ROOM_UTILITY_BY_EXCEL: `${environment.backEndNTSS}/utility/create_room_utility_list`,
    UPDATE: `${environment.backEndNTSS}/utility/update_data`,
    DELETE: `${environment.backEndNTSS}/utility/update_active`,
    APARTMENT_UTILITY_PAGINATION: `${environment.backEndNTSS}/utility/apartment_utility_pagination`,
    ROOM_UTILITY_PAGINATION: `${environment.backEndNTSS}/utility/room_utility_pagination`,
    LOAD_ROOM_BY_APARTMENT: `${environment.backEndNTSS}/utility/load_room_by_apartment`,
  }

  UPLOAD_FILE = {
    UPLOAD_SINGLE: `${environment.backEndNTSS}/uploadFiles/upload_single`,
  }

  PERMISSION = {
    PAGINATION: `${environment.backEndNTSS}/permission/pagination`,
    CREATE: `${environment.backEndNTSS}/permission/create_data`,
  }

  CONTRACT = {
    DETAIL: `${environment.backEndNTSS}/contract/load_detail`,
    PAGINATION: `${environment.backEndNTSS}/contract/pagination`,
    EXPORT_DATA: `${environment.backEndNTSS}/contract/export_data`,
    PAGINATION_ABOUT_TO_EXPIRE: `${environment.backEndNTSS}/contract/pagination_about_to_expire`,
    CREATE: `${environment.backEndNTSS}/contract/create_data`,
    FIND_ACTIVE_CONTRACT: `${environment.backEndNTSS}/contract/find_active_contract`,
    UPDATE: `${environment.backEndNTSS}/contract/update_data`,
    DELETE: `${environment.backEndNTSS}/contract/update_delete`,
    ACTIVE: `${environment.backEndNTSS}/contract/update_active`,
    UPDATE_SERVICE_INIT_INDEX: `${environment.backEndNTSS}/contract/update_service_init_index`,
    UPDATE_SERVICE_CURRENT_INDEX: `${environment.backEndNTSS}/contract/update_service_current_index`,
    LOAD_SERVICE_BY_BILL: `${environment.backEndNTSS}/contract/load_service_by_bill`,
    LOAD_SERVICE_BY_CONTRACT: `${environment.backEndNTSS}/contract/load_service_by_contract`,
    CLOSE: `${environment.backEndNTSS}/contract/update_close`,
    CHECK_CLOSE: `${environment.backEndNTSS}/contract/check_close`,
    LOAD_RESIGN: `${environment.backEndNTSS}/contract/load_resign`,
    UPDATE_EXTEND: `${environment.backEndNTSS}/contract/update_extend`,
    UPDATE_EFFECTIVE: `${environment.backEndNTSS}/contract/update_effective`,
    UPDATE_RETURN_ROOM: `${environment.backEndNTSS}/contract/update_return_room`,
    LOAD_CONTRACT_SENT_ZALO_FAILED: `${environment.backEndNTSS}/contract/load_contract_sent_zalo_failed`,
    CREATE_DEPOSIT: `${environment.backEndNTSS}/contract/create_deposit`,
    IMPORT_EXCEL: `${environment.backEndNTSS}/contract/import_excel`,
    LOAD_LIQUIDATION_CONTRACT: `${environment.backEndNTSS}/contract/load_liquidation_contract`,
    CHECK_CONTRACT_FOR_DEPOSIT: `${environment.backEndNTSS}/contract/check_contract_for_deposit`,
    UPDATE_CONTRACT_DEPOSIT: `${environment.backEndNTSS}/contract/update_contract_deposit`,
    FIND_CONTRACT_RESIDENT_DEPOSIT: `${environment.backEndNTSS}/contract/find_contract_resident_deposit`,
    LOAD_SERVICE_BEFORE_CLOSE_CONTRACT: `${environment.backEndNTSS}/contract/load_service_before_close_contract`,
    COMPUTE_TOTAL_MONEY: `${environment.backEndNTSS}/contract/compute_total_money`,

    /** Dùng để export excel */
    LOAD_SERVICE_OF_CONTRACT: `${environment.backEndNTSS}/contract/load_service_of_contract`,
    LOAD_RESIDENT_OF_CONTRACT: `${environment.backEndNTSS}/contract/load_resident_of_contract`,

    LOAD_WARNING_TEXT: `${environment.backEndNTSS}/contract/load_warning_text_when_create_new`,
  }

  SERVICE = {
    LOAD_DATA: `${environment.backEndNTSS}/service/load_data`,
    PAGINATION: `${environment.backEndNTSS}/service/pagination`,
    CREATE: `${environment.backEndNTSS}/service/create_data`,
    UPDATE: `${environment.backEndNTSS}/service/update_data`,
    DELETE: `${environment.backEndNTSS}/service/update_active`,
    LOAD_SERVICE_BY_APARTMENT: `${environment.backEndNTSS}/service/load_service_by_apartment`,
    LOAD_SERVICE_OF_APARTMENT: `${environment.backEndNTSS}/service/load_service_of_apartment`,
    LOAD_SERVICE_APARTMENT_BY_SERVICE: `${environment.backEndNTSS}/service/load_service_apartment_by_service`,
    UPDATE_SERVICE_APARTMENT: `${environment.backEndNTSS}/service/update_service_apartment`,
    LOAD_DETAIL_SERVICE_TIMELINE: `${environment.backEndNTSS}/service/load_detail_service_timeline`,
    HIDE_SERVICE_APARTMENT: `${environment.backEndNTSS}/service/hide_service_apartment`,
    LOAD_DETAIL: `${environment.backEndNTSS}/service/load_detail`,
  }

  AREA = {
    LOAD_DATA: `${environment.backEndNTSS}/area/load_data`,
    PAGINATION: `${environment.backEndNTSS}/area/pagination`,
    CREATE: `${environment.backEndNTSS}/area/create_data`,
    UPDATE: `${environment.backEndNTSS}/area/update_data`,
    DELETE: `${environment.backEndNTSS}/area/update_active`,
    EXCEL: `${environment.backEndNTSS}/area/create_data_list`,
  }

  APARTMENT = {
    LOAD_DATA: `${environment.backEndNTSS}/apartment/load_data`,
    LOAD_DATA_FOR_SALE: `${environment.backEndNTSS}/apartment/load_data_for_sale`,
    LOAD_DETAIL: `${environment.backEndNTSS}/apartment/load_detail`,
    LOAD_DATA_BY_AREA: `${environment.backEndNTSS}/apartment/load_data_by_area`,
    FIND_ONE: `${environment.backEndNTSS}/apartment/find_one`,
    PAGINATION: `${environment.backEndNTSS}/apartment/pagination`,
    PAGINATION_FOR_WEB: `${environment.backEndNTSS}/apartment/pagination_for_web`,
    PAGINATION_FOR_DECLARATION: `${environment.backEndNTSS}/apartment/pagination_for_declaration`,
    PAGINATION_CUSTOM: `${environment.backEndNTSS}/apartment/pagination_custom`,
    PAGINATION_NO_RELATION: `${environment.backEndNTSS}/apartment/pagination_no_relation`,
    CREATE: `${environment.backEndNTSS}/apartment/create_data`,
    UPDATE: `${environment.backEndNTSS}/apartment/update_data`,
    DELETE: `${environment.backEndNTSS}/apartment/update_active`,
    EXCEL: `${environment.backEndNTSS}/apartment/create_data_list`,
    UPDATE_EMPLOYEE: `${environment.backEndNTSS}/apartment/update_employee`,
    LOAD_SERVICE_IN_APARTMENT: `${environment.backEndNTSS}/apartment/load_service_in_apartment`,
    CREATE_SERVICE_IN_APARTMENT: `${environment.backEndNTSS}/apartment/create_service_in_apartment`,
    UPDATE_NUM_VEHICLE: `${environment.backEndNTSS}/apartment/update_number_of_vehicle`,
    STATISTIC_NUM_VEHICLE: `${environment.backEndNTSS}/apartment/statistic_number_of_vehicle_in_apartment`,
    LOAD_VEHICLE_ROOM_IN_APARTMENT: `${environment.backEndNTSS}/apartment/load_vehicle_room_in_apartment`,
    STATISTIC_NUM_VEHICLE_ROOM: `${environment.backEndNTSS}/apartment/statistic_number_of_vehicle_room`,
    UPDATE_PHONE_NUMBER_FOR_APARTMENT: `${environment.backEndNTSS}/apartment/update_phone_number_for_apartment`,
    DECLARE_PHONE_NUMBER: `${environment.backEndNTSS}/apartment/declare_phone_number_for_apartment`,
    UPDATE_EMPLOYEE_IN_APARTMENT: `${environment.backEndNTSS}/apartment/update_employee_in_apartment`,
    UPDATE_APARTMENT_SALE: `${environment.backEndNTSS}/apartment/update_apartment_sale`,
    UPDATE_LIST_APARTMENT_SALE: `${environment.backEndNTSS}/apartment/update_list_apartment_sale`,
    CREATE_DATA_COMMISSION_BY_EXCEL: `${environment.backEndNTSS}/apartment/create_data_commission_by_excel`,
    CHECK_MANAGER: `${environment.backEndNTSS}/apartment/check_manager_in_apartment`,
  }

  APARTMENT_DISCOUNT_INTRODUCE = {
    PAGINATION: `${environment.backEndNTSS}/apartment_discount_introduce/pagination`,
    CREATE_DATA: `${environment.backEndNTSS}/apartment_discount_introduce/create_data`,
    UPDATE_DATA: `${environment.backEndNTSS}/apartment_discount_introduce/update_data`,
    UPDATE_ACTIVE: `${environment.backEndNTSS}/apartment_discount_introduce/update_active`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/apartment_discount_introduce/create_data_by_excel`,
  }

  APARTMENT_DISCOUNT_INTRODUCE_RESIDENT = {
    PAGINATION: `${environment.backEndNTSS}/apartment_discount_introduce_resident/pagination`,
    UPDATE_ACTIVE: `${environment.backEndNTSS}/apartment_discount_introduce_resident/update_active`,
    UPDATE_MONEY: `${environment.backEndNTSS}/apartment_discount_introduce_resident/update_money`,
    UPDATE_CANCEL_CREATE_STATUS: `${environment.backEndNTSS}/apartment_discount_introduce_resident/update_cancel_create_status`,
  }

  APARTMENT_SALE_COMMISSION = {
    PAGINATION: `${environment.backEndNTSS}/apartment_sale_commission/pagination`,
    CREATE: `${environment.backEndNTSS}/apartment_sale_commission/create_data`,
    UPDATE: `${environment.backEndNTSS}/apartment_sale_commission/update_data`,
    UPDATE_ACTIVE: `${environment.backEndNTSS}/apartment_sale_commission/update_active`,
  }

  ROOM = {
    FIND: `${environment.backEndNTSS}/room/find`,
    LOAD_DATA: `${environment.backEndNTSS}/room/load_data`,
    LOAD_DATA_BY_APARTMENT: `${environment.backEndNTSS}/room/load_data_by_apartment`,
    LOAD_ROOM_BY_APARTMENT: `${environment.backEndNTSS}/room/load_room_by_apartment`,
    PAGINATION: `${environment.backEndNTSS}/room/pagination`,
    PAGINATION_FOR_ROOM_LIST: `${environment.backEndNTSS}/room/pagination_for_room_list`,
    CREATE: `${environment.backEndNTSS}/room/create_data`,
    UPDATE: `${environment.backEndNTSS}/room/update_data`,
    DELETE: `${environment.backEndNTSS}/room/update_active`,
    EXCEL: `${environment.backEndNTSS}/room/create_data_list`,
    FIND_ONE: `${environment.backEndNTSS}/room/find_one`,
    FIND_ONE_IMAGE: `${environment.backEndNTSS}/room/find_one_image`,
    LOCK: `${environment.backEndNTSS}/room/update_lock`,
    UNLOCK: `${environment.backEndNTSS}/room/update_unlock`,
    MAINTANANCE: `${environment.backEndNTSS}/room/update_maintanance`,
    MAINTANANCE_COMPLETE: `${environment.backEndNTSS}/room/update_maintanance_complete`,
    LOAD_SERVICE_OF_ROOM: `${environment.backEndNTSS}/room/load_service_apartment_of_room`,
    FIND_ROOM_RESIDENT_DEPOSIT: `${environment.backEndNTSS}/room/find_room_resident_deposit`,
    UPDATE_NUM_VEHCLE_FOR_ROOM: `${environment.backEndNTSS}/room/update_number_of_vehicle_for_room`,
    LOAD_ROOM_BANK_ACCOUNT: `${environment.backEndNTSS}/room/load_room_bank_account`,
  }

  RESIDENT = {
    FIND_ONE: `${environment.backEndNTSS}/resident/find_one`,
    FIND_BY_IDNO: `${environment.backEndNTSS}/resident/find_by_idno`,
    DETAIL: `${environment.backEndNTSS}/resident/find_detail`,
    LOAD_DATA: `${environment.backEndNTSS}/resident/load_data`,
    PAGINATION: `${environment.backEndNTSS}/resident/pagination`,
    CREATE: `${environment.backEndNTSS}/resident/create_data`,
    UPDATE: `${environment.backEndNTSS}/resident/update_data`,
    UPDATE_TEMP_DECLARATION: `${environment.backEndNTSS}/resident/update_temp_declaration`,
    DELETE: `${environment.backEndNTSS}/resident/update_active`,
    LOAD_RESIDENT: `${environment.backEndNTSS}/resident/load_resident_by_place`,
    IMPORT_EXCEL: `${environment.backEndNTSS}/resident/import_excel`,
    LOAD_VEHICLE: `${environment.backEndNTSS}/resident/resident_vehicle`,
  }

  COMMON = {
    SEND_NOTIFICATION: `${environment.backEndNTSS}/common/send_notification_paid_bill_room`,
  }

  BILL = {
    PAID: `${environment.backEndNTSS}/bill/update_status_paid`,
    LOAD_DETAIL: `${environment.backEndNTSS}/bill/load_detail`,
    COMPUTE: `${environment.backEndNTSS}/bill/compute_bill`,
    LOAD_DATA: `${environment.backEndNTSS}/bill/load_room_in_month`,
    LOAD_DATA_BILL: `${environment.backEndNTSS}/bill/load_bill_of_room_in_month`,
    LOAD_BILL_BY_ROOM: `${environment.backEndNTSS}/bill/load_bill_of_room`,
    COMPUTE_BILL_BY_ROOM: `${environment.backEndNTSS}/bill/compute_bill_by_room`,
    SEND_ZALO_FOR_ROOM: `${environment.backEndNTSS}/bill/send_zalo_for_room`,
    SEND_ZALO_FOR_APARTMENT: `${environment.backEndNTSS}/bill/send_zalo_for_apartment`,
    SEND_ZALO_FOR_PAID_BILL_ROOM: `${environment.backEndNTSS}/bill/send_zalo_for_paid_bill_room`,
    LOAD_BILL_SENT_FAILED: `${environment.backEndNTSS}/bill/load_bill_sent_zalo_failed_in_month`,

    RECOMPUTE_BILL_ROOM: `${environment.backEndNTSS}/bill/recompute_bill_room`,
    CREATE_BILL_SERVICE: `${environment.backEndNTSS}/bill/create_bill_service`,
    CREATE_BILL_OTHER: `${environment.backEndNTSS}/bill/create_bill_other`,
    DELETE_BILL_OTHER: `${environment.backEndNTSS}/bill/delete_bill_other`,

    UPDATE_PAID_BILL: `${environment.backEndNTSS}/bill/update_paid_bill`,
    UPDATE_PAID_BR: `${environment.backEndNTSS}/bill/update_paid_bill_room`,
    UPDATE_PAID_BO: `${environment.backEndNTSS}/bill/update_paid_bill_other`,
    UPDATE_PAID_BS: `${environment.backEndNTSS}/bill/update_paid_bill_service`,
    UPDATE_TRANSFER_BILL: `${environment.backEndNTSS}/bill/update_paid_transfer_bill`,
    UPDATE_TRANSFER_BR: `${environment.backEndNTSS}/bill/update_paid_transfer_bill_room`,
    UPDATE_TRANSFER_BO: `${environment.backEndNTSS}/bill/update_paid_transfer_bill_other`,
    UPDATE_TRANSFER_BS: `${environment.backEndNTSS}/bill/update_paid_transfer_bill_service`,
    CONFIRM_TRANSFER_BILL: `${environment.backEndNTSS}/bill/confirm_paid_transfer_bill`,
    CONFIRM_TRANSFER_BR: `${environment.backEndNTSS}/bill/confirm_paid_transfer_bill_room`,
    CONFIRM_TRANSFER_BO: `${environment.backEndNTSS}/bill/confirm_paid_transfer_bill_other`,
    CONFIRM_TRANSFER_BS: `${environment.backEndNTSS}/bill/confirm_paid_transfer_bill_service`,

    DETAIL_BILL_ROOM: `${environment.backEndNTSS}/bill/load_detail_bill_room`,
    DETAIL_BILL_OTHER: `${environment.backEndNTSS}/bill/load_detail_bill_other`,
    DETAIL_BILL_SERVICE: `${environment.backEndNTSS}/bill/load_detail_bill_service`,

    CREATE_BILL_DISCOUNT: `${environment.backEndNTSS}/bill/create_bill_discount`,
    CREATE_BILL_DISCOUNT_LIST: `${environment.backEndNTSS}/bill/create_bill_discount_list`,
    UPDATE_NOTE: `${environment.backEndNTSS}/bill/update_note`,
    READ_BANK_STATEMENTS: `${environment.backEndNTSS}/bill/read_bank_statements`,
    CONFIRM_BILL_STATEMENTS: `${environment.backEndNTSS}/bill/confirm_bill_statements`,
    CONFIRM_ONE_BILL_STATEMENTS: `${environment.backEndNTSS}/bill/confirm_one_bill_statements`,
  }

  SUPPLIER = {
    LOAD_DATA: `${environment.backEndNTSS}/suppliers/load_data`,
    FIND_ONE: `${environment.backEndNTSS}/suppliers/find_one`,
    PAGINATION: `${environment.backEndNTSS}/suppliers/pagination`,
    CREATE: `${environment.backEndNTSS}/suppliers/create_data`,
    UPDATE: `${environment.backEndNTSS}/suppliers/update_data`,
    DELETE: `${environment.backEndNTSS}/suppliers/update_active`,
  }

  PAYSLIP = {
    FIND_ONE: `${environment.backEndNTSS}/payslip/find_one`,
    PAGINATION: `${environment.backEndNTSS}/payslip/pagination`,
    CREATE: `${environment.backEndNTSS}/payslip/create_data`,
    UPDATE: `${environment.backEndNTSS}/payslip/update_data`,
    DELETE: `${environment.backEndNTSS}/payslip/update_delete`,
    APPROVED: `${environment.backEndNTSS}/payslip/update_approved`,
    CANCEL: `${environment.backEndNTSS}/payslip/update_cancel`,
    PRINT: `${environment.backEndNTSS}/payslip/print`,
    APPROVED_LIST: `${environment.backEndNTSS}/payslip/update_approved_list`,
    ADD_ATTACH: `${environment.backEndNTSS}/payslip/add_attaches_for_payslip`,
    DETAIL: `${environment.backEndNTSS}/payslip/load_detail`,
    PAGINATION_FOR_COMMISSION: `${environment.backEndNTSS}/payslip/pagination_for_commission`,
  }

  RECEIPT = {
    PAGINATION: `${environment.backEndNTSS}/receipt/pagination`,
    PAGINATION_FOR_INCREASE: `${environment.backEndNTSS}/receipt/pagination_for_increase`,
    LOAD_DETAIL: `${environment.backEndNTSS}/receipt/load_detail`,
    PRINT: `${environment.backEndNTSS}/receipt/print`,
    FIND_ONE: `${environment.backEndNTSS}/receipt/find_one`,
    CREATE: `${environment.backEndNTSS}/receipt/create_data`,
    UPDATE: `${environment.backEndNTSS}/receipt/update_data`,
    APPROVED: `${environment.backEndNTSS}/receipt/approved_payment`,
    CANCEL: `${environment.backEndNTSS}/receipt/cancel_payment`,
    APPROVED_LIST: `${environment.backEndNTSS}/receipt/update_approved_list`,
    LOAD_DATA_FOR_EDIT: `${environment.backEndNTSS}/receipt/load_data_for_edit`,
  }

  TRANSFER_MONEY = {
    PAGINATION: `${environment.backEndNTSS}/transfer_money/pagination`,
    CREATE: `${environment.backEndNTSS}/transfer_money/create_data`,
    UPDATE: `${environment.backEndNTSS}/transfer_money/update_data`,
    APPROVED: `${environment.backEndNTSS}/transfer_money/update_approved`,
    CANCEL: `${environment.backEndNTSS}/transfer_money/update_cancel`,
    PAGINATION_HISTORY: `${environment.backEndNTSS}/transfer_money/pagination_history`,
  }

  PAYSLIP_TYPE = {
    PAGINATION: `${environment.backEndNTSS}/payslip_type/pagination`,
    LOAD_DATA: `${environment.backEndNTSS}/payslip_type/load_data`,
    CREATE: `${environment.backEndNTSS}/payslip_type/create_data`,
    UPDATE: `${environment.backEndNTSS}/payslip_type/update_data`,
    DELETE: `${environment.backEndNTSS}/payslip_type/update_active`,
    DATA_SELECT_ONE: `${environment.backEndNTSS}/payslip_type/load_data_select_box_level_one`,
    DATA_SELECT: `${environment.backEndNTSS}/payslip_type/load_data_select_box`,
    DATA_SELECT_FOR_CREATE: `${environment.backEndNTSS}/payslip_type/load_data_select_box_for_create`,
    DATA_SELECT_TWO: `${environment.backEndNTSS}/payslip_type/load_data_select_box_level_two`,
    DATA_SELECT_THREE: `${environment.backEndNTSS}/payslip_type/load_data_select_box_level_three`,
    DATA_SELECT_TWO_WITHOUT_ONE: `${environment.backEndNTSS}/payslip_type/load_data_select_box_level_two_without_one`,
    DATA_SELECT_THREE_WITHOUT_TWO: `${environment.backEndNTSS}/payslip_type/load_data_select_box_level_three_without_two`,
  }

  NOTIFY = {
    FIND_ONE: `${environment.backEndNTSS}/notify/find_one`,
    LOAD: `${environment.backEndNTSS}/notify/load`,
    READ: `${environment.backEndNTSS}/notify/read`,
    READ_LIST: `${environment.backEndNTSS}/notify/read_list_notify`,
  }

  REPORT = {
    PAYSLIP: `${environment.backEndNTSS}/report/payslip_in_month`,
    DYNAMIC_PAYSLIP: `${environment.backEndNTSS}/report/dynamic_payslip_report`,
    REVENUE_BY_APARTMENT: `${environment.backEndNTSS}/report/revenue_by_apartment`,
    DEPOSIT_REPORT: `${environment.backEndNTSS}/report/deposit_report`,
    BILL_SENT_ZALO: `${environment.backEndNTSS}/report/load_bill_sent_zalo_in_month`,
    ROOM_STATUS: `${environment.backEndNTSS}/report/room_status`,
    SENT_ZALO_IN_MONTH: `${environment.backEndNTSS}/report/sent_zalo_in_month`,
    RESIDENT_DEPOSIT_SEND_ZALO: `${environment.backEndNTSS}/report/load_resident_deposit_send_zalo`,
    BILL_COLLECTION_DETAILS_REPORT: `${environment.backEndNTSS}/report/bill_collection_detail_report`,
    DEPOSIT_CONTRACT_REPORT: `${environment.backEndNTSS}/report/deposit_contract_report`,
    CASH_FLOW_REPORT: `${environment.backEndNTSS}/report/cash_flow_report`,
    LIST_UNPAID: `${environment.backEndNTSS}/report/load_list_bill_unpaid_and_partial`,
    REVENUE_LOSS_REPORT: `${environment.backEndNTSS}/report/revenue_loss_report`,
    REVENUE_LOSS_REPORT_IN_APARTMENT: `${environment.backEndNTSS}/report/revenue_loss_report_in_apartment`,
    WATER_REPORT: `${environment.backEndNTSS}/report/water_report`,
    ELECTRICITY_REPORT: `${environment.backEndNTSS}/report/electricity_report`,
    APARTMENT_REPORT: `${environment.backEndNTSS}/report/apartment_report`,
    COMPANY_FINANCIAL_REPORT: `${environment.backEndNTSS}/report/company_financial_report`,
    LOAD_DEPOSIT_DETAIL: `${environment.backEndNTSS}/report/load_deposit_detail`,
    PAYMENT_HISTORY_REPORT: `${environment.backEndNTSS}/report/payment_history_report`,
    DETAIL_COST_BY_APARTMENT: `${environment.backEndNTSS}/report/detail_cost_by_apartment`,
    REPORT_EMPTY_ROOM_BY_MONTH: `${environment.backEndNTSS}/report/report_empty_room_by_month`,
    REPORT_EMPTY_ROOM_CURRENT: `${environment.backEndNTSS}/report/report_empty_room_current`,
    ACCOUNT_BALANCE_REPORT_WITH_ONE_MONTH: `${environment.backEndNTSS}/report/account_balance_report_with_one_month`,
    ACCOUNT_BALANCE_REPORT_WITH_ONE_ACCOUNT: `${environment.backEndNTSS}/report/account_balance_report_with_one_account`,
    ELECTRIC_INDEX_REPORT: `${environment.backEndNTSS}/report/electric_index_report`,
    WATER_INDEX_REPORT: `${environment.backEndNTSS}/report/water_index_report`,
    NEW_COMPANY_REVENUE_REPORT: `${environment.backEndNTSS}/report/new_company_revenue_report`,
    NEW_APARTMENT_REVENUE_REPORT: `${environment.backEndNTSS}/report/new_apartment_revenue_report`,
  }

  USER = {
    LOAD_DATA: `${environment.backEndNTSS}/user/load_data`,
    LOAD_DATA_BY_TYPE: `${environment.backEndNTSS}/user/load_data_by_type`,
  }

  EMPLOYEE_DEBT = {
    PAGINATION: `${environment.backEndNTSS}/employeeDebt/pagination`,
    PAGINATION_REPAIR: `${environment.backEndNTSS}/employeeDebt/pagination_repair`,
    EXPORT_EXCEL_DETAIL: `${environment.backEndNTSS}/employeeDebt/export_excel_detail`,
    LOAD_DETAIL_REPAIR: `${environment.backEndNTSS}/employeeDebt/load_detail_repair`,
    DETAIL: `${environment.backEndNTSS}/employeeDebt/load_detail`,
    UPDATE_PAID: `${environment.backEndNTSS}/employeeDebt/update_paid_status`,
    HISTORY: `${environment.backEndNTSS}/employeeDebt/load_history`,
    APARTMENT: `${environment.backEndNTSS}/employeeDebt/load_apartment_debt`,
    LOAD_CASH: `${environment.backEndNTSS}/employeeDebt/load_cash_apartment_debt`,
    LOAD_UNCOLLECT: `${environment.backEndNTSS}/employeeDebt/load_uncollect_money_apartment`,
    LOAD_RECEIVABLE: `${environment.backEndNTSS}/employeeDebt/load_receivable_apartment`,
    LIST_DEBT_LEFT: `${environment.backEndNTSS}/employeeDebt/list_debt_left`,
    LOAD_UNCOLLECT_MONEY: `${environment.backEndNTSS}/employeeDebt/load_uncollect_money`,
  }

  EMPLOYEE_ADVANCE = {
    CREATE: `${environment.backEndNTSS}/employeeAdvance/create_data`,
    CREATE_DETAIL_APPROVED: `${environment.backEndNTSS}/employeeAdvance/create_advance_detail_approved`,
    PAGINATION: `${environment.backEndNTSS}/employeeAdvance/pagination`,
    ADVANCE_EMPLOYEE_PAGINATION: `${environment.backEndNTSS}/employeeAdvance/advance_employee_pagination`,
    DETAIL: `${environment.backEndNTSS}/employeeAdvance/load_detail`,
    UPDATE: `${environment.backEndNTSS}/employeeAdvance/update_status`,
  }

  EMPLOYEE_ADVANCE_DETAIL = {
    PAGINATION: `${environment.backEndNTSS}/employee_advance_detail/pagination`,
  }

  ARCHIVAL_FILE = {
    LOAD_DETAIL: `${environment.backEndNTSS}/archivalFile/load_detail`,
    PAGINATION: `${environment.backEndNTSS}/archivalFile/pagination`,
    CREATE: `${environment.backEndNTSS}/archivalFile/create_data`,
    UPDATE: `${environment.backEndNTSS}/archivalFile/update_data`,
    DELETE: `${environment.backEndNTSS}/archivalFile/update_active`,
  }

  COUNTER_PARTY = {
    PAGINATION_FOR_SALE: `${environment.backEndNTSS}/counterparty/pagination_for_sale`,
    CREATE: `${environment.backEndNTSS}/counterparty/create_data`,
    UPDATE: `${environment.backEndNTSS}/counterparty/update_data`,
    DELETE: `${environment.backEndNTSS}/counterparty/update_active`,
    UPDATE_PASSWORD: `${environment.backEndNTSS}/counterparty/update_password`,
    LOAD_DATA: `${environment.backEndNTSS}/counterparty/load_data`,
    LOAD_DATA_BY_USER_TYPE: `${environment.backEndNTSS}/counterparty/load_data_by_user_type`,
    CONFIG_SALE_COMMISSION: `${environment.backEndNTSS}/counterparty/config_sale_commission`,
    LOAD_DETAIL: `${environment.backEndNTSS}/counterparty/load_detail_sale_person`,
    UPDATE_SALE_COMMISSION: `${environment.backEndNTSS}/counterparty/update_sale_commission`,
    LOAD_SALE_COMMISSION_BY_USER_ID: `${environment.backEndNTSS}/counterparty/load_sale_commission_by_user_id`,
  }

  /** Giảm giá */
  DISCOUNT = {
    PAGINATION: `${environment.backEndNTSS}/discount/pagination`,
    CREATE: `${environment.backEndNTSS}/discount/create_data`,
    UPDATE: `${environment.backEndNTSS}/discount/update_data`,
    CANCEL: `${environment.backEndNTSS}/discount/update_cancel`,
    LOAD_DETAIL: `${environment.backEndNTSS}/discount/load_detail_discount`,
  }

  /** Giảm giá */
  DISCOUNT_DECLARATION = {
    PAGINATION: `${environment.backEndNTSS}/discount_declaration/pagination`,
    LOAD_DATA_BY_APARTMENT: `${environment.backEndNTSS}/discount_declaration/load_data_by_apartment`,
    CREATE: `${environment.backEndNTSS}/discount_declaration/create_data`,
    CANCEL: `${environment.backEndNTSS}/discount_declaration/update_cancel`,
    LOAD_DETAIL: `${environment.backEndNTSS}/discount_declaration/load_detail_discount`,
    UPDATE: `${environment.backEndNTSS}/discount_declaration/update_data`,
  }

  RESIDENT_DEPOSIT = {
    DETAIL: `${environment.backEndNTSS}/resident_deposit/find_detail`,
    FIND: `${environment.backEndNTSS}/resident_deposit/find`,
    PAGINATION: `${environment.backEndNTSS}/resident_deposit/pagination`,
    CREATE: `${environment.backEndNTSS}/resident_deposit/create_data`,
    UPDATE: `${environment.backEndNTSS}/resident_deposit/update_data`,
    DELETE: `${environment.backEndNTSS}/resident_deposit/update_active`,
    FIND_ROOM_RESIDENT_DEPOSIT: `${environment.backEndNTSS}/resident_deposit/find_room_resident_deposit`,
    CANCEL: `${environment.backEndNTSS}/resident_deposit/update_cancel`,
    UPDATE_NUMBER_RESERVATION_DAYS: `${environment.backEndNTSS}/resident_deposit/update_number_reservation_days`,
  }

  COMMISSION = {
    FIND: `${environment.backEndNTSS}/commission/find`,
    FIND_ONE: `${environment.backEndNTSS}/commission/find_one`,
    DETAIL: `${environment.backEndNTSS}/commission/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/commission/pagination`,
    PAGINATION_FOR_SALE_REPORT: `${environment.backEndNTSS}/commission/pagination_for_sale_report`,
    CREATE: `${environment.backEndNTSS}/commission/create_data`,
    UPDATE: `${environment.backEndNTSS}/commission/update_data`,
    APPROVED_LIST: `${environment.backEndNTSS}/commission/update_approved_list`,
    CAL_COMMISSION: `${environment.backEndNTSS}/commission/cal_commission`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/commission/update_cancel`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/commission/create_data_by_excel`,
    UPDATE_PERCENT_COMMISSION: `${environment.backEndNTSS}/commission/update_percent_commission`,
    GET_TOTAL_ACTUALLY_MONEY: `${environment.backEndNTSS}/commission/get_total_actually_money`,
  }

  PROPERTY_DETAIL = {
    FIND: `${environment.backEndNTSS}/property_detail/find`,
    LOAD_DATA: `${environment.backEndNTSS}/property_detail/load_data`,
    INVENTORY: `${environment.backEndNTSS}/property_detail/inventory`,
    PAGINATION: `${environment.backEndNTSS}/property_detail/pagination`,
    PAGINATION_CUSTOM: `${environment.backEndNTSS}/property_detail/pagination_custom`,
    PAGINATION_CUSTOM_FOR_PROPERTY_TRANSFER: `${environment.backEndNTSS}/property_detail/pagination_custom_for_property_transfer`,
    PAGINATION_CUSTOM_FOR_PROPERTY_REPORT: `${environment.backEndNTSS}/property_detail/pagination_custom_for_property_report`,
    PAGINATION_BY_ROOM: `${environment.backEndNTSS}/property_detail/pagination_by_room`,
    HISTORY_PAGINATION: `${environment.backEndNTSS}/property_detail/history_pagination`,
    CREATE: `${environment.backEndNTSS}/property_detail/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/property_detail/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/property_detail/update_data`,
    ACTIVE: `${environment.backEndNTSS}/property_detail/update_active`,
    PROPERTY_REPORT: `${environment.backEndNTSS}/property_detail/property_report`,
    DOWNLOAD_EXCEL_PROPERTY_REPORT: `${environment.backEndNTSS}/property_detail/download_excel_property_report`,
    GET_DATA_BY_ROOM_AND_WAREHOUSE_OF_APARTMENT: `${environment.backEndNTSS}/property_detail/get_data_by_room_and_warehouse_of_apartment`,
    LOAD_DATA_BY_APARTMENT_AND_PROPERTY_TYPE: `${environment.backEndNTSS}/property_detail/load_data_by_apartment_and_property_type`,
  }

  PROPERTY_TYPE = {
    LOAD_DATA: `${environment.backEndNTSS}/property_type/load_data`,
    LOAD_DETAIL: `${environment.backEndNTSS}/property_type/load_detail`,
    PAGINATION: `${environment.backEndNTSS}/property_type/pagination`,
    REPORT: `${environment.backEndNTSS}/property_type/report`,
    CREATE: `${environment.backEndNTSS}/property_type/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/property_type/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/property_type/update_data`,
    ACTIVE: `${environment.backEndNTSS}/property_type/update_active`,
  }

  PROPERTY = {
    GET_NUMBER_OF_PROPERTY_DETAIL_BY_PROPERTY: `${environment.backEndNTSS}/property/get_number_of_property_detail_by_property`,
    FIND_DETAIL: `${environment.backEndNTSS}/property/find_detail`,
    LOAD_DETAIL: `${environment.backEndNTSS}/property/load_detail`,
    LOAD_DATA: `${environment.backEndNTSS}/property/load_data`,
    PAGINATION: `${environment.backEndNTSS}/property/pagination`,
    CREATE: `${environment.backEndNTSS}/property/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/property/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/property/update_data`,
    ACTIVE: `${environment.backEndNTSS}/property/update_active`,
  }

  SERVICE_TYPE = {
    LOAD_DATA: `${environment.backEndNTSS}/service_type/load_data`,
    PAGINATION: `${environment.backEndNTSS}/service_type/pagination`,
    CREATE: `${environment.backEndNTSS}/service_type/create_data`,
    UPDATE: `${environment.backEndNTSS}/service_type/update_data`,
    DELETE: `${environment.backEndNTSS}/service_type/update_active`,
  }

  // Property Transfer
  PROPERTY_TRANSFER = {
    PAGINATION: `${environment.backEndNTSS}/property_transfer/pagination`,
    CREATE: `${environment.backEndNTSS}/property_transfer/create_data`,
    CONFIRM: `${environment.backEndNTSS}/property_transfer/update_confirm`,
    CANCEL: `${environment.backEndNTSS}/property_transfer/update_cancel`,
    DETAIL: `${environment.backEndNTSS}/property_transfer/load_detail`,
    SEND: `${environment.backEndNTSS}/property_transfer/update_wait`,
  }

  // Property liquidation
  PROPERTY_LIQUIDATION = {
    PAGINATION: `${environment.backEndNTSS}/property_liquidation/pagination`,
    CREATE: `${environment.backEndNTSS}/property_liquidation/create_data`,
    CONFIRM: `${environment.backEndNTSS}/property_liquidation/update_confirm`,
    CANCEL: `${environment.backEndNTSS}/property_liquidation/update_cancel`,
    DETAIL: `${environment.backEndNTSS}/property_liquidation/load_detail`,
    EXCEL: `${environment.backEndNTSS}/property_liquidation/import_excel`,
    CONFIRM_LIST: `${environment.backEndNTSS}/property_liquidation/update_confirm_property_liquidation_list`,
    CANCEL_LIST: `${environment.backEndNTSS}/property_liquidation/cancel_confirm_property_liquidation_list`,
  }

  // Bank Account
  BANK_ACCOUNT = {
    PAGINATION: `${environment.backEndNTSS}/bank_account/pagination`,
    CREATE: `${environment.backEndNTSS}/bank_account/create_data`,
    UPDATE: `${environment.backEndNTSS}/bank_account/update_data`,
    DETAIL: `${environment.backEndNTSS}/bank_account/load_detail`,
    UPDATE_APARTMENT: `${environment.backEndNTSS}/bank_account/update_bank_account_for_apartment`,
    UPDATE_APARTMENT_DECLARATION: `${environment.backEndNTSS}/bank_account/update_bank_account_declaration_for_apartment`,
    LOAD_APARTMENT: `${environment.backEndNTSS}/bank_account/load_apartment_with_bank_account`,
    LOAD_DATA: `${environment.backEndNTSS}/bank_account/load_data`,
    LOAD_APARTMENT_BANK_ACCOUNT: `${environment.backEndNTSS}/bank_account/load_apartment_bank_account`,
    ACTIVE: `${environment.backEndNTSS}/bank_account/update_active`,
  }

  PENALTY = {
    PAGINATION: `${environment.backEndNTSS}/late_bill_penalty/pagination`,
    LOAD_DETAIL: `${environment.backEndNTSS}/late_bill_penalty/load_detail`,
    UPDATE_BILL_AND_CREATE_PENALTY: `${environment.backEndNTSS}/late_bill_penalty/update_bill_and_create_penalty`,
    CANCEL: `${environment.backEndNTSS}/late_bill_penalty/cancel_penalty`,
    CANCEL_LIST: `${environment.backEndNTSS}/late_bill_penalty/cancel_penalty_list`,
  }

  PUBLIC_API = {
    CHECK_ZALO: `${environment.backEndNTSS}/public_api/check_zalo_template_on_production`,
  }

  //#endregion

  // #region API Source Sửa chữa

  // #endregion

  REPAIR_WAREHOUSE_MATERIAL = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_warehouse_material/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_warehouse_material/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_warehouse_material/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_warehouse_material/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_warehouse_material/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_warehouse_material/update_data`,
    DELETE: `${environment.backEndNTSS}/repair_warehouse_material/update_active`,
    REPORT_MATERIAL: `${environment.backEndNTSS}/repair_warehouse_material/report_material`,
    HISTORY_PAGINATION: `${environment.backEndNTSS}/repair_warehouse_material/history_pagination`,
    PAGINATION_REPORT_IN_OUT: `${environment.backEndNTSS}/repair_warehouse_material/pagination_report_in_out`,
    PAGINATION_REPORT_INVENTORY: `${environment.backEndNTSS}/repair_warehouse_material/pagination_report_inventory`,
    LOAD_DETAIL_IN_OUT: `${environment.backEndNTSS}/repair_warehouse_material/load_detail_in_out`,
    ADD_MATERIAL: `${environment.backEndNTSS}/repair_warehouse_material/add_material`,
  }

  REPAIR_UNIT = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_unit/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_unit/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_unit/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_unit/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_unit/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_unit/update_data`,
    DELETE: `${environment.backEndNTSS}/repair_unit/update_active`,
  }

  REPAIR_MATERIAL_CATEGORY = {
    REPORT_MATERIAL: `${environment.backEndNTSS}/repair_material_category/report_material`,
    LOAD_DATA: `${environment.backEndNTSS}/repair_material_category/load_data`,
    LOAD_DATA_NOT_IN_WH: `${environment.backEndNTSS}/repair_material_category/load_data_not_in_warehouse`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_material_category/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_material_category/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_material_category/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_material_category/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_material_category/update_data`,
    DELETE: `${environment.backEndNTSS}/repair_material_category/update_active`,
    PAGINATION_REPORT_IN_OUT: `${environment.backEndNTSS}/repair_material_category/pagination_report_in_out`,
    LOAD_DETAIL_IN_OUT: `${environment.backEndNTSS}/repair_material_category/load_detail_in_out`,
    PAGINATION_REPORT_MATERIAL: `${environment.backEndNTSS}/repair_material_category/pagination_report_inventory`,
  }

  REPAIR_JOB_CATEGORY = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_job_category/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_job_category/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_job_category/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_job_category/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_job_category/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_job_category/update_data`,
    DELETE: `${environment.backEndNTSS}/repair_job_category/update_active`,
    GET_LIST_FOR_REFRESH: `${environment.backEndNTSS}/repair_job_category/get_list_job_category_for_fresh`,
  }

  REPAIR_JOB = {
    PAGINATION: `${environment.backEndNTSS}/repair_job/pagination`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_job/find_detail`,
    REPORT_JOB: `${environment.backEndNTSS}/repair_job/report_job_status_by_employee`,
    REPORT_EMPLOYEE_JOB_REWARD: `${environment.backEndNTSS}/repair_job/report_employee_job_reward`,
    DETAIL_EMPLOYEE_JOB_REWARD: `${environment.backEndNTSS}/repair_job/detail_employee_job_reward`,
    REPORT_APARTMENT_JOB: `${environment.backEndNTSS}/repair_job/report_apartment_job`,
    REPORT_EMPLOYEE_ADVANCE: `${environment.backEndNTSS}/repair_job/report_employee_advance`,
    DETAIL_BUY_MATERIAL: `${environment.backEndNTSS}/repair_job/detail_buy_material`,
    REPORT_PERFOMENT_EMPLOYEE: `${environment.backEndNTSS}/repair_job/report_perfoment_employee`,
    REPORT_PERFOMENT_EMPLOYEE_DETAIL: `${environment.backEndNTSS}/repair_job/detail_report_perfoment_employee`,
  }

  REPAIR_BILL_JOB = {
    PAGINATION: `${environment.backEndNTSS}/repair_bill_job/pagination`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_bill_job/find_detail`,
    UPDATE_PAID: `${environment.backEndNTSS}/repair_bill_job/update_paid_repair_job`,
  }

  REPAIR_REWARD = {
    FIND_DETAIL: `${environment.backEndNTSS}/repair_reward/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_reward/pagination`,
    STATISTIC_BY_EMPLOYEE: `${environment.backEndNTSS}/repair_reward/reward_statistic_by_employee`,
    STATISTIC_BY_JOB_TYPE: `${environment.backEndNTSS}/repair_reward/reward_statistic_by_job_type`,
  }

  REPAIR_JOB_TYPE = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_job_type/load_data`,
    LOAD_DATA_FOR_WEB: `${environment.backEndNTSS}/repair_job_type/load_data_for_web`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_job_type/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_job_type/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_job_type/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_job_type/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_job_type/update_data`,
    DELETE: `${environment.backEndNTSS}/repair_job_type/update_active`,
  }

  REPAIR_INBOUND = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_inbound/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_inbound/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_inbound/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_inbound/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_inbound/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_inbound/update_data`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/repair_inbound/update_cancel`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/repair_inbound/update_approve`,
    CONFIRM_PAYMENT: `${environment.backEndNTSS}/repair_inbound/confirm_payment`,
    CANCEL_PAYMENT: `${environment.backEndNTSS}/repair_inbound/cancel_payment`,
    SPLIT: `${environment.backEndNTSS}/repair_inbound/split`,
  }

  REPAIR_OUTBOUND = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_outbound/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_outbound/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_outbound/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_outbound/create_data`,
    UPDATE: `${environment.backEndNTSS}/repair_outbound/update_data`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/repair_outbound/update_cancel`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/repair_outbound/update_approve`,
    UPDATE_APPROVE_FOR_APPROVING: `${environment.backEndNTSS}/repair_outbound/update_approve_for_approving`,
    SPLIT: `${environment.backEndNTSS}/repair_outbound/split`,
  }

  REPAIR_RETURN_MATERIAL = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_return_material/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_return_material/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_return_material/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_return_material/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_return_material/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_return_material/update_data`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/repair_return_material/update_cancel`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/repair_return_material/update_approve`,
  }

  REPAIR_BUYING_MATERIAL = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_buying_material/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_buying_material/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_buying_material/pagination`,
    PAGINATION_BUYING_MATERIAL_APPROVED: `${environment.backEndNTSS}/repair_buying_material/pagination_for_buying_material_approved`,
    CREATE: `${environment.backEndNTSS}/repair_buying_material/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_buying_material/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_buying_material/update_data`,
    UPDATE_IMAGE: `${environment.backEndNTSS}/repair_buying_material/upload_images`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/repair_buying_material/update_cancel`,
    UPDATE_CANCEL_EACH_ROW: `${environment.backEndNTSS}/repair_buying_material/update_cancel_each_row`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/repair_buying_material/update_approve`,
    ACCOUNTANT_UPDATE_APPROVE: `${environment.backEndNTSS}/repair_buying_material/account_update_approve`,
    UPDATE_APPROVING: `${environment.backEndNTSS}/repair_buying_material/update_approving`,
    SEND_BUYING_MATERIAL_FINANCIAL_ACCOUNTANT: `${environment.backEndNTSS}/repair_buying_material/send_buying_material_financial_accountant`,
    UPDATE_WAITING_BUYING_DATE_EXPIRE: `${environment.backEndNTSS}/repair_buying_material/update_waiting_buying_date_expire`,
    LOAD_ALL_BUYING_MATERIAL_DETAIL_BY_LST_ID: `${environment.backEndNTSS}/repair_buying_material/load_all_buying_material_detail_by_lst_id`,
    APPROVE_ALL_BUYING_MATERIAL_DETAIL_BY_LST_ID: `${environment.backEndNTSS}/repair_buying_material/approve_all_buying_material_detail_by_lst_id`,
    CREATE_EMPLOYEE_ADVANCE_DETAIL_LINK_TO_BUYING_MATERIAL: `${environment.backEndNTSS}/repair_buying_material/create_employee_advance_detail_link_to_buying_material`,
    LOAD_BUYING_MATERIAL_GROUP_BY_WAREHOUSE_MATERIAL: `${environment.backEndNTSS}/repair_buying_material/load_buying_material_group_by_warehouse_material`,
  }

  REPAIR_BUYING_MATERIAL_SUMMARY = {
    CREATE_DATA: `${environment.backEndNTSS}/repair_buying_material_summary/create_data`,
    PAGINATION: `${environment.backEndNTSS}/repair_buying_material_summary/pagination`,
    FIND_LST_BUYING_MATERIAL: `${environment.backEndNTSS}/repair_buying_material_summary/find_lst_buying_material`,
    DELETE: `${environment.backEndNTSS}/repair_buying_material_summary/delete`,
    EXPORT_EXCEL: `${environment.backEndNTSS}/repair_buying_material_summary/export_excel`,
    IMPORT_EXCEL: `${environment.backEndNTSS}/repair_buying_material_summary/import_excel`,
    FIND_LST_INBOUND: `${environment.backEndNTSS}/repair_buying_material_summary/find_lst_inbound`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_buying_material_summary/find_detail`,
  }

  REPAIR_MATERIAL_CATEGORY_DETAIL = {
    PAGINATION: `${environment.backEndNTSS}/repair_material_category_detail/pagination`,
    REPORT_WAREHOUSE_MATERIAL: `${environment.backEndNTSS}/repair_material_category_detail/report_warehouse_material`,
    LOAD_DATA: `${environment.backEndNTSS}/repair_material_category_detail/load_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_material_category_detail/create_data_by_excel`,
    REPORT_MATERIAL_DETAIL: `${environment.backEndNTSS}/repair_material_category_detail/report_material_detail`,
    REPORT_MATERIAL_DETAIL_FOR_EMPLOYEE: `${environment.backEndNTSS}/repair_material_category_detail/report_material_detail_for_employee`,
    REPORT_MATERIAL_DETAIL_BY_MATERIAL: `${environment.backEndNTSS}/repair_material_category_detail/report_material_detail_by_material`,
    REPORT_MATERIAL_BY_EMPLOYEE: `${environment.backEndNTSS}/repair_material_category_detail/report_material_by_employee`,
    MATERIAL_CATEGORY_DETAIL_HISTORY_PAGINATION: `${environment.backEndNTSS}/repair_material_category_detail/material_category_detail_history_pagination`,
  }

  REPORT_MATERIAL_DETAIL = {
    REPORT_USING_MATERIAL_DETAIL: `${environment.backEndNTSS}/repair_job_detail/report_using_material_detail`,
    REPORT_MATERIAL_DETAIL_BY_APARTMENT: `${environment.backEndNTSS}/repair_job_detail/report_material_detail_by_apartment`,
    REPORT_MATERIAL_DETAIL_BY_ROOM: `${environment.backEndNTSS}/repair_job_detail/report_material_detail_by_room`,
    REPORT_MATERIAL_DETAIL_BY_ROOM_DETAIL: `${environment.backEndNTSS}/repair_job_detail/report_material_detail_by_room_detail`,
    REPORT_MATERIAL_DETAIL_BY_EMPLOYEE: `${environment.backEndNTSS}/repair_job_detail/report_material_detail_by_employee`,
    PAGINATION: `${environment.backEndNTSS}/repair_job_detail/pagination`,
  }

  REPAIR_CHECK_INVENTORY = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_check_inventory/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_check_inventory/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_check_inventory/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_check_inventory/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_check_inventory/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_check_inventory/update_data`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/repair_check_inventory/update_cancel`,
    UPDATE_APPROVING: `${environment.backEndNTSS}/repair_check_inventory/update_approving`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/repair_check_inventory/update_approve`,
  }

  REPAIR_TRANSFER_WAREHOUSE = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_transfer_warehouse/load_data`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_transfer_warehouse/find_detail`,
    PAGINATION: `${environment.backEndNTSS}/repair_transfer_warehouse/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_transfer_warehouse/create_data`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_transfer_warehouse/create_data_by_excel`,
    UPDATE: `${environment.backEndNTSS}/repair_transfer_warehouse/update_data`,
    UPDATE_CANCEL: `${environment.backEndNTSS}/repair_transfer_warehouse/update_cancel`,
    UPDATE_SENT: `${environment.backEndNTSS}/repair_transfer_warehouse/update_sent`,
    UPDATE_APPROVE: `${environment.backEndNTSS}/repair_transfer_warehouse/update_approve`,
  }

  REPAIR_SERVICE_FEE_STANDARD = {
    PAGINATION: `${environment.backEndNTSS}/repair_service_fee_standard/pagination`,
    LOAD_DATA: `${environment.backEndNTSS}/repair_service_fee_standard/load_data`,
    CREATE: `${environment.backEndNTSS}/repair_service_fee_standard/create_data`,
    UPDATE: `${environment.backEndNTSS}/repair_service_fee_standard/update_data`,
    UPDATE_STATUS: `${environment.backEndNTSS}/repair_service_fee_standard/update_status`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_service_fee_standard/find_detail`,
  }

  REPAIR_JOB_MOVEMENT_FEE = {
    PAGINATION: `${environment.backEndNTSS}/repair_job_movement_fee/pagination`,
    LOAD_DATA: `${environment.backEndNTSS}/repair_job_movement_fee/load_data`,
    CREATE: `${environment.backEndNTSS}/repair_job_movement_fee/create_data`,
    UPDATE: `${environment.backEndNTSS}/repair_job_movement_fee/update_data`,
    UPDATE_STATUS: `${environment.backEndNTSS}/repair_job_movement_fee/update_status`,
    FIND_DETAIL: `${environment.backEndNTSS}/repair_job_movement_fee/find_detail`,
  }

  REPAIR_JOB_TYPE_APARTMENT_EMPLOYEE = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_job_type_apartment_employee/load_data`,
    SAVE: `${environment.backEndNTSS}/repair_job_type_apartment_employee/save_data`,
    CHECK_JOB_TYPE_EXIST_IN_APARTMENT: `${environment.backEndNTSS}/repair_job_type_apartment_employee/check_job_type_exist_in_apartment`,
  }

  // Criteria
  REPAIR_CRITERIA = {
    LOAD_DATA: `${environment.backEndNTSS}/repair_criteria/load_data`,
    PAGINATION: `${environment.backEndNTSS}/repair_criteria/pagination`,
    CREATE: `${environment.backEndNTSS}/repair_criteria/create_data`,
    UPDATE: `${environment.backEndNTSS}/repair_criteria/update_data`,
    DELETE: `${environment.backEndNTSS}/repair_criteria/update_active`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndNTSS}/repair_criteria/create_data_by_excel`,
  }

  //#region Handle

  objToQueryString = (obj: any) =>
    Object.keys(obj)
      .map((k) => {
        if (Array.isArray(obj[k])) {
          return `${k}=${JSON.stringify(obj[k])}`
        }
        return `${k}=${obj[k]}`
      })
      .join('&')

  postRepair(url: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const headers = new HttpHeaders({
        Authorization: `Bearer `,
      })

      const request = this.http.post(this.hostRepair + url, data, { headers })

      lastValueFrom(request)
        .then((res: any) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  post(url: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const headers = new HttpHeaders({
        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI0NTAzZTg4NS0xMWRiLTRkY2EtOGE1NS0yOTJmNWY4MTY2ZWIiLCJpYXQiOjE3NTEwOTA5ODcsImV4cCI6ODA2MjYxMDk4N30.xSO7nMaoFkNLrG0nyJgrUFaORFwLKHW2ki-hoODHM84`,
      })
      const request = this.http.post(url, data)
      lastValueFrom(request)
        .then((res: any) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  get(url: string, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${url}?${query}`

    return new Promise((resolve, reject) => {
      const request = this.http.get(newUrl)
      lastValueFrom(request)
        .then((res: any) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }
  //#endregion
}
