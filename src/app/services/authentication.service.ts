import { Injectable } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { BehaviorSubject, Observable } from 'rxjs'
import { map } from 'rxjs/operators'
import { User } from '../models'
import { environment } from 'src/environments/environment'

@Injectable({ providedIn: 'root' })
export class AuthenticationService {
  private currentUserSubject: BehaviorSubject<User>
  public currentUser: Observable<User>
  host = environment.backEnd
  constructor(private http: HttpClient) {
    const temp: any = null
    this.currentUserSubject = new BehaviorSubject<User>(JSON.parse(localStorage.getItem('currentUser') || temp))
    this.currentUser = this.currentUserSubject.asObservable()
  }

  public get currentUserValue(): User {
    return this.currentUserSubject.value
  }

  login(username: string, password: string) {
    return this.http.post(`${this.host}/auth/login`, { username, password }).pipe(
      map((user) => {
        // store user details and jwt token in local storage to keep user logged in between page refreshes
        localStorage.setItem('currentUser', JSON.stringify({ ...user, username }))
        this.currentUserSubject.next({ ...user, username } as User)

        return user
      })
    )
  }

  updatePassword(currentPassword: string, newPassword: string, confirmNewPassword: string) {
    return this.http.post(`${this.host}/auth/update-password`, { currentPassword, newPassword, confirmNewPassword }).pipe(
      map((data) => {
        return data
      })
    )
  }

  logout() {
    localStorage.removeItem('Language')
    localStorage.removeItem('LanguageConfig')
    localStorage.removeItem('currentUser')
    localStorage.removeItem('permission')
    const temp: any = null
    this.currentUserSubject.next(temp)
  }

  /** Check quyền */
  public checkPermission(lstFeature: string[], action: string) {
    let lstPermission: any
    lstPermission = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0) {
      return lstPermission.some((c: any) => lstFeature.includes(c.code) && c[`is${action}`])
    }
    return false
  }

  /** Check quyền view */
  public checkPermissionView(lstFeature: string[]) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && lstFeature?.length > 0) {
      return lstPermission.some((c: any) => lstFeature.includes(c.code) && c.isView)
    }
    return false
  }

  /** Check quyền create */
  public checkPermissionCreate(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isCreate)
    }
    return false
  }

  /** Check quyền update */
  public checkPermissionUpdate(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)

    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isUpdate)
    }
    return false
  }

  /** Check quyền delete */
  public checkPermissionDelete(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isDelete)
    }
    return false
  }

  /** Check quyền print */
  public checkPermissionPrint(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isPrint)
    }
    return false
  }

  /** Check quyền export */
  public checkPermissionExport(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isExport)
    }
    return false
  }

  /** Check quyền import */
  public checkPermissionImport(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isImport)
    }
    return false
  }

  /** Check quyền active */
  public checkPermissionActive(featureCode: string) {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)
    if (lstPermission?.length > 0 && featureCode) {
      return lstPermission.some((c: any) => c.code === featureCode && c.isActive)
    }
    return false
  }

  /** Check quyền vào route */
  public checkPermissionRoute(stateUrl: string) {
    if (stateUrl == '/welcome') return true
    const currentUser = JSON.parse(localStorage.getItem('currentUser') as any)
    if (currentUser.isAdmin) return true
    const lstPermission: any = JSON.parse(localStorage.getItem('permission') as any)

    return lstPermission.some((c: any) => c.path == stateUrl && c.isView)
  }
}
