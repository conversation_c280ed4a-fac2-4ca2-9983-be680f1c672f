import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BehaviorSubject, lastValueFrom } from 'rxjs'
import { environment } from 'src/environments/environment'
import { CoreService } from './core.service'

@Injectable()
export class ApiScService {
  hostRepair = `${environment.repair}/`
  eventCloseModal = new BehaviorSubject<boolean>(false)
  eventCloseModalPO = new BehaviorSubject<boolean>(false)
  eventUpdateCollectEmployee = new BehaviorSubject<boolean>(false)
  eventUpdateReadNotify = new BehaviorSubject<boolean>(false)
  eventChangeLocal = new BehaviorSubject<boolean>(false)
  constructor(public coreService: CoreService, private http: HttpClient) {}

  isLoggedIn() {
    return this.eventChangeLocal.asObservable()
  }

  REPAIR_SERVICE_FEE_STANDARD = {
    PAGINATION: `repair_service_fee_standard/pagination`,
    LOAD_DATA: `repair_service_fee_standard/load_data`,
    CREATE: 'repair_service_fee_standard/create_data',
    UPDATE: 'repair_service_fee_standard/update_data',
    UPDATE_STATUS: 'repair_service_fee_standard/update_status',
    FIND_DETAIL: 'repair_service_fee_standard/find_detail',
  }

  REPAIR_BUYING_MATERIAL = {
    LOAD_DATA: `repair_buying_material/load_data`,
    FIND_DETAIL: 'repair_buying_material/find_detail',
    PAGINATION: 'repair_buying_material/pagination',
    PAGINATION_BUYING_MATERIAL_APPROVED: 'repair_buying_material/pagination_for_buying_material_approved',
    CREATE: 'repair_buying_material/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_buying_material/create_data_by_excel',
    UPDATE: 'repair_buying_material/update_data',
    UPDATE_IMAGE: 'repair_buying_material/upload_images',
    UPDATE_CANCEL: 'repair_buying_material/update_cancel',
    UPDATE_CANCEL_EACH_ROW: 'repair_buying_material/update_cancel_each_row',
    UPDATE_APPROVE: 'repair_buying_material/update_approve',
    ACCOUNTANT_UPDATE_APPROVE: 'repair_buying_material/account_update_approve',
    UPDATE_APPROVING: 'repair_buying_material/update_approving',
    SEND_BUYING_MATERIAL_FINANCIAL_ACCOUNTANT: 'repair_buying_material/send_buying_material_financial_accountant',
    UPDATE_WAITING_BUYING_DATE_EXPIRE: 'repair_buying_material/update_waiting_buying_date_expire',
    LOAD_ALL_BUYING_MATERIAL_DETAIL_BY_LST_ID: 'repair_buying_material/load_all_buying_material_detail_by_lst_id',
    APPROVE_ALL_BUYING_MATERIAL_DETAIL_BY_LST_ID: 'repair_buying_material/approve_all_buying_material_detail_by_lst_id',
    CREATE_EMPLOYEE_ADVANCE_DETAIL_LINK_TO_BUYING_MATERIAL: 'repair_buying_material/create_employee_advance_detail_link_to_buying_material',
    LOAD_BUYING_MATERIAL_GROUP_BY_WAREHOUSE_MATERIAL: 'repair_buying_material/load_buying_material_group_by_warehouse_material',
  }

  REPAIR_WAREHOUSE_MATERIAL = {
    LOAD_DATA: `repair_warehouse_material/load_data`,
    FIND_DETAIL: 'repair_warehouse_material/find_detail',
    PAGINATION: 'repair_warehouse_material/pagination',
    CREATE: 'repair_warehouse_material/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_warehouse_material/create_data_by_excel',
    UPDATE: 'repair_warehouse_material/update_data',
    DELETE: 'repair_warehouse_material/update_active',
    REPORT_MATERIAL: 'repair_warehouse_material/report_material',
    HISTORY_PAGINATION: 'repair_warehouse_material/history_pagination',
    PAGINATION_REPORT_IN_OUT: 'repair_warehouse_material/pagination_report_in_out',
    PAGINATION_REPORT_INVENTORY: 'repair_warehouse_material/pagination_report_inventory',
    LOAD_DETAIL_IN_OUT: 'repair_warehouse_material/load_detail_in_out',
    ADD_MATERIAL: 'repair_warehouse_material/add_material',
  }

  REPAIR_MATERIAL_CATEGORY = {
    REPORT_MATERIAL: `repair_material_category/report_material`,
    LOAD_DATA: `repair_material_category/load_data`,
    LOAD_DATA_NOT_IN_WH: `repair_material_category/load_data_not_in_warehouse`,
    FIND_DETAIL: 'repair_material_category/find_detail',
    PAGINATION: 'repair_material_category/pagination',
    CREATE: 'repair_material_category/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_material_category/create_data_by_excel',
    UPDATE: 'repair_material_category/update_data',
    DELETE: 'repair_material_category/update_active',
    PAGINATION_REPORT_IN_OUT: 'repair_material_category/pagination_report_in_out',
    LOAD_DETAIL_IN_OUT: 'repair_material_category/load_detail_in_out',
    PAGINATION_REPORT_MATERIAL: 'repair_material_category/pagination_report_inventory',
  }

  REPAIR_UNIT = {
    LOAD_DATA: `repair_unit/load_data`,
    FIND_DETAIL: 'repair_unit/find_detail',
    PAGINATION: 'repair_unit/pagination',
    CREATE: 'repair_unit/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_unit/create_data_by_excel',
    UPDATE: 'repair_unit/update_data',
    DELETE: 'repair_unit/update_active',
  }

  REPAIR_CHECK_INVENTORY = {
    LOAD_DATA: `repair_check_inventory/load_data`,
    FIND_DETAIL: 'repair_check_inventory/find_detail',
    PAGINATION: 'repair_check_inventory/pagination',
    CREATE: 'repair_check_inventory/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_check_inventory/create_data_by_excel',
    UPDATE: 'repair_check_inventory/update_data',
    UPDATE_CANCEL: 'repair_check_inventory/update_cancel',
    UPDATE_APPROVING: 'repair_check_inventory/update_approving',
    UPDATE_APPROVE: 'repair_check_inventory/update_approve',
  }

  REPAIR_MATERIAL_CATEGORY_DETAIL = {
    PAGINATION: 'repair_material_category_detail/pagination',
    REPORT_WAREHOUSE_MATERIAL: 'repair_material_category_detail/report_warehouse_material',
    LOAD_DATA: `repair_material_category_detail/load_data`,
    CREATE_DATA_BY_EXCEL: 'repair_material_category_detail/create_data_by_excel',
    REPORT_MATERIAL_DETAIL: `repair_material_category_detail/report_material_detail`,
    REPORT_MATERIAL_DETAIL_FOR_EMPLOYEE: `repair_material_category_detail/report_material_detail_for_employee`,
    REPORT_MATERIAL_DETAIL_BY_MATERIAL: `repair_material_category_detail/report_material_detail_by_material`,
    REPORT_MATERIAL_BY_EMPLOYEE: `repair_material_category_detail/report_material_by_employee`,
    MATERIAL_CATEGORY_DETAIL_HISTORY_PAGINATION: `repair_material_category_detail/material_category_detail_history_pagination`,
  }

  REPAIR_INBOUND = {
    LOAD_DATA: `repair_inbound/load_data`,
    FIND_DETAIL: 'repair_inbound/find_detail',
    PAGINATION: 'repair_inbound/pagination',
    CREATE: 'repair_inbound/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_inbound/create_data_by_excel',
    UPDATE: 'repair_inbound/update_data',
    UPDATE_CANCEL: 'repair_inbound/update_cancel',
    UPDATE_APPROVE: 'repair_inbound/update_approve',
    CONFIRM_PAYMENT: 'repair_inbound/confirm_payment',
    CANCEL_PAYMENT: 'repair_inbound/cancel_payment',
    SPLIT: 'repair_inbound/split',
  }

  REPAIR_JOB = {
    PAGINATION: 'repair_job/pagination',
    FIND_DETAIL: 'repair_job/find_detail',
    REPORT_JOB: 'repair_job/report_job_status_by_employee',
    REPORT_EMPLOYEE_JOB_REWARD: 'repair_job/report_employee_job_reward',
    DETAIL_EMPLOYEE_JOB_REWARD: 'repair_job/detail_employee_job_reward',
    REPORT_APARTMENT_JOB: 'repair_job/report_apartment_job',
    REPORT_EMPLOYEE_ADVANCE: 'repair_job/report_employee_advance',
    DETAIL_BUY_MATERIAL: 'repair_job/detail_buy_material',
    REPORT_PERFOMENT_EMPLOYEE: 'repair_job/report_perfoment_employee',
    REPORT_PERFOMENT_EMPLOYEE_DETAIL: 'repair_job/detail_report_perfoment_employee',
  }

  REPAIR_JOB_TYPE = {
    LOAD_DATA: `repair_job_type/load_data`,
    LOAD_DATA_FOR_WEB: `repair_job_type/load_data_for_web`,
    FIND_DETAIL: 'repair_job_type/find_detail',
    PAGINATION: 'repair_job_type/pagination',
    CREATE: 'repair_job_type/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_job_type/create_data_by_excel',
    UPDATE: 'repair_job_type/update_data',
    DELETE: 'repair_job_type/update_active',
  }

  REPAIR_JOB_CATEGORY = {
    LOAD_DATA: `repair_job_category/load_data`,
    FIND_DETAIL: 'repair_job_category/find_detail',
    PAGINATION: 'repair_job_category/pagination',
    CREATE: 'repair_job_category/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_job_category/create_data_by_excel',
    UPDATE: 'repair_job_category/update_data',
    DELETE: 'repair_job_category/update_active',
    GET_LIST_FOR_REFRESH: 'repair_job_category/get_list_job_category_for_fresh',
  }

  REPAIR_OUTBOUND = {
    LOAD_DATA: `repair_outbound/load_data`,
    FIND_DETAIL: 'repair_outbound/find_detail',
    PAGINATION: 'repair_outbound/pagination',
    CREATE: 'repair_outbound/create_data',
    UPDATE: 'repair_outbound/update_data',
    UPDATE_CANCEL: 'repair_outbound/update_cancel',
    UPDATE_APPROVE: 'repair_outbound/update_approve',
    UPDATE_APPROVE_FOR_APPROVING: 'repair_outbound/update_approve_for_approving',
    SPLIT: 'repair_outbound/split',
  }

  REPORT_MATERIAL_DETAIL = {
    REPORT_USING_MATERIAL_DETAIL: 'repair_job_detail/report_using_material_detail',
    REPORT_MATERIAL_DETAIL_BY_APARTMENT: 'repair_job_detail/report_material_detail_by_apartment',
    REPORT_MATERIAL_DETAIL_BY_ROOM: 'repair_job_detail/report_material_detail_by_room',
    REPORT_MATERIAL_DETAIL_BY_ROOM_DETAIL: 'repair_job_detail/report_material_detail_by_room_detail',
    REPORT_MATERIAL_DETAIL_BY_EMPLOYEE: 'repair_job_detail/report_material_detail_by_employee',
    PAGINATION: 'repair_job_detail/pagination',
  }

  REPAIR_RETURN_MATERIAL = {
    LOAD_DATA: `repair_return_material/load_data`,
    FIND_DETAIL: 'repair_return_material/find_detail',
    PAGINATION: 'repair_return_material/pagination',
    CREATE: 'repair_return_material/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_return_material/create_data_by_excel',
    UPDATE: 'repair_return_material/update_data',
    UPDATE_CANCEL: 'repair_return_material/update_cancel',
    UPDATE_APPROVE: 'repair_return_material/update_approve',
  }

  REPAIR_REWARD = {
    FIND_DETAIL: 'repair_reward/find_detail',
    PAGINATION: 'repair_reward/pagination',
    STATISTIC_BY_EMPLOYEE: 'repair_reward/reward_statistic_by_employee',
    STATISTIC_BY_JOB_TYPE: 'repair_reward/reward_statistic_by_job_type',
  }

  REPAIR_JOB_MOVEMENT_FEE = {
    PAGINATION: `repair_job_movement_fee/pagination`,
    LOAD_DATA: `repair_job_movement_fee/load_data`,
    CREATE: 'repair_job_movement_fee/create_data',
    UPDATE: 'repair_job_movement_fee/update_data',
    UPDATE_STATUS: 'repair_job_movement_fee/update_status',
    FIND_DETAIL: 'repair_job_movement_fee/find_detail',
  }

  REPAIR_TRANSFER_WAREHOUSE = {
    LOAD_DATA: `repair_transfer_warehouse/load_data`,
    FIND_DETAIL: 'repair_transfer_warehouse/find_detail',
    PAGINATION: 'repair_transfer_warehouse/pagination',
    CREATE: 'repair_transfer_warehouse/create_data',
    CREATE_DATA_BY_EXCEL: 'repair_transfer_warehouse/create_data_by_excel',
    UPDATE: 'repair_transfer_warehouse/update_data',
    UPDATE_CANCEL: 'repair_transfer_warehouse/update_cancel',
    UPDATE_SENT: 'repair_transfer_warehouse/update_sent',
    UPDATE_APPROVE: 'repair_transfer_warehouse/update_approve',
  }

  // Criteria
  REPAIR_CRITERIA = {
    LOAD_DATA: `repair_criteria/load_data`,
    PAGINATION: 'repair_criteria/pagination',
    CREATE: 'repair_criteria/create_data',
    UPDATE: 'repair_criteria/update_data',
    DELETE: 'repair_criteria/update_active',
    CREATE_DATA_BY_EXCEL: 'repair_criteria/create_data_by_excel',
  }

  postRepair(url: string, data: any): Promise<any> {
    return (
      this.http
        .post(this.hostRepair + url, data)
        .toPromise()
        // tslint:disable-next-line: no-shadowed-variable
        .then((data: any) => {
          return data as any
        })
    )
  }

  get(url: string, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.hostRepair + url}?${query}`

    return (
      this.http
        .get(newUrl)
        .toPromise()
        // tslint:disable-next-line: no-shadowed-variable
        .then((data: any) => {
          return data as any
        })
    )
  }

  objToQueryString = (obj: any) =>
    Object.keys(obj)
      .map((k) => {
        if (Array.isArray(obj[k])) {
          return `${k}=${JSON.stringify(obj[k])}`
        }
        return `${k}=${obj[k]}`
      })
      .join('&')
}
