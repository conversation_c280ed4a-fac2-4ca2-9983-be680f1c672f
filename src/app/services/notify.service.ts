import { Component, Inject, Injectable } from '@angular/core'
import * as $ from 'jquery'
import { MatSnackBar, MatSnackBarRef, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar'
@Injectable({
  providedIn: 'root',
})
export class NotifyService {
  isNotifi = false
  constructor(private readonly snackBar: MatSnackBar) {}

  showError(error: any) {
    let message = 'Đã có lỗi xãy ra'
    if (error.message) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    }
    $('#mainLoading').removeClass('loading-service')
    this.openSnackBar(message, 'error-snackbar')
  }

  showInfo(message: string) {
    $('#mainLoading').removeClass('loading-service')
    this.openSnackBar(message, 'info-snackbar')
  }

  showSuccess(message: string) {
    $('#mainLoading').removeClass('loading-service')
    this.openSnackBar(message, 'success-snackbar')
  }
  showWarning(message: string) {
    $('#mainLoading').removeClass('loading-service')
    this.openSnackBar(message, 'warning-snackbar')
  }

  openSnackBar(message: string, className = '') {
    this.snackBar.openFromComponent(BasicSnackbarComponent, {
      data: message,
      duration: 5000,
      panelClass: [className],
      horizontalPosition: 'end',
      verticalPosition: 'top',
    })
  }

  showloading(tag = '#mainLoading') {
    $(tag).addClass('loading-service')
  }

  hideloading(tag = '#mainLoading') {
    $(tag).removeClass('loading-service')
  }
}
@Component({ template: `Thông báo!<br /><span [innerHTML]="data"></span>` })
export class BasicSnackbarComponent {
  constructor(public sbRef: MatSnackBarRef<BasicSnackbarComponent>, @Inject(MAT_SNACK_BAR_DATA) public data: any) {}
}
