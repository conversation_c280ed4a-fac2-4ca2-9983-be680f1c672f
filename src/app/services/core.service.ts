import { Injectable } from '@angular/core'
import { all, create } from 'mathjs'
import * as moment from 'moment'
import { enumData } from '../core'
const config = {}
const math = create(all, config)

@Injectable()
export class CoreService {
  constructor() {}

  date = {
    to_yyyyMMddHHmmss: (d: Date) => {
      return (
        d.getFullYear() +
        ('0' + (d.getMonth() + 1)).slice(-2) +
        ('0' + d.getDate()).slice(-2) +
        ('0' + d.getHours()).slice(-2) +
        ('0' + d.getMinutes()).slice(-2) +
        ('0' + d.getSeconds()).slice(-2)
      )
    },
    to_ddMMyyyyHHmmss: (d: Date) => {
      return (
        ('0' + d.getDate()).slice(-2) +
        '/' +
        ('0' + (d.getMonth() + 1)).slice(-2) +
        '/' +
        d.getFullYear() +
        ' ' +
        ('0' + d.getHours()).slice(-2) +
        ':' +
        ('0' + d.getMinutes()).slice(-2) +
        ':' +
        ('0' + d.getSeconds()).slice(-2)
      )
    },
    to_ddMMyyyy: (d: Date) => {
      return ('0' + d.getDate()).slice(-2) + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear()
    },
    // 15 giờ 47 phút, ngày 04 tháng 12 năm 2021
    to_HHmmddMMyyyyLong: (d: Date) => {
      return (
        ('0' + d.getHours()).slice(-2) +
        ' giờ ' +
        ('0' + d.getMinutes()).slice(-2) +
        ' phút, ngày ' +
        ('0' + d.getDate()).slice(-2) +
        ' tháng ' +
        ('0' + (d.getMonth() + 1)).slice(-2) +
        ' năm ' +
        d.getFullYear()
      )
    },

    getDate: (pDate: Date) => {
      return pDate && pDate.getTime() ? new Date(pDate.getFullYear(), pDate.getMonth(), pDate.getDate()) : pDate
    },
    getDateHM: (pDate: Date) => {
      return pDate && pDate.getTime() ? new Date(pDate.getFullYear(), pDate.getMonth(), pDate.getDate(), pDate.getHours(), pDate.getMinutes()) : pDate
    },
    getDateHMS: (pDate: Date) => {
      return pDate && pDate.getTime()
        ? new Date(pDate.getFullYear(), pDate.getMonth(), pDate.getDate(), pDate.getHours(), pDate.getMinutes(), pDate.getSeconds())
        : pDate
    },
    addDay: (pDate: Date, pVal: number) => {
      return pDate && pDate.getTime() ? new Date(pDate.getTime() + pVal * 24 * 60 * 60000) : pDate
    },
    addHour: (pDate: Date, pVal: number) => {
      return pDate && pDate.getTime() ? new Date(pDate.getTime() + pVal * 60 * 60000) : pDate
    },
    addMinute: (pDate: Date, pVal: number) => {
      return pDate && pDate.getTime() ? new Date(pDate.getTime() + pVal * 60000) : pDate
    },
    addSecond: (pDate: Date, pVal: number) => {
      return pDate && pDate.getTime() ? new Date(pDate.getTime() + pVal * 1000) : pDate
    },
    setHour: (pDate: Date, pVal: number) => {
      return pDate && pDate.getTime() ? new Date(pDate.getFullYear(), pDate.getMonth(), pDate.getDate(), pVal, 0) : pDate
    },
  }

  /** Hàm filter custom select ant */
  /** Hàm filter custom select ant */
  filterOption = (inputValue: string, option: any) => {
    const labelStr = this.convertToSlug(option.nzLabel)
    const inputStr = this.convertToSlug(inputValue)
    return labelStr.includes(inputStr)
  }

  /** Chuyển đổi "từ tiếng Việt" thành "tu tieng viet"  */
  private convertToSlug(str: string) {
    // Chuyển đổi chuỗi thành chữ thường
    str = str.toLowerCase()

    // Tạo bảng chuyển đổi ký tự đặc biệt sang dấu cách
    const specialChars: any = {
      à: 'a',
      á: 'a',
      ả: 'a',
      ã: 'a',
      ạ: 'a',
      ă: 'a',
      ắ: 'a',
      ằ: 'a',
      ẳ: 'a',
      ẵ: 'a',
      ặ: 'a',
      â: 'a',
      ấ: 'a',
      ầ: 'a',
      ẩ: 'a',
      ẫ: 'a',
      ậ: 'a',
      đ: 'd',
      è: 'e',
      é: 'e',
      ẻ: 'e',
      ẽ: 'e',
      ẹ: 'e',
      ê: 'e',
      ế: 'e',
      ề: 'e',
      ể: 'e',
      ễ: 'e',
      ệ: 'e',
      ì: 'i',
      í: 'i',
      ỉ: 'i',
      ĩ: 'i',
      ị: 'i',
      ò: 'o',
      ó: 'o',
      ỏ: 'o',
      õ: 'o',
      ọ: 'o',
      ô: 'o',
      ố: 'o',
      ồ: 'o',
      ổ: 'o',
      ỗ: 'o',
      ộ: 'o',
      ơ: 'o',
      ớ: 'o',
      ờ: 'o',
      ở: 'o',
      ỡ: 'o',
      ợ: 'o',
      ù: 'u',
      ú: 'u',
      ủ: 'u',
      ũ: 'u',
      ụ: 'u',
      ư: 'u',
      ứ: 'u',
      ừ: 'u',
      ử: 'u',
      ữ: 'u',
      ự: 'u',
      ỳ: 'y',
      ý: 'y',
      ỷ: 'y',
      ỹ: 'y',
      ỵ: 'y',
    }

    // Lặp qua từng ký tự trong chuỗi và thực hiện chuyển đổi
    let slug = ''
    for (let i = 0; i < str.length; i++) {
      const char = str[i]
      if (specialChars[char]) {
        slug += specialChars[char]
      } else if (/[a-z0-9 ]/.test(char)) {
        slug += char
      }
    }

    return slug
  }

  excelDateToJSDate(data: any) {
    const serial = +data
    if (isNaN(serial)) {
      const res = new Date(data)
      return res.toString() !== 'Invalid Date' ? res : undefined
    } else {
      const utcDays = Math.floor(serial - 25569)
      const utcValue = utcDays * 86400
      const dateInfo = new Date(utcValue * 1000)

      const fractionalDay = serial - Math.floor(serial) + 0.0000001

      let totalSeconds = Math.floor(86400 * fractionalDay)

      const seconds = totalSeconds % 60

      totalSeconds -= seconds

      const hours = Math.floor(totalSeconds / (60 * 60))
      const minutes = Math.floor(totalSeconds / 60) % 60

      return new Date(dateInfo.getFullYear(), dateInfo.getMonth(), dateInfo.getDate(), hours, minutes, seconds)
    }
  }

  isEqual2Obj(objA: any, objB: any) {
    // Tạo các mảng chứa tên các property
    const aProps = Object.getOwnPropertyNames(objA)
    const bProps = Object.getOwnPropertyNames(objB)
    // Nếu độ dài của mảng không bằng nhau,
    // thì 2 objects đó không bằnh nhau.
    if (aProps.length !== bProps.length) {
      return false
    }

    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < aProps.length; i++) {
      const propName = aProps[i]
      // Nếu giá trị của cùng một property mà không bằng nhau,
      // thì 2 objects không bằng nhau.
      if (objA[propName] !== objB[propName]) {
        return false
      }
    }
    // Nếu code chạy đến đây,
    // tức là 2 objects được tính lằ bằng nhau.
    return true
  }

  sumArray(arr: any[], prop: string) {
    let total = 0
    for (let i = 0, len = arr.length; i < len; i++) {
      total += arr[i][prop]
    }
    return total
  }

  groupByArray(data: any[], key: string) {
    const groupedObj = data.reduce((prev, cur) => {
      if (!prev[cur[key]]) {
        prev[cur[key]] = [cur]
      } else {
        prev[cur[key]].push(cur)
      }
      return prev
    }, {})
    return Object.keys(groupedObj).map((Heading: string) => ({ heading: Heading, list: groupedObj[Heading] }))
  }

  /** Sort từ nhỏ tới lớn */
  dynamicSort(data: any[], key: string) {
    return data.sort(this.sortArray(key))
  }

  sortArray(key: any) {
    let sortOrder = 1
    if (key[0] === '-') {
      sortOrder = -1
      key = key.substr(1)
    }
    return (a: any, b: any) => {
      const result = a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0
      return result * sortOrder
    }
  }

  convertObjToArray(obj: any) {
    const arr: any[] = []
    for (const key in obj) {
      const value = obj[key]
      arr.push(value)
    }
    return arr
  }

  //#region xếp hạng A B C D

  scoreRankABCD(score: number) {
    if (score === undefined) return ''
    if (score < 0) return 'Đang đánh giá'
    if (score > 90) return `${score.toFixed(2)} - A`
    if (score > 70) return `${score.toFixed(2)} - B`
    if (score > 50) return `${score.toFixed(2)} - C`
    return `${score.toFixed(2)} - D`
  }

  //#endregion

  //#region Cal capacity score

  calScore(item: any) {
    let score = 0
    if (item.__childs__?.length > 0) {
      const length = item.__childs__.length
      let scoreC = 0
      for (let i = 0; i < length; i++) {
        // tslint:disable-next-line: no-shadowed-variable
        let temp = this.calScore(item.__childs__[i])

        if (isNaN(temp)) {
          temp = 0
        }
        if (!isFinite(temp)) {
          temp = 0
        }
        scoreC += temp
      }
      const temp = (item.percent * scoreC) / 100
      score += temp
    } else {
      if (item.value) item.value += ''
      if (item.type === enumData.DataType.Number.code && item.value && item.value.trim() != '') {
        let temp = 0
        const x = +item.value
        if (item.isCalUp) {
          if (x >= item.percentRule) {
            temp = item.percent
          } else {
            temp = (x * item.percent) / item.percentRule // giá trị * tỉ trọng / điều kiện b
          }
        } else {
          if (x <= item.percentRule) {
            temp = item.percent
          } else if (x >= item.percentDownRule) {
            temp = 0
          } else {
            temp = ((item.percentDownRule - x) * item.percent) / (item.percentDownRule - item.percentRule)
          }
        }
        if (isNaN(temp)) {
          score += 0
        } else if (!isFinite(temp)) {
          score += 0
        } else {
          score += temp
        }
      } else if (item.type === enumData.DataType.List.code) {
        const chose = item.__supplierCapacityListDetails__.find((p: any) => p.isChosen)
        const temp = chose ? chose.value : 0
        const finalTemp = (temp * item.percent) / 100
        score += finalTemp
      }
    }

    if (isNaN(score) || !isFinite(score)) return 0
    return score
  }

  convertTypeList(list: any[]) {
    if (typeof list !== 'undefined') {
      const data = list.find((p) => p.isChosen)
      if (data) return data.name
    }

    return ''
  }

  //#endregion

  //#region get enum name

  getEnumElementName(enumData: object, value: string) {
    return this.getEnumElement(enumData, value, 'code', 'name')
  }

  getEnumElement(enumData: object, value: string, keyIn: string, keyOut: string) {
    const data = this.convertObjToArray(enumData)
    const item = data.find((p) => p[keyIn] === value)
    return item && item[keyOut] ? item[keyOut] : ''
  }

  getNameComponent(code: string, enumSource: any) {
    let data: any = []
    let lstEnum = this.convertObjToArray(enumSource)
    for (const module of lstEnum) {
      let lstData = this.convertObjToArray(module.data)
      for (const component of lstData) {
        data.push({ code: component.code, name: component.name })
      }
    }
    const item = data.find((p: any) => p.code === code)
    return item ? item.name : ''
  }

  //#endregion

  /** Tính lại giá */
  calFomular(fomular: string, lstField: any[], item: any) {
    let value = null
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace('[' + col.code + ']', item[col.id])
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, item.number)

    try {
      value = math.evaluate(tempFomular)
      if (typeof value !== 'number') value = null
    } catch {
      return null
    } finally {
      return value
    }
  }

  //#region Language
  getLanguage() {
    let rs = []
    if (localStorage.getItem('Language')) {
      let lang = localStorage.getItem('LanguageConfig')
      if (lang && lang.length > 0) {
        let value = JSON.parse(lang ? lang : '')
        if (value && value.length > 0) {
          var object = value.reduce((obj: any, item: any) => Object.assign(obj, { [item.key]: item.value }), {})
          rs = object
        }
      }
    }
    return rs
  }

  getLanguageByComponent(component: any) {
    let lang = localStorage.getItem('LanguageConfig')
    if (lang) {
      let value = JSON.parse(lang ? lang : '')
      if (value && value.length > 0) {
        var lstLangInCom = value.filter((s: any) => s.component === component)
        if (lstLangInCom.length > 0) {
          var object = lstLangInCom.reduce((obj: any, item: any) => Object.assign(obj, { [item.key]: item.value }), {})
          return object
        }
      }
    }
    return []
  }

  getLanguageModule(code: string, sourceEnum: any) {
    let component: any = {}
    let lstEnum = this.convertObjToArray(sourceEnum)
    for (const module of lstEnum) {
      let lstData = this.convertObjToArray(module.data)
      component = lstData.find((p: any) => p.code === code)
      if (component) {
        return { component, module }
      }
    }
    return null
  }

  //#endregion

  //#region excel

  isFreeText1_50(text: string) {
    const regularExpression = /^.{1,50}$/
    return regularExpression.test(String(text || '').toLowerCase())
  }

  isPass(text: string) {
    const regularExpression = /^.{4,}$/
    return regularExpression.test(String(text || '').toLowerCase())
  }

  validateEmail(email: string) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return regex.test(email)
  }

  isNumeric(str: string) {
    const regex = /^\d+$/
    return regex.test(str)
  }

  //#endregion

  convertToDate(d: any) {
    if (Object.prototype.toString.call(d) === '[object Date]') {
      d = moment(d, 'DD/MM/YYYY').toDate()
      if (isNaN(d)) {
        return null
      } else {
        return new Date(d)
      }
    } else if (Object.prototype.toString.call(d) === '[object Number]') {
      d = this.excelDateToJSDate(d)
      if (isNaN(d)) {
        return null
      } else {
        return d
      }
    } else if (Object.prototype.toString.call(d) === '[object String]') {
      if (this.isValidDate(d) == true) {
        var dateParts = d.split('/')
        var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
        return dateObject
      } else {
        return null
      }
    } else {
      return null
    }
  }

  isValidDate(dateString: any) {
    // First check for the pattern
    if (!/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) return false
    // Parse the date parts to integers
    var parts = dateString.split('/')
    var day = parseInt(parts[0], 10)
    var month = parseInt(parts[1], 10)
    var year = parseInt(parts[2], 10)

    // Check the ranges of month and year
    if (year < 1000 || year > 3000 || month == 0 || month > 12) return false

    var monthLength = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

    // Adjust for leap years
    if (year % 400 == 0 || (year % 100 != 0 && year % 4 == 0)) monthLength[1] = 29

    // Check the range of the day
    return day > 0 && day <= monthLength[month - 1]
  }
  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  addMonthsToDate(date: Date, numberOfMonths: number) {
    const newDate = new Date(date) // Create a new Date object to avoid mutating the original date
    const currentMonth = newDate.getMonth()
    newDate.setMonth(currentMonth + numberOfMonths)
    return newDate
  }

  public getEnumElementColor(enumData: object, value: string) {
    return this.getEnumElement(enumData, value, 'code', 'color')
  }

  countDecimalDigits(number: any) {
    // Chuyển số thành chuỗi
    const numberString = (+number || 0).toString()

    // Kiểm tra xem có dấu chấm (phần thập phân) hay không
    const dotIndex = numberString.indexOf('.')

    if (dotIndex === -1) return 0

    // Nếu có, sử dụng slice để lấy phần thập phân
    const decimalPart = numberString.slice(dotIndex + 1)

    // Trả về số lượng chữ số trong phần thập phân
    return decimalPart.length
  }
}
