import { Injectable } from '@angular/core'
import { Borders, Column, Workbook } from 'exceljs'
import * as moment from 'moment'
import { CoreService } from './core.service'
import * as fs from 'file-saver'

export interface IColum {
  name: string
  code: string
  type?: 'number' | 'string' | 'date' | 'datetime'
  width?: number // độ rộng cột
}

@Injectable({ providedIn: 'root' })
export class ExcelV2Service {
  constructor(private coreService: CoreService) {}

  /** Hàm tải template excel sử dụng thư viện excelJs */
  async onDownloadTemplateExcelJs(data: { lstName: string[]; excelName: string; fileName?: string }) {
    if (!data.fileName) data.fileName = data.excelName
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet(data.excelName)

    const lstHeader = data.lstName
    const headerRow = worksheet.addRow(lstHeader)
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        bgColor: { argb: '001E3E' },
        fgColor: { argb: '001E3E' },
        type: 'pattern',
        pattern: 'solid',
      }
      cell.alignment = { horizontal: 'center', vertical: 'bottom' }
      cell.font = {
        bold: true,
        color: { argb: 'FFFFFF' },
        size: 11,
        name: 'Calibri',
      }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        right: { style: 'thin' },
        bottom: { style: 'thin' },
      }
    })

    const lenHeader = lstHeader.length

    for (let i = 1; i <= lenHeader; i++) {
      worksheet.getColumn(i).width = 15
    }

    const res = await workbook.xlsx.writeBuffer()

    let blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    let date = new Date().toISOString()
    const fileName = `${data.excelName}_${date}.xlsx`
    fs.saveAs(blob, fileName)
    return true
  }

  /** Hàm tải excel sử dụng thư viện excelJs */
  async onDownloadExcelJs(data: { lstHeader: IColum[]; lstData: any[]; excelName: string; fileName?: string }) {
    if (!data.fileName) data.fileName = data.excelName
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet(data.excelName)

    const lstHeader = data.lstHeader.map((header: any) => header.name)
    const headerRow = worksheet.addRow(lstHeader)
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        bgColor: { argb: '001E3E' },
        fgColor: { argb: '001E3E' },
        type: 'pattern',
        pattern: 'solid',
      }
      cell.alignment = { horizontal: 'center', vertical: 'bottom' }
      cell.font = {
        bold: true,
        color: { argb: 'FFFFFF' },
        size: 11,
        name: 'Calibri',
      }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        right: { style: 'thin' },
        bottom: { style: 'thin' },
      }
    })

    const lenHeader = lstHeader.length
    const lenData = data.lstData.length

    for (let i = 1; i <= lenHeader; i++) {
      worksheet.getColumn(i).width = data.lstHeader?.[i - 1].width || 20
    }

    for (let idx = 0; idx < lenData; idx++) {
      const rowDataDetail: any = []
      const item = data.lstData[idx]
      for (let headerConfig of data.lstHeader) {
        if (headerConfig.type === 'date')
          rowDataDetail.push(item[headerConfig.code] ? moment(item[headerConfig.code]).format('DD/MM/YYYY') : '')
        else if (headerConfig.type === 'datetime')
          rowDataDetail.push(
            item[headerConfig.code] ? moment(item[headerConfig.code]).format('DD/MM/YYYY hh:mm:ss') : ''
          )
        else if (headerConfig.type === 'number' && !isNaN(item[headerConfig.code]))
          rowDataDetail.push(item[headerConfig.code] ? +item[headerConfig.code] : 0)
        else rowDataDetail.push(item[headerConfig.code] ? item[headerConfig.code] : '')
      }
      const bodyRow = worksheet.addRow(rowDataDetail)
      bodyRow.eachCell((cell, colNumber) => {
        cell.fill = {
          bgColor: { argb: 'ffffff' },
          fgColor: { argb: 'ffffff' },
          type: 'pattern',
          pattern: 'solid',
        }
        cell.font = { size: 11, name: 'Calibri' }
        if (data.lstHeader[colNumber - 1].type === 'number' && !isNaN(item[data.lstHeader[colNumber - 1].code])) {
          const countDigits = this.coreService.countDecimalDigits(item[data.lstHeader[colNumber - 1].code])
          if (!countDigits) cell.numFmt = '#,##0'
          else cell.numFmt = '#,##0' + '.' + new Array(countDigits).fill(0).join('')
        }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          right: { style: 'thin' },
          bottom: { style: 'thin' },
        }
      })
    }
    const res = await workbook.xlsx.writeBuffer()

    let blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    let date = new Date().toISOString()
    const fileName = `${data.fileName}_${date}.xlsx`
    fs.saveAs(blob, fileName)
    return true
  }
}
