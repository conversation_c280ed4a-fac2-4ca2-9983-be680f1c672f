import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BehaviorSubject, lastValueFrom } from 'rxjs'
import { environment } from 'src/environments/environment'

@Injectable()
export class ApiFnbService {
  eventCallTab = new BehaviorSubject<boolean>(false)
  uploadUrl: string = `${environment.backEndFnb}/uploadFiles/upload-single-s3`
  constructor(private http: HttpClient) {}

  /** Start API END_POINT*/

  AUTH = {
    LOGIN: `${environment.backEndFnb}/auth/login`,
    UPDATE_PASSWORD: `${environment.backEndFnb}/auth/update-password`,
  }

  GRID_CONFIG = {
    DATA_SELECT: `${environment.backEndFnb}/grid-config/find`,
    LOAD_GRID_CONFIG: `${environment.backEndFnb}/grid-config/load-grid-config`,
    SAVE_GRID_CONFIG: `${environment.backEndFnb}/grid-config/save-grid-config`,
  }

  COMPANY = {
    LOAD_DATA: `${environment.backEndFnb}/company/load-data`,
    FIND: `${environment.backEndFnb}/company/find`,
    FIND_ALL: `${environment.backEndFnb}/company/find-all`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/company/load-data-select`,
    LOAD_DATA_SELECT_CHILDREN_COMPANY: `${environment.backEndFnb}/company/load-data-select-children-company`,
    LOAD_DATA_SELECT_INDEPENDENT: `${environment.backEndFnb}/company/load-data-select-independent`,
    LOAD_DATA_SELECT_WITHOUT_PARENT: `${environment.backEndFnb}/company/load-data-select-without-parent`,
    CHECK_TYPE_OF_CURRENT_COMPANY: `${environment.backEndFnb}/company/check-type-of-current-company`,
    CREATE: `${environment.backEndFnb}/company/create-data`,
    UPDATE: `${environment.backEndFnb}/company/update-data`,
    DELETE: `${environment.backEndFnb}/company/update-active`,
    PAGINATION: `${environment.backEndFnb}/company/pagination`,
    LOAD_DEPARTMENTS_BY_COMPANY: `${environment.backEndFnb}/company/load-departments-by-company`,
    IMPORT_EXCEL: `${environment.backEndFnb}/company/create-data-by-excel`,
    LOAD_HISTORY: `${environment.backEndFnb}/company/load-history`,
    LOAD_DETAIL: `${environment.backEndFnb}/company/find-detail`,
    LOAD_DEPARTMENTS_OF_COMPANY: `${environment.backEndFnb}/company/load-department-of-company`,
    LOAD_CHILD_COMPANY: `${environment.backEndFnb}/company/load-child-company`,
    UPDATE_LIST_CHILD_COMPANY: `${environment.backEndFnb}/company/update-list-child-company`,
    LOAD_DATA_SELECT_CHILD: `${environment.backEndFnb}/company/load-data-select-parent`,
    LOAD_DATA_WITHOUT_ID_CURRENT: `${environment.backEndFnb}/company/load-data-without-id-current`,
    LOAD_DATA_SELECT_FETCH_HRM: `${environment.backEndFnb}/company/load-data-select-fetch-hrm`,
  }

  BRANCH = {
    LOAD_DATA: `${environment.backEndFnb}/branch/load-data`,
    LOAD_DATA_BY_COMPANY: `${environment.backEndFnb}/branch/load-data-by-company`,
    LOAD_DATA_BY_DEPARTMENT: `${environment.backEndFnb}/branch/load-data-by-department`,
    LOAD_SELECT_BY_COMPANIES: `${environment.backEndFnb}/branch/load-select-by-companies`,
    LOAD_SELECT_BY_BRAND_LIST: `${environment.backEndFnb}/branch/load-select-by-brand-list`,
    FIND: `${environment.backEndFnb}/branch/find`,
    FIND_ONE: `${environment.backEndFnb}/branch/find-one`,
    FIND_ALL: `${environment.backEndFnb}/branch/find-all`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/branch/load-data-select`,
    CREATE: `${environment.backEndFnb}/branch/create`,
    DELETE: `${environment.backEndFnb}/branch/update-active-status`,
    UPDATE: `${environment.backEndFnb}/branch/update`,
    UPDATE_BRANCH_TO_WAREHOUSE: `${environment.backEndFnb}/branch/update-branch-to-warehouse`,
    PAGINATION: `${environment.backEndFnb}/branch/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/branch/create-data-by-excel`,
    LOAD_HISTORY: `${environment.backEndFnb}/branch/load-history`,
    LOAD_DATA_WITHOUT_ID_CURRENT: `${environment.backEndFnb}/branch/load-data-without-id-current`,
    LOAD_DATA_BY_COMPANY_FETCH_HRM: `${environment.backEndFnb}/branch/load-data-by-company-fetch-hrm`,
    LOAD_DATA_SELECT_FETCH_HRM: `${environment.backEndFnb}/branch/load-data-select-fetch-hrm`,
    FIND_LIST_PART_MASTER_OF_BRANCH: `${environment.backEndFnb}/branch/find-list-part-master-of-branch`,
    LOAD_LIST_PART_OF_BRANCH_AND_DEPARTMENT: `${environment.backEndFnb}/branch/load-list-part-of-branch-and-department`,
    LOAD_LIST_SUPPLIER_OF_BRANCH: `${environment.backEndFnb}/branch/load-supplier-of-branch`,
    PAGINATION_MATRIX: `${environment.backEndFnb}/branch/pagination-matrix`,
    UPDATE_DATA_MATRIX: `${environment.backEndFnb}/branch/update-data-matrix`,
    LOAD_PROVIDER: `${environment.backEndFnb}/branch/load-provider`,
  }

  BRAND = {
    LOAD_DATA: `${environment.backEndFnb}/brand/load-data`,
    LOAD_DATA_BY_COMPANY: `${environment.backEndFnb}/brand/load-data-by-company`,
    FIND: `${environment.backEndFnb}/brand/find`,
    FIND_ALL: `${environment.backEndFnb}/brand/find-all`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/brand/load-data-select`,
    LOAD_DATA_SELECT_WITHOUT_COMPANY: `${environment.backEndFnb}/brand/load-data-select-without-company`,
    LOAD_DATA_HAS_CURRENT_AND_WITHOUT_COMPANY: `${environment.backEndFnb}/brand/load-data-has-current-and-without-company`,
    CREATE: `${environment.backEndFnb}/brand/create`,
    DELETE: `${environment.backEndFnb}/brand/update-active-status`,
    UPDATE: `${environment.backEndFnb}/brand/update`,
    PAGINATION: `${environment.backEndFnb}/brand/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/brand/create-data-by-excel`,
    LOAD_HISTORY: `${environment.backEndFnb}/brand/load-history`,
    LOAD_DETAIL: `${environment.backEndFnb}/brand/load-detail`,
  }

  PRODUCT = {
    LOAD_DATA: `${environment.backEndFnb}/product/load-data-select`,
  }

  ITEM_INDUSTRY = {
    LOAD_DATA: `${environment.backEndFnb}/item-industry/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/item-industry/load-detail`,
    CREATE: `${environment.backEndFnb}/item-industry/create-data`,
    DELETE: `${environment.backEndFnb}/item-industry/update-active-status`,
    REJECT: `${environment.backEndFnb}/item-industry/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/item-industry/send-approved`,
    APPROVED: `${environment.backEndFnb}/item-industry/approved`,
    UPDATE: `${environment.backEndFnb}/item-industry/update-data`,
    PAGINATION: `${environment.backEndFnb}/item-industry/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/item-industry/create-data-by-excel`,
  }

  ITEM_GROUP = {
    LOAD_DATA: `${environment.backEndFnb}/item-group/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/item-group/load-detail`,
    CREATE: `${environment.backEndFnb}/item-group/create-data`,
    DELETE: `${environment.backEndFnb}/item-group/update-active-status`,
    REJECT: `${environment.backEndFnb}/item-group/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/item-group/send-approved`,
    APPROVED: `${environment.backEndFnb}/item-group/approved`,
    UPDATE: `${environment.backEndFnb}/item-group/update-data`,
    PAGINATION: `${environment.backEndFnb}/item-group/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/item-group/create-data-by-excel`,
  }
  ITEM_GROUP_SELL = {
    LOAD_DATA: `${environment.backEndFnb}/item-group-sell/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/item-group-sell/load-detail`,
    CREATE: `${environment.backEndFnb}/item-group-sell/create-data`,
    DELETE: `${environment.backEndFnb}/item-group-sell/update-active-status`,
    REJECT: `${environment.backEndFnb}/item-group-sell/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/item-group-sell/send-approved`,
    APPROVED: `${environment.backEndFnb}/item-group-sell/approved`,
    UPDATE: `${environment.backEndFnb}/item-group-sell/update-data`,
    PAGINATION: `${environment.backEndFnb}/item-group-sell/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/item-group-sell/create-data-by-excel`,
  }

  ITEM = {
    LOAD_DATA: `${environment.backEndFnb}/item/load-data-select`,
    LOAD_DATA_PRODUCT: `${environment.backEndFnb}/item/load-data-select-product`,
    FIND: `${environment.backEndFnb}/item/find`,
    LOAD_DATA_BY_BRANCH_ID: `${environment.backEndFnb}/item/load-data-by-branch-id`,
    FIND_ONE_BY_PRODUCT: `${environment.backEndFnb}/item/find-one-by-product`,
    FIND_LIST_BY_PRODUCT: `${environment.backEndFnb}/item/find-list-by-product`,
    LOAD_DETAIL: `${environment.backEndFnb}/item/load-detail`,
    CREATE: `${environment.backEndFnb}/item/create-data`,
    DELETE: `${environment.backEndFnb}/item/update-active-status`,
    REJECT: `${environment.backEndFnb}/item/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/item/send-approved`,
    APPROVED: `${environment.backEndFnb}/item/approved`,
    UPDATE: `${environment.backEndFnb}/item/update-data`,
    PAGINATION: `${environment.backEndFnb}/item/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/item/create-data-by-excel`,
    LOAD_UNIT: `${environment.backEndFnb}/item/load-unit`,
    LOAD_PRICE_OF_MONTH_BY_ITEM: `${environment.backEndFnb}/item/load-price-of-month-by-item`,
    CALCULATE_AD_PRICE: `${environment.backEndFnb}/item/calculate-ad-price`,
  }

  PROPERTY_TYPE = {
    LOAD_DATA: `${environment.backEndFnb}/property-type/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/property-type/load-detail`,
    CREATE: `${environment.backEndFnb}/property-type/create-data`,
    DELETE: `${environment.backEndFnb}/property-type/update-active-status`,
    REJECT: `${environment.backEndFnb}/property-type/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/property-type/send-approved`,
    APPROVED: `${environment.backEndFnb}/property-type/approved`,
    UPDATE: `${environment.backEndFnb}/property-type/update-data`,
    PAGINATION: `${environment.backEndFnb}/property-type/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/property-type/create-data-by-excel`,
  }

  EQUIPMENT_TYPE = {
    LOAD_DATA: `${environment.backEndFnb}/equipment-type/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/equipment-type/load-detail`,
    CREATE: `${environment.backEndFnb}/equipment-type/create-data`,
    DELETE: `${environment.backEndFnb}/equipment-type/update-active-status`,
    REJECT: `${environment.backEndFnb}/equipment-type/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/equipment-type/send-approved`,
    APPROVED: `${environment.backEndFnb}/equipment-type/approved`,
    UPDATE: `${environment.backEndFnb}/equipment-type/update-data`,
    PAGINATION: `${environment.backEndFnb}/equipment-type/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/equipment-type/create-data-by-excel`,
  }

  PROPERTY = {
    LOAD_DATA: `${environment.backEndFnb}/property/load-data-select`,
    FIND: `${environment.backEndFnb}/property/find`,
    LOAD_DETAIL: `${environment.backEndFnb}/property/load-detail`,
    CREATE: `${environment.backEndFnb}/property/create-data`,
    DELETE: `${environment.backEndFnb}/property/update-active-status`,
    REJECT: `${environment.backEndFnb}/property/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/property/send-approved`,
    APPROVED: `${environment.backEndFnb}/property/approved`,
    UPDATE: `${environment.backEndFnb}/property/update-data`,
    PAGINATION: `${environment.backEndFnb}/property/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/property/create-data-by-excel`,
    LOAD_UNIT: `${environment.backEndFnb}/property/load-unit`,
  }

  EQUIPMENT = {
    LOAD_DATA: `${environment.backEndFnb}/equipment/load-data-select`,
    FIND: `${environment.backEndFnb}/equipment/find`,
    LOAD_DETAIL: `${environment.backEndFnb}/equipment/load-detail`,
    CREATE: `${environment.backEndFnb}/equipment/create-data`,
    DELETE: `${environment.backEndFnb}/equipment/update-active-status`,
    REJECT: `${environment.backEndFnb}/equipment/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/equipment/send-approved`,
    APPROVED: `${environment.backEndFnb}/equipment/approved`,
    UPDATE: `${environment.backEndFnb}/equipment/update-data`,
    PAGINATION: `${environment.backEndFnb}/equipment/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/equipment/create-data-by-excel`,
    LOAD_UNIT: `${environment.backEndFnb}/equipment/load-unit`,
  }

  MATERIAL_TYPE = {
    LOAD_DATA: `${environment.backEndFnb}/material-type/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/material-type/load-detail`,
    CREATE: `${environment.backEndFnb}/material-type/create-data`,
    DELETE: `${environment.backEndFnb}/material-type/update-active-status`,
    REJECT: `${environment.backEndFnb}/material-type/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/material-type/send-approved`,
    APPROVED: `${environment.backEndFnb}/material-type/approved`,
    UPDATE: `${environment.backEndFnb}/material-type/update-data`,
    PAGINATION: `${environment.backEndFnb}/material-type/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/material-type/create-data-by-excel`,
  }

  MATERIAL = {
    LOAD_DATA: `${environment.backEndFnb}/material/load-data-select`,
    FIND: `${environment.backEndFnb}/material/find`,
    LOAD_DETAIL: `${environment.backEndFnb}/material/load-detail`,
    CREATE: `${environment.backEndFnb}/material/create-data`,
    DELETE: `${environment.backEndFnb}/material/update-active-status`,
    REJECT: `${environment.backEndFnb}/material/reject`,
    SEND_APPROVED: `${environment.backEndFnb}/material/send-approved`,
    APPROVED: `${environment.backEndFnb}/material/approved`,
    UPDATE: `${environment.backEndFnb}/material/update-data`,
    PAGINATION: `${environment.backEndFnb}/material/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/material/create-data-by-excel`,
    LOAD_UNIT: `${environment.backEndFnb}/material/load-unit`,
  }

  UNIT = {
    LOAD_DATA: `${environment.backEndFnb}/unit/load-data`,
    FIND: `${environment.backEndFnb}/unit/find`,
    CREATE: `${environment.backEndFnb}/unit/create-data`,
    DELETE: `${environment.backEndFnb}/unit/update-active-status`,
    UPDATE: `${environment.backEndFnb}/unit/update-data`,
    PAGINATION: `${environment.backEndFnb}/unit/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/unit/create-data-by-excel`,
    LOAD_HISTORY: `${environment.backEndFnb}/unit/load-history`,
    LOAD_DETAIL: `${environment.backEndFnb}/unit/load-detail`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/unit/create-data-by-excel`,
  }

  DEPARTMENT = {
    FIND: `${environment.backEndFnb}/department/find`,
    FIND_ALL: `${environment.backEndFnb}/department/find-all`,
    LOAD_DATA: `${environment.backEndFnb}/department/load-data`,
    LOAD_HISTORIES: `${environment.backEndFnb}/department/load-histories`,
    FIND_ONE: `${environment.backEndFnb}/department/find-one`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/department/load-data-select`,
    LOAD_DATA_BY_COMPANY: `${environment.backEndFnb}/department/load-data-by-company`,
    LOAD_DATA_BY_BRANCH: `${environment.backEndFnb}/department/load-data-by-branch`,
    LOAD_DATA_BY_COMPANY_LIST: `${environment.backEndFnb}/department/load-data-select-company-list`,
    LOAD_DATA_SELECT_BY_COMPANY_LIST: `${environment.backEndFnb}/department/load-data-select-by-company-list`,
    CREATE: `${environment.backEndFnb}/department/create-data`,
    DELETE: `${environment.backEndFnb}/department/update-active-status`,
    UPDATE: `${environment.backEndFnb}/department/update-data`,
    PAGINATION: `${environment.backEndFnb}/department/pagination`,
  }

  PART = {
    FIND: `${environment.backEndFnb}/part/find`,
    FIND_ONE: `${environment.backEndFnb}/part/find-one`,
    UPDATE_ACTIVE_STATUS: `${environment.backEndFnb}/part/update-active-status`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/part/load-data-select`,
    LOAD_DATA_SELECT_BY_DEPARTMENT: `${environment.backEndFnb}/part/load-data-select-by-deparment`,
    LOAD_DATA_SELECT_BY_WAREHOUSE: `${environment.backEndFnb}/part/load-data-select-by-warehouse`,
    FIND_DETAIL: `${environment.backEndFnb}/part/load-detail`,
    PAGINATION: `${environment.backEndFnb}/part/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/part/load-history`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/part/create-data-by-excel`,
    UPDATE_PART_TO_PART_PO: `${environment.backEndFnb}/part/update-part-to-part-po`,
    LOAD_DATA: `${environment.backEndFnb}/part/load-data`,
    UPDATE_DATA: `${environment.backEndFnb}/part/update-data`,
  }

  PART_MASTER = {
    FIND_DETAIL: `${environment.backEndFnb}/part-master/load-detail`,
    PAGINATION: `${environment.backEndFnb}/part-master/pagination`,
    CREATE_DATA: `${environment.backEndFnb}/part-master/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/part-master/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/part-master/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/part-master/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/part-master/load-data`,
  }

  CITY = {
    LOAD_DATA: `${environment.backEndFnb}/city/load-data`,
    PAGINATION: `${environment.backEndFnb}/city/pagination`,
    CREATE: `${environment.backEndFnb}/city/create-data`,
    UPDATE: `${environment.backEndFnb}/city/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/city/update-active`,
    CREATE_DATA_EXCEL: `${environment.backEndFnb}/city/create-data-excel`,
  }

  DISTRICT = {
    LOAD_DISTRICT_BY_CITY: `${environment.backEndFnb}/district/load-district-by-city`,
    LOAD_DISTRICT_BY_CITYID: `${environment.backEndFnb}/district/load-district-by-city`,
    LOAD_DATA: `${environment.backEndFnb}/district/load-data`,
    PAGINATION: `${environment.backEndFnb}/district/pagination`,
    CREATE: `${environment.backEndFnb}/district/create-data`,
    UPDATE: `${environment.backEndFnb}/district/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/district/update-active`,
    CREATE_DATE_EXCEL: `${environment.backEndFnb}/district/create-data-excel`,
  }

  POSITION = {
    LOAD_DATA: `${environment.backEndFnb}/position/load-data`,
    FIND: `${environment.backEndFnb}/position/find`,
    FIND_ALL: `${environment.backEndFnb}/position/find-all`,
    FIND_ONE: `${environment.backEndFnb}/position/find-one`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/position/load-data-select`,
    LOAD_SELECT_BY_COMPANY: `${environment.backEndFnb}/position/load-select-by-company`,
    LOAD_SELECT_BY_COMPANIES: `${environment.backEndFnb}/position/load-select-by-companies`,
    DELETE: `${environment.backEndFnb}/position/update-active-status`,
    UPDATE: `${environment.backEndFnb}/position/update`,
    PAGINATION: `${environment.backEndFnb}/position/pagination`,
    LOAD_DATA_BY_DEPARTMENT: `${environment.backEndFnb}/position/load-data-select-by-department`,
    LOAD_DATA_BY_PART: `${environment.backEndFnb}/position/load-data-by-part`,
    LOAD_DATA_SELECT_HAVE_TIME_KEEPING: `${environment.backEndFnb}/position/load-data-select-have-time-keeping`,
    LOAD_DATA_BY_PART_AND_BRANCH: `${environment.backEndFnb}/position/load-data-by-part-and-branch`,
    LOAD_DATA_BY_BRANCH: `${environment.backEndFnb}/position/load-data-by-branch`,
    LOAD_DATA_BY_BRANCH_AND_PART: `${environment.backEndFnb}/position/load-data-by-branch-and-part`,
    LOAD_DATA_SELECT_BY_DEPART_LIST: `${environment.backEndFnb}/position/load-data-select-by-depart-list`,
    LOAD_HISTORY: `${environment.backEndFnb}/position/load-history`,
    LOAD_DETAIL: `${environment.backEndFnb}/position/load-detail`,
  }

  POSITION_MASTER = {
    LOAD_DATA: `${environment.backEndFnb}/position-master/load-data`,
    DELETE: `${environment.backEndFnb}/position-master/update-active-status`,
    UPDATE: `${environment.backEndFnb}/position-master/update`,
    PAGINATION: `${environment.backEndFnb}/position-master/pagination`,
    LOAD_HISTORY: `${environment.backEndFnb}/position-master/load-history`,
    LOAD_DETAIL: `${environment.backEndFnb}/position-master/load-detail`,
    LOAD_DATA_SELECT_BY_PART_MASTER: `${environment.backEndFnb}/position-master/load-data-select-by-part-master`,
    FIND_LIST_PART_BY_POSITION_MASTER: `${environment.backEndFnb}/position-master/find-list-parts-by-postion-master`,
  }

  EMPLOYEE = {
    LOAD_DATA: `${environment.backEndFnb}/employee/load-data`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/employee/load-data-select`,
    LOAD_DATA_SELECT_BY_DEPARTMENT_LIST: `${environment.backEndFnb}/employee/load-data-select-by-department-list`,
    LOAD_DATA_BY_COMPANY: `${environment.backEndFnb}/employee/load-data-select-by-company`,
    LOAD_DATA_BY_BRANCH: `${environment.backEndFnb}/employee/load-data-select-by-branch`,
    LOAD_DATA_BY_POSITION: `${environment.backEndFnb}/employee/load-data-by-position`,
    LOAD_DATA_BY_POSITION_AND_BRANCH: `${environment.backEndFnb}/employee/load-data-by-position-and-branch`,
    LOAD_DATA_SELECT_BY_POSITION: `${environment.backEndFnb}/employee/load-data-select-by-position`,
    FIND_ONE: `${environment.backEndFnb}/employee/find-one`,
    UPDATE_WITH_MUTIPLE_COMPANY: `${environment.backEndFnb}/employee/update-data-with-multiple-company`,
    UPDATE: `${environment.backEndFnb}/employee/update-data`,
    DELETE: `${environment.backEndFnb}/employee/update-active`,
    FIRE: `${environment.backEndFnb}/employee/fire`,
    LOAD_DATA_SELECT_BY_COMPANY_LIST: `${environment.backEndFnb}/employee/load-data-select-by-company-list`,
    FIND_BY_DEPARTMENT: `${environment.backEndFnb}/employee/load-data-by-department`,
    CHANGE_PASSWORD_EMPLOYEE: `${environment.backEndFnb}/employee/update-password`,
    PAGINATION: `${environment.backEndFnb}/employee/pagination`,
    WORK_REPORT_PAGINATION: `${environment.backEndFnb}/employee/work-report-pagination`,
    LOAD_DATA_BY_DEPARTMENT: `${environment.backEndFnb}/employee/load-data-select-by-department`,
    LOAD_DATA_BY_DEPARTMENT_MULTI: `${environment.backEndFnb}/employee/load-data-select-by-department-multi`,
    GET_LIST_EMPLOYEES: `${environment.backEndFnb}/employee/get-list-employees`,
    LOAD_HISTORY: `${environment.backEndFnb}/employee/load-history`,
    LOAD_DATA_SELECT_ALL: `${environment.backEndFnb}/employee/load-data-select-all`,
    LOAD_DATA_BY_APART_AND_BRANCH: `${environment.backEndFnb}/employee/load-data-by-part-and-branch`,
    FIND_SALARY_HISTORY: `${environment.backEndFnb}/employee/find-salary-history`,
    REMOVE: `${environment.backEndFnb}/employee/remove`,
    ACTIVATE: `${environment.backEndFnb}/employee/activate`,
  }

  PRODUCT_CATEGORY = {
    FIND: `${environment.backEndFnb}/product-category/find`,
    FIND_ONE: `${environment.backEndFnb}/product-category/find-one`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/product-category/load-data-select`,
    FIND_DETAIL: `${environment.backEndFnb}/product-category/load-detail`,
    PAGINATION: `${environment.backEndFnb}/product-category/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/product-category/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/product-category/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/product-category/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/product-category/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/product-category/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/product-category/load-data`,
  }

  PRODUCT_INDUSTRY = {
    FIND: `${environment.backEndFnb}/product-industry/find`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/product-industry/load-data-select`,
    LOAD_DATA_BY_PRODUCT_CATEGORY: `${environment.backEndFnb}/product-industry/load-data-by-product-category`,
    FIND_DETAIL: `${environment.backEndFnb}/product-industry/load-detail`,
    PAGINATION: `${environment.backEndFnb}/product-industry/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/product-industry/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/product-industry/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/product-industry/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/product-industry/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/product-industry/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/product-industry/load-data`,
    DELETE: `${environment.backEndFnb}/product-industry/update-active-status`,
  }

  PRODUCT_BRAND = {
    FIND: `${environment.backEndFnb}/product-brand/find`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/product-brand/load-data-select`,
    FIND_DETAIL: `${environment.backEndFnb}/product-brand/load-detail`,
    PAGINATION: `${environment.backEndFnb}/product-brand/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/product-brand/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/product-brand/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/product-brand/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/product-brand/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/product-brand/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/product-brand/load-data`,
    DELETE: `${environment.backEndFnb}/product-brand/update-active-status`,
  }

  PRODUCT_GROUP = {
    FIND: `${environment.backEndFnb}/product-group/find`,
    LOAD_DATA: `${environment.backEndFnb}/product-group/load-data`,
    FIND_DETAIL: `${environment.backEndFnb}/product-group/load-detail`,
    PAGINATION: `${environment.backEndFnb}/product-group/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/product-group/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/product-group/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/product-group/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/product-group/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/product-group/create-data-by-excel`,
    LOAD_DATA_BY_PRODUCT_INDUSTRY: `${environment.backEndFnb}/product-group/load-data-by-product-industry`,
    LOAD_PAGINATION_BY_PRODUCT_INDUSTRY: `${environment.backEndFnb}/product-group/load-pagination-by-product-industry`,
  }

  PRODUCT_PRICE = {
    CREATE: `${environment.backEndFnb}/product-price/create`,
    PAGINATION: `${environment.backEndFnb}/product-price/pagination`,
    APPROVE: `${environment.backEndFnb}/product-price/approve`,
  }

  SUPPLIER = {
    FIND: `${environment.backEndFnb}/supplier/find`,
    LOAD_DATA: `${environment.backEndFnb}/supplier/load-data`,
    FIND_DETAIL: `${environment.backEndFnb}/supplier/load-detail`,
    PAGINATION: `${environment.backEndFnb}/supplier/pagination`,
    SUPPLIER_PRODUCT_PAGINATION: `${environment.backEndFnb}/supplier/supplier-product-pagination`,
    CREATE_DATA: `${environment.backEndFnb}/supplier/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/supplier/update-data`,
    UPDATE_STATUS: `${environment.backEndFnb}/supplier/update-status`,
    UPDATE_LIST_SELECT_SUPPLIER_PRODUCT_STATUS: `${environment.backEndFnb}/supplier/update-list-select-supplier-product-status`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/supplier/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/supplier/create-data-by-excel`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/supplier/load-data-select`,
  }

  SUPPLIER_PRODUCT = {
    FIND: `${environment.backEndFnb}/supplier-product/find`,
    LOAD_DATA: `${environment.backEndFnb}/supplier-product/load-data`,
    FIND_DETAIL: `${environment.backEndFnb}/supplier-product/load-detail`,
    PAGINATION: `${environment.backEndFnb}/supplier-product/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/supplier-product/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/supplier-product/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/supplier-product/update-data`,
    UPDATE_LIST_DATA: `${environment.backEndFnb}/supplier-product/update-list-data`,
    UPDATE_STATUS: `${environment.backEndFnb}/supplier-product/update-status`,
    UPDATE_LIST_STATUS: `${environment.backEndFnb}/supplier-product/update-list-status`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/supplier-product/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/supplier-product/create-data-by-excel`,
    LOAD_PRODUCT_BY_SUPPLIER: `${environment.backEndFnb}/supplier-product/load-product-by-supplier`,
    CHECK_EXISTING_PRODUCT: `${environment.backEndFnb}/supplier-product/check-existing-product`,
    DELETE: `${environment.backEndFnb}/supplier-product/delete`,
    CREATE_LST_SUPPLIER: `${environment.backEndFnb}/supplier-product/create-lst-supplier`,
    CREATE_BY_EXCEL: `${environment.backEndFnb}/supplier-product/create-by-excel`,
    LIST_SUPPLIER_PRODUCT_WITH_PRICE: `${environment.backEndFnb}/supplier-product/list-supplier-product-with-price`,
  }

  SUPPLIER_BRANCH = {
    PAGINATION: `${environment.backEndFnb}/supplier-branch/pagination`,
    UPDATE_DATA: `${environment.backEndFnb}/supplier-branch/update-data`,
    UPDATE_SUPPLIER_FOR_BRANCH: `${environment.backEndFnb}/supplier-branch/update-supplier-for-branch`,
  }

  REASON_TYPE = {
    FIND: `${environment.backEndFnb}/reason-type/find`,
    FIND_ONE: `${environment.backEndFnb}/reason-type/find-one`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/reason-type/load-data-select`,
    FIND_DETAIL: `${environment.backEndFnb}/reason-type/load-detail`,
    PAGINATION: `${environment.backEndFnb}/reason-type/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/reason-type/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/reason-type/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/reason-type/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/reason-type/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/reason-type/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/reason-type/load-data`,
  }

  REASON = {
    FIND: `${environment.backEndFnb}/reason/find`,
    FIND_ONE: `${environment.backEndFnb}/reason/find-one`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/reason/load-data-select`,
    FIND_DETAIL: `${environment.backEndFnb}/reason/load-detail`,
    PAGINATION: `${environment.backEndFnb}/reason/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/reason/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/reason/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/reason/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/reason/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/reason/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/reason/load-data`,
  }

  PAYSLIP = {
    FIND: `${environment.backEndFnb}/payslip/find`,
    FIND_ONE: `${environment.backEndFnb}/payslip/find-one`,
    LOAD_DATA_SELECT_LEVEL_ONE: `${environment.backEndFnb}/payslip/load-data-select-level-one`,
    LOAD_DATA_SELECT_LEVEL_TWO: `${environment.backEndFnb}/payslip/load-data-select-level-two`,
    LOAD_DATA_SELECT_LEVEL_THREE: `${environment.backEndFnb}/payslip/load-data-select-level-three`,
    FIND_DETAIL: `${environment.backEndFnb}/payslip/load-detail`,
    PAGINATION: `${environment.backEndFnb}/payslip/pagination`,
    HISTORY_PAGINATION: `${environment.backEndFnb}/payslip/load-history`,
    CREATE_DATA: `${environment.backEndFnb}/payslip/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/payslip/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/payslip/update-active-status`,
    CREATE_DATA_BY_EXCEL: `${environment.backEndFnb}/payslip/create-data-by-excel`,
    LOAD_DATA: `${environment.backEndFnb}/payslip/load-data`,
  }

  SETTING_STRING = {
    FIND: `${environment.backEndFnb}/setting-string/find`,
    LOAD_DATA: `${environment.backEndFnb}/setting-string/load`,
    PAGINATION: `${environment.backEndFnb}/setting-string/pagination`,
    UPDATE: `${environment.backEndFnb}/setting-string/update`,
    UPDATE_ITEMS: `${environment.backEndFnb}/setting-string/update-items`,
    DELETE: `${environment.backEndFnb}/setting-string/update-active-status`,
  }

  WAREHOUSE_PRODUCT = {
    CHECK_EXISTING_PRODUCT: `${environment.backEndFnb}/warehouse-product/check-existing-product`,
    LOAD_DATA_CONITION: `${environment.backEndFnb}/warehouse-product/load-data-condition`,
    PAGINATION: `${environment.backEndFnb}/warehouse-product/pagination`,
    CREATE_DATA: `${environment.backEndFnb}/warehouse-product/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/warehouse-product/update-data`,
    UPDATE_LIST_DATA: `${environment.backEndFnb}/warehouse-product/update-list-data`,
    DELETE: `${environment.backEndFnb}/warehouse-product/delete-data`,
  }

  /**
   * Phân quyền - Nhóm quyền
   */
  PERMISSION_GROUP = {
    LOAD_DETAIL_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/load-permission-group-by-id`,
    LOAD_PERMISSION_GROUP_BY_ID: `${environment.backEndFnb}/permission-group/load-permission-group-by-id`,
    PAGINATION_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/pagination-permission-group`,
    CREATE_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/create-permission-group`,
    UPDATE_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/update-permission-group`,
    UPDATE_ACTIVE_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/update-active-permission-group`,
    IMPORT_EXCEL_GROUP_PERMISSION: `${environment.backEndFnb}/permission-group/import-excel-group-permission`,
    LOAD_LIST_EMPLOYEE_OF_PERMISSION_GROUP_ID: `${environment.backEndFnb}/permission-group/load-list-employee-of-permission-group-id`,
    LOAD_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/load-permission-group`,
    LOAD_DATA_SELECT_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/load-data-select-permission-group`,
    LOAD_DATA_SELECT_PERMISSION_GROUP_BY_POSITION: `${environment.backEndFnb}/permission-group/load-data-select-permission-group-by-position`,
    UPDATE_DATA_FOR_PERMISSION_GROUP: `${environment.backEndFnb}/permission-group/update-data-for-permission-group`,
    //
    LOAD_DATA: `${environment.backEndFnb}/permission-group/load-data`,
    PAGINATION: `${environment.backEndFnb}/permission-group/pagination`,
    LOAD_PERMISSION_OF_EMPLOYEE: `${environment.backEndFnb}/permission-group/load-permission-of-employee`,
    LOAD_PERMISSION_MOBILE_OF_EMPLOYEE: `${environment.backEndFnb}/permission-group/load-permission-mobile-of-employee`,
    LOAD_PERMISSION_MOBILE_OF_EMPLOYEE_DEFAULT_MODE: `${environment.backEndFnb}/permission-group/load-permission-mobile-of-employee-default-mode`,
    LOAD_PERMISSION_OF_EMPLOYEE_ALL_COMPANY: `${environment.backEndFnb}/permission-group/load-permission-of-employee-all-company`,
    LOAD_PERMISSION_OF_MOBILE_EMPLOYEE_ALL_COMPANY: `${environment.backEndFnb}/permission-group/load-permission-mobile-of-employee-all-company`,
    CREATE: `${environment.backEndFnb}/permission-group/create`,
    UPDATE: `${environment.backEndFnb}/permission-group/update`,
    UPDATE_PERMISSION_MOBILE: `${environment.backEndFnb}/permission-group/update-permission-mobile`,
    UPDATE_PERMISSION_MOBILE_DEFAULT_MODE: `${environment.backEndFnb}/permission-group/update-permission-mobile-default-mode`,
    UPDATE_PERMISSION_FOR_ALL_COMPANY: `${environment.backEndFnb}/permission-group/update-permission-for-all-company`,
    UPDATE_PERMISSION_MOBILE_FOR_ALL_COMPANY: `${environment.backEndFnb}/permission-group/update-permission-mobile-for-all-company`,
  }

  PROMOTION = {
    PAGINATION: `${environment.backEndFnb}/promotion/pagination`,
    CREATE_DATA: `${environment.backEndFnb}/promotion/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/promotion/update-data`,
    LOAD_DATA: `${environment.backEndFnb}/promotion/load-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/promotion/update-active`,
    UPDATE_DISABLE: `${environment.backEndFnb}/promotion/update-disable`,
  }

  PO = {
    PAGINATION: `${environment.backEndFnb}/po/pagination`,
    CREATE_DATA: `${environment.backEndFnb}/po/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/po/update-data`,
    FIND_DETAIL: `${environment.backEndFnb}/po/find-detail`,
    FIND_DETAIL_FOR_SUPPLIER: `${environment.backEndFnb}/po/find-detail-for-supplier`,
    SUPPLIER_SEEN_PO: `${environment.backEndFnb}/po/supplier-seen-po`,
    GROUP_ORDER_BY_PRODUCT: `${environment.backEndFnb}/po/group-order-by-product`,
    COUNT_DATA_VIEW: `${environment.backEndFnb}/po/count-data-for-view-po`,
    CANCEL: `${environment.backEndFnb}/po/cancel`,
    SEND: `${environment.backEndFnb}/po/send`,
    FIND_ORDER_HAVE_NOT_APPROVED: `${environment.backEndFnb}/po/find-order-have-not-approved`,
    FIND_ORDER_HAVE_NOT_CREATE_PO: `${environment.backEndFnb}/po/find-order-have-not-create-po`,
    FIND_BRANCH_HAVE_NOT_ORDER: `${environment.backEndFnb}/po/find-branch-have-not-order`,
  }

  ORDER = {
    PAGINATION: `${environment.backEndFnb}/order/pagination`,
    PAGINATION_FOR_ORDER_INTERNAL: `${environment.backEndFnb}/order/pagination-order-internal`,
    LOAD_GROUP_ORDER_INTERNAL_DETAIL: `${environment.backEndFnb}/order/load-group-order-internal-detail`,
    PAGINATION_GEN_PO: `${environment.backEndFnb}/order/pagination-gen-po`,
    PAGINATION_ORDER_INTERNAL: `${environment.backEndFnb}/order/pagination-order-internal`,
    LOAD_ITEM_UNDER_MIN: `${environment.backEndFnb}/order/item/list-under-min`,
    LOAD_ITEM_ORDER: `${environment.backEndFnb}/order/item/list-to-order`,
    LOAD_PROPERTY_ORDER: `${environment.backEndFnb}/order/property/list-to-order`,
    LOAD_MATERIAL_ORDER: `${environment.backEndFnb}/order/material/list-to-order`,
    LOAD_EQUIPMENT_ORDER: `${environment.backEndFnb}/order/equipment/list-to-order`,
    ITEM_CREATE_ORDER: `${environment.backEndFnb}/order/item/order-create`,
    MATERIAL_CREATE_ORDER: `${environment.backEndFnb}/order/material/order-create`,
    PROPERTY_CREATE_ORDER: `${environment.backEndFnb}/order/property/order-create`,
    EQUIPMENT_CREATE_ORDER: `${environment.backEndFnb}/order/equipment/order-create`,
    SEND_APPROVE: `${environment.backEndFnb}/order/send-approve`,
    CANCEL_ORDER: `${environment.backEndFnb}/order/cancel-order`,
    REJECT_ORDER: `${environment.backEndFnb}/order/reject-order`,
    APPROVE_ORDER: `${environment.backEndFnb}/order/approve-order`,
    UPDATE_ORDER: `${environment.backEndFnb}/order/order-update`,
    ORDER_DETAIL: `${environment.backEndFnb}/order/detail`,
    CREATE_GROUP_ORDER_INTERNAL: `${environment.backEndFnb}/order/create-group-order-internal`,
    OUTBOUND_CREATE_SUMMARY_FROM_ORDERS: `${environment.backEndFnb}/order/outbound-summary-create-from-orders`,
    CREATE_ORDER_BY_MANUFACTURE_NOT_ENOUGH_INVENTORY: `${environment.backEndFnb}/order/create-order-by-manufacture-not-enough-inventory`,
    CREATE_ORDER_BY_PLAN: `${environment.backEndFnb}/order/create-by-plan`,
  }

  CONFIG_CHECK_INVENTORY = {
    PAGINATION: `${environment.backEndFnb}/config-check-inventory/pagination`,
    CREATE_DATA: `${environment.backEndFnb}/config-check-inventory/create-data`,
    UPDATE_DATA: `${environment.backEndFnb}/config-check-inventory/update-data`,
    LOAD_DATA: `${environment.backEndFnb}/config-check-inventory/load-data`,
    LOAD_DETAIL: `${environment.backEndFnb}/config-check-inventory/load-detail`,
    LOAD_HISTORY: `${environment.backEndFnb}/config-check-inventory/load-history`,
  }

  ROLE = {
    DETAIL: `${environment.backEndFnb}/role/find-detail`,
    FIND: `${environment.backEndFnb}/role/find`,
    CREATE: `${environment.backEndFnb}/role/create-data`,
    DELETE: `${environment.backEndFnb}/role/update-active`,
    UPDATE: `${environment.backEndFnb}/role/update-data`,
    PAGINATION: `${environment.backEndFnb}/role/pagination`,
    IMPORT_EXCEL: `${environment.backEndFnb}/role/create-data-by-excel`,
    DATA_SELECT: `${environment.backEndFnb}/role/load-data-select-box`,
    LOAD_PERMISSION: `${environment.backEndFnb}/role/load-permission`,
    SAVE_PERMISSION: `${environment.backEndFnb}/role/save-permission`,
    LOAD_PERMISSION_USER: `${environment.backEndFnb}/role/load-permission-user`,
    IMPORT: `${environment.backEndFnb}/role/import-data`,
  }

  ACTION_LOG = {
    PAGINATION: `${environment.backEndFnb}/action-log/pagination`,
  }
  BRANCH_PRODUCT = {
    //  thêm nguyên vật liệu vào chi nhánh
    CREATE: `${environment.backEndFnb}/branch-product/create`,
    UPDATE: `${environment.backEndFnb}/branch-product/update`,
    UPDATE_BRANCH_FOR_PRODUCT: `${environment.backEndFnb}/branch-product/update-branch-for-product`,
    PAGINATION: `${environment.backEndFnb}/branch-product/pagination`,
    PAGINATION_BY_PRODUCT: `${environment.backEndFnb}/branch-product/pagination-by-product`,
    UPDATE_PART_MASTER_OF_BRANCH_ITEM: `${environment.backEndFnb}/branch-product/update-part-master-of-branch-item`,
    CLONE: `${environment.backEndFnb}/branch-product/clone`,
    UPDATE_ALL: `${environment.backEndFnb}/branch-product/update-all`,
    STOCK_PAGINATION: `${environment.backEndFnb}/branch-product/stock-pagination`,
    DETAIL_STOCK: `${environment.backEndFnb}/branch-product/detail-stock`,
    DEMAND_LOAD_DETAIL: `${environment.backEndFnb}/branch-product/demand-load-detail`,
  }
  BRAND_PRODUCT = {
    //  thêm nguyên vật liệu vào thương hiệu
    CREATE: `${environment.backEndFnb}/brand-product/create`,
    UPDATE_DATA: `${environment.backEndFnb}/brand-product/update-data`,

    PAGINATION: `${environment.backEndFnb}/brand-product/pagination`,
    PAGINATION_BRAND_FOR_PRO: `${environment.backEndFnb}/brand-product/pagination-brand-for-pro`,
    APPLY_STANDARD_FOR_ALL_BRANCH: `${environment.backEndFnb}/brand-product/apply-standard-for-all-branch`,
  }
  STANDARD_MATERIAL = {
    PAGINATION: `${environment.backEndFnb}/standard-material/pagination`,
    UPDATE: `${environment.backEndFnb}/standard-material/update`,
    CREATE: `${environment.backEndFnb}/standard-material/create`,
    UPDATE_ALL: `${environment.backEndFnb}/standard-material/update-all`,
  }

  PLAN_TYPE = {
    PAGINATION: `${environment.backEndFnb}/plan-type/pagination`,
    CREATE: `${environment.backEndFnb}/plan-type/create-data`,
    UPDATE: `${environment.backEndFnb}/plan-type/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/plan-type/update-active-status`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/plan-type/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/plan-type/load-detail`,
    IMPORT_EXCEL: `${environment.backEndFnb}/plan-type/import-excel`,
  }

  PLAN = {
    PAGINATION: `${environment.backEndFnb}/plan/pagination`,
    CREATE: `${environment.backEndFnb}/plan/create-data`,
    UPDATE: `${environment.backEndFnb}/plan/update-data`,
    UPDATE_ACTIVE: `${environment.backEndFnb}/plan/update-active-status`,
    LOAD_DATA_SELECT: `${environment.backEndFnb}/plan/load-data-select`,
    LOAD_DETAIL: `${environment.backEndFnb}/plan/load-detail`,
    IMPORT_EXCEL: `${environment.backEndFnb}/plan/import-excel`,
  }

  PLAN_PRODUCT = {
    PAGINATION: `${environment.backEndFnb}/plan-product/pagination`,
    CREATE_PRODUCT: `${environment.backEndFnb}/plan-product/create-product`,
  }

  /** RECIPE  */
  RECIPE = {
    CREATE: `${environment.backEndFnb}/recipe/create`,
    PAGINATION: `${environment.backEndFnb}/recipe/pagination`,
    UPDATE_STATUS: `${environment.backEndFnb}/recipe/update-status`,
    FIND_DETAIL: `${environment.backEndFnb}/recipe/find-detail`,
    UPDATE: `${environment.backEndFnb}/recipe/update`,
    LOAD_DATA: `${environment.backEndFnb}/recipe/load-data-select`,
    SEND_APPROVE: `${environment.backEndFnb}/recipe/send-approve`,
    APPROVE: `${environment.backEndFnb}/recipe/approve`,
    REJECT: `${environment.backEndFnb}/recipe/reject`,
    ADD_ITEM_TO_RECIPE: `${environment.backEndFnb}/recipe/add-item`,
    LOAD_SUMMARY: `${environment.backEndFnb}/recipe/load-summary`,
    LOAD_ITEM_CODE_NEEDED_CREATE: `${environment.backEndFnb}/recipe/load-item-code-need-create`,
  }

  /** PREPARATION  */
  PREPARATION = {
    CREATE: `${environment.backEndFnb}/preparation/create`,
    PAGINATION: `${environment.backEndFnb}/preparation/pagination`,
    UPDATE: `${environment.backEndFnb}/preparation/update`,
    FIND_DETAIL: `${environment.backEndFnb}/preparation/detail`,
    LOAD_PREPARATION: `${environment.backEndFnb}/preparation/load-preparation-by-branch-product`,
    PREPARATION_PROCESS: `${environment.backEndFnb}/preparation/preparation-process`,
    PREPARATION: `${environment.backEndFnb}/preparation/preparation`,
  }

  /** OUTBOUND  */
  OUTBOUND = {
    APPROVE: `${environment.backEndFnb}/outbound/approve`,
    PAGINATION: `${environment.backEndFnb}/outbound/pagination`,
    DETAIL: `${environment.backEndFnb}/outbound/detail`,
  }

  /** DELIVERY_NOTE  */
  DELIVERY_NOTE = {
    PAGINATION: `${environment.backEndFnb}/delivery-note/pagination`,
    DETAIL: `${environment.backEndFnb}/delivery-note/detail`,
    REJECT: `${environment.backEndFnb}/delivery-note/reject`,
    IMPORT: `${environment.backEndFnb}/delivery-note/receive-and-import`,
    RECEIVE: `${environment.backEndFnb}/delivery-note/receive`,
  }

  /** MANUFACTURING PLAN  */
  MANUFACTURING = {
    CREATE_PLAN: `${environment.backEndFnb}/manufacturing/create-plan`,
    PAGINATION_PLAN: `${environment.backEndFnb}/manufacturing/pagination-plan`,
    PLAN_DETAIL: `${environment.backEndFnb}/manufacturing/plan-detail`,
    LOAD_NVL_NEEDED: `${environment.backEndFnb}/manufacturing/load-item-needed`,
    EXPORT_TO_MANUFACTURING: `${environment.backEndFnb}/manufacturing/export-to-manufacturing`,
    PROCESS_LINE_PAGINATION: `${environment.backEndFnb}/manufacturing/process-line-pagination`,
    PROCESS_LINE_DETAIL: `${environment.backEndFnb}/manufacturing/process-line-detail`,
    IMPORT_PRODUCT: `${environment.backEndFnb}/manufacturing/import-product`,
  }
  /** INBOUND */
  INBOUND = {
    PAGINATION: `${environment.backEndFnb}/inbound/pagination`,
    DETAIL: `${environment.backEndFnb}/inbound/detail`,
    IMPORT: `${environment.backEndFnb}/inbound/import`,
  }

  /** End API END_POINT */

  //#endregion

  //#region Handle

  objToQueryString = (obj: any) =>
    Object.keys(obj)
      .map((k) => {
        if (Array.isArray(obj[k])) {
          return `${k}=${JSON.stringify(obj[k])}`
        }
        return `${k}=${obj[k]}`
      })
      .join('&')

  post(url: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const headers = new HttpHeaders({
        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI0NTAzZTg4NS0xMWRiLTRkY2EtOGE1NS0yOTJmNWY4MTY2ZWIiLCJpYXQiOjE3NTEwMTQ1NzQsImV4cCI6ODA2MjUzNDU3NH0.EatqpFIdi2AWyR14N0RtGs0_0wUz6adKbQO_X0mLNzU`,
      })
      const request = this.http.post(url, data)
      lastValueFrom(request)
        .then((res: any) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }

  get(url: string, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${url}?${query}`

    return new Promise((resolve, reject) => {
      const request = this.http.get(newUrl)
      lastValueFrom(request)
        .then((res: any) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err.response)
        })
    })
  }
}
