import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../services'
import { first } from 'rxjs/operators'
import { FormGroup, FormBuilder, Validators } from '@angular/forms'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup
  returnUrl!: string
  submitted = false
  logo: string = './../assets/img/logo_login.png'
  language_key: any
  subscriptions: Subscription = new Subscription()
  validateForm: any = FormGroup
  passwordVisible = false

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private fb: FormBuilder
  ) {
    // redirect to home if already logged in
    if (this.authenticationService.currentUserValue) {
      this.router.navigate(['/'])
    }
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.validateForm = this.fb.group({
      username: [null, [Validators.required]],
      password: [null, [Validators.required]],
      remember: [true],
    })

    // get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/'
  }

  get f() {
    return this.loginForm.controls
  }

  onSubmit() {
    for (const i in this.validateForm.controls) {
      if (this.validateForm.controls[i]) {
        this.validateForm.controls[i].markAsDirty()
        this.validateForm.controls[i].updateValueAndValidity()
      }
    }
    if (!this.validateForm.controls.username.value || !this.validateForm.controls.password.value) return
    this.router.navigate([this.returnUrl])
    this.submitted = true
    this.authenticationService
      .login(this.validateForm.controls.username.value, this.validateForm.controls.password.value)
      .pipe(first())
      .subscribe((data: any) => {
        if (data) {
          this.apiService
            .post(this.apiService.AUTH.LOAD_PERMISSION_USER, { userId: data.userId })
            .then((res) => {
              localStorage.setItem('permission', JSON.stringify(res))
              this.router.navigate([this.returnUrl])
            })
            .catch((err) => {
              this.authenticationService.logout()
              this.router.navigate(['/user/login'])
            })
        }
      })
  }
}
