* {
  margin: 0px;
  // padding: 0px;
  box-sizing: border-box;
}

body,
html {
  height: 100%;
  font-family: Poppins-Regular, sans-serif;
}

/*---------------------------------------------*/
a {
  font-family: Poppins-Regular;
  font-size: 14px;
  line-height: 1.7;
  color: #666666;
  margin: 0px;
  transition: all 0.4s;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
}

a:focus {
  outline: none !important;
}

a:hover {
  text-decoration: none;
  color: #fff;
}

/*---------------------------------------------*/
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0px;
}

p {
  font-family: Poppins-Regular;
  font-size: 14px;
  line-height: 1.7;
  color: #666666;
  margin: 0px;
}

ul,
li {
  margin: 0px;
  list-style-type: none;
}

/*---------------------------------------------*/
input {
  // outline: none;
  // border: none;
}

textarea {
  outline: none;
  border: none;
}

textarea:focus,
input:focus {
  border-color: transparent !important;
}

input:focus::-webkit-input-placeholder {
  color: transparent;
}
input:focus:-moz-placeholder {
  color: transparent;
}
input:focus::-moz-placeholder {
  color: transparent;
}
input:focus:-ms-input-placeholder {
  color: transparent;
}

textarea:focus::-webkit-input-placeholder {
  color: transparent;
}
textarea:focus:-moz-placeholder {
  color: transparent;
}
textarea:focus::-moz-placeholder {
  color: transparent;
}
textarea:focus:-ms-input-placeholder {
  color: transparent;
}

// input::-webkit-input-placeholder {
//   color: #fff;
// }
// input:-moz-placeholder {
//   color: #fff;
// }
// input::-moz-placeholder {
//   color: #fff;
// }
// input:-ms-input-placeholder {
//   color: #fff;
// }

textarea::-webkit-input-placeholder {
  color: #fff;
}
textarea:-moz-placeholder {
  color: #fff;
}
textarea::-moz-placeholder {
  color: #fff;
}
textarea:-ms-input-placeholder {
  color: #fff;
}

label {
  margin: 0;
  display: block;
}

/*---------------------------------------------*/
button {
  outline: none !important;
  border: none;
  background: transparent;
}

button:hover {
  cursor: pointer;
}

/* [ login ] */

.limiter {
  width: 100%;
  margin: 0 auto;
}

.container-login100 {
  width: 100%;
  min-height: 100vh;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  padding: 15px;

  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  z-index: 1;
}
.container-login100::before {
  content: '';
  display: block;
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.5);
}

.wrap-login100 {
  width: 33%;
  border-radius: 30px;
  overflow: hidden;
  padding: 55px 55px 50px 55px;

  // background: -webkit-linear-gradient(top, #7579ff, #b224ef);
  // background: -o-linear-gradient(top, #7579ff, #b224ef);
  // background: -moz-linear-gradient(top, #7579ff, #b224ef);
  // background: linear-gradient(top, #7579ff, #b224ef);
  // margin-bottom: 8rem;
  // height: 80vh;
}

/* [ Form ]*/

.login100-form {
  width: 100%;
  background: white;
  border-radius: 10%;
}

.login100-form-logo {
  font-size: 60px;
  color: #333333;

  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 15vh;
  border-radius: 50%;
  margin: 0 auto;
}

.login100-form-title {
  font-family: 'Inter';
  font-style: normal;
  font-size: 30px;
  color: #fcae18;
  line-height: 1.2;
  text-align: center;
  text-transform: uppercase;

  display: block;
}

/* [ Input ]*/

.wrap-input100 {
  width: auto;
  position: relative;
  border-bottom: 2px solid rgba(255, 255, 255, 0.24);
  margin-bottom: 30px;
}

.input100 {
  font-family: 'Inter';
  font-style: normal;
  font-size: 14px;
  color: black;
  line-height: 1.2;
  display: block;
  background: transparent;
  height: 40px;
  width: 300px;
  left: 0px;
  border-radius: 4px;
  padding: 12px 12px 12px 8px;
  justify-items: space-between;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: left;
}

/*---------------------------------------------*/
// .focus-input100 {
//   position: absolute;
//   display: block;
//   width: 100%;
//   height: 100%;
//   top: 0;
//   left: 0;
//   pointer-events: none;
//   border-bottom: 1px solid #000;
// }

// .focus-input100::before {
//   content: "";
//   display: block;
//   position: absolute;
//   bottom: -2px;
//   left: 0;
//   width: 0;
//   height: 2px;

//   -webkit-transition: all 0.4s;
//   -o-transition: all 0.4s;
//   -moz-transition: all 0.4s;
//   transition: all 0.4s;
//   background: #f6c451;
// }

// .focus-input100::after {
//   font-family: "Gotham-Thin";
//   font-size: 20px;
//   color: black;

//   content: attr(data-placeholder);
//   display: block;
//   width: 100%;
//   position: absolute;
//   top: 6px;
//   left: 0px;
//   padding-left: 5px;

//   -webkit-transition: all 0.4s;
//   -o-transition: all 0.4s;
//   -moz-transition: all 0.4s;
//   transition: all 0.4s;
// }

// .input100:focus {
//   display: block;
//   padding-left: 5px;
// }

// .input100:focus + .focus-input100::after {
//   top: -22px;
//   font-size: 18px;
// }

// .input100:focus + .focus-input100::before {
//   width: 100%;
// }

/* [ Button ]*/
.container-login100-form-btn {
  // width: 100%;
  // display: -webkit-box;
  // display: -webkit-flex;
  // display: -moz-box;
  // display: -ms-flexbox;
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: center;
}

.login100-form-btn {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #f6c451;
  line-height: 1.2;

  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  min-width: 200px;
  height: 41px;

  // background: -webkit-linear-gradient(bottom, #7579ff, #b224ef);
  // background: -o-linear-gradient(bottom, #7579ff, #b224ef);
  // background: -moz-linear-gradient(bottom, #7579ff, #b224ef);
  // background: linear-gradient(bottom, #7579ff, #b224ef);
  position: relative;
  z-index: 1;

  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  transition: all 0.4s;
}

.login100-form-btn::before {
  content: '';
  display: block;
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 1;
  color: white;
  transition: all 0.4s;
}

.login100-form-btn:hover {
  color: #172b4d;
}

.login100-form-btn:hover:before {
  opacity: 0;
}

/* [ Responsive ]*/

@media (max-width: 576px) {
  .wrap-login100 {
    padding: 55px 15px 37px 15px;
  }
}

/*[ PADDING ] */
.p-t-27 {
  padding-top: 27px;
}
.p-b-34 {
  padding-bottom: 34px;
}

/*[ TEXT ] */
.text-white {
  color: white;
}
.text-black {
  color: black;
}

.text-hov-white:hover {
  color: white;
}

/* ------------------------------------ */
.text-up {
  text-transform: uppercase;
}

/* ------------------------------------ */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-middle {
  vertical-align: middle;
}

.logo-dn {
  width: auto;
  height: 20vh;
}
.mr-6rem {
  margin-right: 6rem;
}

.mb-10rem {
  margin-bottom: 10rem;
}

.ant-input-prefix {
  margin-right: 4px;
}

.ant-input-prefix,
.ant-input-suffix {
  display: flex;
  flex: none;
  align-items: center;
}
.login-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px 12px 12px 8px;
  width: 300px;
  height: 45px;
  background: #000000;
  border-radius: 4px;
}

.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input,
.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover {
  background: none;
  border-color: #ff4d4f;
}

//

.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  padding: 20px;
}

.background-login {
  
  background-size: cover;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: -1;
}

.logo {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-size: cover;
  background-position: center;
}
.login-box {
  position: absolute;
  top: 53%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70vw;
  height: auto;
  max-height: 63vh;
  background-color: #ffffff;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
}

.login-content {
  display: flex;
  height: 100%;
}

.login-left {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-logo {
  width: 307px;
  height: auto

}


.background-login {
  max-width: 100%;
  height: auto;
  background-size: cover;
}

.form-login {
  margin-right: 20px;
  // box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  padding: 40px;
  justify-content: center;
  align-items: center;
}
.login-right {
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.input {
  width: 100%;
  height: 50px;
  border-radius: 8px;
}

.login100-form-btn {
  width: 100%;
  background-color: #E7B008;
  color: #000000;
  border: none;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
}

.title-login {
  font-size: 28px;
  font-weight: bold;
  color: #172b4d;
  align-items: center;
  text-align: center;
}

.form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.wrap-login {
  margin-top: 20px
}


