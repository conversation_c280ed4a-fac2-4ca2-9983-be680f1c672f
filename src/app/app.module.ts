import { OverlayContainer } from '@angular/cdk/overlay'
import { registerLocaleData } from '@angular/common'
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http'
import vi from '@angular/common/locales/vi'
import { NgModule } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { MatSnackBarModule } from '@angular/material/snack-bar'
import { MatTabsModule } from '@angular/material/tabs'
import { BrowserModule } from '@angular/platform-browser'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { IconDefinition } from '@ant-design/icons-angular'
import * as AllIcons from '@ant-design/icons-angular/icons'
import { NzAffixModule } from 'ng-zorro-antd/affix'
import { NzAvatarModule } from 'ng-zorro-antd/avatar'
import { NzBadgeModule } from 'ng-zorro-antd/badge'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NZ_I18N, vi_VN } from 'ng-zorro-antd/i18n'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzLayoutModule } from 'ng-zorro-antd/layout'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { BasicAuthInterceptor } from './_helpers/basic-auth.interceptor'
import { ErrorInterceptor } from './_helpers/error.interceptor'
import { AppRoutingModule } from './app-routing.module'
import { AppComponent } from './app.component'
import { InAppRootOverlayContainer } from './in-app-root-overlay-container'
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component'
import { ConfirmLayoutComponent } from './layouts/confirm-layout/confirm-layout.component'
import { ApiFnbService, ApiNtssService, ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from './services'
import { ApiScService } from './services/apiSc.service'
const antDesignIcons = AllIcons as { [key: string]: IconDefinition }
const icons: IconDefinition[] = Object.keys(antDesignIcons).map((key: string) => antDesignIcons[key])
registerLocaleData(vi)

@NgModule({
  exports: [MatDialogModule, MatTabsModule, MatSnackBarModule],
  declarations: [],
})
export class MaterialModule {}

@NgModule({
  declarations: [AppComponent, AdminLayoutComponent, ConfirmLayoutComponent],
  imports: [
    NzLayoutModule,
    BrowserModule,
    AppRoutingModule,
    NzIconModule.forRoot(icons),
    FormsModule,
    HttpClientModule,
    BrowserAnimationsModule,
    MaterialModule,
    NzBadgeModule,
    NzDropDownModule,
    NzModalModule,
    NzPopoverModule,
    NzButtonModule,
    NzSelectModule,
    NzListModule,
    NzInputModule,
    NzFormModule,
    NzToolTipModule,
    NzAffixModule,
    NzAvatarModule,
  ],
  providers: [
    { provide: MatDialogRef, useValue: {} },
    { provide: NZ_I18N, useValue: vi_VN },
    { provide: HTTP_INTERCEPTORS, useClass: BasicAuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    { provide: OverlayContainer, useClass: InAppRootOverlayContainer },
    NotifyService,
    ApiService,
    ApiFnbService,
    ApiNtssService,
    AuthenticationService,
    CoreService,
    StorageService,
    ApiScService,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
