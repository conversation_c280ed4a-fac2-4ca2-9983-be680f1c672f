<div *ngIf="status === resStatus.IsLoading.value" id="loader"></div>

<div id="myDiv" class="animate-bottom">
  <nz-result
    *ngIf="status === resStatus.IsLoading.value"
    nzStatus="info"
    nzTitle="Thông báo"
    nzSubTitle="Hệ thống đang xử lý... Vui lòng chờ trong giây lát!"
  >
  </nz-result>

  <nz-result
    *ngIf="status === resStatus.Success.value"
    nzStatus="success"
    nzTitle="Duyệt thành công"
    nzSubTitle="Gói thầu đã được đánh giá xong!"
  >
  </nz-result>
  <nz-result
    *ngIf="status === resStatus.Error.value"
    nzStatus="error"
    nzTitle="Duyệt thất bại"
    nzSubTitle="Vui lòng kiểm tra lại mạng, phân quyền và trạng thái của gói thầu, sau đó thử lại!"
  >
  </nz-result>

  <nz-result
    *ngIf="status === resStatus.RejectSuccess.value"
    nzStatus="success"
    nzTitle="Từ chối thành công"
    nzSubTitle="Gói thầu đã được huỷ đánh giá thương mại và giá, chuyển lại bộ phận mua hàng để đánh giá lại!"
  >
  </nz-result>
  <nz-result
    *ngIf="status === resStatus.RejectError.value"
    nzStatus="error"
    nzTitle="Từ chối thất bại"
    nzSubTitle="Vui lòng kiểm tra lại mạng, phân quyền và trạng thái của gói thầu, sau đó thử lại!"
  >
  </nz-result>
</div>
