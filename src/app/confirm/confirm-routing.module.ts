import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { TechLeaderAcceptComponent } from './tech-leader-accept/tech-leader-accept.component'
import { MpoAcceptAllComponent } from './mpo-accept-all/mpo-accept-all.component'
import { AcceptBidTechRateComponent } from './accept-bid-tech-rate/accept-bid-tech-rate.component'
import { AcceptBidPriceRateComponent } from './accept-bid-price-rate/accept-bid-price-rate.component'

const routes: Routes = [
  { path: 'tech-accept', component: TechLeaderAcceptComponent },
  { path: 'mpo-accept-final', component: MpoAcceptAllComponent },
  { path: 'accept-bid-tech-rate', component: AcceptBidTechRateComponent },
  { path: 'accept-bid-price-rate', component: AcceptBidPriceRateComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ConfirmRoutingModule {}
