import { Component, OnInit } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { enumData } from '../../core'
import { ApiService } from '../../services'

@Component({
  selector: 'app-mpo-accept-all',
  templateUrl: './mpo-accept-all.component.html',
  styleUrls: ['./mpo-accept-all.component.scss'],
})
export class MpoAcceptAllComponent implements OnInit {
  id = ''
  resStatus = enumData.ResultStatus
  status = this.resStatus.IsLoading.value
  action = ''
  constructor(private activatedRoute: ActivatedRoute, private apiService: ApiService) {}

  ngOnInit() {
    // Note: Below 'queryParams' can be replaced with 'params' depending on your requirements
    this.activatedRoute.queryParams.subscribe((params) => {
      // tslint:disable-next-line:no-string-literal
      this.id = params['id']
      // tslint:disable-next-line:no-string-literal
      this.action = params['action']
      if (this.action === 'approve') {
        this.confirm()
      } else if (this.action === 'reject') {
        this.reject()
      }
    })
  }

  confirm() {
    const data = { id: this.id, noteMPOLeader: '' }
    this.apiService
      .post(this.apiService.BID.ACCEPT_ALL, { data })
      .then((result) => {
        this.status = this.resStatus.Success.value
      })
      .catch((err) => {
        this.status = this.resStatus.Error.value
      })
  }

  reject() {
    const data = { id: this.id, noteMPOLeader: '' }
    this.apiService
      .post(this.apiService.BID.REJECT_ALL, { data })
      .then((result) => {
        this.status = this.resStatus.RejectSuccess.value
      })
      .catch((err) => {
        this.status = this.resStatus.RejectError.value
      })
  }
}
