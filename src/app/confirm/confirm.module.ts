import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { TechLeaderAcceptComponent } from './tech-leader-accept/tech-leader-accept.component'
import { MpoAcceptAllComponent } from './mpo-accept-all/mpo-accept-all.component'
import { ConfirmRoutingModule } from './confirm-routing.module'
import { NzResultModule } from 'ng-zorro-antd/result'
import { AcceptBidTechRateComponent } from './accept-bid-tech-rate/accept-bid-tech-rate.component'
import { AcceptBidPriceRateComponent } from './accept-bid-price-rate/accept-bid-price-rate.component'

@NgModule({
  declarations: [
    TechL<PERSON>erAcceptComponent,
    MpoAcceptAllComponent,
    AcceptBidTechRateComponent,
    AcceptBidPriceRateComponent,
  ],
  imports: [CommonModule, ConfirmRoutingModule, NzResultModule],
  exports: [],
  bootstrap: [],
})
export class ConfirmModule {}
