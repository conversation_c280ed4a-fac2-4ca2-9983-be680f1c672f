import { Component, OnInit } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { ApiService } from '../../services'
import { enumData } from '../../core'

@Component({
  selector: 'app-tech-leader-accept',
  templateUrl: './tech-leader-accept.component.html',
  styleUrls: ['./tech-leader-accept.component.scss'],
})
export class TechLeaderAcceptComponent implements OnInit {
  id = ''
  resStatus = enumData.ResultStatus
  status = this.resStatus.IsLoading.value
  action = ''
  constructor(private activatedRoute: ActivatedRoute, private apiService: ApiService) {}

  ngOnInit() {
    // Note: Below 'queryParams' can be replaced with 'params' depending on your requirements
    this.activatedRoute.queryParams.subscribe((params) => {
      // tslint:disable-next-line:no-string-literal
      this.id = params['id']
      // tslint:disable-next-line:no-string-literal
      this.action = params['action']
      if (this.action === 'approve') {
        this.confirm()
      } else if (this.action === 'reject') {
        this.reject()
      }
    })
  }

  confirm() {
    const data = { noteTechLeader: '' }
    this.apiService
      .post(this.apiService.BID.TECH_ACCEPT(this.id), { data })
      .then((result) => {
        this.status = this.resStatus.Success.value
      })
      .catch((err) => {
        this.status = this.resStatus.Error.value
      })
  }

  reject() {
    const data = { noteTechLeader: '' }
    this.apiService
      .post(this.apiService.BID.TECH_REJECT(this.id), { data })
      .then((result) => {
        this.status = this.resStatus.RejectSuccess.value
      })
      .catch((err) => {
        this.status = this.resStatus.RejectError.value
      })
  }
}
