import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { User } from '../../../models'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'

@Component({
  selector: 'app-asn-history',
  templateUrl: './asn-history.component.html',
})
export class AsnHistoryComponent implements OnInit, OnDestroy {
  dateFormat = 'dd/MM/yyyy'
  modalTitle: any
  pageIndex: any
  pageSize: any
  total: any
  loading = true
  currentUser!: User
  dataSearch: any = {}
  listOfData: any[] = []
  dataWarehouse: any[] = []
  dataPurchaseOrder: any[] = []
  dataBranch: any[] = []
  dataPurchasePlan: any[] = []
  dataServices: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumData: any

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.History.code)
      }
    })
    this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.History.code)

    this.modalTitle = enumData.Constants.Model_Add
    this.pageIndex = enumData.Page.pageIndex
    this.pageSize = enumData.Page.pageSize
    this.total = enumData.Page.total

    this.loadWarehouse()
    this.loadBranch()
    this.loadPurchasePlan()
    this.loadService()

    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = {}
    if (this.dataSearch.createdAt && this.dataSearch.createdAt !== '' && this.dataSearch.createdAt.length === 2) {
      where.dateFrom = this.dataSearch.createdAt[0]
      where.dateTo = this.dataSearch.createdAt[1]
    }
    if (this.dataSearch.warehouseId) {
      where.warehouseId = this.dataSearch.warehouseId
    }
    if (this.dataSearch.serviceChoose && this.dataSearch.serviceChoose.length > 0) {
      where.serviceId = this.dataSearch.serviceChoose[this.dataSearch.serviceChoose.length - 1]
    }
    if (this.dataSearch.branchId) {
      where.branchId = this.dataSearch.branchId
    }
    if (this.dataSearch.purchasePlanId && this.dataSearch.purchasePlanId !== '') {
      where.purchasePlanId = this.dataSearch.purchasePlanId
    }

    const dataSearch: any = {
      where: where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.ASN.PAGINATION_DETAIL, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  loadWarehouse() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.WAREHOUSE.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataWarehouse = result
    })
  }

  loadBranch() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BRANCH.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataBranch = result
    })
  }

  loadPurchasePlan() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_PLAN.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataPurchasePlan = result
    })
  }

  loadService() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataServices = result
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>((resolve) => {
      if (index < 0) {
        // if index less than 0 it is root node
        node.children = this.dataServices
          .filter((c) => c.level === 1)
          .map((c) => {
            return { value: c.id, label: c.code, isLeaf: c.isLast }
          })
      } else {
        node.children = this.dataServices
          .filter((c) => c.parentId === node.value)
          .map((c) => {
            return { value: c.id, label: c.code, isLeaf: c.isLast }
          })
      }
      resolve()
    })
  }
}
