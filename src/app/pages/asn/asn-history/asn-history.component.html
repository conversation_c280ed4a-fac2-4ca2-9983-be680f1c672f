<nz-row nzGutter="8">
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">
        {{ language_key?.WAREHOUSE || 'Kho' }}
      </nz-form-label>
      <nz-form-control nzSpan="24">
        <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.WAREHOUSE_ENTER || 'Chọn kho'"
          [(ngModel)]="dataSearch.warehouseId" name="warehouseId">
          <nz-option *ngFor="let item of dataWarehouse" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id">
          </nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">
        {{ language_key?.BRANCH || 'Chi nhánh' }}
      </nz-form-label>
      <nz-form-control nzSpan="24">
        <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn chi nhánh' "
          [(ngModel)]="dataSearch.branchId" name="branchId">
          <nz-option *ngFor="let item of dataBranch" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id">
          </nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">
        {{ language_key?.PURCHASE_PLAN || 'Kế hoạch' }}
      </nz-form-label>
      <nz-form-control nzSpan="24">
        <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.PURCHASE_PLAN_ENTER || 'Chọn Kế hoạch' "
          [(ngModel)]="dataSearch.purchasePlanId" name="purchasePlanId">
          <nz-option *ngFor="let item of dataPurchasePlan" [nzLabel]="item.code + ' - ' + item.name"
            [nzValue]="item.id">
          </nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
</nz-row>

<nz-row nzGutter="8">
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">
        {{ language_key?.ASN_DATE || 'Ngày nhập kho' }}
      </nz-form-label>
      <nz-form-control nzSpan="24">
        <nz-range-picker [nzFormat]="dateFormat" [(ngModel)]="dataSearch.createdAt">
        </nz-range-picker>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label nzSpan="24" class="text-left">
        {{ language_key?.MATERIAL_GROUP || 'Nhóm vật tư' }}
      </nz-form-label>
      <nz-form-control nzSpan="24">
        <nz-cascader [nzPlaceHolder]="language_key?.MATERIAL_ENTER || 'Chọn Vật tư'"
          [(ngModel)]="dataSearch.serviceChoose" name="serviceChoose" id="serviceChoose" [nzLoadData]="loadDataService">
        </nz-cascader>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="mt-2 text-center">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-2">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th class="text-nowrap">{{ language_key?.ITEM_CODE || 'Mã Item' }}</th>
        <th class="text-nowrap">{{ language_key?.MATERIAL || 'Vật tư' }}</th>
        <th class="text-nowrap">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
        <th class="text-nowrap">{{ language_key?.ASN_TIME || 'Thời gian nhập kho' }}</th>
        <th class="text-nowrap">{{ language_key?.ASN_CODE || 'Lệnh nhập' }}</th>
        <th class="text-nowrap">{{ language_key?.PO_CODE || 'Mã PO' }}</th>
        <th class="text-nowrap">{{ language_key?.PURCHASE_PLAN || 'Kế hoạch' }}</th>
        <th class="text-nowrap">{{ language_key?.WAREHOUSE || 'Kho' }}</th>
        <th class="text-nowrap">{{ language_key?.BRANCH || 'Chi nhánh' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25"> {{ data.itemCode }} </td>
        <td class="mw-25"> {{ data.itemName }} </td>
        <td class="mw-25"> {{ data.quantity | number }} </td>
        <td class="mw-25"> {{ data.createdAt | date: 'dd/MM/yyyy' }} </td>
        <td class="mw-25"> {{ data.asnCode }} </td>
        <td class="mw-25"> {{ data.poCode }} </td>
        <td class="mw-25"> {{ data.purchasePlanCode }} </td>
        <td class="mw-25"> {{ data.warehouseName }} </td>
        <td class="mw-25"> {{ data.branchName }} </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>