import { Component, Inject, OnD<PERSON>roy, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'

@Component({ templateUrl: './add-or-edit-asn.component.html' })
export class AddOrEditAsnComponent implements OnInit, OnDestroy {
  modalTitle: any
  dataObject: any = {}
  isEditItem = false
  allService: any
  dataWarehouse: any
  dataPurchaseOrder: any
  dataBranch: any
  quantityPo!: number
  quantityAvailable!: number
  dataPurchasePlan: any
  checkService: any
  quantityAsn!: number
  datalstObject: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumData: any
  lstPoFilter: any = []

  constructor(
    private dialogRef: MatDialogRef<AddOrEditAsnComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.AddEdit.code)
      }
    })
    this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.AddEdit.code)

    this.loadAllSelect()
    this.loadObject()
    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.isEditItem = true
      this.modalTitle = 'Cập Nhật Số Lượng Nhập Kho'
      this.notifyService.showloading()
      this.dataObject.quantityPO = 0
      this.dataObject.quantityAsn = 0
      this.notifyService.showloading()
      let arr: any
      await this.apiService.post(this.apiService.ASN.FINDASNPO, { poId: this.data.poId }).then(async (result) => {
        this.notifyService.hideloading()
        if (result) {
          arr = result.__products__
          this.dataObject.quantityPO = result.quantityPO
          this.dataObject.quantityAsn = result.quantityAsn
        }
      })
      let details = this.data.__details__
      let arrProduct = arr
      details.forEach((item: any) => {
        arrProduct.forEach((pd: any) => {
          if (item.poId === pd.poId && item.itemCode === pd.itemCode) {
            item.quantity_asn = pd.quantity_asn
            item.quantity = pd.quantity
            item.quantityInbound = pd.quantityInbound
          }
        })
      })
      this.data.__details__ = details
    } else {
      this.dataObject.__details__ = []
      this.modalTitle = 'Thêm Mới Số Lượng Nhập Kho'
    }
  }
  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.__details__.length === 0) {
      this.notifyService.showError('Vui lòng thêm ít nhất 1 item')
      return
    }
    if (this.dataObject.quantityPO === this.dataObject.quantityAsn) {
      this.notifyService.showError('Số lượng nhập kho bằng số lượng PO. Vui lòng kiểm tra lại ')
      return
    }
    for (let item of this.dataObject.__details__) {
      if (item.quantityInbound === undefined) {
        this.notifyService.showError('Vui lòng kiểm tra số lượng nhập kho')
        return
      }
      if (item.quantityInbound === 0 && item.quantity - item.quantity_asn == 0) {
        this.dataObject.__details__ = this.dataObject.__details__.filter((x: { id: any }) => x.id != item.id)
      }
      if (item.quantityInbound === 0 && item.quantity - item.quantity_asn != 0) {
        this.notifyService.showError('Số lượng nhập kho phải lớn hơn 0')
        return
      }
      if (item.quantityInbound > item.quantity) {
        this.notifyService.showError('Số lượng nhập kho lớn hơn số vượt có thể nhập')
        return
      }
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.ASN.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.ASN.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  loadAllSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.WAREHOUSE.FIND, {}), // 0
      this.apiService.post(this.apiService.PO.FIND, {status: [this.enumData.PurchaseOrderStatus.Delivery.code, this.enumData.PurchaseOrderStatus.Complete.code] }), // 1
      this.apiService.post(this.apiService.BRANCH.FIND, {}), // 2
      this.apiService.post(this.apiService.PURCHASE_PLAN.FIND, {}), // 3
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.dataWarehouse = res[0]
      let rs: any[] = []
      res[1].forEach((element: any) => {
        element.bindName = element.code + ' - ' + element.title
        rs.push(element)
      })

      this.dataPurchaseOrder = rs
      res[2].forEach((element: any) => {
        element.bindName = element.code + ' - ' + element.name
      })
      this.dataBranch = res[2]
      this.dataPurchasePlan = res[3]
    })
  }
  delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  async onChangePlan(event: any) {
    if (event) {
      this.lstPoFilter = []
      let plan = this.dataPurchasePlan.filter((plan: { id: any }) => plan.id == event)
      this.notifyService.showloading()
      await this.apiService
        .post(this.apiService.PO.FIND, {
          products: { serviceId: plan[0].serviceId },
          status: [this.enumData.PurchaseOrderStatus.Delivery.code, this.enumData.PurchaseOrderStatus.Complete.code],
        })
        .then((data) => {
          data.forEach((element: any) => {
            element.bindName = element.code + ' - ' + element.title
            this.lstPoFilter.push(element)
          })
          this.notifyService.hideloading()
        })
    }
  }

  async onchangeQuantity(event: any) {
    if (event) {
      this.dataObject.quantityPO = 0
      this.dataObject.quantityAsn = 0
      this.notifyService.showloading()
      await this.apiService.post(this.apiService.ASN.FINDASNPO, { poId: event }).then(async (result) => {
        await this.delay(1000)
        this.notifyService.hideloading()
        if (result) {
          if (result.quantityPO === result.quantityAsn) {
            this.dataObject.quantityPO = result.quantityPO
            this.dataObject.quantityAsn = result.quantityAsn
            this.dataObject.__details__ = []
            this.notifyService.showError('PO vừa chọn đã nhập đủ số lượng. Vui lòng chọn PO khác')
            return
          } else {
            this.dataObject.__details__ = result.__products__
            this.dataObject.quantityPO = result.quantityPO
            this.dataObject.quantityAsn = result.quantityAsn
          }
        }
      })
      const objPO = this.dataPurchaseOrder.find((c: any) => c.id == event)
      if (objPO) this.dataObject.objectId = objPO.objectId
    } else {
      this.dataObject.quantity = 0
      this.quantityAvailable = 0
      this.dataObject.quantityAvailable = 0
      this.dataObject.__details__ = []
      this.dataObject.quantityAsn = 0
      this.quantityAsn = 0
      this.dataObject.objectId = null
    }
  }

  onDelete(index: any) {
    this.dataObject.__details__.splice(index, 1)
    this.dataObject.__details__ = this.dataObject.__details__.filter((x: any) => x.poId !== null)
  }

  loadObject() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OBJECT.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      if (result) {
        this.datalstObject = result
      }
    })
  }
}
