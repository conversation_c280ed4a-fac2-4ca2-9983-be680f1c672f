<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PURCHASE_PLAN || 'Kế hoạch' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kế hoạch !">
            <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.PURCHASE_PLAN_ENTER || 'Chọn Kế hoạch' "
              [(ngModel)]="dataObject.purchasePlanId" (ngModelChange)="onChangePlan($event)" name="purchasePlanId"> 
              <nz-option *ngFor="let item of dataPurchasePlan" [nzLabel]="item.code + ' - ' + item.name"
                [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.PO || 'Đơn hàng' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn PO">
            <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.PO_ENTER || 'Chọn PO' "
              [(ngModel)]="dataObject.poId" name="poId" (ngModelChange)="onchangeQuantity($event)"
              [disabled]="isEditItem">
              <nz-option *ngFor="let item of lstPoFilter" [nzLabel]="item.bindName" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.OBJECT || 'Đối tượng' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn đối tượng">
            <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.OBJECT_ENTER || 'Chọn đối tượng'"
              [(ngModel)]="dataObject.objectId" name="objectId" required readonly disabled>
              <nz-option *ngFor="let item of datalstObject" [nzLabel]="item.code" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.BRANCH || 'Chi nhánh' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên (1-250 kí tự)!">
            <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn chi nhánh' "
              [(ngModel)]="dataObject.branchId" name="branchId">
              <nz-option *ngFor="let item of dataBranch" [nzLabel]="item.bindName" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.WAREHOUSE || 'Kho' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kho">
            <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.WAREHOUSE_ENTER || 'Chọn kho'"
              [(ngModel)]="dataObject.warehouseId" name="warehouseId">
              <nz-option *ngFor="let item of dataWarehouse" [nzLabel]="item.code + ' - ' + item.name"
                [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.ASN_DATE || 'Ngày nhập kho' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn ngày nhập kho">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.asnDate"
              [nzPlaceHolder]="language_key?.ASN_DATE_ENTER || 'Ngày nhập kho'" name="asnDate" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.NOTE || 'Ghi chú' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input pattern=".{0,250}" placeholder="Nhập không quá 250 kí tự"
              [(ngModel)]="dataObject.description" name="description" pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PO_QUANTITY_TOTAL || 'Tổng Số Lượng Của PO' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <input style="border: none;font-weight: bold;" disabled nz-input currencyMask readonly
              [(ngModel)]="dataObject.quantityPO" name="quantityPO" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.ASN_QUANTITY_TOTAL || 'Tổng số lượng đã nhập kho' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <input style="border: none;font-weight: bold;" disabled nz-input currencyMask readonly
              [(ngModel)]="dataObject.quantityAsn" name="quantityAsn" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-collapse class="mt-3" nz-col nzSpan="24">
        <nz-collapse-panel [nzHeader]="language_key?.ASN_ITEM_LIST || 'DS Item nhập kho'" class="ant-bg-antiquewhite"
          nzActive="true">
          <nz-row>
            <nz-table nz-col nzSpan="24" class="mb-3" [nzShowPagination]="false" nzBordered
              [nzData]="dataObject.__details__.length > 0 ? [''] : []">
              <thead>
                <tr>
                  <th>{{ language_key?.NO || 'STT' }}</th>
                  <th>{{ language_key?.ITEM_CODE || 'Mã Item' }}</th>
                  <th>{{ language_key?.MATERIAL || 'Vật tư' }}</th>
                  <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                  <th>{{ language_key?.PRICE || 'Đơn giá' }}</th>
                  <th>{{ language_key?.QUANTITY_ASN || 'SL đã nhập kho' }}</th>
                  <th>{{ language_key?.QUANTITY_INBOUND || 'SL nhập kho' }}</th>
                  <th>{{ language_key?.NOTE || 'Ghi chú' }}</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of dataObject.__details__; let i = index">
                  <td>{{ i + 1 }} </td>
                  <td>{{ data.itemCode }} </td>
                  <td>{{ data.__service__ ? data.__service__.name : '' }} </td>
                  <td>{{ data.quantity | number }}</td>
                  <td>{{ data.price | number }}</td>
                  <td>{{ data.quantity_asn | number }}</td>
                  <td>
                    <nz-form-control nzSpan="24">
                      <input nz-input currencyMask [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'"
                        [options]="{ prefix: '', thousands: '.', precision: '' , allowNegative: false }"
                        [(ngModel)]="data.quantityInbound" [ngModelOptions]="{standalone: true}" name="quantityInbound"
                        pattern=".{1,50}" />
                    </nz-form-control>
                  </td>
                  <td>
                    <nz-form-control nzSpan="24">
                      <input nz-input placeholder="Nhập ghi chú 1 - 250 kí tự" pattern=".{1,250}"  [(ngModel)]="data.description" [ngModelOptions]="{standalone: true}" name="description" />
                    </nz-form-control>
                  </td>
                  <td><button nz-tooltip [nzTooltipTitle]="language_key?.DELETE || 'Xóa'" class="mr-2" nz-button
                      nzDanger (click)="onDelete(i)"> <span nz-icon nzType="delete"></span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()"> <span nz-icon
          nzType="save"></span>{{
        language_key?.SAVE || 'Lưu' }}</button>
    </nz-col>
  </nz-row>
</form>