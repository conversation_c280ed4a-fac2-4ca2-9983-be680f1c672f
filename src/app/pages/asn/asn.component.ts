import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { enumData } from '../../core'
import { User } from '../../models'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../services'
import { AddOrEditAsnComponent } from './add-or-edit-asn/add-or-edit-asn.component'
import { AsnDetailComponent } from './asn-detail/asn-detail.component'

@Component({ templateUrl: './asn.component.html' })
export class AsnComponent implements OnInit, OnDestroy {
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  listOfData: any[] = []
  dataSearch: any = {}
  loading = true
  currentUser!: User
  isCollapseFilter = false
  dataWarehouse: any[] = []
  dataPurchaseOrder: any[] = []
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  dataBranch: any[] = []
  dataUploadExcel: any[] = []
  isVisible = false
  dataPurchasePlan: any[] = []
  checkTemplete = false
  fileList: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumData: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.loadWarehouse()
    this.searchData()
    this.loadPurchaseOrder()
    this.loadBranch()
    this.loadPurchasePlan()

    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.Asn.code)
      }
    })
    this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.Asn.code)
    this.enumRole = this.enumProject.Features.ASN_001.code
    this.action = this.enumProject.Action
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }

    let where = this.dataFilter()

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.ASN.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  dataFilter() {
    const where: any = {}
    if (this.dataSearch.code && this.dataSearch.code != '') where.code = this.dataSearch.code
    if (this.dataSearch.warehouseId) where.warehouseId = this.dataSearch.warehouseId
    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0)
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    if (this.dataSearch.branchId) where.branchId = this.dataSearch.branchId
    if (this.dataSearch.poId) where.poId = this.dataSearch.poId
    if (this.dataSearch.purchasePlanId) where.purchasePlanId = this.dataSearch.purchasePlanId
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    if (this.dataSearch.dateFrom) where.dateFrom = this.dataSearch.dateFrom
    if (this.dataSearch.dateTo) where.dateTo = this.dataSearch.dateTo

    return where
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditAsnComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  viewDetailInbound(object: any) {
    this.dialog.open(AsnDetailComponent, { disableClose: false, data: object })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.ASN.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  loadWarehouse() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.WAREHOUSE.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataWarehouse = result
    })
  }

  onCancel(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.ASN.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  loadPurchaseOrder() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataPurchaseOrder = result
    })
  }

  loadBranch() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BRANCH.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      result.forEach((element: any) => {
        element.lable = element.code + ' - ' + element.name
      })
      this.dataBranch = result
    })
  }

  loadPurchasePlan() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_PLAN.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataPurchasePlan = result
    })
  }

  async downloadExcel() {
    this.notifyService.showloading()

    let where = this.dataFilter()

    const dataSearch: any = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }

    this.loading = true
    this.apiService.post(this.apiService.ASN.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.notifyService.hideloading()
        if (data && data[0].length > 0) {
          let date = new Date().toISOString()
          const fileName = 'Danh_Sach_Nhap_kho' + date + '.xlsx'
          let dataExcel: any[] = []
          data[0].forEach((s: any) => {
            dataExcel.push({
              Mã: s.code,
              'Mã kế hoạch': s.__purchasePlan__ ? s.__purchasePlan__.code : '',
              'Mã PO': s.__po__ ? s.__po__.code : '',
              'Chi nhánh': s.__branch__ ? s.__branch__.name : '',
              Kho: s.__warehouse__ ? s.__warehouse__.name : '',
              'Vật tư': s.__service__ ? s.__service__.name : '',
              'Ngày nhập kho': moment(s.inventoryDate).format('DD-MM-YYYY'),
              'Số lượng': s.quantity,
              'Ghi chú': s.description,
            })
          })
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
          const wb: XLSX.WorkBook = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Danh sách nhập kho')

          XLSX.writeFile(wb, fileName)
        }
      }
    })
  }

  clickExportExcel() {
    this.notifyService.showloading()
    let lstDataExport = []

    //#region header
    const title: any = {
      zenId: 'CẤU HÌNH NHẬP KHO',
      purchasePlan: '',
      purchaseOrder: '',
      branch: '',
      warehouse: '',
      asnDate: '',
      blank: '',
      zenListId: 'CẤU HÌNH CÁC TRƯỜNG DANH SÁCH',
      itemCode: '',
      quantity: '',
      note: '',
    }
    lstDataExport.push(title)
    const numColTable1 = 7
    const header: any = {
      zenId: 'Cột',
      purchasePlan: 'Kế hoạch',
      purchaseOrder: 'PO',
      branch: 'Chi nhánh',
      warehouse: 'Kho',
      asnDate: 'Ngày nhập kho',
      blank: '',
      zenListId: 'Cột',
      itemCode: 'Mã Item',
      quantity: 'Số lượng nhập kho',
      note: 'Ghi chú',
    }
    lstDataExport.push(header)

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `Template cấu hình nhập kho.xlsx`
    const sheetName = 'Nhập kho'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    //#region custom data before export
    let i1 = 1
    wb.Sheets[sheetName]['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: numColTable1 - 1 } } /* A1:L1 */,
      {
        s: { r: 0, c: numColTable1 + 0 },
        e: { r: 0, c: numColTable1 + 4 },
      } /* N1:P1 */,
    ]
    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['zenId', 'purchasePlan', 'purchaseOrder', 'branch', 'warehouse', 'asnDate', 'blank', 'zenListId', 'itemCode', 'quantity', 'note'],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()
      // console.log(jsonData)
      // // Kiểm tra header
      if (
        header.zenId !== 'Cột' ||
        header.purchasePlan !== 'Kế hoạch' ||
        header.purchaseOrder !== 'PO' ||
        header.branch !== 'Chi nhánh' ||
        header.warehouse !== 'Kho' ||
        header.asnDate !== 'Ngày nhập kho' ||
        header.zenListId !== 'Cột' ||
        header.itemCode !== 'Mã Item' ||
        header.quantity !== 'Số lượng nhập kho' ||
        header.note !== 'Ghi chú'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError('File không đúng template ')
        return
      }

      // Tách và kiểm tra data từng bảng
      const lstDataTable1: any[] = []
      const lstDataTable2 = []
      let strErr = ''
      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có thông tin nhập kho cần import!`)
        return
      }
      for (const row of jsonData) {
        // add data table 1
        if (row.zenId != null && row.zenId != '') {
          const dataTable1: any = {}
          dataTable1.zenId = (row.zenId + '').trim()
          dataTable1.purchasePlan = row.purchasePlan
          dataTable1.purchaseOrder = row.purchaseOrder
          dataTable1.branch = row.branch
          dataTable1.asnDate = row.asnDate
          dataTable1.warehouse = row.warehouse
          if (dataTable1.purchaseOrder === null || dataTable1.purchaseOrder.trim() === '') {
            strErr += 'Mã PO không được để trống <br>'
          }

          // if (dataTable1.asnDate.trim() === '') {
          //   strErr += 'Ngày nhập kho không được để trống <br>';
          // }

          if (dataTable1.warehouse === null || dataTable1.warehouse.trim() === '') {
            strErr += 'Mã Kho không được để trống <br>'
          }

          if (dataTable1.branch === null || dataTable1.branch.trim() === '') {
            strErr += 'Mã Chi nhánh không được để trống <br>'
          }

          lstDataTable1.push(dataTable1)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (const row of jsonData) {
        // add data table 2
        if (row.zenListId != null && row.zenListId != '') {
          const dataTable2: any = {}
          dataTable2.zenListId = (row.zenListId + '').trim()
          dataTable2.itemCode = (row.itemCode || '') + ''
          dataTable2.quantity = row.quantity || 0
          dataTable2.note = row.note
          const trade = lstDataTable1.find((c) => c.zenId == dataTable2.zenListId)
          if (!trade) {
            strErr += `Cột [${row.zenListId}] không tồn tại, không xác định thuộc mã  nào<br>`
          }

          if (dataTable2.itemCode.trim() === '') {
            strErr += 'Mã Item không được để trống<br>'
          }
          if (dataTable2.quantity == null || dataTable2.quantity === '') {
            strErr += 'Số lượng nhập kho không được để trống<br>'
          }

          lstDataTable2.push(dataTable2)
        }
        if (strErr.length > 0) {
          this.notifyService.showError(strErr)
          return
        }
      }
      const param = { lstDataTable1, lstDataTable2 }
      this.notifyService.showloading()
      this.apiService.post(this.apiService.ASN.CREATEEXCEL, param).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })
    }
  }
}
