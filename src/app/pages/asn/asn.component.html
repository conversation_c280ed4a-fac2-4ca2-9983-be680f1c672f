<nz-tabset>
  <nz-tab [nzTitle]="tabTitle1" *ngIf="authenticationService.checkPermission([enumRole], action.View.code)">
    <ng-template #tabTitle1>
      <i nz-icon nzType="home"></i>
      <b>{{language_key?.ASN_WAREHOUSE || 'Nhập Kho'}}</b>
    </ng-template>
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button nzType="primary"
          (click)="clickAdd()" class="mr-2">
          <span nz-icon nzType="plus"></span>{{ language_key?.ADD || 'Thêm mới' }}
        </button>

        <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button
          (click)="clickExportExcel()" class="mr-2">
          <span nz-icon nzType="import"></span>{{ language_key?.DOWNLOAD_EXCEL || 'Tải Excel' }}
        </button>

        <input *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="hidden" type="file"
          id="file" (change)="clickImportExcel($event)" onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file"
          class="lable-custom-file">
          <span nz-icon nzType="upload"></span>
          {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
        </label>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom"
          [nzPlaceHolder]="language_key?.ENTER_DATEFROM || 'Nhập Từ ngày' ">
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo"
          [nzPlaceHolder]="language_key?.ENTER_DATETO || 'Nhập Đến ngày'">
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-collapse class="mt-5">
      <nz-collapse-panel [nzHeader]="language_key?.SEARCH_ADVANCED || 'Tìm kiếm nâng cao'" class="ant-bg-antiquewhite"
        [(nzActive)]="isCollapseFilter">
        <nz-row nzGutter="8">

          <nz-col nzSpan="6">
            <nz-form-item>
              <nz-form-label class="text-left" nzSpan="24">
                {{ language_key?.ASN_CODE || 'Mã nhập kho' }}
              </nz-form-label>
              <nz-form-control nzSpan="24">
                <input nz-input nz-col [placeholder]="language_key?.ASN_CODE_ENTER || 'Nhập Mã Kho'"
                  [(ngModel)]="dataSearch.code" name="code" pattern=".{1,50}" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="6">
            <nz-form-item>
              <nz-form-label class="text-left" nzSpan="24">
                {{ language_key?.WAREHOUSE || 'Kho' }}
              </nz-form-label>
              <nz-form-control nzSpan="24">
                <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.WAREHOUSE_ENTER || 'Chọn kho'"
                  [(ngModel)]="dataSearch.warehouseId" name="warehouseId">
                  <nz-option *ngFor="let item of dataWarehouse" [nzLabel]="item.code + ' - ' + item.name"
                    [nzValue]="item.id">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="6">
            <nz-form-item>
              <nz-form-label class="text-left" nzSpan="24">
                {{ language_key?.BRANCH || 'Chi nhánh' }}
              </nz-form-label>
              <nz-form-control nzSpan="24">
                <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn chi nhánh' "
                  [(ngModel)]="dataSearch.branchId" name="branchId">
                  <nz-option *ngFor="let item of dataBranch" [nzLabel]="item.lable" [nzValue]="item.id">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="6">
            <nz-form-item>
              <nz-form-label class="text-left" nzSpan="24">PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.PO_ENTER || 'Chọn PO' "
                  [(ngModel)]="dataSearch.poId" name="poId">
                  <nz-option *ngFor="let item of dataPurchaseOrder" [nzLabel]="item.code" [nzValue]="item.id">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="6">
            <nz-form-item>
              <nz-form-label class="text-left" nzSpan="24">
                {{ language_key?.PURCHASE_PLAN || 'Kế hoạch' }}
              </nz-form-label>
              <nz-form-control nzSpan="24">
                <nz-select nzShowSearch nzAllowClear
                  [nzPlaceHolder]="language_key?.PURCHASE_PLAN_ENTER || 'Chọn Kế hoạch' "
                  [(ngModel)]="dataSearch.purchasePlanId" name="purchasePlanId">
                  <nz-option *ngFor="let item of dataPurchasePlan" [nzLabel]="item.code + ' - ' + item.name"
                    [nzValue]="item.id">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="6">
            <nz-form-item>
              <nz-form-label class="text-left" nzSpan="24">
                {{ language_key?.STATUS || 'Trạng thái' }}
              </nz-form-label>
              <nz-form-control nzSpan="24">
                <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
                  [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
                  <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>
    <nz-row class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>

    <nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
        [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th class="text-nowrap">{{ language_key?.ASN_CODE || 'Mã nhập kho' }}</th>
            <th class="text-nowrap">{{ language_key?.PURCHASE_PLAN_CODE || 'Mã kế hoạch' }}</th>
            <th class="text-nowrap">{{ language_key?.PO_CODE || 'Mã PO' }}</th>
            <th class="text-nowrap">{{ language_key?.ASN_TIME || 'Thời gian nhập kho' }}</th>
            <th class="text-nowrap">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
            <th class="text-nowrap">{{ language_key?.NOTE || 'Ghi chú' }}</th>
            <th class="text-nowrap">{{ language_key?.ASN_ACTION || 'Tác Vụ' }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of ajaxTable.data">
            <td (click)="viewDetailInbound(data)">{{ data.code }}</td>
            <td (click)="viewDetailInbound(data)" class="mw-25">{{ data.__purchasePlan__?.code || '' }}</td>
            <td (click)="viewDetailInbound(data)" class="mw-25">{{ data.__po__?.code || '' }}</td>
            <td (click)="viewDetailInbound(data)">{{ data.asnDate | date: 'dd/MM/yyyy' }}</td>
            <td (click)="viewDetailInbound(data)">{{ data.quantity | number }}</td>
            <td (click)="viewDetailInbound(data)">{{ data.description }}</td>
            <td class="text-nowrap">
              <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [Khôi phục] Số lượng nhập kho ?"
                nzPopconfirmPlacement="bottom" (nzOnConfirm)="onCancel(data)" nz-tooltip
                [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
                <span nz-icon nzType="stop"></span>
              </button>
              <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [Xóa] Số lượng nhập kho ?"
                nzPopconfirmPlacement="bottom" (nzOnConfirm)="onCancel(data)" nz-tooltip
                [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'" nz-button nzType="primary">
                <span nz-icon nzType="play-circle"></span>
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
      <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger>
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total>
        {{ range[0] }}-{{ range[1] }} of {{ total }} items
      </ng-template>
    </nz-row>
  </nz-tab>

  <nz-tab [nzTitle]="tabTitle2" *ngIf="authenticationService.checkPermission([enumRole], action.View.code)">
    <ng-template #tabTitle2>
      <i nz-icon nzType="history"></i>
      <b>{{language_key?.ASN_HISTORY || 'Lịch Sử Nhập Kho'}}</b>
    </ng-template>
    <app-asn-history></app-asn-history>
  </nz-tab>

</nz-tabset>