import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { AuthenticationService, CoreService, StorageService } from '../../../services'

@Component({ templateUrl: './asn-detail.component.html' })
export class AsnDetailComponent implements OnInit, OnDestroy {
  modalTitle!: string
  dataObject: any = {}
  enumData: any
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    public coreService: CoreService,
    private storageService: StorageService,
    private authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.Detail.code)
      }
    })
    this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Admin.Asn.data.Detail.code)

    this.dataObject = { ...this.data }
    this.modalTitle = `Chi tiết nhập kho với mã là: [${this.data.code}]`
    this.listOfData = this.data.__details__ ? this.data.__details__ : this.data.__asnItems__
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }
}
