<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<nz-tabset matDialogContent>
  <nz-tab [nzTitle]="language_key?.GENERAL_INFO || 'Thông Tin Chung'">
    <nz-row style="margin-left: 20px;">
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.ASN_CODE || 'Mã nhập kho' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.code }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">{{ language_key?.PURCHASE_PLAN_CODE || 'Mã kế hoạch'
            }}</nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.__purchasePlan__ ? dataObject.__purchasePlan__.code : '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>



      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.ASN_DATE || 'Ngày nhập kho' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.asnDate | date: 'dd/MM/yyyy' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PO_CODE || 'Mã PO' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.__po__?.code || '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.WAREHOUSE || 'Kho' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.__warehouse__ ? dataObject.__warehouse__?.name : '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.BRANCH || 'Chi nhánh' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.__branch__ ? dataObject.__branch__?.name : '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>



      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.QUANTITY || 'Số lượng' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.quantity | number}}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>


      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.NOTE || 'Ghi chú' }}</nz-form-label>
          <nz-form-control nzSpan="24">
            <b [innerHtml]="dataObject.description"></b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </nz-tab>
  <nz-tab [nzTitle]="language_key?.ITEM_LIST || 'Danh sách Item'">
    <nz-row>
      <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="listOfData" [nzShowPagination]="true" nzBordered>
        <thead>
          <tr class="text-nowrap">
            <th>{{ language_key?.ASN_CODE || 'Mã ASN' }}</th>
            <th>{{ language_key?.ITEM_CODE || 'Mã Item' }}</th>
            <th>{{ language_key?.MATERIAL_CODE || 'Mã LVKD' }}</th>
            <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
            <th>{{ language_key?.NOTE || 'Ghi chú' }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of listOfData; let i = index">
            <td class="mw-25">{{ data.__asn__ ? data.__asn__?.code : ''}}</td>
            <td class="mw-25">{{ data.itemCode }} </td>
            <td class="mw-25">{{ data.__service__ ? data?.__service__.code : ''}}</td>
            <td class="mw-25">{{ data.quantity | number }} </td>
            <td class="mw-25">{{ data.description }} </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-tab>
  <nz-tab [nzTitle]="language_key?.PO_INFO || 'Thông Tin PO'">
    <nz-row style="margin-left: 20px;">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PO_CODE || 'Mã PO' }}
          </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <b>{{ dataObject.__po__?.code || '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PO_VALUE || 'Giá trị PO' }}
          </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <b>{{ dataObject.__po__ ? (dataObject.__po__?.money | number) : '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PO_STATUS || 'Trạng thái PO' }}
          </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <b>{{ dataObject.__po__ ? (coreService.getEnumElementName(enumData.PurchaseOrderStatus,
              dataObject.__po__.status)) : '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PO_NAME || 'Tên PO' }}
          </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <b>{{ dataObject.__po__ ? dataObject.__po__?.title : '' }}</b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </nz-tab>
</nz-tabset>