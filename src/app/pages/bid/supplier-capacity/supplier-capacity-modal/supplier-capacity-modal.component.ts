import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './supplier-capacity-modal.component.html' })
export class SupplierCapacityModalComponent implements OnInit {
  comment: any
  approverComment: any
  isAccesser = false
  isApprover = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<SupplierCapacityModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.comment = this.data.comment
    this.approverComment = this.data.approverComment
    this.checkPermissionAccess()
    this.checkPermissionApprove()
  }

  checkPermissionAccess() {
    this.apiService.get(this.apiService.SUPPLIER_REVIEWAL.CHECK_PERMISSION_ACCESS(this.data.serviceId), {}).then((data) => {
      if (data) {
        const lstStatusAccess = [
          enumData.SupplierServiceStatus.MoiDangKy.code,
          enumData.SupplierServiceStatus.CapNhatThongTin.code,
          enumData.SupplierServiceStatus.KhongDuyet.code,
        ]
        this.isAccesser = data.isAccesser && lstStatusAccess.includes(this.data.status)
      }
    })
  }

  checkPermissionApprove() {
    this.apiService.get(this.apiService.SUPPLIER_REVIEWAL.CHECK_PERMISSION_APPROVE(this.data.serviceId), {}).then((data) => {
      if (data) {
        this.isApprover = data.isApprover && this.data.status === enumData.SupplierServiceStatus.PhuTrachDuyet.code
      }
    })
  }

  onEmployeeReject() {
    this.apiService
      .post(this.apiService.SUPPLIER_REVIEWAL.EMPLOYEE_REJECT_SUPPLIER, {
        supplierServiceId: this.data.supplierServiceId,
        comment: this.comment,
      })
      .then((data) => {
        if (data) {
          this.notifyService.showSuccess(data.message)
          this.closeDialog(true)
        }
      })
  }

  onEmployeeApprove() {
    this.apiService
      .post(this.apiService.SUPPLIER_REVIEWAL.EMPLOYEE_APPROVE_SUPPLIER, {
        supplierServiceId: this.data.supplierServiceId,
        comment: this.comment,
      })
      .then((data) => {
        if (data) {
          this.notifyService.showSuccess(data.message)
          this.closeDialog(true)
        }
      })
  }

  onReject() {
    this.apiService
      .post(this.apiService.SUPPLIER_REVIEWAL.REJECT_SUPPLIER, {
        supplierServiceId: this.data.supplierServiceId,
        comment: this.approverComment,
      })
      .then((data) => {
        if (data) {
          this.notifyService.showSuccess(data.message)
          this.closeDialog(true)
        }
      })
  }

  onApprove() {
    this.apiService
      .post(this.apiService.SUPPLIER_REVIEWAL.APPROVE_SUPPLIER, {
        supplierServiceId: this.data.supplierServiceId,
        comment: this.approverComment,
      })
      .then((data) => {
        if (data) {
          this.notifyService.showSuccess(data.message)
          this.closeDialog(true)
        }
      })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
