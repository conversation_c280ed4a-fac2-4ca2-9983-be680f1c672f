<nz-tabset matDialogContent>
  <nz-tab nzTitle="Thông tin pháp lý">
    <app-supplier-basic-detail [supplierId]="data.supplierId"></app-supplier-basic-detail>
  </nz-tab>
  <nz-tab [nzTitle]="titleTemplate" *ngIf="data.supplierServiceId">
    <ng-template #titleTemplate>Thông tin năng lực chung - Lĩnh vực kinh doanh {{ data.itemName }} </ng-template>
    <app-supplier-capacity-detail [data]="{ supplierServiceId: data.supplierServiceId }">
    </app-supplier-capacity-detail>
    <div>
      <h4>Phụ trách mua hàng đánh giá:</h4>
      <textarea nz-input rows="2" [disabled]="!isAccesser" auto placeholder="Nhân viên phụ trách nhận xét"
        [(ngModel)]="comment" name="comment"></textarea>
    </div>
    <div>
      <h4>Người duyệt đánh giá</h4>
      <textarea nz-input rows="2" [disabled]="!isApprover" auto placeholder="Nhân viên duyệt nhận xét"
        [(ngModel)]="approverComment" name="approverComment"></textarea>
    </div>
  </nz-tab>
</nz-tabset>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button nz-button *ngIf="isApprover && authenticationService.checkPermission([enumRole], action.Update.code)"
      class="mr-2" nzDanger (click)="onReject()">
      Từ chối
    </button>
    <button nz-button *ngIf="isApprover && authenticationService.checkPermission([enumRole], action.Update.code)"
      class="mr-2" nzType="primary" (click)="onApprove()">
      Duyệt
    </button>
    <button nz-button *ngIf="isAccesser && authenticationService.checkPermission([enumRole], action.Update.code)"
      class="mr-2" nzDanger (click)="onEmployeeReject()">
      Từ chối
    </button>
    <button nz-button *ngIf="isAccesser && authenticationService.checkPermission([enumRole], action.Update.code)"
      nzType="primary" (click)="onEmployeeApprove()">
      Duyệt
    </button>
  </nz-col>
</nz-row>