<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tuỳ chọn tìm kiếm" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Chọn lĩnh vực mua hàng</nz-form-label>
          <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
            [nzLoadData]="loadDataService">
          </nz-cascader>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.INTERPRISE_CODENAME || 'Mã hoặc tên doanh nghiệp' }}
          </nz-form-label>
          <input nz-input [placeholder]="language_key?.INTERPRISE_CODENAME || 'Mã hoặc tên doanh nghiệp'"
            [(ngModel)]="dataSearch.supplierName" name="supplierName" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">{{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
            [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
            <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Trạng thái thẩm định</nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusExpertise" name="statusExpertise"
            nzPlaceHolder="Trạng thái thẩm định">
            <nz-option *ngFor="let item of dataExpertiseStatus" [nzLabel]="item.name" [nzValue]="item.code">
            </nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Từ ngày đăng ký</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Từ ngày đăng ký">
            <nz-date-picker [(ngModel)]="dataSearch.fromDate" name="dataSearch.fromDate"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Đến ngày đăng ký</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Đến ngày đăng ký">
            <nz-date-picker [(ngModel)]="dataSearch.toDate" name="dataSearch.toDate"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" [nzScroll]="{ x: '1250px' }" class="mb-3" #basicTable [nzData]="listOfData"
    [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
        <th>Lĩnh vực kinh doanh</th>
        <th>Điểm năng lực</th>
        <th>Thẩm định</th>
        <th>Phụ trách mua hàng</th>
        <th>Người duyệt</th>
        <th nzWidth="170px">{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th>Ngày đăng ký</th>
        <th nzRight>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td class="mw-25" (click)="showModalDetail(data)">{{ data.supplierName }}</td>
        <td (click)="showModalDetail(data)">{{ data.itemName }}</td>
        <td class="text-right" (click)="showModalDetail(data)">{{ data.score }}</td>
        <td (click)="showModalDetail(data)">
          {{ data.lastUpdateExpertise ? (data.lastUpdateExpertise | date: 'dd/MM/yyyy') : '' }}
        </td>
        <td (click)="showModalDetail(data)">{{ data.acceptEmployeeName }}</td>
        <td (click)="showModalDetail(data)">{{ data.approveByName }}</td>
        <td (click)="showModalDetail(data)">
          <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName}}</nz-tag>
        </td>
        <td (click)="showModalDetail(data)">{{ data.createdAt | date: 'dd/MM/yyyy' }}</td>
        <td nzRight class="text-nowrap">
          <button class="mr-2"
            *ngIf="checkPermissionDelete(data) && authenticationService.checkPermission([enumRole], action.Delete.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn xóa thông tin đăng ký?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="deleteSupplier(data)" nz-tooltip nzTooltipTitle="Xóa thông tin đăng ký" nz-button nzDanger>
            <span nz-icon nzType="delete"></span>
          </button>
          <button
            *ngIf="checkPermissionActive(data) && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn cập nhật trạng thái 'Đã là thành viên'?"
            nzPopconfirmPlacement="bottom" (nzOnConfirm)="actionSupplier(data)" nz-tooltip
            nzTooltipTitle="Cập nhật trạng thái 'Đã là thành viên'" nz-button nzType="primary">
            <span nz-icon nzType="play-circle"></span></button>
          <button
            *ngIf="checkPermissionDeActive(data) && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngừng hoạt động?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="actionSupplier(data)" nz-tooltip nzTooltipTitle="Ngừng hoạt động" nz-button nzType="primary">
            <span nz-icon nzType="stop"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>