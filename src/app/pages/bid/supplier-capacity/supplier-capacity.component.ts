import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { SupplierCapacityModalComponent } from './supplier-capacity-modal/supplier-capacity-modal.component'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './supplier-capacity.component.html' })
export class SupplierCapacityComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataServiceChild: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  dataStatus = [
    enumData.SupplierServiceStatus.MoiDangKy,
    enumData.SupplierServiceStatus.CapNhatThongTin,
    enumData.SupplierServiceStatus.PhuTrachDuyet,
    enumData.SupplierServiceStatus.PhuTrachKhongDuyet,
    enumData.SupplierServiceStatus.KhongDuyet,
    enumData.SupplierServiceStatus.DaDuyet,
    enumData.SupplierServiceStatus.NgungHoatDong,
  ]

  dataExpertiseStatus = this.coreService.convertObjToArray(enumData.SupplierServiceExpertiseStatus)
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.enumRole = this.enumProject.Features.SUPPLIER_003.code
    this.action = this.enumProject.Action
    this.searchData()
    // this.loadService()
  }

  async searchData(reset = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = { isDeleted: false }
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.statusExpertise) {
      where.statusExpertise = this.dataSearch.statusExpertise
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.supplierName && this.dataSearch.supplierName.length !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.SUPPLIER_REVIEWAL.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.listOfData = data[0]
        this.total = data[1]
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  showModalDetail(item: any) {
    this.dialog
      .open(SupplierCapacityModalComponent, {
        disableClose: false,
        data: {
          supplierServiceId: item.id,
          supplierId: item.supplierId,
          serviceId: item.serviceId,
          status: item.status,
          comment: item.comment,
          approverComment: item.approverComment,
          itemName: item.itemName,
        },
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  checkPermissionDelete(item: any) {
    let result = false
    if (item.status === enumData.SupplierServiceStatus.PhuTrachKhongDuyet.code || item.status === enumData.SupplierServiceStatus.KhongDuyet.code) {
      result = true
    }
    return result
  }

  checkPermissionActive(item: any) {
    return item.status === enumData.SupplierServiceStatus.NgungHoatDong.code
  }

  checkPermissionDeActive(item: any) {
    return item.status === enumData.SupplierServiceStatus.DaDuyet.code || item.status === enumData.SupplierServiceStatus.CapNhatThongTin.code
  }

  /** Xóa thông tin đăng ký Item NCC */
  deleteSupplier(item: any) {
    this.notifyService.showloading()
    this.apiService.put(this.apiService.SUPPLIER_REVIEWAL.DELETE_SUPPLIER(item.id), {}).then((data) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData(true)
    })
  }

  /** Ngưng hoạt động/Hoạt động lại Item NCC */
  actionSupplier(item: any) {
    this.notifyService.showloading()
    this.apiService.put(this.apiService.SUPPLIER_REVIEWAL.DEACTIVE_SUPPLIER(item.id), {}).then((data) => {
      this.notifyService.showSuccess(data.message)
      this.searchData(true)
    })
  }
}
