import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-supplier-basic-check-detail-print',
  templateUrl: './supplier-basic-check-detail-print.component.html',
})
export class SupplierBasicCheckDetailPrintComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  dataObject: any = {}
  dataObjectNew: any = {}
  dataHistory: any
  loading = false
  comment = ''
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  supplierExpertiseId: any
  @Input()
  dialogRef: any
  constructor(private apiService: ApiService, public coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    
    if (!this.supplierExpertiseId) return
    this.loadDetail()
  }

  loadDetail() {
    this.loading = true
    this.apiService.get(this.apiService.SUPPLIER_EXPERTISE.GET_LAW(this.supplierExpertiseId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataObject = data.supplierExpertise.__supplier__
        this.comment = data.supplierExpertise.comment
        this.dataObjectNew = {}
        if (data.supplierExpertise.__supplierExpertiseLawDetails__?.length > 0) {
          this.dataObjectNew = data.supplierExpertise.__supplierExpertiseLawDetails__[0]
        }
      }
    })
  }
}
