import { Component, OnInit, Input } from '@angular/core'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, StorageService } from '../../../../../services'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-supplier-capacity-check-detail-print',
  templateUrl: './supplier-capacity-check-detail-print.component.html',
})
export class SupplierCapacityCheckDetailPrintComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  listOfData: any[] = []
  dataObjectNew: any[] = []

  loading = false
  dataService: any[] = []
  dataType = enumData.DataType
  dataHistoryCapacity: any[] = []
  comment = ''
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  supplierExpertiseId: any
  @Input()
  dialogRef: any
  constructor(private apiService: ApiService, private coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.supplierExpertiseId) return
    this.loadDataList()
  }

  async loadDataList() {
    if (this.supplierExpertiseId) {
      this.loading = true
      this.apiService.get(this.apiService.SUPPLIER_EXPERTISE.GET_CAPACITY(this.supplierExpertiseId), {}).then(async (data) => {
        if (data) {
          this.loading = false
          this.listOfData = data.supplierCapacity || []
          this.comment = data.supplierExpertise.commentCapacity
          this.dataHistoryCapacity = data.supplierExpertise.__supplierExpertiseDetails__
          for (const item of this.listOfData) {
            const itemValue: any = {}
            itemValue.supplierCapacityId = item.id
            itemValue.name = item.name
            itemValue.isChangeByYear = item.isChangeByYear
            itemValue.sort = item.sort
            itemValue.type = item.type
            itemValue.childs = []
            if (itemValue.isChangeByYear) {
              itemValue.listDetailYear = []
            }
            const history = await this.getHistoryCapacity(item)
            if (history) {
              itemValue.value = history.value
              itemValue.listDetailYear = history.listDetailYear
              itemValue.comment = history.comment
            }

            for (const itemC of item.__childs__) {
              const itemValueC: any = {}
              itemValueC.supplierCapacityId = itemC.id
              itemValueC.sort = itemC.sort
              itemValueC.isChangeByYear = itemC.isChangeByYear
              itemValueC.name = itemC.name
              itemValueC.type = itemC.type
              if (itemValueC.isChangeByYear) {
                itemValueC.listDetailYear = []
              }
              const historyC = await this.getHistoryCapacity(itemC)
              if (historyC) {
                itemValueC.value = historyC.value
                itemValueC.listDetailYear = historyC.listDetailYear
                itemValueC.comment = historyC.comment
              }
              itemValue.childs.push(itemValueC)
            }
            this.dataObjectNew.push(itemValue)
          }
        }
      })
    }
  }

  convertTypeList(list: any[]) {
    try {
      const data = list.find((p: any) => p.isChosen)
      if (data) return data.name
      return ''
    } catch {
      return ''
    }
  }

  convertTypeListFromValue(value: any, list: any[]) {
    try {
      const data = list.find((p: any) => p.id == value)
      if (data) return data.name

      return ''
    } catch {
      return ''
    }
  }

  async getHistoryCapacity(obj: any) {
    const findData: any = this.dataHistoryCapacity.find((p) => p.supplierCapacityId === obj.id)
    if (findData) {
      if (!obj.isChangeByYear) {
        if (obj.value !== findData.value) {
          return { value: findData.value, comment: findData.comment }
        }
      } else {
        return { listDetailYear: findData.__supplierExpertiseYearDetails__ || [], comment: findData.comment }
      }
    }
    return
  }
}
