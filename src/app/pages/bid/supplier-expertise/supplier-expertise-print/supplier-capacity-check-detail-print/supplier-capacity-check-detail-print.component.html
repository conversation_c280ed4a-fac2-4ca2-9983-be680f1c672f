<nz-row nzGutter="2" class="mt-2">
  <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
    [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tiêu chí</th>
        <th>Nhà cung cấp đã đăng ký</th>
        <th>Yêu cầu điều chỉnh</th>
        <th>Đánh giá chung</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let item of listOfData; let i = index">
        <tr>
          <td class="mw-25">{{ item.name }}</td>
          <td>
            <div *ngIf="!item.isChangeByYear; else changeByYear1">
              <p *ngIf="item.type === dataType.String.code">{{ item.value }}</p>
              <p class="text-right" *ngIf="item.type === dataType.Number.code">{{ item.value | number}}</p>
              <p *ngIf="item.type === dataType.Date.code">{{ item.value | date: 'dd/MM/yyyy HH:mm' }}</p>
              <p *ngIf="item.type === dataType.List.code">
                {{ convertTypeList(item.__supplierCapacityListDetails__) }}
              </p>
              <p *ngIf="item.type === dataType.File.code && item.value">{{ item.value }}</p>
            </div>
            <ng-template #changeByYear1>
              <div *ngFor="let itemYear of item.__supplierCapacityYearValues__">
                <p *ngIf="item.type === dataType.String.code">
                  Năm {{ itemYear.year }} - {{ itemYear.value }}
                </p>
                <p *ngIf="item.type === dataType.Number.code">
                  Năm {{ itemYear.year }} - {{ itemYear.value | number}}
                </p>
                <p *ngIf="item.type === dataType.File.code && itemYear.value">
                  Năm {{ itemYear.year }} - {{ itemYear.value }}
                </p>
              </div>
            </ng-template>
          </td>
          <td>
            <div *ngIf="!item.isChangeByYear; else changeByYear2">
              <p *ngIf="item.type === dataType.String.code && item.__childs__.length === 0">
                {{dataObjectNew[i].value}}
              </p>
              <p *ngIf="item.type === dataType.Number.code && item.__childs__.length === 0">
                {{dataObjectNew[i].value | number}}
              </p>
              <p *ngIf="item.type === dataType.Date.code && item.__childs__.length === 0">
                {{dataObjectNew[i].value | date: 'dd/MM/yyyy HH:mm'}}
              </p>
              <p *ngIf="item.type === dataType.List.code && item.__childs__.length === 0">
                {{ convertTypeListFromValue(dataObjectNew[i].value, item.__supplierCapacityListDetails__) }}
              </p>
              <p *ngIf="item.type === dataType.File.code && item.value">{{ dataObjectNew[i].value }}</p>
            </div>
            <ng-template #changeByYear2>
              <div *ngFor="let itemYear of dataObjectNew[i].listDetailYear">
                <p *ngIf="item.type === dataType.String.code">
                  Năm {{ itemYear.year }} - {{ itemYear.value }}
                </p>
                <p *ngIf="item.type === dataType.Number.code">
                  Năm {{ itemYear.year }} - {{ itemYear.value | number}}
                </p>
                <p *ngIf="item.type === dataType.File.code && itemYear.value">
                  Năm {{ itemYear.year }} - {{ itemYear.value }}
                </p>
              </div>
            </ng-template>
          </td>
          <td>{{ dataObjectNew[i].comment }}</td>
        </tr>
        <ng-container>
          <tr *ngFor="let itemC of item.__childs__; let j = index">
            <td [nzIndentSize]="20">
              {{ itemC.name }}
            </td>
            <td>
              <div *ngIf="!itemC.isChangeByYear; else changeByYear1">
                <p *ngIf="itemC.type === dataType.String.code">{{ itemC.value }}</p>
                <p class="text-right" *ngIf="itemC.type === dataType.Number.code">{{ itemC.value | number}}</p>
                <p *ngIf="itemC.type === dataType.Date.code">{{ itemC.value | date: 'dd/MM/yyyy HH:mm' }}</p>
                <p *ngIf="itemC.type === dataType.List.code">
                  {{ convertTypeList(itemC.__supplierCapacityListDetails__) }}
                </p>
                <p *ngIf="itemC.type === dataType.File.code && itemC.value">
                  {{ itemC.value }}
                </p>
              </div>
              <ng-template #changeByYear1>
                <div *ngFor="let itemYear of itemC.__supplierCapacityYearValues__">
                  <p *ngIf="itemC.type === dataType.String.code">{{ itemYear.year }} - {{ itemYear.value }}</p>
                  <p *ngIf="itemC.type === dataType.Number.code">{{ itemYear.year }} -
                    {{ itemYear.value | number}}</p>
                  <p *ngIf="itemC.type === dataType.File.code && itemYear.value">{{ itemYear.year }} -
                    {{ itemYear.value }}
                  </p>
                </div>
              </ng-template>
            </td>
            <td>
              <div *ngIf="!itemC.isChangeByYear; else changeByYear2">
                <p *ngIf="itemC.type === dataType.String.code">
                  {{dataObjectNew[i].childs[j].value}}
                </p>
                <p *ngIf="itemC.type === dataType.Number.code">
                  {{dataObjectNew[i].childs[j].value | number}}
                </p>
                <p *ngIf="itemC.type === dataType.Date.code">
                  {{dataObjectNew[i].childs[j].value | date: 'dd/MM/yyyy HH:mm'}}
                </p>
                <p *ngIf="itemC.type === dataType.List.code">
                  {{ convertTypeListFromValue(dataObjectNew[i].childs[j].value, itemC.__supplierCapacityListDetails__)
                  }}
                </p>
                <p *ngIf="itemC.type === dataType.File.code && itemC.value">
                  {{ dataObjectNew[i].childs[j].value }}
                </p>
              </div>
              <ng-template #changeByYear2>
                <div *ngFor="let itemYear of dataObjectNew[i].childs[j].listDetailYear">
                  <p *ngIf="item.type === dataType.String.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value }}
                  </p>
                  <p *ngIf="item.type === dataType.Number.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value | number}}
                  </p>
                  <p *ngIf="item.type === dataType.File.code && itemYear.value">
                    Năm {{ itemYear.year }} - {{ itemYear.value }}
                  </p>
                </div>
              </ng-template>

            </td>
            <td>{{ dataObjectNew[i].childs[j].comment }}</td>
          </tr>
        </ng-container>
      </ng-container>
    </tbody>
  </nz-table>
</nz-row>

<nz-row class="mt-3">
  <h4>Ghi chú thẩm định năng lực:</h4>
  <p>{{comment}}</p>
</nz-row>