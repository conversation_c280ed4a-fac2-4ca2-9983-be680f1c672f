import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { SupplierExpertiseDetailComponent } from './supplier-expertise-detail-model/supplier-expertise-detail.component'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { SupplierExpertisePrintComponent } from './supplier-expertise-print/supplier-expertise-print.component'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './supplier-expertise.component.html' })
export class SupplierExpertiseComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataServiceChild: any[] = []
  dataServiceChildLast: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  dataCapacityStatus = this.coreService.convertObjToArray(enumData.SupplierExpertiseCapacityStatus)
  dataLawStatus = this.coreService.convertObjToArray(enumData.SupplierExpertiseLawStatus)
  dataExpertiseStatus = this.coreService.convertObjToArray(enumData.SupplierExpertiseStatus)
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.EXPERTISE_003.code
    this.action = this.enumProject.Action
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
  }

  async searchData(reset = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.expertiseStatus && this.dataSearch.expertiseStatus !== '') {
      where.expertiseStatus = this.dataSearch.expertiseStatus
    }

    if (this.dataSearch.lawStatus && this.dataSearch.lawStatus !== '') {
      where.lawStatus = this.dataSearch.lawStatus
    }

    if (this.dataSearch.capacityStatus && this.dataSearch.capacityStatus !== '') {
      where.capacityStatus = this.dataSearch.capacityStatus
    }

    if (this.dataSearch.supplierName && this.dataSearch.supplierName !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.SUPPLIER_EXPERTISE.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  checkFinish(item: any) {
    let result = false
    if (item.finish && item.isApproved && item.status === enumData.SupplierExpertiseStatus.DangThamDinh.code) {
      result = true
    }
    return result
  }

  checkCancel(item: any) {
    let result = false
    if (item.status === enumData.SupplierExpertiseStatus.DangThamDinh.code) {
      result = true
    }
    return result
  }

  /** Chi tiết thẩm định */
  showDetail(item: any) {
    this.dialog
      .open(SupplierExpertiseDetailComponent, { data: { supplierExpertiseId: item.id } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData(true)
      })
  }

  /** Hoàn tất thẩm định */
  onSave(expertiseId: string) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SUPPLIER_EXPERTISE.FINISH, { expertiseId }).then((result) => {
      this.notifyService.showSuccess(result.message)
      this.searchData(true)
    })
  }

  /** Huỷ yêu cầu thẩm định */
  onCancel(expertiseId: string) {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.SUPPLIER_EXPERTISE.CANCEL, {
        expertiseId,
        comment: '',
      })
      .then((result) => {
        this.notifyService.showSuccess(result.message)
        this.searchData(true)
      })
  }

  onPrint(item: any) {
    const param = {
      supplierName: item.supplierName,
      expertiseDate: item.changeDate,
      lawName: item.approvedLawName,
      capacityName: item.approvedCapacityName,
      memberName: item.memberName,
      supplierExpertiseId: item.id,
    }
    this.dialog.open(SupplierExpertisePrintComponent, {
      disableClose: false,
      data: param,
    })
  }
}
