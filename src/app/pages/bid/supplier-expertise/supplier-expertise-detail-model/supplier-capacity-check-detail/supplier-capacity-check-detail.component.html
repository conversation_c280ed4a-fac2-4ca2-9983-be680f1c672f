<nz-row nzGutter="2" class="mt-2">
  <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
    [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tiêu chí</th>
        <th>Kiểu dữ liệu</th>
        <th>Nhà cung cấp đã đăng ký</th>
        <th>Yêu cầu điều chỉnh</th>
        <th><PERSON><PERSON>h giá chung</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let item of listOfData; let i = index">
        <tr>
          <td class="mw-25">{{ item.name }}</td>
          <td>{{ item.type }}</td>
          <td>
            <div *ngIf="!item.isChangeByYear; else changeByYear1">
              <p *ngIf="item.type === dataType.String.code">{{ item.value }}</p>
              <p class="text-right" *ngIf="item.type === dataType.Number.code">{{ item.value | number }}</p>
              <p *ngIf="item.type === dataType.Date.code">{{ item.value | date: 'dd/MM/yyyy' }}</p>
              <p *ngIf="item.type === dataType.List.code">
                {{ convertTypeList(item.__supplierCapacityListDetails__) }}
              </p>
              <p *ngIf="item.type === dataType.File.code && item.value">
                <a href="{{ item.value }}" target="_blank">
                  <span nz-icon nzType="file-text"></span>
                  Xem file đính kèm
                </a>
              </p>
            </div>
            <ng-template #changeByYear1>
              <p nz-popover [nzPopoverContent]="contentTemplate">
                Chi tiết
              </p>
              <ng-template #contentTemplate>
                <div *ngFor="let itemYear of item.__supplierCapacityYearValues__">
                  <p *ngIf="item.type === dataType.String.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value }}
                  </p>
                  <p *ngIf="item.type === dataType.Number.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                  </p>
                  <p *ngIf="item.type === dataType.File.code && itemYear.value">
                    <a href="{{ itemYear.value }}" target="_blank">
                      Năm {{ itemYear.year }} -
                      <span nz-icon nzType="file-text"></span> Xem file đính kèm
                    </a>
                  </p>
                </div>
              </ng-template>
            </ng-template>
          </td>
          <td>
            <div *ngIf="!item.isChangeByYear; else changeByYear2">
              <div *ngIf="item.type === dataType.String.code && item.__childs__.length === 0">
                <input [readonly]="!isEnableBtnSave" nz-input [(ngModel)]="dataObjectNew[i].value"
                  placeholder="Nhập giá trị cần điều chỉnh cho tiêu chí này" />
              </div>
              <div *ngIf="item.type === dataType.Number.code && item.__childs__.length === 0">
                <input [readonly]="!isEnableBtnSave" nz-input currencyMask [(ngModel)]="dataObjectNew[i].value"
                  placeholder="Nhập giá trị cần điều chỉnh cho tiêu chí này" />
              </div>
              <div *ngIf="item.type === dataType.Date.code && item.__childs__.length === 0">
                <nz-date-picker [nzDisabled]="!isEnableBtnSave" [(ngModel)]="dataObjectNew[i].value">
                </nz-date-picker>
              </div>
              <div *ngIf="item.type === dataType.List.code && item.__childs__.length === 0">
                <nz-select [nzDisabled]="!isEnableBtnSave" nzShowSearch nzAllowClear
                  [(ngModel)]="dataObjectNew[i].value">
                  <nz-option *ngFor="let itemList of item.__supplierCapacityListDetails__" [nzLabel]="itemList.name"
                    [nzValue]="itemList.id"></nz-option>
                </nz-select>
              </div>
            </div>
            <ng-template #changeByYear2>
              <div *ngFor="let detailYear of dataObjectNew[i].listDetailYear; index as i2;" nz-col nzSpan="24"
                class="form-item">
                <nz-input-group nzCompact class="change-by-year-item">
                  <input nz-input currencyMask placeholder="Năm" [(ngModel)]="detailYear.year"
                    [ngModelOptions]="{standalone: true}" class="year" [disabled]="!isEnableBtnSave" />
                  <input *ngIf="item.type === dataType.String.code" nz-input [(ngModel)]="detailYear.value"
                    [ngModelOptions]="{standalone: true}" class="value" placeholder="Giá trị"
                    [disabled]="!isEnableBtnSave" />
                  <input *ngIf="item.type === dataType.Number.code" nz-input currencyMask placeholder="Giá trị"
                    [(ngModel)]="detailYear.value" [ngModelOptions]="{standalone: true}" class="value"
                    [disabled]="!isEnableBtnSave" />

                  <input *ngIf="item.type === dataType.File.code" nz-input placeholder="Chọn file" disabled
                    [ngModelOptions]="{standalone: true}" class="value" [(ngModel)]="detailYear.value" />
                  <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(dataObjectNew[i], i2)"
                    [disabled]="!isEnableBtnSave">
                    <span nz-icon nzType="minus"></span>
                  </button>
                  <label *ngIf="item.type === dataType.File.code" [for]="'zen' + item.id + i2"
                    class="custom-file-upload">
                    <span nz-icon nzType="upload"></span> Upload File
                  </label>
                  <input *ngIf="item.type === dataType.File.code" class="hidden" type="file" [id]="'zen' + item.id + i2"
                    (change)="handleFileInput($event, detailYear)" [disabled]="!isEnableBtnSave">

                </nz-input-group>
              </div>
              <nz-col nzSpan="24" class="form-item">
                <button nz-button type="button" nzType="dashed" class="add-change-by-year"
                  (click)="addChangeByYear(dataObjectNew[i])" [disabled]="!isEnableBtnSave">
                  <span nz-icon nzType="plus"></span>
                  Thêm giá trị
                </button>
              </nz-col>
            </ng-template>
          </td>
          <td>
            <input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew[i].comment" name="comment" nz-input
              placeholder="Nhập đánh giá" />
          </td>
        </tr>
        <ng-container>
          <tr *ngFor="let itemC of item.__childs__; let j = index">
            <td [nzIndentSize]="20">{{ itemC.name }}</td>
            <td>{{ itemC.type }}</td>
            <td>
              <div *ngIf="!itemC.isChangeByYear; else changeByYear1">
                <p *ngIf="itemC.type === dataType.String.code">{{ itemC.value }}</p>
                <p class="text-right" *ngIf="itemC.type === dataType.Number.code">{{ itemC.value | number }}</p>
                <p *ngIf="itemC.type === dataType.Date.code">{{ itemC.value | date: 'dd/MM/yyyy' }}</p>
                <p *ngIf="itemC.type === dataType.List.code">
                  {{ convertTypeList(itemC.__supplierCapacityListDetails__) }}
                </p>
                <p *ngIf="itemC.type === dataType.File.code && itemC.value">
                  <a href="{{ itemC.value }}" target="_blank">
                    <span nz-icon nzType="file-text"></span> Xem file đính kèm
                  </a>
                </p>
              </div>
              <ng-template #changeByYear1>
                <p nz-popover [nzPopoverContent]="contentTemplateC">
                  Chi tiết
                </p>
                <ng-template #contentTemplateC>
                  <div *ngFor="let itemYear of itemC.__supplierCapacityYearValues__">
                    <p *ngIf="itemC.type === dataType.String.code">
                      {{ itemYear.year }} - {{ itemYear.value }}
                    </p>
                    <p *ngIf="itemC.type === dataType.Number.code">
                      {{ itemYear.year }} - {{ itemYear.value | number }}
                    </p>
                    <p *ngIf="itemC.type === dataType.File.code && itemYear.value">
                      <a href="{{ itemYear.value }}" target="_blank">{{ itemYear.year }} -
                        <span nz-icon nzType="file-text"></span> Xem file đính kèm
                      </a>
                    </p>
                  </div>
                </ng-template>
              </ng-template>
            </td>
            <td>
              <div *ngIf="!itemC.isChangeByYear; else changeByYear2">
                <div *ngIf="itemC.type === dataType.String.code">
                  <input [readonly]="!isEnableBtnSave" nz-input [(ngModel)]="dataObjectNew[i].childs[j].value"
                    name="value" placeholder="Nhập giá trị cần điều chỉnh cho tiêu chí này" />
                </div>
                <div *ngIf="itemC.type === dataType.Number.code">
                  <input [readonly]="!isEnableBtnSave" nz-input currencyMask
                    [(ngModel)]="dataObjectNew[i].childs[j].value" name="value"
                    placeholder="Nhập giá trị cần điều chỉnh cho tiêu chí này" />
                </div>
                <div *ngIf="itemC.type === dataType.Date.code">
                  <nz-date-picker [nzDisabled]="!isEnableBtnSave" [(ngModel)]="dataObjectNew[i].childs[j].value"
                    name="value"> </nz-date-picker>
                </div>
                <div *ngIf="itemC.type === dataType.List.code">
                  <nz-select [nzDisabled]="!isEnableBtnSave" nzShowSearch nzAllowClear
                    [(ngModel)]="dataObjectNew[i].childs[j].value" name="value">
                    <nz-option *ngFor="let itemList of itemC.__supplierCapacityListDetails__" [nzLabel]="itemList.name"
                      [nzValue]="itemList.id"></nz-option>
                  </nz-select>
                </div>
              </div>
              <ng-template #changeByYear2>
                <div *ngFor="let detailYear of dataObjectNew[i].childs[j].listDetailYear; index as j2;" nz-col
                  nzSpan="24" class="form-item">
                  <nz-input-group nzCompact class="change-by-year-item">
                    <input nz-input currencyMask placeholder="Năm" [(ngModel)]="detailYear.year"
                      [ngModelOptions]="{standalone: true}" class="year" [disabled]="!isEnableBtnSave" />
                    <input *ngIf="itemC.type === dataType.String.code" nz-input [(ngModel)]="detailYear.value"
                      [ngModelOptions]="{standalone: true}" class="value" placeholder="Giá trị"
                      [disabled]="!isEnableBtnSave" />
                    <input *ngIf="itemC.type === dataType.Number.code" nz-input currencyMask placeholder="Giá trị"
                      [disabled]="!isEnableBtnSave" [(ngModel)]="detailYear.value" [ngModelOptions]="{standalone: true}"
                      class="value" />

                    <input *ngIf="itemC.type === dataType.File.code" nz-input placeholder="Chọn file" disabled
                      [ngModelOptions]="{standalone: true}" class="value" [(ngModel)]="detailYear.value" />
                    <button nz-button type="button" nzType="primary"
                      (click)="deleteChangeByYear(dataObjectNew[i].childs[j], j2)" [disabled]="!isEnableBtnSave">
                      <span nz-icon nzType="minus"></span>
                    </button>
                    <label *ngIf="itemC.type === dataType.File.code" [for]="'zen' + itemC.id + j2"
                      class="custom-file-upload">
                      <span nz-icon nzType="upload"></span> Upload File
                    </label>
                    <input *ngIf="itemC.type === dataType.File.code" class="hidden" type="file"
                      [disabled]="!isEnableBtnSave" [id]="'zen' + itemC.id + j2"
                      (change)="handleFileInput($event, detailYear)">

                  </nz-input-group>
                </div>
                <nz-col nzSpan="24" class="form-item">
                  <button nz-button type="button" nzType="dashed" class="add-change-by-year"
                    (click)="addChangeByYear(dataObjectNew[i].childs[j])" [disabled]="!isEnableBtnSave">
                    <span nz-icon nzType="plus"></span>
                    Thêm giá trị
                  </button>
                </nz-col>
              </ng-template>
            </td>
            <td>
              <input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew[i].childs[j].comment" name="comment"
                nz-input placeholder="Nhập đánh giá" />
            </td>
          </tr>
        </ng-container>
      </ng-container>
    </tbody>
  </nz-table>
</nz-row>
<nz-row class="mt-3">
  <h4>{{ language_key?.NOTE || 'Ghi chú' }} :</h4>
  <nz-col nzSpan="24" class="text-center">
    <textarea nz-input rows="2" [readonly]="!isEnableBtnSave" auto placeholder="Nhập nhận xét" [(ngModel)]="comment"
      name="comment">
    </textarea>
  </nz-col>
</nz-row>
<nz-row class="mt-3">
  <nz-col nzSpan="24" class="text-center">
    <button *ngIf="isEnableBtnSave" nz-button nzType="primary" (click)="onSave()">
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
  </nz-col>
</nz-row>