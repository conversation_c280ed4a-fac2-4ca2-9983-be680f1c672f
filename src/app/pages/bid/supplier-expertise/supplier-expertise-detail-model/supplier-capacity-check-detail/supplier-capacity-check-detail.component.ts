import { Component, OnInit, Input } from '@angular/core'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({
  selector: 'app-supplier-capacity-check-detail',
  templateUrl: './supplier-capacity-check-detail.component.html',
  styleUrls: ['./supplier-capacity-check-detail.component.scss'],
})
export class SupplierCapacityCheckDetailComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  listOfData: any[] = []
  dataObjectNew: any[] = []
  maxSizeUpload = enumData.maxSizeUpload

  loading = false
  dataService: any[] = []
  isEnableBtnSave = false
  dataType = enumData.DataType

  dataHistoryCapacity: any[] = []
  comment: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  supplierExpertiseId: any
  @Input()
  dialogRef: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.supplierExpertiseId) return
    this.loadDataList()
  }

  loadDataList() {
    this.loading = true
    this.apiService.get(this.apiService.SUPPLIER_EXPERTISE.GET_CAPACITY(this.supplierExpertiseId), {}).then(async (data) => {
      if (data) {
        this.loading = false
        this.listOfData = data.supplierCapacity || []
        this.comment = data.supplierExpertise.commentCapacity
        this.dataHistoryCapacity = data.supplierExpertise.__supplierExpertiseDetails__
        for (const item of this.listOfData) {
          const itemValue: any = {}
          itemValue.supplierCapacityId = item.id
          itemValue.name = item.name
          itemValue.isChangeByYear = item.isChangeByYear
          itemValue.sort = item.sort
          itemValue.type = item.type
          itemValue.childs = []
          if (itemValue.isChangeByYear) {
            itemValue.listDetailYear = []
          }
          const history = await this.getHistoryCapacity(item)
          if (history) {
            itemValue.value = history.value
            itemValue.listDetailYear = history.listDetailYear
            itemValue.comment = history.comment
          }

          for (const itemC of item.__childs__) {
            const itemValueC: any = {}
            itemValueC.supplierCapacityId = itemC.id
            itemValueC.sort = itemC.sort
            itemValueC.isChangeByYear = itemC.isChangeByYear
            itemValueC.name = itemC.name
            itemValueC.type = itemC.type
            if (itemValueC.isChangeByYear) {
              itemValueC.listDetailYear = []
            }
            const historyC = await this.getHistoryCapacity(itemC)
            if (historyC) {
              itemValueC.value = historyC.value
              itemValueC.listDetailYear = historyC.listDetailYear
              itemValueC.comment = historyC.comment
            }
            itemValue.childs.push(itemValueC)
          }
          this.dataObjectNew.push(itemValue)
        }

        if (
          data.supplierExpertise.isCheckCapacity &&
          data.capacity &&
          (data.supplierExpertise.statusCapacity === enumData.SupplierExpertiseCapacityStatus.ChuaThamDinh.code ||
            data.supplierExpertise.statusCapacity === enumData.SupplierExpertiseCapacityStatus.DaThamDinh.code) &&
          data.supplierExpertise.status === enumData.SupplierExpertiseStatus.DangThamDinh.code
        ) {
          this.isEnableBtnSave = true
        }
      }
    })
  }

  onSave() {
    this.notifyService.showloading()
    const isNotRequireEdit = !this.dataObjectNew.some(
      (c) =>
        (c.value != null && c.value != undefined) ||
        c.listDetailYear?.length > 0 ||
        c.childs.some((d: any) => (d.value != null && d.value != undefined) || d.listDetailYear?.length > 0)
    )
    this.apiService
      .post(this.apiService.SUPPLIER_EXPERTISE.SAVE_CAPACITY, {
        expertiseId: this.supplierExpertiseId,
        comment: this.comment,
        isNotRequireEdit,
        detail: this.dataObjectNew,
      })
      .then((result) => {
        this.notifyService.showSuccess(result.message)
        this.isEnableBtnSave = false
        this.dialogRef.close(1)
      })
  }

  convertTypeList(list: any[]) {
    if (typeof list !== 'undefined') {
      const data = list.find((p: any) => p.isChosen)
      if (data) return data.name
    }

    return ''
  }

  /** Lấy giá trị yêu cầu điều chỉnh và đánh giá đi kèm */
  private async getHistoryCapacity(obj: any) {
    const findData: any = this.dataHistoryCapacity.find((p) => p.supplierCapacityId === obj.id)
    if (findData) {
      if (!obj.isChangeByYear) {
        if (obj.value !== findData.value) {
          return { value: findData.value, comment: findData.comment }
        }
      } else {
        return { listDetailYear: findData.__supplierExpertiseYearDetails__ || [], comment: findData.comment }
      }
    }
    return
  }

  addChangeByYear(item: any) {
    if (!item.listDetailYear) item.listDetailYear = []
    item.listDetailYear.push({
      year: null,
      type: item.type,
      value: item.type === enumData.DataType.Number.code ? null : '',
    })
  }

  deleteChangeByYear(item: any, index: any) {
    item.listDetailYear.splice(index, 1)
  }

  handleFileInput(event: any, rowData: any) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) rowData.value = res[0]
        else rowData.value = ''
      })
    }
  }
}
