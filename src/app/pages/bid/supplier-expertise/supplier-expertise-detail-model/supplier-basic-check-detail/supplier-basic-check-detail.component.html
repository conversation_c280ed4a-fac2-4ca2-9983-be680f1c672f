<form nz-form #frmAdd="ngForm">
  <nz-row nzGutter="2" class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="['']" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th>Thông tin</th>
          <th>Nhà cung cấp đã đăng ký</th>
          <th>Yêu cầu điều chỉnh</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{language_key?.INTERPRISE_CODE || 'Mã doanh nghiệp' }}</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.code" name="code" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.code" name="code2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Tên chính thức</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.name" name="name" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.name" name="name2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Tên giao dịch</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.dealName" name="dealName" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.dealName" name="dealName2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Địa chỉ trụ sở</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.address" name="address" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.address" name="address2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Địa chỉ giao dịch</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.dealAddress" name="dealAddress" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.dealAddress" name="dealAddress2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Giấy phép ĐKKD</td>
          <td>
            <a *ngIf="dataObject.fileMST" href="{{dataObject.fileMST}}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </td>
          <td>
          </td>
        </tr>

        <tr>
          <td>Đại diện pháp luật</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.represen" name="represen" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.represen" name="represen2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Tên giám đốc</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.chief" name="chief" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.chief" name="chief2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Số tài khoản</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.bankNumber" name="bankNumber" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.bankNumber" name="bankNumber2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Ngân hàng</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.bankname" name="bankname" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.bankname" name="bankname2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>
            {{language_key?.BRANCH || 'Chi nhánh' }}
          </td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.bankBrand" name="bankBrand" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.bankBrand" name="bankBrand2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Thông báo mở TK</td>
          <td>
            <a *ngIf="dataObject.fileAccount" href="{{dataObject.fileAccount}}" target="_blank">
              <span nz-icon nzType="file-text"></span> {{dataObject.fileAccount ? 'Xem file đính kèm' : 'Không có'}}
            </a>
          </td>
          <td>
          </td>
        </tr>

        <tr>
          <td>Người liên hệ</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.contactName" name="contactName" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.contactName" name="contactName2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Email</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.email" name="email" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.email" name="email2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Điện thoại</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.phone" name="phone" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.phone" name="phone2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Năm thành lập</td>
          <td>
            <input nz-input readonly [(ngModel)]="dataObject.createYear" name="createYear" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.createYear" name="createYear2"
                placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Vốn điều lệ (tỷ đồng)</td>
          <td class="text-right">
            <input nz-input currencyMask readonly [(ngModel)]="dataObject.capital" name="capital" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input currencyMask [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.capital"
                name="capital2" placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>Tài sản cố định (tỷ đồng)</td>
          <td class="text-right">
            <input nz-input currencyMask readonly [(ngModel)]="dataObject.assets" name="assets" />
          </td>
          <td>
            <nz-form-control nzSpan="24" class="mr-3">
              <input nz-input currencyMask [readonly]="!isEnableBtnSave" [(ngModel)]="dataObjectNew.assets"
                name="assets2" placeholder="Nhập giá trị cần điều chỉnh cho mục thông tin này" />
            </nz-form-control>
          </td>
        </tr>

        <tr>
          <td>HĐ mẫu/phiếu thu/biên lai</td>
          <td>
            <a *ngIf="dataObject.fileBill" href="{{dataObject.fileBill}}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </td>
          <td>
          </td>
        </tr>

        <tr>
          <td>Thông tin phát hành HĐ</td>
          <td>
            <a *ngIf="dataObject.fileInfoBill" href="{{dataObject.fileInfoBill ? dataObject.fileInfoBill : '#'}}"
              target="_blank">
              <span nz-icon nzType="file-text"></span> {{dataObject.fileInfoBill ? 'Xem file đính kèm' : 'Không có'}}
            </a>
          </td>
          <td>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
  <nz-row class="mt-3">
    <h4>{{language_key?.NOTE || 'Ghi chú' }} :</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" [readonly]="!isEnableBtnSave" auto placeholder="Nhập nhận xét" [(ngModel)]="comment"
        name="comment">
      </textarea>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <nz-col nzSpan="24" class="text-center">
      <button *ngIf="isEnableBtnSave" nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>