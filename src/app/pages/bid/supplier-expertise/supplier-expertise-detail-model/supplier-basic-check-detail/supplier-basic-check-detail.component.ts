import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-supplier-basic-check-detail',
  templateUrl: './supplier-basic-check-detail.component.html',
})
export class SupplierBasicCheckDetailComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  dataObject: any = {}
  dataObjectNew: any = {}
  loading = false
  isEnableBtnSave = false
  comment = ''
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  supplierExpertiseId: any
  @Input()
  dialogRef: any

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.supplierExpertiseId) return
    this.loadDetail()
  }

  loadDetail() {
    this.loading = true
    this.apiService.get(this.apiService.SUPPLIER_EXPERTISE.GET_LAW(this.supplierExpertiseId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataObject = data.supplierExpertise.__supplier__
        this.comment = data.supplierExpertise.comment
        this.dataObjectNew = {}
        if (data.supplierExpertise.__supplierExpertiseLawDetails__?.length > 0) {
          this.dataObjectNew = data.supplierExpertise.__supplierExpertiseLawDetails__[0]
        }
        if (
          data.supplierExpertise.isCheckLaw &&
          data.legal &&
          (data.supplierExpertise.statusLaw === enumData.SupplierExpertiseLawStatus.ChuaThamDinh.code ||
            data.supplierExpertise.statusLaw === enumData.SupplierExpertiseLawStatus.DaThamDinh.code) &&
          data.supplierExpertise.status === enumData.SupplierExpertiseStatus.DangThamDinh.code
        ) {
          this.isEnableBtnSave = true
        }
      }
    })
  }

  onSave() {
    this.notifyService.showloading()
    let countProp = 0
    for (const prop in this.dataObjectNew) {
      if (this.dataObjectNew[prop] != null && this.dataObjectNew[prop] != undefined && this.dataObjectNew[prop] != '') {
        countProp++
      }
    }
    if (countProp == 0) this.dataObjectNew.isNotRequireEdit = true
    this.dataObjectNew.assets = +this.dataObjectNew.assets
    this.dataObjectNew.capital = +this.dataObjectNew.capital

    this.apiService
      .post(this.apiService.SUPPLIER_EXPERTISE.SAVE_LAW, { expertiseId: this.supplierExpertiseId, detail: this.dataObjectNew, comment: this.comment })
      .then((result) => {
        this.notifyService.showSuccess(result.message)
        this.isEnableBtnSave = false
        this.dialogRef.close(1)
      })
  }
}
