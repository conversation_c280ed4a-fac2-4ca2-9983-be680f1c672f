import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './supplier-expertise-detail.component.html' })
export class SupplierExpertiseDetailComponent implements OnInit {
  isEnableLaw = false
  isEnableCapacity = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    public dialogRef: MatDialogRef<SupplierExpertiseDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.checkPermission()
  }

  checkPermission() {
    this.apiService.get(this.apiService.SUPPLIER_EXPERTISE.GET_PERMISSION(this.data.supplierExpertiseId), {}).then((result) => {
      if (!result.capacity && !result.legal) {
        this.notifyService.showError('Bạn không có quyền truy cập!')
        this.dialogRef.close(0)
      }

      this.isEnableLaw = true
      if (result.capacity) {
        this.isEnableCapacity = true
      }
    })
  }
}
