import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { BidTradeListDetailComponent } from './bid-trade-list-detail/bid-trade-list-detail.component'
import { AddOrEditBidTradeComponent } from './add-or-edit-bid-trade/add-or-edit-bid-trade.component'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-trade.component.html' })
export class BidTradeComponent implements OnInit {
  modalTitle = 'Thông tin điều kiện thương mại'
  loading = false
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  pageSize = enumData.Page.pageSizeMax
  dicActiveCollapse: any = {}
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidTradeComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.searchData()
  }

  async searchData() {
    this.loading = true
    await this.apiService.get(this.apiService.BID.GET_TRADE(this.data.id), {}).then(async (res) => {
      this.loading = false
      this.data = res
    })
  }

  async loadTrade(item: any) {
    this.loading = true
    await this.apiService.get(this.apiService.BID.LOADTRADE(item.id), {}).then(() => {
      this.loading = false
      this.searchData()
    })
  }

  checkData() {
    let strErr = ''
    for (const item of this.data.listItem) {
      let sumPercent = 0
      for (const v of item.listTrade) {
        if (v.type === enumData.DataType.List.code && v.__bidTradeListDetails__.length === 0) {
          strErr += `Tiêu chí [${v.name}] của Item [${item.itemName}] chưa thiết lập danh sách!<br>`
        }

        const lstChildTypeList = v.__childs__.filter((c: any) => c.type === enumData.DataType.List.code && c.__bidTradeListDetails__.length === 0)
        for (const x of lstChildTypeList) {
          strErr += `*** Tiêu chí [${x.name}] thuộc tiêu chí cấp 1 [${v.name}] của Item [${item.itemName}] chưa thiết lập danh sách!<br>`
        }

        const lstChildCalPercent = v.__childs__.filter((c: any) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
        if (lstChildCalPercent.length > 0) {
          v.sumPercent = 0
          for (const x of lstChildCalPercent) {
            if (x.percent > 0) {
              v.sumPercent += x.percent
            }
          }
          if (v.sumPercent < 100) {
            strErr += `Tổng tỉ trọng tiêu chí [${v.name}] của Item [${item.itemName}] chưa đủ 100%!<br>`
          }
          if (v.sumPercent > 100) {
            strErr += `Tổng tỉ trọng tiêu chí [${v.name}] của Item [${item.itemName}] vượt quá 100%!<br>`
          }
        }

        if (v.percent > 0) {
          sumPercent += v.percent
        }
      }

      if (sumPercent > 100) strErr += `Tổng tỉ trọng Item [${item.itemName}] vượt quá 100%!<br>`
    }

    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      return false
    }
    return true
  }

  async createTrade() {
    this.notifyService.showloading()
    if (this.checkData()) {
      await this.apiService.post(this.apiService.BID.CREATE_TRADE(this.data.id), this.data).then(async (res) => {
        if (res) {
          this.notifyService.hideloading()
          this.closeDialog(true)
        }
      })
    }
  }

  settingList(object: any) {
    object.isMPO = this.data.isMPO
    this.dialog
      .open(BidTradeListDetailComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => this.searchData())
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  clickAdd(item: any) {
    const itemTrade = {
      name: '',
      type: enumData.DataType.String.code,
      isRequired: true,
      isCalUp: true,
      bidId: item.id,
    }
    this.dialog
      .open(AddOrEditBidTradeComponent, { disableClose: false, data: itemTrade })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(itemTech: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    itemTech.isMPO = this.data.isMPO
    this.dialog
      .open(AddOrEditBidTradeComponent, { disableClose: false, data: itemTech })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickDelete(itemTech: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.BIDTRADE_DELETE, { id: itemTech.id }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData()
    })
  }

  clickDeleteAll(item: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.BIDTRADE_DELETEALL, { id: item.id }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData()
    })
  }

  clickExportExcel(item: any) {
    this.notifyService.showloading()
    let lstDataExport: any[] = []

    //#region header
    const title: any = {
      zenId: 'CẤU HÌNH TEMPLATE',
      sort: '',
      name: '',
      percent: '',
      percentRule: '',
      type: '',
      isRequired: '',
      isCalUp: '',
      percentDownRule: '',
      blank: '',
      zenListId: 'CẤU HÌNH CÁC TRƯỜNG DANH SÁCH',
      nameList: '',
      valueList: '',
    }
    lstDataExport.push(title)

    const numColTable1 = 9
    const header: any = {
      zenId: 'Cột',
      sort: this.language_key?.NO || 'STT',
      name: 'Tên ĐKTM',
      percent: 'Tỉ trọng (%)',
      percentRule: 'Giá trị đạt',
      type: 'Kiểu dữ liệu',
      isRequired: 'Bắt buộc?',
      isCalUp: 'Tính điểm thuận?',
      percentDownRule: 'Điều kiện liệt tỉ trọng',
      blank: '',
      zenListId: 'Cột',
      nameList: 'Tên',
      valueList: '% Tỉ trọng',
    }
    lstDataExport.push(header)
    //#endregion

    //#region custom data before export
    let i1 = 1
    const lstDataTable1: any[] = []
    const lstDataTable2: any[] = []
    const lstDataLv1 = item.listTrade
    for (const data1 of lstDataLv1) {
      const dataTable1Lv1: any = {}
      dataTable1Lv1.zenId = '' + i1
      i1++
      dataTable1Lv1.sort = data1.sort > 0 ? data1.sort : ''
      dataTable1Lv1.name = data1.name
      dataTable1Lv1.percent = data1.percent
      dataTable1Lv1.percentRule = data1.percentRule
      dataTable1Lv1.type = data1.type
      dataTable1Lv1.isRequired = data1.isRequired
      dataTable1Lv1.isCalUp = data1.isCalUp
      dataTable1Lv1.percentDownRule = data1.percentDownRule

      lstDataTable1.push(dataTable1Lv1)

      if (data1.__bidTradeListDetails__ && data1.__bidTradeListDetails__.length > 0) {
        for (const detail of data1.__bidTradeListDetails__) {
          const dataTable2Lv1: any = {}
          dataTable2Lv1.zenListId = dataTable1Lv1.zenId
          dataTable2Lv1.nameList = detail.name
          dataTable2Lv1.valueList = detail.value
          lstDataTable2.push(dataTable2Lv1)
        }
      }

      let i2 = 1
      const lstDataLv2 = data1.__childs__
      for (const data2 of lstDataLv2) {
        const dataTable1Lv2: any = {}
        dataTable1Lv2.zenId = dataTable1Lv1.zenId + '.' + i2
        i2++
        dataTable1Lv2.sort = data2.sort > 0 ? data2.sort : ''
        dataTable1Lv2.name = data2.name
        dataTable1Lv2.percent = data2.percent
        dataTable1Lv2.percentRule = data2.percentRule
        dataTable1Lv2.type = data2.type
        dataTable1Lv2.isRequired = data2.isRequired
        dataTable1Lv2.isCalUp = data2.isCalUp
        dataTable1Lv2.percentDownRule = data2.percentDownRule
        lstDataTable1.push(dataTable1Lv2)

        if (data2.__bidTradeListDetails__ && data2.__bidTradeListDetails__.length > 0) {
          for (const detail of data2.__bidTradeListDetails__) {
            const dataTable2Lv2: any = {}
            dataTable2Lv2.zenListId = dataTable1Lv2.zenId
            dataTable2Lv2.nameList = detail.name
            dataTable2Lv2.valueList = detail.value
            lstDataTable2.push(dataTable2Lv2)
          }
        }
      }
    }

    //#endregion

    let numRowData = lstDataTable1.length > lstDataTable2.length ? lstDataTable1.length : lstDataTable2.length
    for (let i = 0; i < numRowData; i++) {
      const dataTable1 = lstDataTable1[i] || {}
      const dataTable2 = lstDataTable2[i] || {}
      lstDataExport.push({ ...dataTable1, ...dataTable2 })
    }
    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template cấu hình ĐKTM của Item [${item.itemName}] gói thầu [${
      this.data.name
    }].xlsx`
    const sheetName = 'Thương mại'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)
    wb.Sheets[sheetName]['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: numColTable1 - 1 } } /* A1:L1 */,
      { s: { r: 0, c: numColTable1 + 1 }, e: { r: 0, c: numColTable1 + 3 } } /* N1:P1 */,
    ]

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  clickImportExcel(ev: any, item: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'sort',
          'name',
          'percent',
          'percentRule',
          'type',
          'isRequired',
          'isCalUp',
          'percentDownRule',
          'blank',
          'zenListId',
          'nameList',
          'valueList',
        ],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()

      // Kiểm tra header
      if (
        header.zenId !== 'Cột' ||
        header.sort !== (this.language_key?.NO || 'STT') ||
        header.name !== 'Tên ĐKTM' ||
        header.percent !== 'Tỉ trọng (%)' ||
        header.percentRule !== 'Giá trị đạt' ||
        header.type !== 'Kiểu dữ liệu' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.isCalUp !== 'Tính điểm thuận?' ||
        header.percentDownRule !== 'Điều kiện liệt tỉ trọng' ||
        header.zenListId !== 'Cột' ||
        header.nameList !== 'Tên' ||
        header.valueList !== '% Tỉ trọng'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(`File không đúng template ĐKTM của Item [${item.itemName}] gói thầu ${this.data.name}`)
        return
      }

      // Tách và kiểm tra data từng bảng
      const lstDataTable1: any[] = []
      const lstDataTable2: any[] = []
      let strErr = ''
      for (const row of jsonData) {
        // add data table 1
        if (row.zenId != null && row.zenId != '') {
          const dataTable1: any = {}
          dataTable1.zenId = (row.zenId + '').trim()
          dataTable1.sort = row.sort || 0
          dataTable1.name = (row.name || '') + ''
          dataTable1.type = (row.type || '') + ''

          dataTable1.isRequired = row.isRequired
          if (dataTable1.isRequired == null || dataTable1.isRequired === '' || typeof dataTable1.isRequired !== 'boolean') {
            dataTable1.isRequired = false
          }

          dataTable1.isCalUp = row.isCalUp
          if (dataTable1.isCalUp == null || dataTable1.isCalUp === '' || typeof dataTable1.isCalUp !== 'boolean') {
            dataTable1.isCalUp = false
          }
          if (!dataTable1.isCalUp) {
            dataTable1.percentDownRule = row.percentDownRule || 0
          } else {
            dataTable1.percentDownRule = null
          }

          if (dataTable1.type === enumData.DataType.Number.code || dataTable1.type === enumData.DataType.List.code) {
            dataTable1.percent = row.percent || 0
            if (dataTable1.type === enumData.DataType.Number.code) {
              dataTable1.percentRule = row.percentRule
            } else dataTable1.percentRule = null
          } else {
            dataTable1.percent = null
            dataTable1.percentRule = null
          }

          const lstId = dataTable1.zenId.split('.')
          dataTable1.level = lstId.length
          if (dataTable1.level < 1 || dataTable1.level > 2) {
            strErr += `Cột [${dataTable1.zenId}] không hợp lệ, không xác định được level nào<br>`
          }
          for (const id of lstId) {
            try {
              let intId = parseInt(id)
              if (intId <= 0) {
                strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số dương<br>`
              }
            } catch {
              strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số<br>`
            }
          }
          if (dataTable1.level == 2) {
            dataTable1.parentZenId = lstId[0] + ''
            if (!lstDataTable1.some((c) => c.zenId == dataTable1.parentZenId)) {
              strErr += `Không tìm thấy cấp cha của cột [${dataTable1.zenId}] ở phía trên của dòng này<br>`
            }
          }

          if (dataTable1.name.trim() === '') {
            strErr += 'Tên ĐKTM không được để trống<br>'
          }

          if (dataTable1.type.trim() === '') {
            strErr += 'Kiểu dữ liệu không được để trống<br>'
          }
          if (dataTable1.type.trim().length > 0) {
            if (!this.lstDataType.some((c) => c.code === dataTable1.type)) {
              strErr += `Kiểu dữ liệu [${dataTable1.type}] không tồn tại trong [String, Number, List, File, Date]<br>`
            }
          }

          if (dataTable1.percentRule !== null && dataTable1.percentRule <= 0) {
            strErr += 'Giá trị đạt phải là số lớn hơn 0<br>'
          }

          if (dataTable1.percentDownRule !== null && dataTable1.percentDownRule <= 0) {
            strErr += 'Điều kiện liệt tỉ trọng phải là số lớn hơn 0<br>'
          }

          lstDataTable1.push(dataTable1)
        }
      }
      // check percent
      if (lstDataTable1.length > 0) {
        let sumPercent = 0
        const lstDataLv1 = lstDataTable1.filter((c) => c.level == 1)
        for (const dataLv1 of lstDataLv1) {
          if (dataLv1.percent > 0) sumPercent += dataLv1.percent
          var lstDataLv2 = lstDataTable1.filter((c) => c.level == 2 && c.parentZenId == dataLv1.zenId)
          if (lstDataLv2.length > 0) {
            let sumPercentLv2 = 0
            for (const dataLv2 of lstDataLv2) {
              if (dataLv2.percent > 0) sumPercentLv2 += dataLv2.percent
            }
            if (sumPercentLv2 > 100) {
              strErr += `Tổng tỉ trọng tiêu chí [${dataLv1.name}] vượt quá 100%<br>`
            }
          }
        }

        if (sumPercent > 100) {
          strErr += 'Tổng tỉ trọng vượt quá 100%<br>'
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (const row of jsonData) {
        // add data table 2
        if (row.zenListId != null && row.zenListId != '') {
          const dataTable2: any = {}
          dataTable2.zenListId = (row.zenListId + '').trim()
          dataTable2.nameList = (row.nameList || '') + ''
          dataTable2.valueList = row.valueList || 0
          const trade = lstDataTable1.find((c) => c.zenId == dataTable2.zenListId)
          if (!trade) {
            strErr += `Cột [${row.zenListId}] không tồn tại, không xác định thuộc ĐKTM nào<br>`
          }
          if (trade.type != enumData.DataType.List.code) {
            strErr += `Cột [${row.zenListId}] không phải kiểu danh sách<br>`
          }
          if (dataTable2.nameList.trim() === '') {
            strErr += 'Tên không được để trống<br>'
          }
          if (dataTable2.valueList == null || dataTable2.valueList === '') {
            strErr += '% Tỉ trọng không được để trống<br>'
          }

          lstDataTable2.push(dataTable2)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      this.notifyService.showloading()
      this.apiService.post(this.apiService.BID.BIDTRADE_IMPORT(item.id), { lstDataTable1, lstDataTable2 }).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })

      return
    }
  }
}
