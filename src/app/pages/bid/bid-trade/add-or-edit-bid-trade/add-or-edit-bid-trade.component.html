<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Tên điều kiện thương mại</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập tên điều kiện thương mại (3-250 kí tự)!">
            <input nz-input placeholder="Nhập tên điều kiện thương mại (3-250 kí tự)" [(ngModel)]="dataObject.name"
              name="name" required pattern=".{3,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="!dataObject.id">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Thuộc cấp 1</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.parentId" name="parentId"
              nzPlaceHolder="Chọn điều kiện thương mại cấp 1">
              <nz-option *ngFor="let item of dataBidTrade" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Kiểu dữ liệu</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn kiểu dữ liệu!">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.type" name="type" required
              nzPlaceHolder="Chọn kiểu dữ liệu" (ngModelChange)="onCheckShowPercent()">
              <nz-option *ngFor="let item of lstDataType" [nzLabel]="item.code" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="isShowPercent">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Tỉ trọng(%)</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập số từ 0 đến 100!">
            <input nz-input placeholder="Nhập số từ 0 đến 100" [(ngModel)]="dataObject.percent" name="percent"
              type="number" min="0" max="100" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="dataObject.type === dataType.Number.code">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Giá trị đạt</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập số lớn hơn 0!">
            <input nz-input placeholder="Nhập số lớn hơn 0" [(ngModel)]="dataObject.percentRule" name="percentRule"
              currencyMask />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Thứ tự sắp xếp</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng không nhập số nhỏ hơn 0!">
            <input nz-input placeholder="Nhập số" [(ngModel)]="dataObject.sort" name="sort" type="number" min="0" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6" [nzOffset]="6">
        <nz-form-item nzFlex>
          <nz-form-control nzSpan="24">
            <label nz-checkbox [(ngModel)]="dataObject.isRequired" name="isRequired"> Bắt buộc?</label>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-control nzSpan="24">
            <label nz-checkbox [(ngModel)]="dataObject.isCalUp" name="isCalUp" nz-tooltip
              nzTooltipTitle="Hệ thống tự tính điểm theo loại càng cao càng tốt"> Tính điểm thuận? </label>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="!dataObject.isCalUp">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nz-tooltip
            nzTooltipTitle="Tỉ trọng của tiêu chí là 0, nếu lớn hơn giá trị này"> Điều kiện liệt tỉ trọng
          </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập số lớn hơn 0!">
            <input nz-input placeholder="Nhập số" [(ngModel)]="dataObject.percentDownRule" name="percentDownRule"
              currencyMask required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions *ngIf="data.isMPO || !dataObject.id">
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" class="mr-3" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>