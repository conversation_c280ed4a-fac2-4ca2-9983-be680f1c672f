<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <div nzBordered="false" *ngFor="let item of data.listItem" class="mt-2">
    <nz-row *ngIf="data.isTech" nzGutter="8" class="mt-2">
      <nz-col nzSpan="18">
        <button class="mr-2" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button
          (click)="clickAdd(item)" nzType="primary">
          <span nz-icon nzType="plus"></span>
          {{ language_key?.ADD || 'Thêm mới' }}
        </button>
        <button class="mr-2" nz-button nz-popconfirm
          *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
          nzPopconfirmTitle="Bạn có chắc muốn xo<PERSON> tất cả tiêu chí kỹ thuật?" nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="clickDeleteAll(item)" nzDanger>Xoá tất cả</button>
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="mr-2" nz-button
          (click)="clickExportExcel(item)">
          <span nz-icon nzType="download"></span>Xuất excel</button>
        <input *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="hidden" type="file"
          id="file" (change)="clickImportExcel($event, item)" onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file"
          class="lable-custom-file">
          <span nz-icon nzType="upload"></span>
          {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
        </label>
      </nz-col>
      <nz-col nzSpan="6">
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
          (click)="loadTech(item)" class="ant-btn-blue">
          Tải yêu cầu kỹ thuật từ template
        </button>
      </nz-col>
    </nz-row>

    <nz-row class="mt-1">
      <span *ngIf="item.sumPercent < 100" class="text-orange"> Tổng tỉ trọng đạt {{ item.sumPercent }}%, chưa đủ 100%
      </span>
      <nz-table nz-col nzSpan="24" [nzData]="item.listTech" [(nzPageSize)]="pageSize" [nzLoading]="loading"
        [nzShowPagination]="false" nzBordered>
        <thead>
          <!-- header gid level 1 -->
          <tr>
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Tên tiêu chí</th>
            <th>Tỉ trọng(%)</th>
            <th>Giá trị đạt</th>
            <th>Kiểu dữ liệu</th>
            <th>Bắt buộc?</th>
            <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let rowData of item.listTech">
            <tr>
              <td (click)="clickEdit(rowData)">
                {{ rowData.sort > 0 ? rowData.sort : '' }}</td>
              <td (click)="clickEdit(rowData)" class="mw-25">
                {{ rowData.name }}
              </td>
              <td (click)="clickEdit(rowData)" class="text-right">{{ rowData.percent }}</td>
              <td (click)="clickEdit(rowData)" class="text-right">{{ rowData.percentRule | number }}</td>
              <td (click)="clickEdit(rowData)">
                {{ rowData.type }}</td>
              <td (click)="clickEdit(rowData)">
                {{ rowData.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td>
                <button
                  *ngIf="rowData.type === dataType.List.code && authenticationService.checkPermission([enumRole], action.View.code)"
                  nz-tooltip nzTooltipTitle="Thiết lập danh sách" class="mr-2" nz-button
                  [nzType]="rowData.__bidTechListDetails__ && rowData.__bidTechListDetails__.length > 0 ? 'default' : 'dashed'"
                  (click)="settingList(rowData)">
                  <span nz-icon nzType="unordered-list"></span>
                </button>
                <button *ngIf="data.isTech && authenticationService.checkPermission([enumRole], action.Delete.code)"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?" nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="clickDelete(rowData)" nz-tooltip nzTooltipTitle="Xoá tiêu chí" nz-button nzDanger>
                  <span nz-icon nzType="delete"></span>
                </button>
              </td>
            </tr>
            <ng-container>
              <td [nzIndentSize]="10" colspan="8" scope="colgroup"
                *ngIf="rowData.sumPercent < 100 && rowData.sumPercent >= 0" class="text-orange">
                Tổng tỉ trọng trong mục này đạt {{ rowData.sumPercent }}%, chưa đủ 100%
              </td>
              <tr *ngFor="let childData of rowData.__childs__">
                <td (click)="clickEdit(childData)" [nzIndentSize]="10">{{ childData.sort > 0 ? childData.sort : '' }}
                </td>
                <td (click)="clickEdit(childData)" class="mw-25">
                  {{ childData.name }}
                </td>
                <td (click)="clickEdit(childData)" class="text-right">{{ childData.percent }}</td>
                <td (click)="clickEdit(childData)" class="text-right">{{ childData.percentRule | number }}</td>
                <td (click)="clickEdit(childData)">
                  {{ childData.type }}</td>
                <td (click)="clickEdit(childData)">
                  {{ childData.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                <td>
                  <button
                    *ngIf="childData.type === dataType.List.code &&  authenticationService.checkPermission([enumRole], action.View.code)"
                    nz-tooltip nzTooltipTitle="Thiết lập danh sách" class="mr-2" nz-button
                    [nzType]="childData.__bidTechListDetails__ && childData.__bidTechListDetails__.length > 0 ? 'default' : 'dashed'"
                    (click)="settingList(childData)">
                    <span nz-icon nzType="unordered-list"></span>
                  </button>
                  <button *ngIf="data.isTech && authenticationService.checkPermission([enumRole], action.Delete.code)"
                    nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?" nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="clickDelete(childData)" nz-tooltip nzTooltipTitle="Xoá tiêu chí" nz-button nzDanger>
                    <span nz-icon nzType="delete"></span>
                  </button>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </nz-table>
    </nz-row>

  </div>

  <nz-row class="mt-3">
    <h4>Người yêu cầu ghi chú:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'" [(ngModel)]="data.noteTech"
        [disabled]="!data.isShowCreateTech"></textarea>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <h4>Người duyệt ghi chú:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.noteTechLeader" [disabled]="!data.isShowAcceptTech"></textarea>
    </nz-col>
  </nz-row>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="createTech()" class="ant-btn-blue mr-2"
      *ngIf="data.isShowCreateTech && authenticationService.checkPermission([enumRole], action.Update.code)">
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
    <button nz-button (click)="acceptTech()" class="ant-btn-blue mr-2"
      *ngIf="data.isShowAcceptTech && authenticationService.checkPermission([enumRole], action.Update.code)">
      Phê duyệt
    </button>
    <button nz-button (click)="rejectTech()" nzDanger
      *ngIf="data.isShowAcceptTech && authenticationService.checkPermission([enumRole], action.Update.code)">
      Yêu cầu kiểm tra lại
    </button>
  </nz-col>
</nz-row>