import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { BidComponent } from './bid.component'
import { SupplierServiceToExpertiseComponent } from './supplier-service-to-expertise/supplier-service-to-expertise.component'
import { SupplierCapacityComponent } from './supplier-capacity/supplier-capacity.component'
import { SupplierExpertiseComponent } from './supplier-expertise/supplier-expertise.component'
import { BidRateComponent } from './bid-rate/bid-rate.component'
import { BidInfoComponent } from './bid-info/bid-info.component'
import { ReportSupplierComponent } from './report-supplier/report-supplier.component'
import { ReportExpertiseComponent } from './report-expertise/report-expertise.component'
import { ReportBidComponent } from './report-bid/report-bid.component'
import { SupplierManageComponent } from './supplier-manage/supplier-manage.component'
import { ReportHistoryPriceSupplierComponent } from './report-history-price-supplier/report-history-price-supplier.component'
import { ReportHistoryPriceServiceComponent } from './report-history-price-service/report-history-price-service.component'
import { ReportHistoryPriceCategoryComponent } from './report-history-price-category/report-history-price-category.component'
import { ReportHistoryBidSupplierComponent } from './report-history-bid-supplier/report-history-bid-supplier.component'
import { ReportHistoryBuyerComponent } from './report-history-buyer/report-history-buyer.component'
import { AuctionComponent } from './auction/auction.component'

const routes: Routes = [
  { path: '', component: BidComponent },
  { path: 'bid-new', component: BidComponent },
  { path: 'bid-rate', component: BidRateComponent },
  { path: 'bid-info', component: BidInfoComponent },
  { path: 'auction', component: AuctionComponent },
  { path: 'supplier', component: SupplierServiceToExpertiseComponent },
  { path: 'supplier-capacity', component: SupplierCapacityComponent },
  { path: 'supplier-expertise', component: SupplierExpertiseComponent },
  { path: 'report-supplier', component: ReportSupplierComponent },
  { path: 'report-expertise', component: ReportExpertiseComponent },
  { path: 'report-bid', component: ReportBidComponent },
  { path: 'report-history-price-supplier', component: ReportHistoryPriceSupplierComponent },
  { path: 'report-history-price-service', component: ReportHistoryPriceServiceComponent },
  { path: 'report-history-price-category', component: ReportHistoryPriceCategoryComponent },
  { path: 'report-history-bid-supplier', component: ReportHistoryBidSupplierComponent },
  { path: 'report-history-buyer', component: ReportHistoryBuyerComponent },
  { path: 'supplier-manage', component: SupplierManageComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BidRoutingModule {}
