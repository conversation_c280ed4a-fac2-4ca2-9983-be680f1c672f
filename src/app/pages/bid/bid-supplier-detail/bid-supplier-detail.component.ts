import { Component, OnInit, Input } from '@angular/core'
import { Subscription } from 'rxjs'
import { CoreService, StorageService } from '../../../services'

@Component({ templateUrl: './bid-supplier-detail.component.html' })
export class BidSupplierDetailComponent implements OnInit {
  @Input()
  public supplierId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(private coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
  }
}
