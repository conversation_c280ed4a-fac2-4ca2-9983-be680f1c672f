<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row *ngIf="data.isMPO">
    <button nz-button (click)="searchData()" class="mr-2"><PERSON><PERSON>m mới</button>
    <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" (click)="addRow()" nzType="primary" class="mr-2">
      Thêm
    </button>
    <button
      nz-button
      nz-popconfirm
      *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
      nzPopconfirmTitle="Bạn có chắc muốn xoá tất cả cột?"
      nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="deleteAll()"
      nzDanger
    >
      <PERSON><PERSON><PERSON> tất cả cột
    </button>
  </nz-row>

  <nz-row class="mt-1">
    <nz-table
      nz-col
      nzSpan="24"
      class="mb-3"
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr>
          <th class="mw-30">Mã cột</th>
          <th class="mw-30">Tên cột</th>
          <th>Công thức</th>
          <th>Kiểu dữ liệu</th>
          <th>Loại cột</th>
          <th>Bắt buộc?</th>
          <th>Thứ tự cột</th>
          <th *ngIf="data.isMPO">{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataRow of listOfData">
          <td class="mw-30">
            <ng-container *ngIf="!editCache[dataRow.id].edit; else codeInputTpl">
              {{ dataRow.code }}
            </ng-container>
            <ng-template #codeInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[dataRow.id].data.code" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>
          <td class="mw-30">
            <ng-container *ngIf="!editCache[dataRow.id].edit; else nameInputTpl">
              {{ dataRow.name }}
            </ng-container>
            <ng-template #nameInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[dataRow.id].data.name" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>
          <td class="mw-30">
            <ng-container *ngIf="!editCache[dataRow.id].edit; else fomularInputTpl">
              {{ dataRow.fomular }}
            </ng-container>
            <ng-template #fomularInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[dataRow.id].data.fomular" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[dataRow.id].edit; else typeInputTpl">
              {{ dataRow.typeName }}
            </ng-container>
            <ng-template #typeInputTpl>
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="editCache[dataRow.id].data.type"
                name="type"
                required
                nzPlaceHolder="Chọn kiểu dữ liệu"
              >
                <nz-option *ngFor="let item of lstDataType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[dataRow.id].edit; else colTypeInputTpl">
              {{ dataRow.colTypeName }}
            </ng-container>
            <ng-template #colTypeInputTpl>
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="editCache[dataRow.id].data.colType"
                name="colType"
                required
                nzPlaceHolder="Chọn loại cột"
              >
                <nz-option *ngFor="let item of dataColType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </ng-template>
          </td>
          <td style="width: 50px">
            <ng-container *ngIf="!editCache[dataRow.id].edit; else isRequiredInputTpl">
              <label nz-checkbox nzDisabled [ngModel]="dataRow.isRequired"></label>
            </ng-container>
            <ng-template #isRequiredInputTpl>
              <label nz-checkbox [(ngModel)]="editCache[dataRow.id].data.isRequired"></label>
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[dataRow.id].edit; else sortInputTpl">
              {{ dataRow.sort > 0 ? dataRow.sort : '' }}
            </ng-container>
            <ng-template #sortInputTpl>
              <input type="number" nz-input [(ngModel)]="editCache[dataRow.id].data.sort" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>

          <td class="text-nowrap" *ngIf="data.isMPO">
            <div class="editable-row-operations">
              <ng-container *ngIf="!editCache[dataRow.id].edit; else saveTpl">
                <button
                  nz-tooltip
                  nzTooltipTitle="Sửa"
                  class="mr-2"
                  nz-button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
                  (click)="startEdit(dataRow.id)"
                >
                  <span nz-icon nzType="edit"></span>
                </button>
                <button
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn xoá?"
                  nzPopconfirmPlacement="bottom"
                  *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
                  (nzOnConfirm)="startDelete(dataRow)"
                  nz-tooltip
                  nzTooltipTitle="Xoá"
                  class="mr-2"
                  nz-button
                  nzDanger
                >
                  <span nz-icon nzType="delete"></span>
                </button>
              </ng-container>
              <ng-template #saveTpl>
                <button
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.SAVE || 'Lưu'"
                  class="mr-2"
                  nz-button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  nzType="primary"
                  (click)="saveEdit(dataRow.id)"
                >
                  <span nz-icon nzType="save"></span>
                </button>
                <button
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.CANCEL || 'Hủy'"
                  class="mr-2"
                  nz-button
                  nzDanger
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  (click)="cancelEdit(dataRow.id)"
                >
                  <span nz-icon nzType="close"></span>
                </button>
              </ng-template>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>
