import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-bid-price.component.html' })
export class AddOrEditBidPriceComponent implements OnInit {
  modalTitle = 'Thêm mới bảng giá'
  dataObject: any = {}
  lstUnit: any[] = []
  lstCurrency: any[] = []
  dataBidPrice: any[] = []
  dataBidItem: any[] = []
  dataBidPrice2: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditBidPriceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    await this.loadDataBidPrice()
    await this.loadAllList()
    if (this.data && this.data !== null) {
      if (this.data.isCustomPrice) this.modalTitle = 'Thêm mới cơ cấu giá'
      if (this.data.id) {
        this.modalTitle = 'Chỉnh sửa bảng giá'
        if (this.data.isCustomPrice) this.modalTitle = 'Chỉnh sửa cơ cấu giá'
        // edit
        this.dataObject = { ...this.data }
      } else {
        this.dataObject.parentId = this.data.parentId
        this.dataObject.bidId = this.data.bidId
        this.dataObject.type = enumData.DataType.Number.code
        this.dataObject.isSetup = false
        this.dataObject.isRequired = true
        this.dataObject.isTemplate = false
        this.dataObject.number = 0
        this.dataObject.isCustomPrice = this.data.isCustomPrice
      }
    }
  }

  async loadDataBidPrice() {
    await this.apiService.post(this.apiService.BID.BIDPRICE_FIND, { bidId: this.data.bidId }).then((result) => {
      this.dataBidPrice = result
      this.dataBidPrice2 = []
    })

    await this.apiService.get(this.apiService.BID.BIDITEM_GET(this.data.bidId), {}).then((result) => {
      this.dataBidItem = result
    })
  }

  onChangeParent() {
    this.dataObject.parentId2 = null
    this.dataBidPrice2 = []
    if (this.dataObject.parentId) {
      var obj = this.dataBidPrice.find((c) => c.id == this.dataObject.parentId)
      if (obj) this.dataBidPrice2 = obj['__childs__']
    }
  }

  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.parentId2 && this.dataObject.parentId2 !== '') {
      this.dataObject.level = 3
      this.dataObject.parentId = this.dataObject.parentId2
    } else if (this.dataObject.parentId && this.dataObject.parentId !== '') this.dataObject.level = 2
    else this.dataObject.level = 1

    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    let url = this.apiService.BID.BIDPRICE_CREATE
    if (this.dataObject.isCustomPrice) url = this.apiService.BID.BIDCUSTOMPRICE_CREATE

    this.apiService.post(url, this.dataObject).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(true)
    })
  }

  updateObject() {
    let url = this.apiService.BID.BIDPRICE_UPDATE
    if (this.dataObject.isCustomPrice) url = this.apiService.BID.BIDCUSTOMPRICE_UPDATE

    this.apiService.post(url, this.dataObject).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
