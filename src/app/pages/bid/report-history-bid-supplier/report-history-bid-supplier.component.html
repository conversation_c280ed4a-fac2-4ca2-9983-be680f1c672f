<nz-collapse nzBordered="false">
    <nz-collapse-panel nzHeader="Bộ tìm kiếm dữ liệu báo cáo" [(nzActive)]="isCollapseFilter">
        <nz-row nzGutter="8">
            <nz-col nzSpan="8">
                <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.supplierId" name="supplierId"
                    [nzPlaceHolder]="language_key?.INTERPRISE_CHOOSE || 'Chọn doanh nghiệp'">
                    <nz-option *ngFor="let item of lstSupplier" [nzLabel]="'(' + item.code + ') ' + item.name"
                        [nzValue]="item.id">
                    </nz-option>
                </nz-select>
            </nz-col>
            <nz-col nzSpan="8">
                <input nz-input [(ngModel)]="dataSearch.code" placeholder="Tìm theo mã gói thầu" />
            </nz-col>
            <nz-col nzSpan="8">
                <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo tên gói thầu" />
            </nz-col>
        </nz-row>

        <nz-row nzGutter="8" class="mt-3">
            <nz-col nzSpan="8">
                <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
                    [nzLoadData]="loadDataService">
                </nz-cascader>
            </nz-col>
            <nz-col nzSpan="8">
                <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom"
                    nzPlaceHolder="Từ ngày đăng tải">
                </nz-date-picker>
            </nz-col>
            <nz-col nzSpan="8">
                <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
                </nz-date-picker>
            </nz-col>
        </nz-row>

        <nz-row nzGutter="8" class="mt-3">
            <nz-col nzSpan="24" class="text-center">
                <button nz-button (click)="searchData(true)" class="mr-2">
                    <span nz-icon nzType="search"></span>
                    {{ language_key?.SEARCH || 'Tìm kiếm' }}
                </button>
                <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)"
                    (click)="clickExportExcel()">
                    <span nz-icon nzType="download"></span>
                    Xuất excel
                </button>
            </nz-col>
        </nz-row>
    </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" class="mb-3" id="report-table" [nzData]="listOfData" [(nzPageSize)]="pageSize"
        [nzLoading]="loading" [nzShowPagination]="false" nzBordered [nzScroll]="{ x: '2000px' }" nzTableLayout="fixed">
        <thead>
            <tr>
                <th>Lĩnh vực mua hàng (tầng 1)</th>
                <th>Lĩnh vực mua hàng (tầng 2)</th>
                <th>Lĩnh vực mua hàng (tầng 3)</th>
                <th>Tên gói thầu</th>
                <th>Tổng điểm năng lực</th>
                <th>Tổng điểm kỹ thuật</th>
                <th>Tổng điểm giá</th>
                <th>Tổng điểm ĐKTM</th>
                <th>Ngày tạo gói thầu</th>
                <th>Ngày đóng thầu</th>
                <th>Tình trạng</th>
                <th>Tổng giá trị gói thầu</th>
                <th>Kết quả</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of listOfData">
                <td (click)="clickView(data)">{{ data.serviceLv1Code }}</td>
                <td (click)="clickView(data)">{{ data.serviceLv2Code }}</td>
                <td (click)="clickView(data)">{{ data.serviceLv3Code }}</td>
                <td (click)="clickView(data)">{{ data.bidName }}</td>
                <td class="text-right" (click)="clickView(data)">{{ data.scoreCapacity | number }}</td>
                <td class="text-right" (click)="clickView(data)">{{ data.scoreTech | number }}</td>
                <td class="text-right" (click)="clickView(data)">{{ data.scorePrice | number }}</td>
                <td class="text-right" (click)="clickView(data)">{{ data.scoreTrade | number }}</td>
                <td class="text-nowrap" (click)="clickView(data)">
                    {{ data.bid.createdAt | date: 'dd/MM/yyyy HH:mm' }}
                </td>
                <td class="text-nowrap" (click)="clickView(data)">
                    {{ data.bid.bidCloseDate | date: 'dd/MM/yyyy HH:mm' }}
                </td>
                <td (click)="clickView(data)">{{ data.bidStatus }}</td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.price | number }}</td>
                <td (click)="clickView(data)">{{ data.successBidStatus }}</td>
            </tr>
        </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
        {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
</nz-row>