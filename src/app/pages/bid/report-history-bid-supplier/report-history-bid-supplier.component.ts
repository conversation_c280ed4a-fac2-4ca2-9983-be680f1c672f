import { Component, OnInit } from '@angular/core'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'

import * as moment from 'moment'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './report-history-bid-supplier.component.html' })
export class ReportHistoryBidSupplierComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  lstSupplier: any[] = []
  dataSearch: any = {}
  listOfData: any[] = []
  isCollapseFilter: boolean = true
  loading = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadSupplier()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
  }

  loadSupplier() {
    this.apiService.post(this.apiService.SUPPLIER.FIND, {}).then((res) => {
      this.lstSupplier = res
    })
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    if (!this.dataSearch.supplierId) {
      this.notifyService.showError('Vui lòng chọn doanh nghiệp trước')
      return
    }
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Vui lòng chọn [Từ ngày đăng tải] sớm hơn [Đến ngày đăng tải]')
      return
    }
    this.loading = true
    const where: any = {}
    where.supplierId = this.dataSearch.supplierId
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }

    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }

    const dataSearch = {
      where: where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.REPORT.GET_REPORT_HISTORY_BID_SUPPLIER, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickView(object: any) {
    // this.dialog.open(BidDetailComponent, { disableClose: false, data: object })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  clickExportExcel() {
    this.notifyService.showloading()
    if (!this.dataSearch.supplierId) {
      this.notifyService.showError('Vui lòng chọn doanh nghiệp trước')
      return
    }
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Vui lòng chọn [Từ ngày đăng tải] sớm hơn [Đến ngày đăng tải]')
      return
    }
    const where: any = {}
    where.supplierId = this.dataSearch.supplierId
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }

    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }

    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: enumData.Page.pageSizeMax,
    }
    this.apiService.post(this.apiService.REPORT.GET_REPORT_HISTORY_BID_SUPPLIER, dataSearch).then((data) => {
      const lstData = data[0]
      const workbook = new Workbook()
      const worksheet = workbook.addWorksheet('Sheet 1')

      //#region Header

      //title 1
      worksheet.getCell('A1').value = 'BÁO CÁO LỊCH SỬ THAM GIA ĐẤU THẦU'
      worksheet.getCell('A1').style = {
        font: { name: 'Calibri', family: 4, size: 16, bold: true, color: { argb: '426EB4' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
      }
      worksheet.mergeCells('A1:N1')

      //title 2
      let supplierName = ''
      const supplier = this.lstSupplier.find((c) => c.id == this.dataSearch.supplierId)
      if (supplier) supplierName = supplier.name

      worksheet.getCell('A2').value = supplierName
      worksheet.getCell('A2').style = {
        font: { name: 'Calibri', family: 4, size: 16, bold: true, color: { argb: '426EB4' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
      }
      worksheet.mergeCells('A2:N2')

      //title 3
      const todate = new Date()
      let minDate = todate
      if (!this.dataSearch.dateFrom) {
        for (const data of lstData) {
          const dtTemp = new Date(data.dateSubmitPrice)
          if (dtTemp < minDate) minDate = dtTemp
        }
      }

      const dtFrom = this.dataSearch.dateFrom || minDate
      const dtTo = this.dataSearch.dateTo || todate
      worksheet.getCell('A3').value = `(Thời gian: Từ ${moment(dtFrom).format('DD/MM/YYYY')} - Đến ${moment(dtTo).format('DD/MM/YYYY')})`
      worksheet.getCell('A3').style = {
        font: { name: 'Calibri', family: 4, size: 14, bold: true, color: { argb: 'DE9E43' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
      }
      worksheet.mergeCells('A3:N3')

      // Blank Row
      worksheet.addRow([])
      //#endregion

      //#region Body Table
      const header = [
        this.language_key?.NO || 'STT',
        'Lĩnh vực mua hàng (tầng 1)',
        'Lĩnh vực mua hàng (tầng 2)',
        'Lĩnh vực mua hàng (tầng 3)',
        'Tên gói thầu',
        'Tổng điểm năng lực',
        'Tổng điểm kỹ thuật',
        'Tổng điểm giá',
        'Tổng điểm ĐKTM',
        'Ngày tạo gói thầu',
        'Ngày đóng thầu',
        'Tình trạng',
        'Tổng giá trị gói thầu',
        'Kết quả',
      ]
      const headerRow = worksheet.addRow(header)

      // Cell Style: Fill and Border
      headerRow.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFF' },
        }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: true }
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

        switch (colNumber) {
          case 1:
            worksheet.getColumn(colNumber).width = 5
            break
          case 2:
          case 3:
          case 4:
          case 5:
          case 12:
          case 13:
          case 14:
            worksheet.getColumn(colNumber).width = 20
            break
          default:
            worksheet.getColumn(colNumber).width = 15
            break
        }
      })

      // Add Data and Conditional Formatting
      for (const data of lstData) {
        const rowData = [
          data.sort, //'{{ language_key?.NO || 'STT' }}',
          data.serviceLv1Code, //'Lĩnh vực mua hàng (tầng 1)',
          data.serviceLv2Code, //'Lĩnh vực mua hàng (tầng 2)',
          data.serviceLv3Code, //'Lĩnh vực mua hàng (tầng 3)',
          data.bidName, //'Tên gói thầu',
          data.scoreCapacity || 0, //'Tổng điểm năng lực',
          data.scoreTech || 0, //'Tổng điểm kỹ thuật',
          data.scorePrice || 0, //'Tổng điểm giá',
          data.scoreTrade || 0, //'Tổng điểm ĐKTM',
          data.bid.createdAt ? new Date(data.bid.createdAt) : '', //'Ngày tạo gói thầu',
          data.bid.bidCloseDate ? new Date(data.bid.bidCloseDate) : '', //'Ngày đóng thầu',
          data.bidStatus, //'Tình trạng',
          data.price, //'Tổng giá trị gói thầu',
          data.successBidStatus, //'Kết quả',
        ]
        const row = worksheet.addRow(rowData)
        row.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFFF' },
          }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
          cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }

          switch (colNumber) {
            // string
            case 2:
            case 3:
            case 4:
            case 5:
            case 12:
            case 14:
              cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
              break
            // date
            case 10:
            case 11:
              cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }
              cell.numFmt = 'dd/mm/yyyy'
              break
            //number
            default:
              cell.alignment = { horizontal: 'right', vertical: 'middle', wrapText: true }
              break
          }
        })
      }

      // Blank Row
      worksheet.addRow([])
      //#endregion

      //#region Footer
      let footerRow = worksheet.addRow([])
      footerRow.getCell(14).value = `Ngày ${moment(todate).format('DD')} tháng ${moment(todate).format('MM')} năm ${moment(todate).format('YYYY')}`
      footerRow.getCell(14).style = {
        font: { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'DE9E43' } },
        alignment: { horizontal: 'right', vertical: 'middle' },
      }
      //#endregion

      //#region Save File
      workbook.xlsx.writeBuffer().then((data) => {
        let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Báo cáo lịch sử tham gia đấu thầu nhà cung cấp.xlsx`
        fs.saveAs(blob, fileName)
        this.notifyService.hideloading()
      })
      //#endregion
    })
  }
}
