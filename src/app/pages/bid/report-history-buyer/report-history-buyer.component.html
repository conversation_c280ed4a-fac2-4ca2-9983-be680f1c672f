<nz-collapse nzBordered="false">
    <nz-collapse-panel nzHeader="Bộ tìm kiếm dữ liệu báo cáo" [(nzActive)]="isCollapseFilter">
        <nz-row nzGutter="8">
            <nz-col nzSpan="6">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn buyer" [(ngModel)]="dataSearch.employeeId"
                    name="employeeId">
                    <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                </nz-select>
            </nz-col>
            <nz-col nzSpan="6">
                <input nz-input [(ngModel)]="dataSearch.category" placeholder="Tìm theo tên hạng mục chào giá" />
            </nz-col>
            <nz-col nzSpan="6">
                <input nz-input [(ngModel)]="dataSearch.code" placeholder="Tì<PERSON> theo mã số gói thầu" />
            </nz-col>
            <nz-col nzSpan="6">
                <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo tên gói thầu" />
            </nz-col>
        </nz-row>

        <nz-row nzGutter="8" class="mt-3">
            <nz-col nzSpan="12">
                <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
                    [nzLoadData]="loadDataService">
                </nz-cascader>
            </nz-col>
            <nz-col nzSpan="6">
                <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom"
                    nzPlaceHolder="Từ ngày đăng tải">
                </nz-date-picker>
            </nz-col>
            <nz-col nzSpan="6">
                <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
                </nz-date-picker>
            </nz-col>
        </nz-row>

        <nz-row nzGutter="8" class="mt-3">
            <nz-col nzSpan="24" class="text-center">
                <button nz-button (click)="searchData(true)" class="mr-2">
                    <span nz-icon nzType="search"></span>
                    {{ language_key?.SEARCH || 'Tìm kiếm' }}
                </button>
                <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
                    (click)="clickExportExcel()">
                    <span nz-icon nzType="download"></span>Xuất excel
                </button>
            </nz-col>
        </nz-row>
    </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" class="mb-3" id="report-table" [nzData]="listOfData" [nzScroll]="{ x: '2500px' }"
        nzTableLayout="fixed" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
        <thead>
            <tr>
                <th>Công ty</th>
                <th>Item</th>
                <th>Mã số gói thầu</th>
                <th>Tên gói thầu</th>
                <th>Tên hạng mục giá</th>
                <th>Đơn vị tính</th>
                <th>Đơn vị tiền tệ</th>
                <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th>{{ language_key?.PRICE || 'Đơn giá' }}</th>
                <th>Thành tiền</th>
                <th>Ngày mở thầu</th>
                <th>Ngày đóng thầu</th>
                <th>Tình trạng</th>
                <th>Kết quả</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of listOfData">
                <td class="mw-25" (click)="clickView(data)">{{ data.supplierName }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ data.itemName }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ data.bidCode }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ data.bidName }}</td>
                <td (click)="clickView(data)">{{ data.name }}</td>
                <td (click)="clickView(data)">{{ data.priceLast.objPrice.unit }}</td>
                <td (click)="clickView(data)">{{ data.priceLast.objPrice.currency }}</td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.number | number }}</td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.unitPrice | number }}</td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.price | number }}</td>
                <td class="text-nowrap" (click)="clickView(data)">
                    {{ data.startBidDate | date: 'dd/MM/yyyy HH:mm' }}
                </td>
                <td class="text-nowrap" (click)="clickView(data)">
                    {{ data.bidCloseDate | date: 'dd/MM/yyyy HH:mm' }}
                </td>
                <td class="mw-25" (click)="clickView(data)">{{ data.bidStatus }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ data.successBidStatus }}</td>
            </tr>
        </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
        {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
</nz-row>