import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-bidding-price.component.html' })
export class AddBiddingPriceComponent implements OnInit {
  modalTitle = 'Chỉnh sửa hạng mục cơ cấu giá'
  dataObject: any = {}
  lstUnit: any[] = []
  lstCurrency: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddBiddingPriceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.dataObject = { ...this.data }
    this.modalTitle = 'Chỉnh sửa hạng mục cơ cấu giá'
    if (this.dataObject && this.dataObject.isNew) {
      this.modalTitle = 'Thêm mới hạng mục cơ cấu giá'
    }
    this.loadAllSelect()
  }

  loadAllSelect() {
    const lstPromise = [
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ]
    Promise.all(lstPromise).then((res) => {
      this.lstUnit = res[0]
      this.lstCurrency = res[1]
    })
  }

  closeDialog() {
    this.dialogRef.close(this.dataObject)
  }

  handleOk() {
    this.closeDialog()
  }
}
