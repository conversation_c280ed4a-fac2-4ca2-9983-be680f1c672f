import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { Subscription } from 'rxjs'
import * as $ from 'jquery'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Borders, Workbook } from 'exceljs'
import * as fs from 'file-saver'

@Component({
  selector: 'app-bidding-tech',
  templateUrl: './bidding-tech.component.html',
})
export class BiddingTechComponent implements OnInit {
  listOfData: any[] = []
  public dataType = enumData.DataType
  maxSizeUpload = enumData.maxSizeUpload
  pageSize = enumData.Page.pageSizeMax
  loading = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  bidId!: string
  @Input()
  supplierId!: string
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadData()
  }

  getDataSave = () => {
    return this.listOfData
  }

  async handleFileInput(event: any, rowData: any) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res: any[]) => {
        if (res && res.length) rowData.value = res[0]
        else rowData.value = ''
      })
    }
  }

  handleClearFile(rowData: any) {
    rowData.value = undefined
    $(`#zen${rowData.id}`).val('')
  }

  loadData = () => {
    this.notifyService.showloading()
    this.loading = true

    this.apiService
      .post(this.apiService.BIDDING.LOAD_DATA_BID_TECH, { bidId: this.bidId, supplierId: this.supplierId })
      .then((res: never[]) => {
        this.listOfData = res || []
        if (this.listOfData.length > 0) {
          for (const data1 of this.listOfData) {
            data1.bidTechId = data1.id
            for (const data2 of data1.__childs__) data2.bidTechId = data2.id
          }
        }
      })
      .finally(() => {
        this.loading = false
        this.notifyService.hideloading()
      })
  }

  resetError(item: any) {
    item.isError = false
    item.errorText = ''
  }

  //#region excel kỹ thuật
  dicExcel: any = {}
  clickExportExcel() {
    this.notifyService.showloading()
    //#region Kiểm tra data
    if (this.listOfData.length == 0) {
      this.notifyService.showError('Item chưa có thiết lập tiêu chí kỹ thuật.')
      return
    }
    const lstOfDataExcel: any[] = []
    let isRemove = false
    for (const itemLv1 of this.listOfData) {
      itemLv1.level = 1
      if (itemLv1.type !== this.dataType.File.code || itemLv1.__childs__.length > 0) {
        lstOfDataExcel.push(itemLv1)
      } else isRemove = true
      for (const itemLv2 of itemLv1.__childs__) {
        itemLv2.level = 2
        if (itemLv2.type !== this.dataType.File.code) {
          lstOfDataExcel.push(itemLv2)
        } else isRemove = true
      }
    }
    if (isRemove) {
      if (lstOfDataExcel.length == 0) {
        this.notifyService.showError(
          'Item chưa có thiết lập tiêu chí kỹ thuật có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.'
        )
        return
      }
      this.notifyService.showInfo('Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.')
    }
    this.dicExcel[this.bidId] = lstOfDataExcel
    //#endregion

    //#region Body Table
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const headerRow = worksheet.addRow(['ID', 'Tiêu chí', 'Kiểu dữ liệu', 'Bắt buộc?', 'Giá trị'])

    // Cell Style : Fill and Border
    const border: Partial<Borders> = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '08298A' },
      }
      cell.border = border
      cell.font = { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'FFFFFF' } }
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 70
          worksheet.getColumn(colNumber).hidden = true // ẩn cột
          break
        case 2:
        case 5:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    // Màu theo 2 level
    const arrColor = ['ebebeb', 'ffffff']
    for (const data of lstOfDataExcel) {
      data.valueString = data.value
      if (data.type == this.dataType.Date.code && data.value) data.valueString = `'${moment(new Date(data.value)).format('YYYY-MM-DD')}`
      if (data.type == enumData.DataType.List.code) {
        for (const itemList of data.__bidTechListDetails__) {
          if (itemList.id == data.value) data.valueString = itemList.name
        }
      }
      const rowData = [
        data.id || '', //'ID',
        data.name || '', //'Tiêu chí',
        data.type || '', //'Kiểu dữ liệu',
        data.isRequired ? 'x' : '', //'Bắt buộc?',
        data.valueString || '', //'Giá trị',
      ]
      const row = worksheet.addRow(rowData)
      row.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: arrColor[data.level - 1] },
        }
        cell.border = border
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
        cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
        if (colNumber == 5 && data.type == enumData.DataType.List.code) {
          cell.dataValidation = {
            type: 'list',
            formulae: ['"' + data.__bidTechListDetails__.map((c: any) => c.name).join() + '"'],
          }
        }
      })
    }
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template nhập hồ sơ kỹ thuật.xlsx`
      fs.saveAs(blob, fileName)
      setTimeout(() => {
        this.notifyService.showSuccess('Tải template thành công!')
      }, 300)
    })
    //#endregion
  }

  clickImportExcel(ev: any) {
    this.notifyService.showloading()
    if (this.listOfData.length == 0) {
      this.notifyService.showError('Item chưa có thiết lập tiêu chí kỹ thuật.')
      return
    }
    let workBook = null
    let jsonData: any[] = []
    const lstHeader = ['id', 'name', 'type', 'isRequired', 'value']
    let lstOfDataExcel: any[] = this.dicExcel[this.bidId]
    if (!lstOfDataExcel) {
      lstOfDataExcel = []
      for (const itemLv1 of this.listOfData) {
        if (itemLv1.type !== this.dataType.File.code || itemLv1.__childs__.length > 0) {
          lstOfDataExcel.push(itemLv1)
        }
        for (const itemLv2 of itemLv1.__childs__) {
          if (itemLv2.type !== this.dataType.File.code) {
            lstOfDataExcel.push(itemLv2)
          }
        }
      }
    }

    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      //#region check template
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== 'Tiêu chí' ||
        header.type !== 'Kiểu dữ liệu' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.value !== 'Giá trị'
      ) {
        this.notifyService.showError('File không đúng template (cột đã bị thay đổi)!')
        return
      }
      if (jsonData.some((c) => !c.id) || lstOfDataExcel.length != jsonData.length) {
        this.notifyService.showError(`File không đúng template (dòng đã bị thay đổi)!`)
        return
      }
      //#endregion

      //#region check value
      const dicValue: any = {}
      for (const row of jsonData) {
        const item = lstOfDataExcel.find((c) => c.id == row.id && c.name == row.name)
        if (!item) {
          this.notifyService.showError(`File không đúng template (dòng đã bị thay đổi)!`)
          return
        }
        if (item.isRequired && (row.value == null || row.value === '') && !item.__childs__?.length) {
          this.notifyService.showError(`Giá trị của tiêu chí [${row.name}] bắt buộc không được để trống!`)
          return
        }
        if (row.value != null && row.value !== '') {
          if (item.type == this.dataType.Date.code) {
            const value = new Date(row.value)
            if (typeof row.value !== 'string' || isNaN(value.getTime())) {
              this.notifyService.showError(`[Giá trị] là [${row.value}] không phải kiểu ${item.type}, ngày phải có định dạng yyyy-mm-dd`)
              return
            }
            dicValue[row.id] = value
          } else if (item.type == this.dataType.Number.code) {
            const value = +row.value
            if (isNaN(value) || !isFinite(value)) {
              this.notifyService.showError(`[Giá trị] là [${row.value}] không phải kiểu ${item.type}`)
              return
            }
            dicValue[row.id] = value
          } else if (item.type == this.dataType.List.code) {
            const itemChoose = item.__bidTechListDetails__.find((c: any) => c.name == row.value)
            if (!itemChoose) {
              this.notifyService.showError(`[Giá trị] là [${row.value}] không nằm trong List`)
              return
            }
            dicValue[row.id] = itemChoose.id
          } else dicValue[row.id] = row.value
        }
      }
      //#endregion

      //#region fill value
      for (const item of lstOfDataExcel) {
        item.value = dicValue[item.id]
      }
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
      //#endregion
    }
  }
  //#endregion
}
