<div *ngIf="bidId">
  <nz-row class="mt-2">
    <button class="mr-2" *ngIf=" authenticationService.checkPermission([enumRole], action.Import.code)" nz-button
      (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>Xuất excel</button>
    <input class="hidden" type="file" id="filePrice" (change)="clickImportExcel($event)" onclick="this.value=null"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
    <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="filePrice"
      class="lable-custom-file">
      <span nz-icon nzType="upload"></span>
      {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
    </label>
  </nz-row>

  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th>Tên hạng mục</th>
          <th *ngFor="let col of bidPriceCol"
            [ngClass]="{ 'dynamic-col-mpo': col.colType===mpoType, 'dynamic-col-supplier': col.colType===supType}">
            {{ col.name }}
          </th>
          <th class="text-nowrap">Đơn vị tính</th>
          <th class="text-nowrap">Đơn vị tiền tệ</th>
          <th class="text-nowrap">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th class="mw-20 miw-15" *ngIf="!hasFomularValue">{{ language_key?.PRICE || 'Đơn giá' }}</th>
          <th>Thông tin chi tiết</th>
        </tr>
      </thead>
      <tbody>
        <!-- level 1 -->
        <ng-container *ngFor="let data1 of listOfData">
          <tr>
            <td class="mw-20 miw-15">
              <span *ngIf="data1.isRequired" class="text-danger">*</span>
              <span nz-tooltip nzTooltipPlacement="topLeft" [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
            </td>
            <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
              <span *ngIf="col.colType===mpoType && !col.fomular?.length">
                {{ data1[col.id] }}</span>
              <span *ngIf="col.type===numberType && col.fomular?.length > 0">{{
                data1[col.id] | number }}</span>
              <input *ngIf="col.colType===supType && col.type===numberType && !col.fomular?.length" nz-input
                currencyMask [(ngModel)]="data1[col.id]" (ngModelChange)="onChangeCalFomular(data1)" />
              <input *ngIf="col.colType===supType && col.type===stringType" nz-input [(ngModel)]="data1[col.id]" />
            </td>
            <td class="mw-25">{{ data1.unit }}</td>
            <td class="mw-25">{{ data1.currency }}</td>
            <td class="mw-25 text-right">{{ data1.number | number }}</td>
            <td class="mw-20 miw-15" (click)="resetError(data1)">
              <input nz-input currencyMask [(ngModel)]="data1.value" name="value" />
              <!-- <span *ngIf="!hasFomularValue">{{data1.value | number}}</span> -->
              <span *ngIf="data1.isError" class="text-danger">{{data1.errorText}}</span>
            </td>
            <td class="miw-15">
              <button nz-tooltip nzTooltipTitle="Thông tin chi tiết" class="mr-2" nz-button
                *ngIf="data1.__bidPriceListDetails__?.length" nzType="dashed" (click)="viewDetail(data1)">
                <span nz-icon nzType="exclamation-circle"></span>
              </button>
            </td>
          </tr>
          <!-- level 2 -->
          <ng-container *ngFor="let data2 of data1.__childs__">
            <tr>
              <td [nzIndentSize]="5" class="mw-20 miw-15">
                <span *ngIf="data2.isRequired" class="text-danger">*</span>
                <span nz-tooltip nzTooltipPlacement="topLeft" [nzTooltipTitle]="data2.name">{{ data2.name }}</span>
              </td>
              <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
                <span *ngIf="col.colType===mpoType && !col.fomular?.length">
                  {{ data2[col.id] }}</span>
                <span *ngIf="col.type===numberType && col.fomular?.length > 0">{{
                  data2[col.id] | number }}</span>
                <input *ngIf="col.colType===supType && col.type===numberType && !col.fomular?.length" nz-input
                  currencyMask [(ngModel)]="data2[col.id]" (ngModelChange)="onChangeCalFomular(data2)" />
                <input *ngIf="col.colType===supType && col.type===stringType" nz-input [(ngModel)]="data2[col.id]" />
              </td>
              <td class="mw-25">{{ data2.unit }}</td>
              <td class="mw-25">{{ data2.currency }}</td>
              <td class="mw-25 text-right">{{ data2.number | number }}</td>
              <td class="mw-20 miw-15" (click)="resetError(data2)">
                <input *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data2.value" name="value" />
                <span *ngIf="hasFomularValue">{{data2.value | number}}</span>
                <span *ngIf="data2.isError" class="text-danger">{{data2.errorText}}</span>
              </td>
              <td class="miw-15">
                <button nz-tooltip nzTooltipTitle="Thông tin chi tiết" class="mr-2" nz-button
                  *ngIf="data2.__bidPriceListDetails__?.length" nzType="dashed" (click)="viewDetail(data2)">
                  <span nz-icon nzType="exclamation-circle"></span>
                </button>
              </td>
            </tr>
            <!-- level 3 -->
            <ng-container *ngFor="let data3 of data2.__childs__">
              <tr>
                <td [nzIndentSize]="30" class="mw-20 miw-15">
                  <span *ngIf="data3.isRequired" class="text-danger">*</span>
                  <span nz-tooltip nzTooltipPlacement="topLeft" [nzTooltipTitle]="data3.name">{{ data3.name }}</span>
                </td>
                <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
                  <span *ngIf="col.colType===mpoType && !col.fomular?.length">
                    {{ data3[col.id] }}</span>
                  <span *ngIf="col.type===numberType && col.fomular?.length > 0">
                    {{ data3[col.id] | number }}</span>
                  <input *ngIf="col.colType===supType && col.type===numberType && !col.fomular?.length" nz-input
                    currencyMask [(ngModel)]="data3[col.id]" (ngModelChange)="onChangeCalFomular(data3)" />
                  <input *ngIf="col.colType===supType && col.type===stringType" nz-input [(ngModel)]="data3[col.id]" />
                </td>
                <td class="mw-25">{{ data3.unit }}</td>
                <td class="mw-25">{{ data3.currency }}</td>
                <td class="mw-25 text-right">{{ data3.number | number }}</td>
                <td class="mw-20 miw-15" (click)="resetError(data3)">
                  <input *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data3.value" name="value" />
                  <span *ngIf="hasFomularValue">{{data3.value | number}}</span>
                  <span *ngIf="data3.isError" class="text-danger">{{data3.errorText}}</span>
                </td>
                <td class="miw-15">
                  <button nz-tooltip nzTooltipTitle="Thông tin chi tiết" class="mr-2" nz-button
                    *ngIf="data3.__bidPriceListDetails__?.length" nzType="dashed" (click)="viewDetail(data3)">
                    <span nz-icon nzType="exclamation-circle"></span>
                  </button>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>
  <nz-row *ngIf="listOfData.length == 0" class="text-danger mt-3">
    <i>Yêu cầu bảng giá bổ sung sau</i>
  </nz-row>
</div>


<app-bidding-price-list-detail #biddingPriceListDetail> </app-bidding-price-list-detail>