import { Component, OnInit, Input, ViewChild } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { BiddingPriceListDetailComponent } from '../bidding-price-list-detail/bidding-price-list-detail.component'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Borders, Workbook } from 'exceljs'
import * as fs from 'file-saver'

@Component({
  selector: 'app-bidding-price',
  templateUrl: './bidding-price.component.html',
})
export class BiddingPriceComponent implements OnInit {
  bidPriceCol: any[] = []
  listOfData: any[] = []
  bid: any

  @Input() bidId!: string
  @Input() supplierId!: string

  pageSize = enumData.Page.pageSizeMax
  loading = false
  hasFomularValue = false
  mpoType = enumData.ColType.MPO.code
  supType = enumData.ColType.Supplier.code
  stringType = enumData.DataType.String.code
  numberType = enumData.DataType.Number.code
  @ViewChild('biddingPriceListDetail', { static: false })
  biddingPriceListDetail!: BiddingPriceListDetailComponent
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
  }
  loadData = () => {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BIDDING.LOAD_DATA_BID_PRICE, { bidId: this.bidId, supplierId: this.supplierId })
      .then((res) => {
        this.listOfData = res[0]
        this.bidPriceCol = res[1]
        this.bid = res[2]
        if (this.bid.fomular?.length > 0) {
          this.hasFomularValue = true
        }

        for (const data1 of this.listOfData) {
          data1.bidPriceId = data1.id
          for (const data2 of data1.__childs__) {
            data2.bidPriceId = data2.id
            for (const data3 of data2.__childs__) data3.bidPriceId = data3.id
          }
        }
      })
      .finally(() => {
        this.notifyService.hideloading()
      })
  }

  getDataSave = () => {
    return this.listOfData
  }

  getCheckFomular = () => {
    return this.hasFomularValue
  }

  getDynamicColRequired = () => {
    const lstDynamicColRequired = this.bidPriceCol.filter(
      (c) => c.colType === enumData.ColType.Supplier.code && c.isRequired === true && (c.fomular == null || c.fomular.length === 0)
    )
    return lstDynamicColRequired
  }

  viewDetail(rowData: any) {
    this.biddingPriceListDetail.showModal(rowData)
  }

  resetError(rowData: any) {
    rowData.isError = false
    rowData.errorText = ''
  }

  dicExcel: any = {}
  clickExportExcel() {
    this.notifyService.showloading()
    //#region Kiểm tra data
    if (this.listOfData.length == 0) {
      this.notifyService.showError('Item chưa có thiết lập hạng mục chào giá.')
      return
    }
    const lstOfDataExcel: any[] = []
    for (const itemLv1 of this.listOfData) {
      lstOfDataExcel.push(itemLv1)
      for (const itemLv2 of itemLv1.__childs__) {
        lstOfDataExcel.push(itemLv2)
        for (const itemLv3 of itemLv2.__childs__) {
          lstOfDataExcel.push(itemLv3)
        }
      }
    }
    this.dicExcel[this.bidId] = lstOfDataExcel

    const lstColExcel = this.bidPriceCol.filter((c) => c.fomular == null || c.fomular.length === 0)
    const lstColName = lstColExcel.map((c) => c.name)
    //#endregion

    //#region Body Table
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const lstHeader = ['ID', 'Tên hạng mục', ...lstColName, 'Đơn vị tính', 'Đơn vị tiền tệ', 'Số lượng', 'Bắt buộc?']
    if (!this.hasFomularValue) {
      lstHeader.push('Đơn giá')
    }
    const headerRow = worksheet.addRow(lstHeader)

    // Cell Style : Fill and Border
    const border: Partial<Borders> = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '08298A' },
      }
      cell.border = border
      cell.font = { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'FFFFFF' } }
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 70
          worksheet.getColumn(colNumber).hidden = true // ẩn cột
          break
        case 2:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    // Màu theo 3 level
    const arrColor = ['ffffff', 'f7f7f7', 'ebebeb']
    for (const data of lstOfDataExcel) {
      const rowData = [
        data.id || '', //'ID',
        data.name || '', //'Tên hạng mục',
      ]
      for (const col of lstColExcel) {
        rowData.push(data[col.id] || '') // giá trị các cột động
      }
      rowData.push(
        ...[
          data.unit, //'Đơn vị tính'
          data.currency, //'Đơn vị tiền tệ'
          data.number, //'Số lượng'
          data.isRequired ? 'x' : '', //'Bắt buộc?',
          data.value || '', //'Đơn giá',
        ]
      )
      const row = worksheet.addRow(rowData)
      row.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: arrColor[data.level - 1] },
        }
        cell.border = border
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
        cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
      })
    }
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template nhập bảng chào giá.xlsx`
      fs.saveAs(blob, fileName)
      setTimeout(() => {
        this.notifyService.showSuccess('Tải template thành công!')
      }, 300)
    })
    //#endregion
  }

  clickImportExcel(ev: any) {
    this.notifyService.showloading()
    if (this.listOfData.length == 0) {
      this.notifyService.showError('Item chưa có thiết lập hạng mục chào giá.')
      return
    }
    let workBook = null
    let jsonData: any = null
    // lấy những cột không có cthuc
    const lstColExcel = this.bidPriceCol.filter((c) => c.fomular == null || c.fomular.length === 0)
    const lstColId = lstColExcel.map((c) => c.id)
    const lstHeader = ['id', 'name', ...lstColId, 'unit', 'currency', 'number', 'isRequired']
    if (!this.hasFomularValue) {
      lstHeader.push('value')
    }
    let lstOfDataExcel: any[] = this.dicExcel[this.bidId]
    if (!lstOfDataExcel) {
      lstOfDataExcel = []
      for (const itemLv1 of this.listOfData) {
        lstOfDataExcel.push(itemLv1)
        for (const itemLv2 of itemLv1.__childs__) {
          lstOfDataExcel.push(itemLv2)
          for (const itemLv3 of itemLv2.__childs__) {
            lstOfDataExcel.push(itemLv3)
          }
        }
      }
    }

    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      // bỏ dòng đầu tiên
      let isErr = false
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== 'Tên hạng mục' ||
        header.unit !== 'Đơn vị tính' ||
        header.currency !== 'Đơn vị tiền tệ' ||
        header.number !== 'Số lượng' ||
        header.isRequired !== 'Bắt buộc?' ||
        (!this.hasFomularValue && header.value !== 'Đơn giá')
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError('File không đúng template (cột đã bị thay đổi)!')
        return
      }

      const lstColSup = lstColExcel.filter((c) => c.colType === this.supType)
      const lstColFomular = this.bidPriceCol.filter((c) => c.fomular?.length > 0)
      for (const row of jsonData) {
        if (!this.hasFomularValue) {
          if (row.isRequired && (row.value == null || row.value == '')) {
            this.notifyService.showError(`Hạng mục [${row.name}] cột [Đơn giá] không được để trống`)
            return
          }
          if (row.value && typeof row.value !== 'number') {
            this.notifyService.showError(`Hạng mục [${row.name}] cột [Đơn giá] phải nhập số`)
            return
          }
        }

        for (const col of lstColSup) {
          if (col.isRequired && (row[col.id] == null || row[col.id] == '')) {
            this.notifyService.showError(`Hạng mục [${row.name}] cột [${col.name}] không được để trống`)
            return
          }
          if (row[col.id] && col.type === this.numberType && typeof row[col.id] !== 'number') {
            this.notifyService.showError(`Hạng mục [${row.name}] cột [${col.name}] phải nhập số`)
            return
          }
        }
      }

      for (const item of lstOfDataExcel) {
        const jsonItem = jsonData.find((c: any) => c.id === item.id)
        if (jsonItem) {
          item.value = jsonItem.value
          for (const col of lstColSup) item[col.id] = jsonItem[col.id]
        }
      }

      // tính công thức
      for (const data1 of lstOfDataExcel) {
        for (const col of lstColFomular) {
          data1[col.id] = this.coreService.calFomular(col.fomular, this.bidPriceCol, data1)
        }
        if (this.hasFomularValue) data1.value = this.coreService.calFomular(this.bid.fomular, this.bidPriceCol, data1)
      }
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
    }
  }

  onChangeCalFomular(rowData: any) {
    this.loading = true
    setTimeout(async () => {
      const lstDynamicColFomular = this.bidPriceCol.filter((c) => c.fomular?.length > 0)
      for (const col of lstDynamicColFomular) {
        rowData[col.id] = this.coreService.calFomular(col.fomular, this.bidPriceCol, rowData)
      }
      if (this.hasFomularValue) rowData.value = this.coreService.calFomular(this.bid.fomular, this.bidPriceCol, rowData)

      this.loading = false
    }, 10)
  }
}
