import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../../core'
@Component({
  selector: 'app-bidding-price-list-detail',
  templateUrl: './bidding-price-list-detail.component.html',
})
export class BiddingPriceListDetailComponent implements OnInit {
  lstOfData: any[] = []
  isVisible = false
  title = ''
  loading = false
  dataType = enumData.DataType
  constructor() {}

  ngOnInit() {}

  showModal(data: any) {
    this.isVisible = true
    this.title = `Thông tin chi tiết - ${data.name}`
    this.lstOfData = data.__bidPriceListDetails__
  }

  handleCancel() {
    this.isVisible = false
  }
}
