<nz-modal [(nzVisible)]="isVisible" [nzTitle]="title" [nzFooter]="null" (nzOnCancel)="handleCancel()"
  [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row nzGutter="8">
      <nz-table nz-col nzSpan="24" [nzData]="lstOfData" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th>Tên trường thông tin</th>
            <th>Giá trị</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of lstOfData">
            <td class="mw-25">{{ item.name }}</td>
            <td class="mw-25">
              <span *ngIf="item.type === dataType.String.code || item.type === dataType.Address.code">
                {{ item.value }}
              </span>
              <span *ngIf="item.type === dataType.Km.code || item.type === dataType.Time.code">
                {{ item.value | number: '1.0-2' }}
              </span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>