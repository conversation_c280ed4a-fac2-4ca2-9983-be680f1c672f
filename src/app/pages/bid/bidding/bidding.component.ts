import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core'
import { BiddingTechComponent } from './bidding-tech/bidding-tech.component'
import { BiddingTradeComponent } from './bidding-trade/bidding-trade.component'
import { BiddingPriceComponent } from './bidding-price/bidding-price.component'
import { BiddingCustomPriceComponent } from './bidding-custom-price/bidding-custom-price.component'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { NzModalService } from 'ng-zorro-antd/modal'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bidding.component.html' })
export class BiddingComponent implements OnInit {
  bidId!: string
  supplierId!: string
  bidDetail: any
  isError = false
  isSuccess = false
  current = 0
  language_key: any
  subscriptions: Subscription = new Subscription()

  @ViewChild('biddingTech', { static: false })
  biddingTech!: BiddingTechComponent
  @ViewChild('biddingTrade', { static: false })
  biddingTrade!: BiddingTradeComponent
  @ViewChild('biddingPrice', { static: false })
  biddingPrice!: BiddingPriceComponent
  @ViewChild('biddingCustomPrice', { static: false })
  biddingCustomPrice!: BiddingCustomPriceComponent
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private modal: NzModalService,
    private dialogRef: MatDialogRef<BiddingComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { bidId: string; supplierId: string; bidCode: string; itemName: string; supplierName: string },
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
  }

  saveAllData() {
    // this.saveData()
    const check0 = this.checkContent(0)
    const check1 = this.checkContent(1)
    const check2 = this.checkContent(2)
    const check3 = this.checkContent(3)
    if (check0 && check1 && check2 && check3) {
      this.modal.confirm({
        nzTitle: '<i>Bạn có thực sự muốn hoàn tất hồ sơ đã điền?</span>',
        nzContent: `<b>Hồ sơ đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu.
        Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận</b>`,
        nzOnOk: () => {
          this.saveData()
        },
      })
    } else {
      this.notifyService.showError('Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.')
      if (!check0) {
        this.jumpError(0)
      } else if (!check1) {
        this.jumpError(1)
      } else if (!check2) {
        this.jumpError(2)
      } else if (!check3) {
        this.jumpError(3)
      }
    }
  }

  saveData = () => {
    this.notifyService.showloading()
    const dataSave = {
      bidId: this.data.bidId,
      supplierId: this.data.supplierId,
      techInfo: this.biddingTech.getDataSave(),
      tradeInfo: this.biddingTrade.getDataSave(),
      priceInfo: this.biddingPrice.getDataSave(),
      customPriceInfo: this.biddingCustomPrice.getDataSave(),
    }
    this.apiService.post(this.apiService.BIDDING.CREATE_BID_SUPPLIER, dataSave).then(() => {
      this.notifyService.showSuccess('Đã bổ sung hồ sơ thành công!')
      this.closeDialog(true)
    })
  }

  pre() {
    this.current -= 1
  }

  next() {
    this.current += 1
  }

  jumpError(pos: number) {
    this.current = pos
  }

  checkDynamicColRequired(row: any, lstDynamicColRequired: any[]) {
    for (const col of lstDynamicColRequired) {
      // Nếu chưa nhập thì báo lỗi
      if (row[col.id]) row[col.id] += ''
      if (row[col.id] == null || row[col.id] === '' || row[col.id].trim() === '') {
        return true
      }
    }

    return false
  }

  checkContent(key: number) {
    let checkData: any
    let step = ''
    let flag = true
    const hasFomularValue = this.biddingPrice.getCheckFomular()
    const lstDynamicColRequired = this.biddingPrice.getDynamicColRequired()
    if (key === 0) {
      step = 'Yêu cầu kỹ thuật'
      checkData = this.biddingTech.getDataSave()
    } else if (key === 1) {
      step = 'Chào giá'
      checkData = this.biddingPrice.getDataSave()
    } else if (key === 2) {
      step = 'Cơ cấu giá'
      checkData = this.biddingCustomPrice.getDataSave()
    } else if (key === 3) {
      step = 'Điều kiện thương mại'
      checkData = this.biddingTrade.getDataSave()
    }

    for (const data1 of checkData) {
      data1.isError = false
      data1.errorText = ''
      if (hasFomularValue && key === 1) {
        // không kiểm tra require
      } else if (data1.isRequired && !data1.__childs__?.length) {
        if (!data1.value) {
          data1.isError = true
          data1.errorText = 'Vui lòng nhập dữ liệu trước'
          this.notifyService.showError(`${step} - Chưa nhập dữ liệu hạng mục [${data1.name}]`)
          flag = false
        }
      }
      if (key === 1 || key === 2) {
        if (data1.value != null && data1.value <= 0) {
          data1.isError = true
          data1.errorText = 'Vui lòng nhập giá lớn hơn 0'
          this.notifyService.showError(`${step} - Hạng mục [${data1.name}] giá phải lớn hơn 0`)
          flag = false
        }
      }

      if (hasFomularValue && key === 1) {
        // không kiểm tra require
      } else {
        if (data1.__childs__?.length > 0) {
          for (const data2 of data1.__childs__) {
            data2.isError = false
            data2.errorText = ''
            if (data2.isRequired && !data2.__childs__?.length) {
              if (!data2.value) {
                data2.isError = true
                data2.errorText = 'Vui lòng nhập dữ liệu trước'
                this.notifyService.showError(`${step} - Chưa nhập dữ liệu hạng mục [${data2.name}]`)
                flag = false
              }
            }

            if (data2.__childs__?.length > 0) {
              for (const data3 of data2.__childs__) {
                data3.isError = false
                data3.errorText = ''
                if (data3.isRequired) {
                  if (!data3.value) {
                    data3.isError = true
                    data3.errorText = 'Vui lòng nhập dữ liệu trước'
                    this.notifyService.showError(`${step} - Chưa nhập dữ liệu hạng mục [${data3.name}]`)
                    flag = false
                  }
                }
              }
            }
          }
        }
      }

      // Kiểm tra bắt buộc nhập thông tin cột động bảng chào giá
      if (key == 1 && lstDynamicColRequired && lstDynamicColRequired.length > 0) {
        if (this.checkDynamicColRequired(data1, lstDynamicColRequired)) {
          data1.isError = true
          data1.errorText = 'Vui lòng điền đủ thông tin các cột động bắt buộc'
          this.notifyService.showError(`${step} - Chưa điền đủ thông tin hạng mục [${data1.name}]`)
          flag = false
        }
        for (const data2 of data1.__childs__) {
          if (this.checkDynamicColRequired(data2, lstDynamicColRequired)) {
            data2.isError = true
            data2.errorText = 'Vui lòng điền đủ thông tin các cột động bắt buộc'
            this.notifyService.showError(`${step} - Chưa điền đủ thông tin hạng mục [${data2.name}]`)
            flag = false
          }
          for (const data3 of data2.__childs__) {
            if (this.checkDynamicColRequired(data3, lstDynamicColRequired)) {
              data3.isError = true
              data3.errorText = 'Vui lòng điền đủ thông tin các cột động bắt buộc'
              this.notifyService.showError(`${step} - Chưa điền đủ thông tin hạng mục [${data3.name}]`)
              flag = false
            }
          }
        }
      }
    }

    return flag
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
