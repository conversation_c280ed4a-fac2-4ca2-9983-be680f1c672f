import { Component, OnInit, Input } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import * as uuid from 'uuid'
import { AddBiddingPriceComponent } from '../add-bidding-price/add-bidding-price.component'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Borders, Workbook } from 'exceljs'
import * as fs from 'file-saver'

@Component({
  selector: 'app-bidding-custom-price',
  templateUrl: './bidding-custom-price.component.html',
})
export class BiddingCustomPriceComponent implements OnInit {
  @Input() bidId!: string
  @Input() supplierId!: string
  listOfData: any[] = []
  pageSize = enumData.Page.pageSizeMax
  loading = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  lstCurrency: any[] = []
  lstUnit: any[] = []
  isLoadData = false

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.loadData()
  }

  loadData = () => {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BIDDING.LOAD_DATA_BID_CUSTOMPRICE, { bidId: this.bidId, supplierId: this.supplierId })
      .then((res) => {
        this.listOfData = res || []
      })
      .finally(() => {
        this.notifyService.hideloading()
      })
  }

  getDataSave = () => {
    return this.listOfData
  }

  clickAddCustomPrice() {
    const item = {
      id: uuid.v4(),
      name: '',
      value: '',
      unit: null,
      currency: null,
      number: 0,
      isNew: true,
      sort: 0,
      isRequired: true,
      childs: [],
    }

    this.dialog
      .open(AddBiddingPriceComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((data) => {
        if (data && data.isNew) {
          this.listOfData.push(data)
          this.listOfData = this.listOfData.filter((c) => c.id !== '')
        }
      })
  }

  clickEditCustomPrice(item: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    const itemOld = { ...item }
    item.isNew = false
    this.dialog
      .open(AddBiddingPriceComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((data) => {
        if (!data) {
          item.name = itemOld.name
          item.value = itemOld.value
          item.number = itemOld.number
          item.unit = itemOld.unit
          item.currency = itemOld.currency
          item.sort = itemOld.sort
          item.isRequired = itemOld.isRequired
          item.isNew = itemOld.isNew
        }
      })
  }

  clickDeleteCustomPrice(item: any) {
    this.listOfData = this.listOfData.filter((c) => c.id !== item.id)
  }

  resetError(item: any) {
    item.isError = false
    item.errorText = ''
  }

  //#region excel cơ cấu giá
  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
    this.isLoadData = true
  }

  clickExportExcel() {
    this.notifyService.showloading()

    //#region Body Table
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const headerRow = worksheet.addRow(['ID', 'Tên hạng mục', 'Đơn vị tính', 'Đơn vị tiền tệ', 'Số lượng', 'Bắt buộc?', 'Đơn giá'])

    // Cell Style : Fill and Border
    const border: Partial<Borders> = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '08298A' },
      }
      cell.border = border
      cell.font = { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'FFFFFF' } }
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 70
          worksheet.getColumn(colNumber).hidden = true // ẩn cột
          break
        case 2:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    // Màu theo 2 level
    const arrColor = ['ebebeb', 'ffffff']
    for (const data of this.listOfData) {
      const rowData = [
        data.id || '', //'ID',
        data.name || '', //'Tên hạng mục',
        data.unit || '', //'Đơn vị tính',
        data.currency || '', //'Đơn vị tiền tệ',
        data.number || '', //'Số lượng',
        data.isRequired ? 'x' : '', //'Bắt buộc?',
        data.value || '', //'Đơn giá',
      ]
      const row = worksheet.addRow(rowData)
      row.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: arrColor[data.level - 1] },
        }
        cell.border = border
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
        cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
      })
    }
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template nhập hồ sơ cơ cấu giá.xlsx`
      fs.saveAs(blob, fileName)
      setTimeout(() => {
        this.notifyService.showSuccess('Tải template thành công!')
      }, 300)
    })
    //#endregion
  }

  async clickImportExcel(ev: any) {
    this.notifyService.showloading()
    if (!this.isLoadData) {
      await this.loadAllList()
    }
    let workBook = null
    let jsonData: any[] = []
    const lstHeader = ['id', 'name', 'unit', 'currency', 'number', 'isRequired', 'value']

    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      //#region check template
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== 'Tên hạng mục' ||
        header.unit !== 'Đơn vị tính' ||
        header.currency !== 'Đơn vị tiền tệ' ||
        header.number !== 'Số lượng' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.value !== 'Đơn giá'
      ) {
        this.notifyService.showError('File không đúng template (cột đã bị thay đổi)!')
        return
      }
      //#endregion

      //#region check value
      for (const row of jsonData) {
        row.sort = 0
        if (!row.id) row.id = uuid.v4()

        if (row.isRequired == 'x' || row.isRequired == 'X') {
          row.isRequired = true
        } else row.isRequired = false
        if (row.name == null || row.name === '') {
          this.notifyService.showError('Tên hạng mục không được để trống')
        }
        row.unit = (row.unit || '') + ''
        if (row.unit?.length > 0) {
          const objUnit = this.lstUnit.find((c: any) => c.code === row.unit)
          if (!objUnit) {
            this.notifyService.showError(`Đơn vị tính [${row.unit}] không tồn tại`)
          }
        }
        row.currency = (row.currency || '') + ''
        if (row.currency != null && row.currency.length > 0) {
          const objCurrency = this.lstCurrency.find((c: any) => c.code === row.currency)
          if (!objCurrency) {
            this.notifyService.showError(`Đơn vị tiền tệ ${row.currency} không tồn tại`)
          }
        }
        if (row.number == null || typeof row.number !== 'number') {
          this.notifyService.showError('Số lượng là số, không được để trống')
        }

        if (row.isRequired && (row.value == null || row.value === '')) {
          this.notifyService.showError(`Đơn giá của hạng mục [${row.name}] là bắt buộc không được để trống!`)
          return
        }
        if (row.value != null && row.value !== '') {
          const value = +row.value
          if (isNaN(value) || !isFinite(value)) {
            this.notifyService.showError(`[Đơn giá] là [${row.value}] không phải kiểu Number`)
            return
          }
        }
      }
      this.listOfData = jsonData
      //#endregion

      //#region fill value
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
      //#endregion
    }
  }
  //#endregion
}
