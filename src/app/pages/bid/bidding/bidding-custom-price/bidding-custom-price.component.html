<div *ngIf="bidId">
  <nz-row class="mt-2">
    <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
      (click)="clickAddCustomPrice()" nzType="primary" class="mr-2">
      Thê<PERSON> hạng mục cơ cấu giá
    </button>
    <button class="mr-2" nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
      (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>Xuất excel
    </button>
    <input class="hidden" type="file" id="fileCustomPrice"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" (change)="clickImportExcel($event)"
      onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
    <label nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="fileCustomPrice"
      class="lable-custom-file">
      <span nz-icon nzType="upload"></span> Nhập excel
    </label>
  </nz-row>

  <nz-row class="mt-1">
    <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th class="hidden">ID</th>
          <th>Tên hạng mục</th>
          <th>Đơn vị tính</th>
          <th>Đơn vị tiền tệ</th>
          <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th>{{ language_key?.PRICE || 'Đơn giá' }}</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data1 of listOfData">
          <td class="hidden">{{ data1.id }}</td>
          <td class="mw-25" (click)="clickEditCustomPrice(data1)">
            <span *ngIf="data1.isRequired" class="text-danger">*</span>
            <span nz-tooltip nzTooltipPlacement="topLeft" [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
          </td>
          <td (click)="clickEditCustomPrice(data1)">
            {{ data1.unit }}</td>
          <td (click)="clickEditCustomPrice(data1)">
            {{ data1.currency }}</td>
          <td (click)="clickEditCustomPrice(data1)" class="text-right">{{ data1.number | number }}</td>
          <td (click)="resetError(data1)">
            <input nz-input currencyMask [(ngModel)]="data1.value" name="value" />
            <span *ngIf="data1.isError" class="text-danger">{{data1.errorText}}</span>
          </td>
          <td>
            <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục cơ cấu giá này?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="clickDeleteCustomPrice(data1)" nz-tooltip nzTooltipTitle="Xoá hạng mục" nz-button nzDanger>
              <span nz-icon nzType="delete"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>