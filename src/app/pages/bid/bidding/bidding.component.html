<div *ngIf="data.bidId && !isError && !isSuccess">
  <div matDialogTitle>
    <h1>Nộ<PERSON> <PERSON><PERSON> sơ gói thầu {{data.bidCode}} NCC {{data.supplierName}}</h1>
    <input nz-input disabled value="Item {{data.itemName}}" />
    <nz-steps [nzCurrent]="current" class="mt-3">
      <nz-step nzTitle="Yêu cầu kỹ thuật"></nz-step>
      <nz-step nzTitle="Chào giá"></nz-step>
      <nz-step nzTitle="Cơ cấu giá"></nz-step>
      <nz-step nzTitle="Điều kiện thương mại&emsp;"></nz-step>
    </nz-steps>
  </div>

  <div matDialogContent>
    <div [ngClass]="{ 'hidden': current !== 0 }">
      <app-bidding-tech [bidId]="data.bidId" [supplierId]="data.supplierId" #biddingTech></app-bidding-tech>
    </div>
    <div [ngClass]="{ 'hidden': current !== 1 }">
      <app-bidding-price [bidId]="data.bidId" [supplierId]="data.supplierId" #biddingPrice></app-bidding-price>
    </div>
    <div [ngClass]="{ 'hidden': current !== 2 }">
      <app-bidding-custom-price [bidId]="data.bidId" [supplierId]="data.supplierId" #biddingCustomPrice>
      </app-bidding-custom-price>
    </div>
    <div [ngClass]="{ 'hidden': current !== 3 }">
      <app-bidding-trade [bidId]="data.bidId" [supplierId]="data.supplierId" #biddingTrade></app-bidding-trade>
    </div>
  </div>

  <div matDialogActions>
    <button nz-button class="mr-2" (click)="pre()"
      *ngIf="current > 0 && authenticationService.checkPermission([enumRole], action.Create.code)">
      <span>Quay lại</span>
    </button>
    <button nz-button class="mr-2" (click)="next()"
      *ngIf="current < 3 && authenticationService.checkPermission([enumRole], action.Create.code)">
      <span>Tiếp tục</span>
    </button>
    <button nz-button nzType="primary" (click)="saveAllData()"
      *ngIf="current === 3 && authenticationService.checkPermission([enumRole], action.Create.code)"><span>Nộp hồ
        sơ</span></button>
  </div>
</div>