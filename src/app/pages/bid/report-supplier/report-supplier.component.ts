import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import * as moment from 'moment'
import { SupplierCapacityModalComponent } from '../supplier-capacity/supplier-capacity-modal/supplier-capacity-modal.component'
import * as XLSX from 'xlsx'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './report-supplier.component.html' })
export class ReportSupplierComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataServiceChild: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  dataStatus = [
    enumData.SupplierServiceStatus.MoiDangKy,
    enumData.SupplierServiceStatus.CapNhatThongTin,
    enumData.SupplierServiceStatus.PhuTrachDuyet,
    enumData.SupplierServiceStatus.PhuTrachKhongDuyet,
    enumData.SupplierServiceStatus.KhongDuyet,
    enumData.SupplierServiceStatus.DaDuyet,
    enumData.SupplierServiceStatus.NgungHoatDong,
  ]
  screenWidth: any
  dataExpertiseStatus = this.coreService.convertObjToArray(enumData.SupplierServiceExpertiseStatus)
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.screenWidth = window.screen.width
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = { isDeleted: false }
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.statusExpertise && this.dataSearch.statusExpertise !== '') {
      where.statusExpertise = this.dataSearch.statusExpertise
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.supplierName && this.dataSearch.supplierName.length !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.REPORT.GET_REPORT_SUPPLIER, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0].sort((a: any, b: any) => a.serviceId - b.serviceId)
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  showModalDetail(item: any) {
    this.dialog
      .open(SupplierCapacityModalComponent, {
        disableClose: false,
        data: {
          supplierServiceId: item.id,
          supplierId: item.supplierId,
          serviceId: item.serviceId,
          status: item.status,
          comment: item.comment,
          approverComment: item.approverComment,
          itemName: item.itemName,
        },
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickExportExcel() {
    const tbl = document.getElementById('reprot-html-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Báo cáo phân tích nhà cung cấp.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
