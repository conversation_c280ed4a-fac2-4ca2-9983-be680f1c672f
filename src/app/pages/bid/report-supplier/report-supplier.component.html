<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tuỳ chọn tìm kiếm" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Chọn lĩnh vực mua hàng</nz-form-label>
          <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose" [nzLoadData]="loadDataService"> </nz-cascader>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}
          </nz-form-label>
          <input
            nz-input
            [placeholder]="language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp'"
            [(ngModel)]="dataSearch.supplierName"
            name="supplierName"
          />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
            <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8">
      <nz-col nzSpan="6" class="mt-1" *ngIf="false">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Trạng thái thẩm định</nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusExpertise" name="statusExpertise" nzPlaceHolder="Trạng thái thẩm định">
            <nz-option *ngFor="let item of dataExpertiseStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Từ ngày đăng ký</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Từ ngày đăng ký">
            <nz-date-picker [(ngModel)]="dataSearch.fromDate" name="dataSearch.fromDate"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Đến ngày đăng ký</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Đến ngày đăng ký">
            <nz-date-picker [(ngModel)]="dataSearch.toDate" name="dataSearch.toDate"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)" class="mr-2">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button (click)="clickExportExcel()">
          <span nz-icon nzType="download"></span>Xuất excel
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<!-- <nz-row class="mt-3 table-scroll-x mb-3" [ngStyle]="{'min-width': screenWidth > 1400 ? '100%' : '' }"> -->
<nz-table
  class="mt-3"
  nz-col
  nzSpan="24"
  id="reprot-html-table"
  [nzData]="listOfData"
  [(nzPageSize)]="pageSize"
  [nzLoading]="loading"
  [nzShowPagination]="false"
  nzBordered
  [nzScroll]="{ x: '2000px' }"
  nzTableLayout="fixed"
>
  <thead>
    <tr>
      <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
      <th>Lĩnh vực kinh doanh</th>
      <th>Điểm năng lực</th>
      <th>Thẩm định</th>
      <th>Phụ trách mua hàng</th>
      <th>Người duyệt</th>
      <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
      <th>Ngày đăng ký</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let data of listOfData">
      <td class="mw-25" (click)="showModalDetail(data)">{{ data.supplierName }}</td>
      <td (click)="showModalDetail(data)">{{ data.itemName }}</td>
      <td (click)="showModalDetail(data)" class="text-right">{{ data.score }}</td>
      <td (click)="showModalDetail(data)">
        {{ data.lastUpdateExpertise ? (data.lastUpdateExpertise | date : 'dd/MM/yyyy') : '' }}
      </td>
      <td (click)="showModalDetail(data)">{{ data.acceptEmployeeName }}</td>
      <td (click)="showModalDetail(data)">{{ data.approveByName }}</td>
      <td (click)="showModalDetail(data)">
        {{ data.statusName }}
      </td>
      <td (click)="showModalDetail(data)">{{ data.createdAt | date : 'dd/MM/yyyy' }}</td>
    </tr>
  </tbody>
</nz-table>
<!-- </nz-row> -->
