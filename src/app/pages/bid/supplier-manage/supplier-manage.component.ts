import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditSupplierComponent } from './add-or-edit-supplier/add-or-edit-supplier.component'
import { SupplierCapacityModalComponent } from '../supplier-capacity/supplier-capacity-modal/supplier-capacity-modal.component'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { SupplierServiceComponent } from './supplier-service/supplier-service.component'

@Component({ templateUrl: './supplier-manage.component.html' })
export class SupplierManageComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataSearch: any = {}
  dataStatus = this.coreService.convertObjToArray(enumData.SupplierStatus)
  listOfData: any[] = []
  errorString!: string
  isExporting = false
  lstErrorImport: any[] = []
  isVisibleError = false
  isVisibleChangePw = false
  supplierChoose: any

  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any

  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    this.action = this.enumProject.Action
    this.searchData()
  }

  async searchData(reset = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = { isDeleted: false }
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.supplierCode && this.dataSearch.supplierCode.length !== '') {
      where.supplierCode = this.dataSearch.supplierCode
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.SUPPLIER.SUPPLIER_PAGINATION, dataSearch).then((res) => {
      this.loading = false
      this.total = res[1]
      this.listOfData = res[0]
      for (const item of this.listOfData) {
        item.itemName = item.listSupplierService.map((c: any) => c.itemName).join()
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  showModalDetail(item: { id: any; listSupplierService: any }) {
    this.dialog
      .open(SupplierCapacityModalComponent, {
        disableClose: false,
        data: {
          supplierId: item.id,
          listSupplierService: item.listSupplierService,
        },
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickExportExcel() {
    this.notifyService.showloading()
    let lstDataExport: any[] = []

    //#region header
    const header: any = {
      code: (this.language_key?.INTERPRISE_CODE || 'Mã doanh nghiệp') + ' *',
      name: (this.language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp') + ' *',
      dealName: 'Tên giao dịch *',
      createYear: 'Năm thành lập công ty *',
      address: 'Địa chỉ trụ sở *',
      dealAddress: 'Địa chỉ giao dịch *',
      represen: 'Người đại diện pháp luật *',
      chief: 'Tên giám đốc *',
      capital: 'Vốn điều lệ (tỷ đồng) *',
      assets: 'Tài sản cố định (tỷ đồng) *',
      bankNumber: 'Số tài khoản ngân hàng *',
      bankname: 'Tên ngân hàng *',
      bankBrand: 'Tên chi nhánh ngân hàng *',
      description: 'Mô tả về doanh nghiệp *',
      contactName: 'Người liên hệ *',
      phone: 'Số điện thoại *',
      email: 'Email *',
      itemName: 'Lĩnh vực kinh doanh',
    }
    lstDataExport.push(header)
    //#endregion

    const where: any = { isDeleted: false }
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.supplierCode && this.dataSearch.supplierCode.length !== '') {
      where.supplierCode = this.dataSearch.supplierCode
    }
    const dataSearch: any = {
      where,
      skip: 0,
      take: this.pageSizeMax,
    }
    this.apiService.post(this.apiService.SUPPLIER.SUPPLIER_PAGINATION, dataSearch).then((res) => {
      for (const item of res[0]) {
        const row: any = {}
        row.code = item.code
        row.name = item.name
        row.dealName = item.dealName
        row.createYear = item.createYear
        row.address = item.address
        row.dealAddress = item.dealAddress
        row.represen = item.represen
        row.chief = item.chief
        row.capital = item.capital
        row.assets = item.assets
        row.bankNumber = item.bankNumber
        row.bankname = item.bankname
        row.bankBrand = item.bankBrand
        row.description = item.description
        row.contactName = item.contactName
        row.phone = item.phone
        row.email = item.email
        row.itemName = item.listSupplierService.map((c: any) => c.itemName).join(`, `)

        lstDataExport.push(row)
      }

      var ws = XLSX.utils.json_to_sheet(lstDataExport, {
        skipHeader: true,
      })
      var wb = XLSX.utils.book_new()
      const fileName = `Danh sách nhà cung cấp.xlsx`
      XLSX.utils.book_append_sheet(wb, ws)

      XLSX.writeFile(wb, fileName)

      setTimeout(() => {
        this.notifyService.hideloading()
      }, 100)
    })
  }

  changePassword(object: any) {
    this.supplierChoose = { ...object }
    this.isVisibleChangePw = true
  }

  confirmChangePassword() {
    this.notifyService.showloading()
    if (this.supplierChoose.newPassword != this.supplierChoose.confirmNewPassword) {
      this.notifyService.showError('Mật khẩu không khớp!')
      return
    }

    this.apiService
      .post(this.apiService.SUPPLIER.UPDATE_PASSWORD, {
        id: this.supplierChoose.id,
        newPassword: this.supplierChoose.newPassword,
        confirmNewPassword: this.supplierChoose.confirmNewPassword,
      })
      .then((result) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.isVisibleChangePw = false
      })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditSupplierComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditSupplierComponent, {
        disableClose: false,
        data: object,
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickExportExcelTemplate() {
    this.notifyService.showloading()
    let lstDataExport: any[] = []
    //#region header
    const header: any = {
      zenId: (this.language_key?.NO || 'STT') + ' *',
      code: (this.language_key?.INTERPRISE_CODE || 'Mã doanh nghiệp') + ' *',
      name: (this.language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp') + ' *',
      dealName: 'Tên giao dịch *',
      createYear: 'Năm thành lập công ty *',
      address: 'Địa chỉ trụ sở *',
      dealAddress: 'Địa chỉ giao dịch *',
      represen: 'Người đại diện pháp luật *',
      chief: 'Tên giám đốc *',
      capital: 'Vốn điều lệ (tỷ đồng) *',
      assets: 'Tài sản cố định (tỷ đồng) *',
      bankNumber: 'Số tài khoản ngân hàng *',
      bankname: 'Tên ngân hàng *',
      bankBrand: 'Tên chi nhánh ngân hàng *',
      description: 'Mô tả về doanh nghiệp *',
      contactName: 'Người liên hệ *',
      phone: 'Số điện thoại *',
      email: 'Email *',
      username: 'Tên đăng nhập *',
      password: 'Mật khẩu *',
    }
    lstDataExport.push(header)
    //#endregion

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `Template import supplier.xlsx`
    const sheetName = 'Supplier'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)
    ws['!cols'] = [
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
      { width: 40 },
    ]
    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  clickImportExcel(ev: any) {
    this.notifyService.showloading()
    this.lstErrorImport = []
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'code',
          'name',
          'dealName',
          'createYear',
          'address',
          'dealAddress',
          'represen',
          'chief',
          'capital',
          'assets',
          'bankNumber',
          'bankname',
          'bankBrand',
          'description',
          'contactName',
          'phone',
          'email',
          'username',
          'password',
        ],
      })

      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()
      // Kiểm tra header
      if (
        header.zenId !== (this.language_key?.NO || 'STT') + ' *' ||
        header.code !== (this.language_key?.INTERPRISE_CODE || 'Mã doanh nghiệp') + ' *' ||
        header.name !== (this.language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp') + ' *' ||
        header.dealName !== 'Tên giao dịch *' ||
        header.createYear !== 'Năm thành lập công ty *' ||
        header.address !== 'Địa chỉ trụ sở *' ||
        header.dealAddress !== 'Địa chỉ giao dịch *' ||
        header.represen !== 'Người đại diện pháp luật *' ||
        header.chief !== 'Tên giám đốc *' ||
        header.capital !== 'Vốn điều lệ (tỷ đồng) *' ||
        header.assets !== 'Tài sản cố định (tỷ đồng) *' ||
        header.bankNumber !== 'Số tài khoản ngân hàng *' ||
        header.bankname !== 'Tên ngân hàng *' ||
        header.bankBrand !== 'Tên chi nhánh ngân hàng *' ||
        header.description !== 'Mô tả về doanh nghiệp *' ||
        header.contactName !== 'Người liên hệ *' ||
        header.phone !== 'Số điện thoại *' ||
        header.email !== 'Email *' ||
        header.username !== 'Tên đăng nhập *' ||
        header.password !== 'Mật khẩu *'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(`File không đúng template import supplier`)
        return
      }

      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có NCC cần import!`)
        return
      }

      jsonData = jsonData.filter(
        (c: any) => c.zenId != null && c.zenId !== '' && (c.zenId > 0 || (typeof c.zenId == 'string' && c.zenId.trim() !== ''))
      )
      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có NCC cần import. Lưu ý, chỉ dòng có STT mới được import!`)
        return
      }

      this.apiService.post(this.apiService.SUPPLIER.IMPORT, { lstData: jsonData }).then((res) => {
        if (!res) this.notifyService.hideloading()
        // Lỗi khi kiểm tra trước lúc vào transaction (không import)
        if (res.isCheckError) {
          this.notifyService.showError(`${res.message}`)
          this.lstErrorImport = res.lstError
          this.isVisibleError = true
        }
        // Lỗi khi vào transaction (import 1 phần)
        else if (res.lstError.length > 0) {
          this.notifyService.showError(`${res.message}`)
          this.lstErrorImport = res.lstError
          this.isVisibleError = true
          this.searchData(true)
        }
        // import 100%
        else {
          this.notifyService.showSuccess(`${res.message}`)
          this.searchData(true)
          this.lstErrorImport = []
        }
      })
    }
  }

  clickShowErrorImport() {
    this.isVisibleError = true
  }

  closeModelError() {
    this.isVisibleError = false
  }

  /** Danh sách LVMH của NCC */
  viewSupplierService(data: any) {
    this.dialog
      .open(SupplierServiceComponent, { data: { id: data.id, name: data.name } })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }
}
