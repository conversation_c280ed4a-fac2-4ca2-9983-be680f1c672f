<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="6" class="mt-2" *ngIf="authenticationService.checkPermission([enumRole], action.View.code)">
    <nz-col nzSpan="16">
      <nz-cascader [nzShowSearch]="true" [nzOptions]="cascaderDataOptions" [(ngModel)]="serviceChoose"
        nzPlaceHolder="Chọn lĩnh vực kinh doanh để thêm">
      </nz-cascader>
    </nz-col>
    <nz-col nzSpan="8">
      <button nz-button (click)="addService()" [disabled]="!serviceChoose.length">
        <span nz-icon nzType="plus-square"></span> Thêm lĩnh vực kinh doanh
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false"
      nzBordered [nzLoading]="loading">
      <thead>
        <tr>
          <th>{{ language_key?.NO || 'STT' }}</th>
          <th>Lĩnh vực kinh doanh</th>
          <th>Trạng thái</th>
          <th>Trạng thái thẩm định</th>
          <th>Phân loại NCC</th>
          <th>Tùy chọn</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of listOfData; let i = index">
          <td>{{ i + 1 }}</td>
          <td>{{ data.itemName }}</td>
          <td>{{ data.statusName }}</td>
          <td>{{ data.statusExpertiseName }}</td>
          <td *ngIf="!data.isEdit">{{ data.supplierType }}</td>
          <td *ngIf="data.isEdit">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn phân loại NCC" [(ngModel)]="data.supplierType2">
              <nz-option *ngFor="let itemList of dataSupplierServiceType" [nzLabel]="itemList.name"
                [nzValue]="itemList.code"></nz-option>
            </nz-select>
          </td>
          <td>
            <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
              (click)="data.isEdit = !data.isEdit; data.netPrice2 = data.netPrice; data.planningDeliveryDate2 = data.planningDeliveryDate;data.supplierType2 = data.supplierType;"
              nz-tooltip nzTooltipTitle="Chỉnh sửa/Hủy chỉnh sửa " nz-button class="mr-2">
              <span *ngIf="!data.isEdit" nz-icon nzType="edit"></span>
              <span *ngIf="data.isEdit" nz-icon nzType="rollback"></span>
            </button>
            <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code) && data.isEdit"
              (click)="saveSupplierService(data)" nz-tooltip [nzTooltipTitle]="language_key?.SAVE || 'Lưu'" nz-button
              nzType="primary" class="mr-2">
              <span nz-icon nzType="save"></span>
            </button>
            <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-tooltip
              nzTooltipTitle="Thông tin năng lực" nz-button (click)="showCapacity(data)">
              <span nz-icon nzType="solution"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>