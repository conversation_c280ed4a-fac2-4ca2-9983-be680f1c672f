<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row>
    <button class="mr-2" nz-button (click)="loadCapacity()" nzDanger>
      <span nz-icon nzType="redo"></span>
      Tải template mới từ Item
    </button>
    <button class="mr-2" nz-button (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>Xuất excel
    </button>
    <input class="hidden" type="file" id="fileCapacity" (change)="clickImportExcel($event)" onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
    <label for="fileCapacity" class="lable-custom-file">
      <span nz-icon nzType="upload"></span> Nhập excel
    </label>
  </nz-row>
  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="lstCapacity" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th>Tiêu chí</th>
          <th>Kiểu dữ liệu</th>
          <th>Giá trị</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data1 of lstCapacity">
          <tr>
            <td class="w-50">
              <span *ngIf="data1.isRequired" class="text-danger">*</span>
              <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
            </td>
            <td>{{data1.type}}</td>
            <td (click)="resetError(data1)">
              <div *ngIf="data1.__childs__.length == 0">
                <div *ngIf="!data1.isChangeByYear">
                  <div *ngIf="data1.type === dataType.String.code">
                    <input nz-input [(ngModel)]="data1.value" name="value" />
                  </div>
                  <div *ngIf="data1.type === dataType.Number.code">
                    <input nz-input currencyMask [(ngModel)]="data1.value" name="value" />
                  </div>
                  <div *ngIf="data1.type === dataType.Date.code">
                    <nz-date-picker [(ngModel)]="data1.value" name="value"> </nz-date-picker>
                  </div>
                  <div *ngIf="data1.type === dataType.List.code">
                    <nz-select nzShowSearch nzAllowClear [(ngModel)]="data1.value" name="value">
                      <nz-option *ngFor="let itemList of data1.listDetail" [nzLabel]="itemList.name"
                        [nzValue]="itemList.id"></nz-option>
                    </nz-select>
                  </div>
                  <div *ngIf="data1.type === dataType.File.code">
                    <nz-input-group nzSearch>
                      <input class="passenger-input" nz-input [(ngModel)]="data1.value" name="value" disabled />
                      <button nz-button nzType="primary" (click)="handleClearFile(data1)">
                        <span nz-icon nzType="delete"></span>
                      </button>
                    </nz-input-group>
                    <label [for]="'zen' + data1.id" class="custom-file-upload">
                      <span nz-icon nzType="upload"></span> Upload File
                    </label>
                    <input class="hidden" type="file" [id]="'zen' + data1.id"
                      (change)="handleFileInput($event, data1)" />
                  </div>
                </div>
                <div *ngIf="data1.isChangeByYear">
                  <div *ngFor="let detailYear of data1.listDetailYear; index as i1;" nz-col nzSpan="24"
                    class="form-item">
                    <nz-input-group nzCompact class="change-by-year-item">
                      <input nz-input currencyMask placeholder="Năm" [(ngModel)]="detailYear.year"
                        [ngModelOptions]="{standalone: true}" class="year" />
                      <input *ngIf="data1.type === dataType.String.code" nz-input [(ngModel)]="detailYear.value"
                        [ngModelOptions]="{standalone: true}" class="value" placeholder="Giá trị" />
                      <input *ngIf="data1.type === dataType.Number.code" nz-input currencyMask placeholder="Giá trị"
                        [(ngModel)]="detailYear.value" [ngModelOptions]="{standalone: true}" class="value" />

                      <input *ngIf="data1.type === dataType.File.code" nz-input placeholder="Chọn file" disabled
                        [ngModelOptions]="{standalone: true}" class="value" [(ngModel)]="detailYear.value" />
                      <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(data1, i1)">
                        <span nz-icon nzType="minus"></span>
                      </button>
                      <label *ngIf="data1.type === dataType.File.code" [for]="'zen' + data1.id + i1"
                        class="custom-file-upload">
                        <span nz-icon nzType="upload"></span> Upload File
                      </label>
                      <input *ngIf="data1.type === dataType.File.code" class="hidden" type="file"
                        [id]="'zen' + data1.id + i1" (change)="handleFileInput($event, detailYear)">

                    </nz-input-group>
                  </div>
                  <nz-col nzSpan="24" class="form-item">
                    <button nz-button type="button" nzType="dashed" class="add-change-by-year"
                      (click)="addChangeByYear(data1)">
                      <span nz-icon nzType="plus"></span>
                      Thêm giá trị
                    </button>
                  </nz-col>
                </div>
              </div>

              <span *ngIf="data1.isError" class="text-danger">{{data1.errorText}}</span>
            </td>
          </tr>
          <ng-container>
            <tr *ngFor="let data2 of data1.__childs__">
              <td [nzIndentSize]="20" class="w-50">
                <span *ngIf="data2.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="data2.name">{{ data2.name }}</span>
              </td>
              <td>{{data2.type}}</td>
              <td (click)="resetError(data2)">
                <div *ngIf="!data2.isChangeByYear">
                  <div *ngIf="data2.type === dataType.String.code">
                    <input nz-input [(ngModel)]="data2.value" name="value" />
                  </div>
                  <div *ngIf="data2.type === dataType.Number.code">
                    <input nz-input currencyMask [(ngModel)]="data2.value" name="value" />
                  </div>
                  <div *ngIf="data2.type === dataType.Date.code">
                    <nz-date-picker [(ngModel)]="data2.value" name="value"> </nz-date-picker>
                  </div>
                  <div *ngIf="data2.type === dataType.List.code">
                    <nz-select nzShowSearch nzAllowClear [(ngModel)]="data2.value" name="value">
                      <nz-option *ngFor="let itemList of data2.listDetail" [nzLabel]="itemList.name"
                        [nzValue]="itemList.id"></nz-option>
                    </nz-select>
                  </div>
                  <div *ngIf="data2.type === dataType.File.code">
                    <nz-input-group nzSearch>
                      <input class="passenger-input" nz-input [(ngModel)]="data2.value" name="value" disabled />
                      <button nz-button nzType="primary" (click)="handleClearFile(data2)">
                        <span nz-icon nzType="delete"></span>
                      </button>
                    </nz-input-group>
                    <label [for]="'zen' + data2.id" class="custom-file-upload">
                      <span nz-icon nzType="upload"></span> Upload File
                    </label>
                    <input class="hidden" type="file" [id]="'zen' + data2.id"
                      (change)="handleFileInput($event, data2)" />
                  </div>
                </div>
                <div *ngIf="data2.isChangeByYear">
                  <nz-col nzSpan="24" *ngFor="let detailYear of data2.listDetailYear; index as i2;" class="form-item">
                    <nz-input-group nzCompact class="change-by-year-item">
                      <input nz-input currencyMask placeholder="Năm" [(ngModel)]="detailYear.year"
                        [ngModelOptions]="{standalone: true}" class="year" />
                      <input *ngIf="data2.type === dataType.String.code" nz-input [(ngModel)]="detailYear.value"
                        [ngModelOptions]="{standalone: true}" class="value" placeholder="Giá trị" />
                      <input *ngIf="data2.type === dataType.Number.code" nz-input currencyMask placeholder="Giá trị"
                        [(ngModel)]="detailYear.value" [ngModelOptions]="{standalone: true}" class="value" />

                      <input *ngIf="data2.type === dataType.File.code" nz-input placeholder="Chọn file" disabled
                        [ngModelOptions]="{standalone: true}" class="value" [(ngModel)]="detailYear.value" />
                      <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(data2, i2)">
                        <span nz-icon nzType="minus"></span>
                      </button>
                      <label *ngIf="data2.type === dataType.File.code" [for]="'zen' + data2.id + i2"
                        class="custom-file-upload">
                        <span nz-icon nzType="upload"></span> Upload File
                      </label>
                      <input *ngIf="data2.type === dataType.File.code" class="hidden" type="file"
                        [id]="'zen' + data2.id + i2" (change)="handleFileInput($event, detailYear)">

                    </nz-input-group>
                  </nz-col>
                  <nz-col nzSpan="24" class="form-item">
                    <button nz-button type="button" nzType="dashed" class="add-change-by-year"
                      (click)="addChangeByYear(data2)">
                      <span nz-icon nzType="plus"></span>
                      Thêm giá trị
                    </button>
                  </nz-col>
                </div>

                <span *ngIf="data2.isError" class="text-danger">{{data2.errorText}}</span>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>
</div>

<nz-row matDialogActions class="mt-3">
  <nz-col nzSpan="24" class="text-center">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button
      (click)="saveCapacity()" nzType="primary">
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
  </nz-col>
</nz-row>