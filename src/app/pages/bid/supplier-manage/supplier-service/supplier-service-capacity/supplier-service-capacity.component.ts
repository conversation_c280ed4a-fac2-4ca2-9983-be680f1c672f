import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'
import * as $ from 'jquery'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Borders, Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { NzModalService } from 'ng-zorro-antd/modal'

@Component({
  templateUrl: './supplier-service-capacity.component.html',
  styleUrls: ['./supplier-service-capacity.component.scss'],
})
export class SupplierServiceCapacityComponent implements OnInit {
  modalTitle = 'Nhập thông tin năng lực'
  lstCapacity: any[] = []
  loading = false
  pageSize = enumData.Page.pageSizeMax
  public dataType = enumData.DataType
  maxSizeUpload = enumData.maxSizeUpload
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private modal: NzModalService,
    private dialogRef: MatDialogRef<SupplierServiceCapacityComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.modalTitle += ` [${this.data.itemName}]`
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    this.loadData()
  }

  loadData = () => {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SUPPLIER.LOAD_CAPACITY, { supplierServiceId: this.data.id }).then((res) => {
      this.lstCapacity = res || []
      this.notifyService.hideloading()
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  resetError(item: { isError: boolean; errorText: string }) {
    item.isError = false
    item.errorText = ''
  }

  handleFileInput(event: any, rowData: any) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) rowData.value = res[0]
        else rowData.value = ''
      })
    }
  }

  handleClearFile(rowData: { value: any; id: any }) {
    rowData.value = undefined
    $(`#zen${rowData.id}`).val('')
  }

  addChangeByYear(item: any) {
    if (!item.listDetailYear) item.listDetailYear = []
    item.listDetailYear.push({
      year: null,
      type: item.type,
      value: item.type === enumData.DataType.Number.code ? null : '',
    })
  }

  deleteChangeByYear(item: any, index: any) {
    item.listDetailYear.splice(index, 1)
  }

  saveCapacity() {
    this.notifyService.showloading()
    for (const data1 of this.lstCapacity) {
      data1.isError = false
      data1.errorText = ''
      if (data1.isRequired && !data1.__childs__?.length) {
        if (!data1.isChangeByYear && !data1.value) {
          data1.isError = true
          data1.errorText = 'Vui lòng nhập dữ liệu trước'
          this.notifyService.showError(`Chưa nhập dữ liệu cho tiêu chí: ${data1.name}`)
          return
        }

        if (data1.isChangeByYear && (!data1.listDetailYear || data1.listDetailYear.length == 0)) {
          data1.isError = true
          data1.errorText = 'Vui lòng nhập dữ liệu trước'
          this.notifyService.showError(`Chưa nhập dữ liệu cho tiêu chí: ${data1.name}`)
          return
        }

        if (data1.isChangeByYear && data1.listDetailYear?.length > 0) {
          if (data1.listDetailYear.some((c: any) => c.year == 0 || c.year == null || c.value == null || c.value === '')) {
            data1.isError = true
            data1.errorText = 'Vui lòng nhập đầy đủ dữ liệu'
            this.notifyService.showError(`Chưa nhập đầy đủ dữ liệu cho tiêu chí: ${data1.name}`)
            return
          }
        }
      }

      if (data1.__childs__?.length > 0) {
        for (const data2 of data1.__childs__) {
          data2.isError = false
          data2.errorText = ''
          if (!data2.isRequired) continue

          if (!data2.isChangeByYear && !data2.value) {
            data2.isError = true
            data2.errorText = 'Vui lòng nhập dữ liệu trước'
            this.notifyService.showError(`Chưa nhập dữ liệu cho tiêu chí: ${data2.name}`)
            return
          }

          if (data2.isChangeByYear && (!data2.listDetailYear || data2.listDetailYear.length == 0)) {
            data2.isError = true
            data2.errorText = 'Vui lòng nhập dữ liệu trước'
            this.notifyService.showError(`Chưa nhập dữ liệu cho tiêu chí: ${data2.name}`)
            return
          }

          if (data2.isChangeByYear && data2.listDetailYear && data2.listDetailYear.length > 0) {
            if (data2.listDetailYear.some((c: any) => c.year == 0 || c.year == null || c.value == null || c.value === '')) {
              data2.isError = true
              data2.errorText = 'Vui lòng nhập đầy đủ dữ liệu'
              this.notifyService.showError(`Chưa nhập đầy đủ dữ liệu cho tiêu chí: ${data2.name}`)
              return
            }
          }
        }
      }
    }

    this.apiService
      .post(this.apiService.SUPPLIER.SAVE_SUPPLIER_SERVICE_CAPACITY, {
        supplierServiceId: this.data.id,
        lstCapacity: this.lstCapacity,
      })
      .then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
        this.closeDialog(true)
      })
  }

  //#region Excel
  dicExcel: any = {}
  clickExportExcel() {
    this.notifyService.showloading()
    //#region Kiểm tra data
    if (this.lstCapacity.length == 0) {
      this.notifyService.showError('Item chưa có thiết lập tiêu chí năng lực.')
      return
    }
    const lstOfDataExcel: any[] = []
    let isRemove = false
    for (const itemLv1 of this.lstCapacity) {
      itemLv1.level = 1
      if (itemLv1.type !== this.dataType.File.code || itemLv1.__childs__.length > 0) {
        lstOfDataExcel.push(itemLv1)
      } else isRemove = true
      for (const itemLv2 of itemLv1.__childs__) {
        itemLv2.level = 2
        if (itemLv2.type !== this.dataType.File.code) {
          lstOfDataExcel.push(itemLv2)
        } else isRemove = true
      }
    }
    if (isRemove) {
      if (lstOfDataExcel.length == 0) {
        this.notifyService.showError(
          'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.'
        )
        return
      }
      this.notifyService.showInfo('Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.')
    }
    this.dicExcel[this.data.id] = lstOfDataExcel
    //#endregion

    //#region Body Table
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const headerRow = worksheet.addRow(['ID', 'Tiêu chí', 'Kiểu dữ liệu', 'Bắt buộc?', 'Giá trị', 'Hướng dẫn/Mô tả'])

    // Cell Style : Fill and Border
    const border: Partial<Borders> = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '08298A' },
      }
      cell.border = border
      cell.font = { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'FFFFFF' } }
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 70
          worksheet.getColumn(colNumber).hidden = true // ẩn cột
          break
        case 2:
        case 5:
          worksheet.getColumn(colNumber).width = 50
          break
        case 6:
          worksheet.getColumn(colNumber).width = 70
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    // Màu theo 2 level
    const arrColor = ['ebebeb', 'ffffff']
    for (const data of lstOfDataExcel) {
      data.valueString = data.value
      if (data.type == this.dataType.Date.code && data.value) data.valueString = `'${moment(new Date(data.value)).format('YYYY-MM-DD')}`
      if (data.type == enumData.DataType.List.code) {
        for (const itemList of data.listDetail) {
          if (itemList.id == data.value) data.valueString = itemList.name
        }
      }
      if (data.isChangeByYear) {
        data.example = `[Kiểu theo năm] Nhập như ví dụ:\n`
        data.example += `2020 -- 10000000\n`
        data.example += `2021 -- 15000000`
        data.valueString = ``
        for (const detailYear of data.listDetailYear) {
          data.valueString += `${detailYear.year} -- ${detailYear.value}\n`
        }
      }
      const rowData = [
        data.id || '', //'ID',
        data.name || '', //'Tiêu chí',
        data.type || '', //'Kiểu dữ liệu',
        data.isRequired ? 'x' : '', //'Bắt buộc?',
        data.valueString || '', //'Giá trị',
        data.example || '', //'Mô tả'
      ]
      const row = worksheet.addRow(rowData)
      row.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: arrColor[data.level - 1] },
        }
        cell.border = border
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
        cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
        if (colNumber == 5 && data.type == enumData.DataType.List.code) {
          cell.dataValidation = {
            type: 'list',
            formulae: ['"' + data.listDetail.map((c: any) => c.name).join() + '"'],
          }
        }
      })
    }
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template nhập hồ sơ năng lực Item [${this.data.itemName}].xlsx`
      fs.saveAs(blob, fileName)
      setTimeout(() => {
        this.notifyService.showSuccess('Tải template thành công!')
      }, 300)
    })
    //#endregion
  }

  clickImportExcel(event: any) {
    this.notifyService.showloading()
    if (this.lstCapacity.length == 0) {
      this.notifyService.showError('Item chưa có thiết lập tiêu chí năng lực.')
      return
    }
    let workBook = null
    let jsonData: any[] = []
    const lstHeader = ['id', 'name', 'type', 'isRequired', 'value']
    let lstOfDataExcel: any[] = this.dicExcel[this.data.id]
    if (!lstOfDataExcel) {
      lstOfDataExcel = []
      for (const itemLv1 of this.lstCapacity) {
        if (itemLv1.type !== this.dataType.File.code || itemLv1.__childs__.length > 0) {
          lstOfDataExcel.push(itemLv1)
        }
        for (const itemLv2 of itemLv1.__childs__) {
          if (itemLv2.type !== this.dataType.File.code) {
            lstOfDataExcel.push(itemLv2)
          }
        }
      }
    }

    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      //#region check template
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== 'Tiêu chí' ||
        header.type !== 'Kiểu dữ liệu' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.value !== 'Giá trị'
      ) {
        this.notifyService.showError('File không đúng template (cột đã bị thay đổi)!')
        return
      }
      if (jsonData.some((c) => !c.id) || lstOfDataExcel.length != jsonData.length) {
        this.notifyService.showError(`File không đúng template (dòng đã bị thay đổi)!`)
        return
      }
      //#endregion

      //#region check value
      const dicValue: any = {}
      for (const row of jsonData) {
        const item = lstOfDataExcel.find((c) => c.id == row.id && c.name == row.name)
        if (!item) {
          this.notifyService.showError(`File không đúng template (dòng đã bị thay đổi)!`)
          return
        }
        if (item.isRequired && (row.value == null || row.value === '') && !item.__childs__?.length) {
          this.notifyService.showError(`Giá trị của tiêu chí [${row.name}] bắt buộc không được để trống!`)
          return
        }
        if (row.value != null && row.value !== '') {
          if (!item.isChangeByYear) {
            if (item.type == this.dataType.Date.code) {
              const value = new Date(row.value)
              if (typeof row.value !== 'string' || isNaN(value.getTime())) {
                this.notifyService.showError(`[Giá trị] là [${row.value}] không phải kiểu ${item.type}, ngày phải có định dạng yyyy-mm-dd`)
                return
              }
              dicValue[row.id] = value
            } else if (item.type == this.dataType.Number.code) {
              const value = +row.value
              if (isNaN(value) || !isFinite(value)) {
                this.notifyService.showError(`[Giá trị] là [${row.value}] không phải kiểu ${item.type}`)
                return
              }
              dicValue[row.id] = value
            } else if (item.type == this.dataType.List.code) {
              const itemChoose = item.listDetail.find((c: any) => c.name == row.value)
              if (!itemChoose) {
                this.notifyService.showError(`[Giá trị] là [${row.value}] không nằm trong List`)
                return
              }
              dicValue[row.id] = itemChoose.id
            } else dicValue[row.id] = row.value
          } else {
            try {
              const listDetailYear = []
              const lstYear = row.value.split('\n').filter((c: any) => c != '')
              for (const year of lstYear) {
                const arr = year.split(' -- ')
                const checkYear = +arr[0]
                if (isNaN(checkYear) || !isFinite(checkYear)) {
                  this.notifyService.showError(`[Giá trị] là [${row.value}] không hợp lệ với kiểu theo năm kiểu ${item.type}`)
                  return
                }
                let checkValue = arr[1]
                if (item.type == this.dataType.Number.code) {
                  checkValue = +checkValue
                  if (isNaN(checkValue) || !isFinite(checkValue)) {
                    this.notifyService.showError(`[Giá trị] là [${row.value}] không hợp lệ với kiểu theo năm kiểu ${item.type}`)
                    return
                  }
                }
                listDetailYear.push({ year: checkYear, value: checkValue })
              }
              dicValue[row.id] = listDetailYear
            } catch {
              this.notifyService.showError(`[Giá trị] là [${row.value}] không hợp lệ với kiểu theo năm kiểu ${item.type}`)
              return
            }
          }
        }
      }
      //#endregion

      //#region fill value
      for (const item of lstOfDataExcel) {
        if (!item.isChangeByYear) item.value = dicValue[item.id]
        else item.listDetailYear = dicValue[item.id]
      }
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
      //#endregion
    }
  }
  //#endregion

  loadCapacity() {
    this.modal.confirm({
      nzTitle: '<i>Bạn có thực sự muốn Tải template mới từ Item?</span>',
      nzContent: `<b>Thao tác này sẽ xóa hết template năng lực và giá trị cũ đã nhập của Item. Bạn cần chắc chắn trước khi xác nhận!</b>`,
      nzOnOk: () => {
        this.notifyService.showloading()
        this.apiService.post(this.apiService.SUPPLIER.DELETE_ALL_CAPACITY, { supplierServiceId: this.data.id }).then((res) => {
          this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
          this.loadData()
        })
      },
    })
  }
}
