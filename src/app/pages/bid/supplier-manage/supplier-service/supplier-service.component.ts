import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { SupplierServiceCapacityComponent } from './supplier-service-capacity/supplier-service-capacity.component'

@Component({ templateUrl: './supplier-service.component.html' })
export class SupplierServiceComponent implements OnInit {
  pageSizeMax = enumData.Page.pageSizeMax
  loading = true
  listOfData: any[] = []
  serviceChoose: string[] = []

  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  dataSupplierServiceType = this.coreService.convertObjToArray(enumData.SupplierServiceType)
  modalTitle = ''
  cascaderDataOptions: any[] = []
  allService: any[] = []

  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    this.action = this.enumProject.Action
    this.modalTitle = `Danh sách Lĩnh vực mua hàng NCC [${this.data.name}]`
    this.loadService()
    this.loadData()
  }

  loadData() {
    this.loading = true
    const where: any = { supplierId: this.data.id }
    this.apiService.post(this.apiService.SUPPLIER.LOAD_SUPPLIER_SERVICE, where).then((res) => {
      this.loading = false
      this.listOfData = res
    })
  }

  loadService() {
    this.notifyService.showloading()
    const where: any = { supplierId: this.data.id }
    this.apiService.post(this.apiService.SUPPLIER.GET_SERVICES_CAN_ADD, where).then((res) => {
      this.notifyService.hideloading()
      this.allService = res
      this.cascaderDataOptions = this.genCascaderData()
    })
  }

  genCascaderData(parentId = null) {
    const res: any[] = []
    const lstService = this.allService.filter((c) => c.parentId == parentId)
    for (const service of lstService) {
      const data: any = {
        value: service.id,
        label: service.name,
        isLeaf: !!service.isLast,
      }
      if (!data.isLeaf) {
        data.children = this.genCascaderData(data.value)
      }
      res.push(data)
    }
    return res
  }

  addService() {
    this.notifyService.showloading()
    if (!this.serviceChoose || this.serviceChoose.length == 0) {
      this.notifyService.showError(`Vui lòng chọn lĩnh vực kinh doanh trước`)
      return
    }

    let serviceId = this.serviceChoose[this.serviceChoose.length - 1]
    if (this.listOfData.some((c: any) => c.serviceId === serviceId)) {
      this.notifyService.showError(`Vui lòng chọn lĩnh vực kinh doanh chưa đăng ký trước đó`)
      return
    }
    // call api
    this.apiService.post(this.apiService.SUPPLIER.ADD_SUPPLIER_SERVICE, { supplierId: this.data.id, serviceId }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.loadData()
    })
  }

  showCapacity(supplierService: any) {
    this.dialog
      .open(SupplierServiceCapacityComponent, {
        disableClose: false,
        data: supplierService,
      })
      .afterClosed()
      .subscribe(() => {
        this.loadData()
      })
  }

  saveSupplierService(data: { supplierType: any; supplierType2: any; isEdit: boolean }) {
    data.supplierType = data.supplierType2

    // call api
    this.apiService.post(this.apiService.SUPPLIER.SAVE_SUPPLIER_SERVICE, data).then((res) => {
      data.isEdit = false
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
    })
  }
}
