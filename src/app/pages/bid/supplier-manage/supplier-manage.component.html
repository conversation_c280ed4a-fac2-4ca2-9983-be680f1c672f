<nz-collapse nzBordered="false">
  <nz-collapse-panel [nzHeader]="language_key?.SEARCH_COMPANY_INFO || 'T<PERSON><PERSON> kiếm thông tin doanh nghiệp'">
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.CHOOSE_BUSINESS_CATEGORY || 'Chọn lĩnh vực kinh doanh' }}
          </nz-form-label>
          <nz-col nzSpan="24">
            <nz-cascader nzPlaceHolder="Chọn lĩnh vực kinh doanh" [(ngModel)]="dataSearch.serviceChose"
              [nzLoadData]="loadDataService">
            </nz-cascader>
          </nz-col>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.INTERPRISE_CODE || 'Mã số doanh nghiệp' }}
          </nz-form-label>
          <input nz-input [placeholder]="language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp'"
            [(ngModel)]="dataSearch.supplierCode" name="supplierCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
            [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
            <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code">
            </nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.REGISTRY_FROM || 'Từ ngày đăng ký' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Từ ngày đăng ký">
            <nz-date-picker [(ngModel)]="dataSearch.fromDate" name="dataSearch.fromDate">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.REGISTRY_TO || 'Đến ngày đăng ký' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Đến ngày đăng ký">
            <nz-date-picker [(ngModel)]="dataSearch.toDate" name="dataSearch.toDate"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)" class="mr-2">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
        <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)"
          (click)="clickExportExcel()" nzType="primary">
          <span nz-icon nzType="download"></span>
          {{ language_key?.EXPORT_EXCEL || 'Xuất excel' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.View.code)" nzType="primary"
      (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus-square"></span>
      {{ language_key?.CREATE_SUPPLIER || 'Tạo mới nhà cung cấp' }}
    </button>
    <button class="mr-2" *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button
      (click)="clickExportExcelTemplate()">
      <span nz-icon nzType="download"></span>
      {{ language_key?.EXPORT_EXCEL_TEMPLATE || 'Xuất template excel' }}
    </button>
    <input class="hidden" *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" type="file"
      id="file" (change)="clickImportExcel($event)" onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
    <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file"
      class="lable-custom-file mr-2">
      <span nz-icon nzType="upload"></span>
      {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
    </label>
    <button *ngIf="lstErrorImport?.length && authenticationService.checkPermission([enumRole], action.Export.code)"
      nz-button (click)="clickShowErrorImport()" nzType="primary" nzDanger>
      <span nz-icon nzType="warning"></span>
      {{ language_key?.ERROR_IMPORT || 'Danh sách lỗi import' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-2">
  <nz-table nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        <th>{{ language_key?.INTERPRISE_CODE || 'Mã số doanh nghiệp' }}</th>
        <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
        <th>{{ language_key?.BUSINESS_CATEGORY || 'Lĩnh vực kinh doanh' }}</th>
        <th>{{ language_key?.ACCOUNT || 'Tài khoản' }}</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th>{{ language_key?.REGISTRY_DATE || 'Ngày đăng ký' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td class="text-nowrap">
          <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-tooltip
            nzTooltipTitle="Chỉnh sửa thông tin chung" (click)="clickEdit(data)" class="mr-2" nz-button nzType="dashed">
            <span nz-icon nzType="form"></span>
          </button>
          <button (click)="viewSupplierService(data)"
            *ngIf="authenticationService.checkPermission([enumRole], action.View.code)" nz-tooltip
            nzTooltipTitle="Danh sách Lĩnh vực kinh doanh" nz-button>
            <span nz-icon nzType="unordered-list"></span>
          </button>
          <!-- <span nz-icon nzType="edit" nz-tooltip nzTooltipTitle="Cập nhật mật khẩu"
                        style="font-size: 20px;" (click)="changePassword(data)"></span> -->
        </td>
        <td class="mw-25">{{ data.code }}</td>
        <td class="mw-25">{{ data.name }}</td>
        <td>{{ data.itemName }}</td>
        <td>{{ data.username }}</td>
        <td>
          <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName}}</nz-tag>
        </td>
        <td>{{ data.createdAt | date: 'dd/MM/yyyy' }}</td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisibleChangePw" nzTitle="Cập nhật mật khẩu cho doanh nghiệp"
  (nzOnCancel)="isVisibleChangePw = false" [nzWidth]="'60vw'" [nzFooter]="null">
  <ng-container *nzModalContent>
    <form nz-form #frmAdd="ngForm">
      <nz-row nzGutter="8" *ngIf="supplierChoose">
        <nz-col nzSpan="24">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Mật khẩu</nz-form-label>
            <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập mật khẩu ít nhất 4 kí tự!">
              <input type="password" nz-input placeholder="Nhập ít nhất 4 kí tự"
                [(ngModel)]="supplierChoose.newPassword" name="newPassword" required pattern=".{4,}" />
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Xác nhận mật khẩu</nz-form-label>
            <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập xác nhận mật khẩu ít nhất 4 kí tự!">
              <input type="password" nz-input placeholder="Nhập lại mật khẩu"
                [(ngModel)]="supplierChoose.confirmNewPassword" name="confirmNewPassword" required pattern=".{4,}" />
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
      <nz-row>
        <nz-col nzSpan="24" class="text-center">
          <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn cập nhật mật khẩu cho doanh nghiệp này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="confirmChangePassword()" nz-button [disabled]="!frmAdd.form.valid" class="ant-btn-blue">
            Đồng ý
          </button>
        </nz-col>
      </nz-row>
    </form>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleError" [nzWidth]="'60vw'" nzTitle="Danh sách lỗi nhập excel"
  (nzOnCancel)="closeModelError()" (nzOnOk)="closeModelError()">
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="lstErrorImport" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false"
        nzBordered>
        <thead>
          <tr>
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Nội dung lỗi</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of lstErrorImport">
            <td>{{ data.zenId }}</td>
            <td>{{ data.errorMessage }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>