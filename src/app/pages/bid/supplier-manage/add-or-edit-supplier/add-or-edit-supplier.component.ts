import { Component, OnInit, Optional, Inject } from '@angular/core'
import { FormBuilder, Validators } from '@angular/forms'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-supplier.component.html' })
export class AddOrEditSupplierComponent implements OnInit {
  modalTitle = 'Thêm mới doanh nghiệp'
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  dataObject: any = {}

  maxSizeUpload = enumData.maxSizeUpload
  address!: string
  fieldCurrent: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private dialogRef: MatDialogRef<AddOrEditSupplierComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    this.validateForm = this.fb.group({
      name: [null, [Validators.required, Validators.maxLength(250)]],
      dealName: [null, [Validators.required, Validators.maxLength(250)]],
      createYear: [null, [Validators.required, Validators.maxLength(50)]],
      code: [null, [Validators.required, Validators.maxLength(50)]],
      address: [null, [Validators.required, Validators.maxLength(250)]],
      dealAddress: [null, [Validators.required, Validators.maxLength(250)]],
      represen: [null, [Validators.required, Validators.maxLength(50)]],
      chief: [null, [Validators.required, Validators.maxLength(50)]],
      capital: [null, [Validators.required, Validators.min(0)]],
      assets: [null, [Validators.required, Validators.min(0)]],
      fileBill: [null, [Validators.required, Validators.maxLength(500)]],
      fileInfoBill: [null, [Validators.required, Validators.maxLength(500)]],
      fileMST: [null, [Validators.required, Validators.maxLength(500)]],
      description: [null, [Validators.required, Validators.maxLength(250)]],
      bankname: [null, [Validators.required, Validators.maxLength(250)]],
      bankNumber: [null, [Validators.required, Validators.maxLength(50)]],
      fileAccount: [null, [Validators.required, Validators.maxLength(500)]],
      bankBrand: [null, [Validators.required, Validators.maxLength(250)]],
      contactName: [null, [Validators.required, Validators.maxLength(250)]],
      phone: [null, [Validators.required, Validators.maxLength(50)]],
      email: [null, [Validators.required, Validators.maxLength(50)]],
    })

    if (this.data && this.data.id) {
      this.modalTitle = 'Chỉnh sửa doanh nghiệp'
      this.dataObject = { ...this.data }
    }
  }

  submitForm() {
    this.notifyService.showloading()
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty()
      this.validateForm.controls[i].updateValueAndValidity()
    }
    if (!this.validateForm.valid) {
      this.notifyService.showError(`Dữ liệu nhập không hợp lệ, vui lòng kiểm tra lại!`)
      return
    }
    if (!this.dataObject.fileAccount || !this.dataObject.fileBill || !this.dataObject.fileInfoBill || !this.dataObject.fileMST) {
      this.notifyService.showError('Đã có lỗi xảy ra vui lòng kiểm tra lại các trường Upload File')
      return
    }

    //#region Check data
    if (this.dataObject.password != this.dataObject.confirmPassword) {
      this.notifyService.showError(`Mật khẩu xác nhận lại không khớp vui lòng kiểm tra lại`)
      return
    }
    //#endregion

    //#region Lưu supplier
    this.dataObject.createYear = '' + this.dataObject.createYear
    this.dataObject.capital = +this.dataObject.capital
    this.dataObject.assets = +this.dataObject.assets
    if (!this.dataObject.id) {
      this.apiService.post(this.apiService.SUPPLIER.CREATE_SUPPLIER, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      })
    } else {
      this.apiService.post(this.apiService.SUPPLIER.UPDATE_SUPPLIER, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      })
    }
    //#endregion
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }
}
