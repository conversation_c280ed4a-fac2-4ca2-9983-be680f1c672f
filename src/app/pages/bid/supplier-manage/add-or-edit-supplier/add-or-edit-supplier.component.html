<form nz-form [formGroup]="validateForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-collapse nzBordered="false">
      <nz-collapse-panel [nzHeader]="language_key?.GENERAL_INFO || 'Thông Tin Chung'" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="name" nzRequired class="text-left">
                {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên dopanh nghiệp (1-250 kí tự)!">
                <input nz-input formControlName="name" id="name" [(ngModel)]="dataObject.name" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="dealName" nzRequired class="text-left"> Tên giao dịch </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên giao dịch (1-250 kí tự)!">
                <input nz-input formControlName="dealName" id="dealName" [(ngModel)]="dataObject.dealName" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="createYear" nzRequired class="text-left"> Năm thành lập công ty </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập năm thành lập công ty!">
                <input
                  class="input-rtl"
                  nz-input
                  formControlName="createYear"
                  id="createYear"
                  [(ngModel)]="dataObject.createYear"
                  type="number"
                  min="0"
                  required
                />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="code" nzRequired class="text-left">
                {{ language_key?.INTERPRISE_CODE || 'Mã số doanh nghiệp' }}
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mã số doanh nghiệp (1-50 kí tự)!">
                <input nz-input formControlName="code" id="code" [(ngModel)]="dataObject.code" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="address" nzRequired class="text-left"> Địa chỉ trụ sở </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn địa chỉ trụ sở (1-250 kí tự)!">
                <nz-input-group nzSearch>
                  <input
                    nz-input
                    nzPlaceHolder="Chọn địa chỉ trụ sở"
                    [(ngModel)]="dataObject.address"
                    formControlName="address"
                    id="'address'"
                    required
                  />
                </nz-input-group>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="dealAddress" nzRequired class="text-left"> Địa chỉ giao dịch </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn địa chỉ giao dịch (1-250 kí tự)!">
                <nz-input-group nzSearch>
                  <input
                    nz-input
                    nzPlaceHolder="Chọn địa chỉ giao dịch"
                    [(ngModel)]="dataObject.dealAddress"
                    formControlName="dealAddress"
                    id="'dealAddress'"
                    required
                  />
                </nz-input-group>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="represen" nzRequired class="text-left"> Người đại diện pháp luật </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!">
                <input nz-input formControlName="represen" id="represen" [(ngModel)]="dataObject.represen" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="chief" nzRequired class="text-left"> Tên giám đốc </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên giám đốc (1-50 kí tự)!">
                <input nz-input formControlName="chief" id="chief" [(ngModel)]="dataObject.chief" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="capital" nzRequired class="text-left"> Vốn điều lệ (tỷ đồng) </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập vốn điều lệ (tỷ đồng)!">
                <input nz-input currencyMask formControlName="capital" id="capital" [(ngModel)]="dataObject.capital" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="assets" nzRequired class="text-left"> Tài sản cố định (tỷ đồng) </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tài sản cố định (tỷ đồng)!">
                <input nz-input currencyMask formControlName="assets" id="assets" [(ngModel)]="dataObject.assets" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileBill" nzRequired class="text-left"> File hóa đơn mẫu/phiếu thu/biên lai </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai">
                <label for="fileBill" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input
                  class="hidden"
                  type="file"
                  formControlName="fileBill"
                  id="fileBill"
                  (change)="handleFileInput($event, 'fileBill')"
                  [(ngModel)]="dataObject.fileBill"
                />
                <div class="tooltip" *ngIf="dataObject.fileBill?.length > 0">
                  <a href="{{ dataObject.fileBill }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileInfoBill" nzRequired class="text-left"> File thông tin phát hành hóa đơn </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file thông tin phát hành hóa đơn">
                <label for="fileInfoBill" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input
                  class="hidden"
                  type="file"
                  formControlName="fileInfoBill"
                  id="fileInfoBill"
                  (change)="handleFileInput($event, 'fileInfoBill')"
                  [(ngModel)]="dataObject.fileInfoBill"
                />
                <div class="tooltip" *ngIf="dataObject.fileInfoBill && dataObject.fileInfoBill.length > 0">
                  <a href="{{ dataObject.fileInfoBill }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileMST" nzRequired class="text-left"> Giấy phép kinh doanh/Mã số thuế </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload giấy phép kinh doanh/Mã số thuế">
                <label for="fileMST" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input
                  class="hidden"
                  type="file"
                  formControlName="fileMST"
                  id="fileMST"
                  (change)="handleFileInput($event, 'fileMST')"
                  [(ngModel)]="dataObject.fileMST"
                />
                <div class="tooltip" *ngIf="dataObject.fileMST && dataObject.fileMST.length > 0">
                  <a href="{{ dataObject.fileMST }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="description" nzRequired class="text-left"> Mô tả về doanh nghiệp </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mô tả về doanh nghiệp (1-250 kí tự)!">
                <textarea nz-input formControlName="description" id="description" [(ngModel)]="dataObject.description" rows="5" auto></textarea>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse nzBordered="false">
      <nz-collapse-panel nzHeader="Thông tin tài khoản" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row nzGutter="8" *ngIf="!dataObject.id">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="username" nzRequired class="text-left"> Tên đăng nhập </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên đăng nhập (1-50 kí tự)!">
                <input nz-input id="username" [(ngModel)]="dataObject.username" [ngModelOptions]="{ standalone: true }" required />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8" *ngIf="!dataObject.id">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="password" nzRequired class="text-left"> Mật khẩu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mật khẩu!">
                <nz-input-group [nzSuffix]="suffixTemplate1">
                  <input
                    [type]="passwordVisible ? 'text' : 'password'"
                    nz-input
                    id="password"
                    [(ngModel)]="dataObject.password"
                    [ngModelOptions]="{ standalone: true }"
                    required
                  />
                </nz-input-group>
                <ng-template #suffixTemplate1>
                  <span *ngIf="passwordVisible" nz-icon nzType="eye-invisible" (click)="passwordVisible = !passwordVisible"></span>
                  <span *ngIf="!passwordVisible" nz-icon nzType="eye" (click)="passwordVisible = !passwordVisible"></span>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="confirmPassword" nzRequired class="text-left"> Xác nhận mật khẩu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập xác nhận mật khẩu!">
                <nz-input-group [nzSuffix]="suffixTemplate2">
                  <input
                    [type]="passwordVisible2 ? 'text' : 'password'"
                    nz-input
                    id="confirmPassword"
                    [(ngModel)]="dataObject.confirmPassword"
                    [ngModelOptions]="{ standalone: true }"
                    required
                  />
                </nz-input-group>
                <ng-template #suffixTemplate2>
                  <span *ngIf="passwordVisible2" nz-icon nzType="eye-invisible" (click)="passwordVisible2 = !passwordVisible2"></span>
                  <span *ngIf="!passwordVisible2" nz-icon nzType="eye" (click)="passwordVisible2 = !passwordVisible2"></span>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="bankname" nzRequired class="text-left"> Tên ngân hàng </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên ngân hàng (1-250 kí tự)!">
                <input nz-input formControlName="bankname" id="bankname" [(ngModel)]="dataObject.bankname" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="bankNumber" nzRequired class="text-left"> Số tài khoản ngân hàng </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!">
                <input nz-input formControlName="bankNumber" id="bankNumber" [(ngModel)]="dataObject.bankNumber" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="bankBrand" nzRequired class="text-left"> Tên chi nhánh ngân hàng </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!">
                <input nz-input formControlName="bankBrand" id="bankBrand" [(ngModel)]="dataObject.bankBrand" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileAccount" nzRequired class="text-left"> File thông báo mở tài khoản/mẫu 08 </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file thông báo mở tài khoản/mẫu 08">
                <label for="fileAccount" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input
                  class="hidden"
                  type="file"
                  formControlName="fileAccount"
                  id="fileAccount"
                  (change)="handleFileInput($event, 'fileAccount')"
                  [(ngModel)]="dataObject.fileAccount"
                />
                <div class="tooltip" *ngIf="dataObject.fileAccount && dataObject.fileAccount.length > 0">
                  <a href="{{ dataObject.fileAccount }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="contactName" nzRequired class="text-left"> Người liên hệ </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập người liên hệ (1-50 kí tự)!">
                <input nz-input formControlName="contactName" id="contactName" [(ngModel)]="dataObject.contactName" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="phone" nzRequired class="text-left"> Số điện thoại </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số điện thoại (1-50 kí tự)!">
                <input nz-input formControlName="phone" id="phone" [(ngModel)]="dataObject.phone" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="email" nzRequired class="text-left"> Email </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập email (1-50 kí tự)!">
                <input nz-input formControlName="email" id="email" [(ngModel)]="dataObject.email" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button
        nz-button
        *ngIf="!dataObject.id && authenticationService.checkPermission([enumRole], action.Create.code)"
        nzType="primary"
        class="mr-2"
        (click)="submitForm()"
      >
        Tạo nhà cung cấp
      </button>
      <button
        nz-button
        *ngIf="dataObject.id && authenticationService.checkPermission([enumRole], action.Update.code)"
        nzType="primary"
        (click)="submitForm()"
      >
        Lưu thông tin
      </button>
    </nz-col>
  </nz-row>
</form>
