import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { enumData } from '../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-setting-rate.component.html' })
export class BidSettingRateComponent implements OnInit {
  dataObject: any = {}
  timeDefaultValue = new Date()
  yesterday = new Date()
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidSettingRateComponent>,
    public authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.yesterday.setDate(this.yesterday.getDate() - 1)
    if (this.data && this.data.id) {
      this.apiService.post(this.apiService.BID.FIND_DETAIL_EDIT, { id: this.data.id }).then((res) => {
        this.dataObject = res
      })
    } else {
      this.notifyService.showError('Không xác định được gói thầu đã chọn!')
    }
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.timeTechDate = new Date(this.dataObject.timeTechDate)
    this.dataObject.timeTechDate.setMilliseconds(0)

    this.dataObject.timePriceDate = new Date(this.dataObject.timePriceDate)
    this.dataObject.timePriceDate.setMilliseconds(0)

    this.dataObject.acceptEndDate = new Date(this.dataObject.acceptEndDate)
    this.dataObject.acceptEndDate.setMilliseconds(0)

    this.dataObject.submitEndDate = new Date(this.dataObject.submitEndDate)
    this.dataObject.submitEndDate.setMilliseconds(0)

    this.dataObject.timeCheckTechDate = new Date(this.dataObject.timeCheckTechDate)
    this.dataObject.timeCheckTechDate.setMilliseconds(0)

    this.dataObject.timeCheckPriceDate = new Date(this.dataObject.timeCheckPriceDate)
    this.dataObject.timeCheckPriceDate.setMilliseconds(0)

    if (this.dataObject.timeTechDate >= this.dataObject.acceptEndDate) {
      this.notifyService.showError('Ngày (1) phải sớm hơn ngày (3).')
      return
    }
    if (this.dataObject.timePriceDate >= this.dataObject.acceptEndDate) {
      this.notifyService.showError('Ngày (2) phải sớm hơn ngày (3).')
      return
    }
    if (this.dataObject.acceptEndDate >= this.dataObject.submitEndDate) {
      this.notifyService.showError('Ngày (3) phải sớm hơn ngày (4).')
      return
    }
    if (this.dataObject.submitEndDate >= this.dataObject.timeCheckTechDate) {
      this.notifyService.showError('Ngày (4) phải sớm hơn ngày (5).')
      return
    }
    if (this.dataObject.submitEndDate >= this.dataObject.timeCheckPriceDate) {
      this.notifyService.showError('Ngày (4) sớm hơn ngày (6).')
      return
    }

    this.apiService.post(this.apiService.BID.RATE_UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.dialogRef.close(1)
      }
    })
  }

  disabledDate = (current: Date): boolean => {
    return current < this.yesterday
  }
}
