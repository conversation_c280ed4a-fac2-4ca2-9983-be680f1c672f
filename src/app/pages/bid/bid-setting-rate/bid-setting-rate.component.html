<form nz-form #frmAdd="ngForm" class="mt-3 ml-3 mr-3 mb-3">
  <nz-row nzGutter="8" class="mt-2">
    <!-- Thời hạn -->
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">
          1.Th<PERSON><PERSON> hạn thiết lập các yêu c<PERSON><PERSON> k<PERSON> thuật:
        </nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.timeTechDate" name="timeTechDate"
            nzDisabled>
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">
          2.Th<PERSON><PERSON> hạn thiết lậ<PERSON> các hạng mục chào giá và điều kiện thương mại:
        </nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.timePriceDate" name="timePriceDate"
            nzDisabled>
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>
  <nz-row nzGutter="8">
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left" nz-tooltip
          nzTooltipTitle="Sau ngày (1, 2) và trước ngày (4)">
          3.Ngày hết hạn xác nhận tham gia đấu thầu
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn ngày hết hạn xác nhận tham gia đấu thầu!">
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.acceptEndDate" name="acceptEndDate"
            [nzDisabledDate]="disabledDate" [nzShowTime]="{ nzDefaultOpenValue: timeDefaultValue }">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left" nz-tooltip
          nzTooltipTitle="Sau ngày (3) và trước ngày (5,6)">
          4.Ngày hết hạn nộp hồ sơ thầu
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn ngày hết hạn nộp hồ sơ thầu!">
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.submitEndDate" name="submitEndDate"
            [nzDisabledDate]="disabledDate" [nzShowTime]="{ nzDefaultOpenValue: timeDefaultValue }">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-row nzGutter="8">
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left" nz-tooltip nzTooltipTitle="Sau ngày (4)">
          5.Thời hạn đánh giá các yêu cầu kỹ thuật
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thời hạn đánh giá các yêu cầu kỹ thuật!">
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.timeCheckTechDate"
            name="timeCheckTechDate" [nzDisabledDate]="disabledDate"
            [nzShowTime]="{ nzDefaultOpenValue: timeDefaultValue }">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left" nz-tooltip nzTooltipTitle="Sau ngày (4)">
          6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại
        </nz-form-label>
        <nz-form-control nzSpan="24"
          nzErrorTip="Vui lòng chọn thời hạn đánh giá kết quả chào giá và điều kiện thương mại!">
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.timeCheckPriceDate"
            name="timeCheckPriceDate" [nzDisabledDate]="disabledDate"
            [nzShowTime]="{ nzDefaultOpenValue: timeDefaultValue }">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>
  <!-- <nz-row nzGutter="8">
    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">Tỉ trọng điểm đánh giá kỹ thuật
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số (1-99)!">
          <input nz-input type="number" [(ngModel)]="dataObject.percentTech" name="percentTech" pattern=".{1,50}" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">Tỉ trọng điểm đánh giá ĐKTM
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số (1-99)!">
          <input nz-input type="number" [(ngModel)]="dataObject.percentTrade" name="percentTrade" pattern=".{1,50}" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">Tỉ trọng điểm đánh giá bảng giá
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số (1-99)!">
          <input nz-input type="number" [(ngModel)]="dataObject.percentPrice" name="percentPrice" pattern=".{1,50}" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>
  <nz-row>
    <span class="text-danger"
      *ngIf="dataObject.percentTech != null && dataObject.percentTrade != null && dataObject.percentPrice != null && dataObject.percentTech + dataObject.percentTrade + dataObject.percentPrice != 100">
      Tổng % các tỉ lệ là 100%
    </span>
  </nz-row> -->
  <nz-row nzGutter="8">
    <nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-control nzSpan="24">
          <label nz-checkbox [(ngModel)]="dataObject.isSendEmailInviteBid" name="isSendEmailInviteBid"
            [nzDisabled]="dataObject.hasSendEmailInviteBid">
            Gửi thông báo mời thầu cho NCC
          </label>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>
  <nz-row>
    <span class="text-info" *ngIf="dataObject.hasSendEmailInviteBid">
      Đã gửi thông báo mời thầu cho NCC
    </span>
  </nz-row>
  <!-- <nz-row nzGutter="8">
    <nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-control nzSpan="24">
          <label nz-checkbox [(ngModel)]="dataObject.isAutoBid" name="isAutoBid">
            Tự động chọn NCC thắng thầu và kết thúc thầu
          </label>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row> -->
  <nz-row>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
        [disabled]="!frmAdd.form.valid" nzType="primary" class="mr-3" (click)="onSave()">{{
        language_key?.SAVE || 'Lưu' }}</button>
    </nz-col>
  </nz-row>
</form>