<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="<PERSON><PERSON><PERSON> yêu cầu thẩm định" class="ant-bg-antiquewhite">
    <form nz-form #frmAdd="ngForm">
      <nz-row nzGutter="8">
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" class="text-left" nzSpan="24" nzRequired><PERSON><PERSON><PERSON> thẩ<PERSON> định
            </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Chọn ngày thẩm định!">
              <nz-date-picker required [(ngModel)]="changeDate" name="changeDate"> </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" nzRequired>Th<PERSON><PERSON> định pháp lý
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="isCheckLaw" name="isCheckLaw">
                Thẩm định pháp lý
              </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" nzRequired>Thẩm định năng lực
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="isCheckCapacity" name="isCheckCapacity">
                Thẩm định năng lực
              </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>

      <nz-row nzGutter="8">
        <nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" [nzRequired]="isCheckLaw">Thành viên
              thẩm định thông tin pháp lý
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Thành viên thẩm định thông tin pháp lý"
                [(ngModel)]="employeeLawId" name="employeeLawId">
                <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                  [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" nzRequired>Thành viên ban thẩm định
            </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Chọn thành viên ban thẩm định!">
              <nz-select nzShowSearch nzAllowClear nzMode="multiple" nzPlaceHolder="Thành viên ban thẩm định"
                [(ngModel)]="employeeAccessId" name="employeeAccessId" required>
                <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                  [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>

      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">
              {{ language_key?.NOTE || 'Ghi chú' }}
            </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Nhập ghi chú!">
              <textarea nz-input placeholder="Ghi chú" [(ngModel)]="note" name="note" pattern=".{1,250}" rows="3"
                auto></textarea>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
    </form>
  </nz-collapse-panel>
</nz-collapse>


<nz-collapse nzBordered="false" class="mt-3">
  <nz-collapse-panel nzHeader="Chọn nhà cung cấp" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Chọn lĩnh vực mua hàng</nz-form-label>
          <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
            [nzLoadData]="loadDataService">
          </nz-cascader>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.INTERPRISE_CODENAME || 'Mã hoặc tên doanh nghiệp' }}
          </nz-form-label>
          <input nz-input [placeholder]="language_key?.INTERPRISE_CODENAME || 'Mã hoặc tên doanh nghiệp'"
            [(ngModel)]="dataSearch.supplierName" name="supplierName" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Trạng thái thẩm định</nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusExpertise" name="statusExpertise"
            nzPlaceHolder="Trạng thái thẩm định">
            <nz-option *ngFor="let item of dataExpertiseStatus" [nzLabel]="item.name" [nzValue]="item.code">
            </nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row>
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
    <nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
        [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th nzShowCheckbox [(nzChecked)]="isAllDisplayDataChecked" [nzIndeterminate]="isIndeterminate"
              (nzCheckedChange)="checkAll($event)"></th>
            <th>Công ty</th>
            <th>Lĩnh vực mua hàng</th>
            <th>Trạng thái thẩm định</th>
            <th>Ngày thẩm định gần nhất</th>
            <th>Điểm</th>
            <th>Ngày đăng ký</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <td nzShowCheckbox [(nzChecked)]="mapOfCheckedId[data.id]" (nzCheckedChange)="refreshStatus()"></td>
            <td class="mw-25" (click)="showDetail(data)">{{ data.supplierName }}
            </td>
            <td (click)="showDetail(data)">{{ data.itemName }}</td>
            <td (click)="showDetail(data)">{{ data.statusExpertiseName }}</td>
            <td (click)="showDetail(data)">
              {{ data.lastUpdateExpertise ? (data.lastUpdateExpertise | date: 'dd/MM/yyyy') : ''}}</td>
            <td (click)="showDetail(data)">{{ data.score | number: '1.0-2'}}</td>
            <td (click)="showDetail(data)">{{ data.createdAt | date: 'dd/MM/yyyy' }}</td>
          </tr>
        </tbody>
      </nz-table>
      <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger>
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total>
        {{ range[0] }}-{{ range[1] }} of {{ total }} items
      </ng-template>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row nzGutter="8">
  <nz-col nzSpan="20" class="mt-3 text-center">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
      [disabled]="!frmAdd.form.valid" nz-button nzType="primary" nz-tooltip
      nzTooltipTitle="Thẩm định nhà cung cấp đã chọn" (click)="onExpertise()">
      Tạo yêu cầu thẩm định
    </button>
  </nz-col>
</nz-row>