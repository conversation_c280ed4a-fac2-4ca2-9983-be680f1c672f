import { Component, OnInit } from '@angular/core'
import { SupplierDetailComponent } from '../supplier-detail/supplier-detail.component'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'

/** Tạo yêu cầu thẩm định cho Item NCC */
@Component({ templateUrl: './supplier-service-to-expertise.component.html' })
export class SupplierServiceToExpertiseComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = false
  dataDepartment: any[] = []
  dataEmployee: any[] = []
  dataService: any[] = []
  dataServiceChild: any[] = []
  dataServiceChildLast: any[] = []
  dataSearch: any = {}
  dataExpertiseStatus = this.coreService.convertObjToArray(enumData.SupplierServiceExpertiseStatus)
  listOfData: any[] = []
  isAllDisplayDataChecked = false
  isIndeterminate = false
  listOfAllData: any[] = []
  mapOfCheckedId: { [key: string]: boolean } = {}
  numberOfChecked = 0
  employeeLawId?: string
  employeeAccessId: any[] = []
  note!: string
  changeDate: Date = new Date()
  isCheckLaw = true
  isCheckCapacity = true
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private router: Router,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.EXPERTISE_001.code
    this.action = this.enumProject.Action
    this.dataSearch.statusExpertise = enumData.SupplierServiceExpertiseStatus.ChuaThamDinh.code
    this.searchData()
    this.loadEmployee()
  }

  searchData(reset = false) {
    this.loading = true
    if (reset) {
      this.pageIndex = 1
      this.operateData()
    }
    const where: any = { isDeleted: false }
    if (this.dataSearch.supplierName && this.dataSearch.supplierName !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.statusExpertise && this.dataSearch.statusExpertise !== '') {
      where.statusExpertise = this.dataSearch.statusExpertise
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.SUPPLIER.SUPPLIER_SERVICE_PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        this.listOfData.forEach((item) => {
          const find = this.listOfAllData.find((p) => p.id === item.id)
          if (!find) this.listOfAllData.push(item)
        })
        this.refreshStatus()
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  loadEmployee() {
    this.dataEmployee = []
    this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((result) => {
      this.dataEmployee = result
    })
  }

  onExpertise() {
    this.notifyService.showloading()
    const arr: any[] = []
    if (this.isCheckLaw && !this.employeeLawId) {
      this.notifyService.showError('Vui lòng chọn nhân viên duyệt pháp lý!')
      return
    }

    if (!this.changeDate) {
      this.notifyService.showError('Vui lòng chọn ngày thẩm định!')
      return
    }

    if (!this.isCheckLaw && !this.isCheckCapacity) {
      this.notifyService.showError('Vui lòng chọn ít nhất 1 loại hình thẩm định!')
      return
    }
    // tslint:disable-next-line: forin
    for (const key in this.mapOfCheckedId) {
      const value = this.mapOfCheckedId[key]
      arr.push({ key, value })
    }

    const listId = arr.filter((p) => p.value).map((p) => p.key)
    if (listId.length === 0) {
      this.notifyService.showError('Vui lòng chọn nhà cung cấp cần thẩm định!')
      return
    }
    this.apiService
      .post(this.apiService.SUPPLIER.SUPPLIER_SERVICE_TO_EXPERTISE, {
        supplierServiceIds: listId,
        note: this.note,
        employeeLawId: this.employeeLawId,
        changeDate: this.changeDate,
        isCheckLaw: this.isCheckLaw,
        isCheckCapacity: this.isCheckCapacity,
        members: this.employeeAccessId,
      })
      .then((result) => {
        if (result && !result.error) {
          this.searchData(true)
          this.notifyService.showSuccess(result.message)
          this.router.navigate(['/bid/supplier-expertise'])
        } else this.notifyService.hideloading()
      })
  }

  showDetail(item: { supplierId: any; id: any; comment: any; approverComment: any }) {
    this.dialog.open(SupplierDetailComponent, {
      disableClose: false,
      data: {
        supplierId: item.supplierId,
        supplierServiceId: item.id,
        comment: item.comment,
        approverComment: item.approverComment,
      },
    })
  }

  // Reload lại danh sách đã chọn
  operateData() {
    this.listOfData = []
    this.listOfAllData = []
    this.mapOfCheckedId = {}
    this.numberOfChecked = 0
    setTimeout(() => {
      this.listOfAllData.forEach((item) => (this.mapOfCheckedId[item.id] = false))
      this.refreshStatus()
    }, 1000)
  }

  // load lại danh sách hiển thị mới
  refreshStatus() {
    this.isAllDisplayDataChecked = this.listOfData.every((item) => this.mapOfCheckedId[item.id])
    this.isIndeterminate = this.listOfData.some((item) => this.mapOfCheckedId[item.id]) && !this.isAllDisplayDataChecked
    this.numberOfChecked = this.listOfAllData.filter((item) => this.mapOfCheckedId[item.id]).length
  }

  // Chọn tất cả
  checkAll(value: boolean) {
    this.listOfAllData.forEach((item) => (this.mapOfCheckedId[item.id] = value))
    this.refreshStatus()
  }
}
