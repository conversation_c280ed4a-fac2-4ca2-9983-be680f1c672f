import { Component, OnInit } from '@angular/core'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './report-history-price-supplier.component.html' })
export class ReportHistoryPriceSupplierComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  lstSupplier: any[] = []
  dataSearch: any = {}
  listOfData: any[] = []
  isCollapseFilter = true
  loading = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadSupplier()

    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
  }

  loadSupplier() {
    this.apiService.post(this.apiService.SUPPLIER.FIND, {}).then((res) => {
      this.lstSupplier = res
    })
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    if (!this.dataSearch.supplierId) {
      this.notifyService.showError('Vui lòng chọn doanh nghiệp trước')
      return
    }
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Vui lòng chọn [Từ ngày đăng tải] sớm hơn [Đến ngày đăng tải]')
      return
    }
    this.loading = true
    const where: any = {}
    where.supplierId = this.dataSearch.supplierId
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }

    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.REPORT.GET_REPORT_HISTORY_PRICE_SUPPLIER, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickView(object: any) {
    // this.dialog.open(BidDetailComponent, { disableClose: false, data: object })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  clickExportExcel() {
    const tbl = document.getElementById('report-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Báo cáo lịch sử giá theo doanh nghiệp.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
