<div matDialogTitle>
  <nz-row nzGutter="6">
    <nz-col nzSpan="24">
      <h4>T<PERSON>y chọn in:</h4>
    </nz-col>
    <nz-col nzSpan="6" *ngFor="let item of lstOptionPrint">
      <label nz-checkbox [(ngModel)]="item.isShow" (ngModelChange)="changeOptionPrint()">{{item.name}}</label>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3 mb-5">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)" nz-button nzType="primary"
      [useExistingCss]="true" printSectionId="print-section" ngxPrint>
      In gói thầu
    </button>
  </nz-row>
</div>

<div matDialogContent id="print-section" *ngIf="dataPrint"
  style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <img src="../../../../../assets/img/logoform.jpg" alt="logo" height="50" width="150" />
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BÁO CÁO KẾT QUẢ GÓI THẦU</div>
    <div>TÊN GÓI THẦU: {{ data.name | uppercase }}</div>
    <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
  </div>

  <!-- Thông tin chung -->
  <div style="margin-top: 20px; border: 1px solid black; padding: 0px 5px;">
    <table style="margin-bottom: 10px;">
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px; font-weight: 700;">I.</th>
          <th style="width: 40%; font-weight: 700;">{{language_key?.GENERAL_INFO || 'Thông Tin Chung'}} :</th>
          <th style="width: 60%;"></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td>Công ty mời thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.companyInvite}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Địa chỉ nộp hồ sơ thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.addressSubmit}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Các địa điểm thực hiện gói thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.listAddress}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Các thành viên hội đồng xét thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.listMember}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Các thành viên khác:</td>
          <td style="font-weight: 700;">{{dataPrint.listOther}}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Thông tin gói thầu: -->
  <div style="border: 1px solid black; padding: 0px 5px;">
    <table style="margin-bottom: 10px;">
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px; font-weight: 700;">II.</th>
          <th style="width: 40%; font-weight: 700;">Thông tin gói thầu:</th>
          <th style="width: 60%;"></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td>Tên gói thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.name}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Mã TBMT:</td>
          <td style="font-weight: 700;">{{dataPrint.code}}</td>
        </tr>
        <tr *ngIf="dataPrint.prId">
          <td></td>
          <td>PR:</td>
          <td style="font-weight: 700;">{{dataPrint.prCode}}</td>
        </tr>

        <tr>
          <td></td>
          <td>Trạng thái gói thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.statusName}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Hình thức đấu thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.bidTypeName}}</td>
        </tr>

        <tr>
          <td></td>
          <td>1.Thời hạn thiết lập các yêu cầu kỹ thuật:</td>
          <td style="font-weight: 700;">{{dataPrint.timeTechDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại:</td>
          <td style="font-weight: 700;">{{dataPrint.timePriceDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>3.Ngày hết hạn xác nhận tham gia đấu thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.acceptEndDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>4.Ngày hết hạn nộp hồ sơ thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.submitEndDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>5.Thời hạn đánh giá các yêu cầu kỹ thuật:</td>
          <td style="font-weight: 700;">{{dataPrint.timeCheckTechDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại:</td>
          <td style="font-weight: 700;">{{dataPrint.timeCheckPriceDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>

        <tr>
          <td></td>
          <td>Hiệu lực hợp đồng (tháng):</td>
          <td style="font-weight: 700;">{{dataPrint.timeserving}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Hình thức bảo lãnh dự thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.masterBidGuaranteeName}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Số tiền bảo lãnh dự thầu (VNĐ):</td>
          <td style="font-weight: 700;">{{dataPrint.moneyGuarantee | number}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Thời hạn bảo lãnh dự thầu (tháng):</td>
          <td style="font-weight: 700;">{{dataPrint.timeGuarantee}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Thời gian đăng tải:</td>
          <td style="font-weight: 700;">{{dataPrint.publicDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Thời điểm mở thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.startBidDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Thành viên phụ trách mua hàng:</td>
          <td style="font-weight: 700;">{{dataPrint.mpo}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Người duyệt nội dung mua hàng:</td>
          <td style="font-weight: 700;">{{dataPrint.mpoLeader}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Thành viên phụ trách yêu cầu kỹ thuật:</td>
          <td style="font-weight: 700;">{{dataPrint.tech}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Người duyệt yêu cầu kỹ thuật:</td>
          <td style="font-weight: 700;">{{dataPrint.techLeader}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Mô tả nội dung mời thầu:</td>
          <td style="font-weight: 700;">{{dataPrint.serviceInvite}}</td>
        </tr>

      </tbody>
    </table>
  </div>

  <!-- Kết quả đánh giá -->
  <div style="border: 1px solid black; padding: 0px 5px; page-break-inside: avoid;">
    <table style="margin-bottom: 10px; border-spacing: 0;">
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px;">III.</th>
          <th style="width: 40%;">Kết quả đánh giá:</th>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
        </tr>
        <tr style="border: darkgrey 1px solid; text-align: center;">
          <th style="width: 30px; border: darkgrey 1px solid;">
            {{ language_key?.NO || 'STT' }}
          </th>
          <th style="width: 40%; border: darkgrey 1px solid;">
            {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}
          </th>
          <th style="width: 10%; border: darkgrey 1px solid;">Tổng điểm</th>
          <th style="width: 10%; border: darkgrey 1px solid;">Điểm đánh giá kỹ thuật</th>
          <th style="width: 10%; border: darkgrey 1px solid;">Điểm đánh giá ĐKTM</th>
          <th style="width: 10%; border: darkgrey 1px solid;">Điểm đánh giá bảng giá</th>
          <th style="width: 10%; border: darkgrey 1px solid;">Trạng thái hồ sơ</th>
          <th style="width: 10%; border: darkgrey 1px solid;">Duyệt chọn thầu</th>
        </tr>
      </thead>
      <tbody>
        <tr style="text-align: center; border: darkgrey 1px solid;">
          <td style="border: darkgrey 1px solid;"></td>
          <td style="border: darkgrey 1px solid;"><i>Điểm tối đa</i></td>
          <td style="border: darkgrey 1px solid;"><i>100</i></td>
          <td style="border: darkgrey 1px solid;"><i>100</i></td>
          <td style="border: darkgrey 1px solid;"><i>100</i></td>
          <td style="border: darkgrey 1px solid;"><i>100</i></td>
          <td style="border: darkgrey 1px solid;"></td>
          <td style="border: darkgrey 1px solid;"></td>
        </tr>
        <ng-container *ngFor="let bidItem of dataPrint.listItem; index as i2">
          <tr style="text-align: center; border: darkgrey 1px solid;">
            <td colspan="8" style="border: darkgrey 1px solid; background: #e5e7f9;">
              ({{i2+1}}) Tên Item: <b>{{bidItem.itemName}}</b> &nbsp;&nbsp;&nbsp; Số lượng: <b>{{bidItem.quantityItem |
                number: '1.0-2'}}</b>
            </td>
          </tr>
          <tr style="text-align: center; border: darkgrey 1px solid;"
            *ngFor="let bidSupplier of bidItem.lstBidSupplier; let i = index">
            <td style="border: darkgrey 1px solid;">{{i+1}}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{bidSupplier.supplierName}}</td>
            <td style="border: darkgrey 1px solid;">{{bidSupplier.scoreTotal | number: '1.0-2'}}</td>
            <td style="border: darkgrey 1px solid;">{{bidSupplier.scoreTech | number: '1.0-2'}}</td>
            <td style="border: darkgrey 1px solid;">{{bidSupplier.scoreTrade | number: '1.0-2'}}</td>
            <td style="border: darkgrey 1px solid;">{{bidSupplier.scorePrice | number: '1.0-2'}}</td>
            <td style="border: darkgrey 1px solid;">{{bidSupplier.validStatus}}</td>
            <td style="border: darkgrey 1px solid;">{{bidSupplier.successBidStatus}}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>

  <!-- Hồ sơ đánh giá chi tiết đính kèm -->
  <div style="border: 1px solid black; padding: 0px 5px; page-break-inside: avoid;">
    <table style="margin-bottom: 10px;">
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px;">*</th>
          <th style="width: 100%;">Hồ sơ đánh giá chi tiết đính kèm:</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of lstOptionPrintChoose; let i = index;">
          <td>{{i+1}}.</td>
          <td>{{item.name}}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px; page-break-inside: avoid;">
    <div style="text-align: right; font-weight: 700;" *ngIf="dataPrint.approveChooseSupplierWinDate">
      Ngày {{dataPrint.approveChooseSupplierWinDate | date: 'dd'}}
      tháng {{dataPrint.approveChooseSupplierWinDate | date: 'MM'}}
      năm {{dataPrint.approveChooseSupplierWinDate | date: 'yyyy'}}
    </div>
    <div style="text-align: right; font-weight: 700;" *ngIf="!dataPrint.approveChooseSupplierWinDate">
      Ngày .....
      tháng .....
      năm .....
    </div>
    <table style="margin: 10px 0 100px 0;">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 30px;"></th>
          <th style="width: 25%;">BP. MUA HÀNG</th>
          <th style="width: 25%;">BP. YÊU CẦU</th>
          <th style="width: 25%;">BP. TÀI CHÍNH</th>
          <th style="width: 25%;">BAN GIÁM ĐỐC</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>