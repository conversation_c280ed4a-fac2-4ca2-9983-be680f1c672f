import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-result-print.component.html' })
export class BidResultPrintComponent implements OnInit {
  dataPrint: any
  todate = new Date()
  lstOptionPrint = [
    { name: '<PERSON><PERSON><PERSON> đ<PERSON>h giá hồ sơ năng lực', isShow: true },
    { name: '<PERSON>ảng đánh giá kỹ thuật', isShow: true },
    { name: 'Bảng đánh giá ĐKTM', isShow: true },
    { name: 'Bảng báo giá', isShow: true },
  ]
  lstOptionPrintChoose: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code

    this.changeOptionPrint()
    this.loadDataPrint()
  }

  loadDataPrint() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.BID_RATE.GET_DATA_PRINT(this.data.id), {}).then(async (res) => {
      this.notifyService.hideloading()
      this.dataPrint = res
    })
  }

  changeOptionPrint() {
    this.lstOptionPrintChoose = this.lstOptionPrint.filter((c) => c.isShow)
  }
}
