import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Subscription } from 'rxjs'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../services'
import { BidDetailComponent } from '../bid-detail/bid-detail.component'
import { BidRateReportComponent } from '../bid-rate/bid-result-report/bid-result-report.component'
import { BidResultPrintComponent } from './bid-result-print/bid-result-print.component'

@Component({ templateUrl: './bid-info.component.html' })
export class BidInfoComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  listBidStatus = [
    enumData.BidStatus.DangNhanBaoGia,
    enumData.BidStatus.DangDanhGia,
    enumData.BidStatus.DangDuyetDanhGia,
    enumData.BidStatus.HoanTatDanhGia,
    enumData.BidStatus.HoanTat,
  ]
  listOfData: any[] = []
  showReport = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  enumRole1: any
  constructor(
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()

    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_012.code
    this.enumRole1 = this.enumProject.Features.BID_011.code
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') where.name = this.dataSearch.name
    if (this.dataSearch.status && this.dataSearch.status.length > 0) where.status = this.dataSearch.status
    if (this.dataSearch.dateFrom) where.dateFrom = this.dataSearch.dateFrom
    if (this.dataSearch.dateTo) where.dateTo = this.dataSearch.dateTo

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    this.loading = true
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.BID_RATE.RESULT_PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        for (const item of this.listOfData) {
          item.isShowPrint = item.isMPO || item.isMPOLeader

          // report: các thành viên trong hội đồng thầu, mpo, mpoLead
          item.isShowReport = (item.isMPO || item.isMPOLeader || item.isMember) && item.status !== enumData.BidStatus.DangNhanBaoGia.code
          if (item.isShowReport) this.showReport = true
        }
      }
    })
  }

  clickView(object: any) {
    this.dialog.open(BidDetailComponent, { disableClose: false, data: object })
  }

  bidPrint(object: any) {
    this.dialog.open(BidResultPrintComponent, { disableClose: false, data: object })
  }

  /** Báo cáo kết quả đánh giá */
  reportRateBid(object: any) {
    this.dialog.open(BidRateReportComponent, { disableClose: false, data: object.id })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve(1)
    })
  }
}
