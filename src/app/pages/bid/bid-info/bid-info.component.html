<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tìm kiếm thông tin gói thầu">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
          [nzLoadData]="loadDataService">
        </nz-cascader>
      </nz-col>
      <nz-col nzSpan="6">
        <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo mã số hoặc tên gói thầu" />
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Từ ngày đăng tải">
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="12">
        <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
          [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
          <nz-option *ngFor="let item of listBidStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Mã TBMT</th>
        <th>Tên gói thầu</th>
        <th>Phụ trách kỹ thuật</th>
        <th>Phụ trách mua hàng</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th *ngIf="showReport">Báo cáo</th>
        <th class="text-center">In hồ sơ thầu</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td (click)="clickView(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.name }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.techName }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.mpoName }}</td>
        <td class="text-nowrap" (click)="clickView(data)">{{ data.statusName }}</td>
        <td class="text-nowrap" *ngIf="showReport">
          <button *ngIf="data.isShowReport && authenticationService.checkPermission([enumRole1], action.View.code)"
            nz-tooltip nzTooltipTitle="Báo cáo kết quả đánh giá" (click)="reportRateBid(data)" nz-button
            nzType="primary">
            <span nz-icon nzType="file-text"></span>
          </button>
        </td>
        <td class="text-center">
          <button *ngIf="data.isShowPrint && authenticationService.checkPermission([enumRole], action.Print.code)"
            nz-tooltip nzTooltipTitle="In hồ sơ thầu" (click)="bidPrint(data)" nz-button nzType="dashed">
            <span nz-icon nzType="printer"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>