import { Component, OnInit } from '@angular/core'
import { enumData } from '../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../services'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditBidComponent } from './add-or-edit-bid/add-or-edit-bid.component'
import { BidDetailComponent } from './bid-detail/bid-detail.component'
import { BidTechComponent } from './bid-tech/bid-tech.component'
import { BidTradeComponent } from './bid-trade/bid-trade.component'
import { BidPriceComponent } from './bid-price/bid-price.component'
import { BidChooseSupplierComponent } from './bid-choose-supplier/bid-choose-supplier.component'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { BidSettingRateComponent } from './bid-setting-rate/bid-setting-rate.component'
import * as XLSX from 'xlsx'
import { Subscription } from 'rxjs'
import { BiddingAdminComponent } from './bidding-admin/bidding-admin.component'

@Component({ templateUrl: './bid.component.html' })
export class BidComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  isVisible = false
  dataSearch: any = {}
  dataEmail: any = {}
  bidStatus = enumData.BidStatus
  bidTechStatus = enumData.BidTechStatus
  bidTradeStatus = enumData.BidTradeStatus
  bidPriceStatus = enumData.BidPriceStatus
  listBidStatus: any[] = []
  listOfData: any[] = []
  isCollapseFilter = false
  lstBidEmployee: any
  lstSupplier: any
  isVisibleError = false
  lstErrorImport: any[] = []
  isShowCopyBid = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  enumRole2: any
  enumRole3: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.BID_001.code
    this.enumRole2 = this.enumProject.Features.BID_003.code
    this.enumRole3 = this.enumProject.Features.BID_004.code
    this.action = this.enumProject.Action
    this.listBidStatus = [
      enumData.BidStatus.GoiThauTam,
      enumData.BidStatus.ChoDuyetGoiThauTam,
      enumData.BidStatus.DangCauHinhGoiThau,
      enumData.BidStatus.DangChonNCC,
      enumData.BidStatus.TuChoiGoiThau,
      enumData.BidStatus.DangDuyetGoiThau,
      enumData.BidStatus.DangNhanBaoGia,
    ]
    this.searchData()
  }

  async searchData(reset = false) {
    this.loading = true
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.loading = false
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.status && this.dataSearch.status.length > 0) {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }

    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }

    // Lấy gói thầu cần duyệt
    if (this.dataSearch.isGetBidNeedApprove) {
      where.isGetBidNeedApprove = this.dataSearch.isGetBidNeedApprove
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.BID.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditBidComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditBidComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEditOther(object: any) {
    this.dialog
      .open(BidSettingRateComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickView(object: any) {
    this.dialog
      .open(BidDetailComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidTech(object: any) {
    this.dialog
      .open(BidTechComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  bidTrade(object: any) {
    this.dialog
      .open(BidTradeComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  bidPrice(object: any) {
    this.dialog
      .open(BidPriceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  chooseSupplier(object: any) {
    this.dialog
      .open(BidChooseSupplierComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  async sendEmailBid(object: any) {
    this.dataEmail = {
      bidId: object.id,
      emailContent: '',
      lstEmployeeId: [],
      lstSupplierId: [],
    }
    this.lstBidEmployee = await this.apiService.post(this.apiService.EMPLOYEE.FIND, { bidId: object.id })
    this.lstSupplier = await this.apiService.post(this.apiService.SUPPLIER.FIND, { bidId: object.id })
    this.isVisible = true
  }

  onSendEmail() {
    if (this.dataEmail.emailContent == null || this.dataEmail.emailContent.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập nội dung gửi email.')
      return
    }
    if (this.dataEmail.lstEmployeeId == null || this.dataEmail.lstEmployeeId.length === 0) {
      this.notifyService.showError('Vui lòng chọn nhân viên nội bộ.')
      return
    }
    this.apiService.post(this.apiService.BID.SEND_EMAIL_BID, this.dataEmail).then(() => {
      this.notifyService.showSuccess('Gửi email thông báo thành công.')
      this.isVisible = false
    })
  }

  handleCancel() {
    this.isVisible = false
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve(1)
    })
  }

  //#region Import excel service
  clickExportExcelTemplate() {
    this.notifyService.showloading()
    let lstDataExport = []
    //#region header
    const header: any = {
      zenId: (this.language_key?.NO || 'STT') + ' *',
      companyInvite: 'Công ty mời thầu *',
      addressSubmit: 'Địa chỉ nộp hồ sơ thầu',
      listAddress: 'Các địa điểm thực hiện gói thầu',
      anotherRoleNames: 'Các thành viên hội đồng xét thầu *',
      otherRoleNames: 'Các thành viên khác',
      name: 'Tên gói thầu *',
      scoreDLC: 'Điểm tối đa cho các hạng mục chào giá *',
      bidTypeName: 'Hình thức đấu thầu *',

      acceptEndDate: '3.Ngày hết hạn xác nhận tham gia đấu thầu *',
      submitEndDate: '4.Ngày hết hạn nộp hồ sơ thầu *',
      timeserving: 'Hiệu lực hợp đồng (tháng) *',
      masterBidGuaranteeName: 'Hình thức bảo lãnh dự thầu',
      moneyGuarantee: 'Số tiền bảo lãnh dự thầu (VNĐ)',
      timeGuarantee: 'Thời hạn bảo lãnh dự thầu (tháng)',

      mpoName: 'Thành viên phụ trách mua hàng *',
      mpoLeadName: 'Người duyệt nội dung mua hàng *',
      techName: 'Thành viên phụ trách yêu cầu kỹ thuật *',
      techLeadName: 'Người duyệt yêu cầu kỹ thuật *',

      timeTechDate: '1.Thời hạn thiết lập các yêu cầu kỹ thuật *',
      timePriceDate: '2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *',
      timeCheckTechDate: '5.Thời hạn đánh giá các yêu cầu kỹ thuật *',
      timeCheckPriceDate: '6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *',
      percentTech: 'Tỉ lệ phần trăm kỹ thuật *',
      percentTrade: 'Tỉ lệ phần trăm ĐKTM *',
      percentPrice: 'Tỉ lệ phần trăm giá *',
      serviceInvite: 'Mô tả nội dung mời thầu *',
      isShowHomePage: 'Cho phép hiển thị thông tin mời thầu ở trang tin đấu thầu',
      isSendEmailInviteBid: 'Gửi thông báo mời thầu cho NCC',
      isAutoBid: 'Tự động chọn NCC thắng thầu và kết thúc thầu',
    }
    lstDataExport.push(header)
    //#endregion

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `Template import bid.xlsx`
    XLSX.utils.book_append_sheet(wb, ws)

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  excelDateToJSDate(serial: number): Date {
    const excelEpochStart = new Date(Date.UTC(1899, 11, 30)) // Ngày 0 trong Excel (30/12/1899)
    const jsDate = new Date(excelEpochStart.getTime() + serial * 24 * 60 * 60 * 1000) // Thêm số ngày
    return jsDate
  }

  clickImportExcel(ev: any) {
    this.notifyService.showloading()
    this.lstErrorImport = []
    let workBook = null
    let jsonData: any[] = []
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'companyInvite',
          'addressSubmit',
          'listAddress',
          'anotherRoleNames',
          'otherRoleNames',
          'name',
          'scoreDLC',
          'bidTypeName',
          'acceptEndDate',
          'submitEndDate',
          'timeserving',
          'masterBidGuaranteeName',
          'moneyGuarantee',
          'timeGuarantee',
          'mpoName',
          'mpoLeadName',
          'techName',
          'techLeadName',
          'timeTechDate',
          'timePriceDate',
          'timeCheckTechDate',
          'timeCheckPriceDate',
          'percentTech',
          'percentTrade',
          'percentPrice',
          'serviceInvite',
          'isShowHomePage',
          'isSendEmailInviteBid',
          'isAutoBid',
        ],
      })

      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()
      // Kiểm tra header
      if (
        header.zenId !== (this.language_key?.NO || 'STT') + ' *' ||
        header.companyInvite !== 'Công ty mời thầu *' ||
        header.addressSubmit !== 'Địa chỉ nộp hồ sơ thầu' ||
        header.listAddress !== 'Các địa điểm thực hiện gói thầu' ||
        header.anotherRoleNames !== 'Các thành viên hội đồng xét thầu *' ||
        header.otherRoleNames !== 'Các thành viên khác' ||
        header.name !== 'Tên gói thầu *' ||
        header.scoreDLC !== 'Điểm tối đa cho các hạng mục chào giá *' ||
        header.bidTypeName !== 'Hình thức đấu thầu *' ||
        header.acceptEndDate !== '3.Ngày hết hạn xác nhận tham gia đấu thầu *' ||
        header.submitEndDate !== '4.Ngày hết hạn nộp hồ sơ thầu *' ||
        header.timeserving !== 'Hiệu lực hợp đồng (tháng) *' ||
        header.masterBidGuaranteeName !== 'Hình thức bảo lãnh dự thầu' ||
        header.moneyGuarantee !== 'Số tiền bảo lãnh dự thầu (VNĐ)' ||
        header.timeGuarantee !== 'Thời hạn bảo lãnh dự thầu (tháng)' ||
        header.mpoName !== 'Thành viên phụ trách mua hàng *' ||
        header.mpoLeadName !== 'Người duyệt nội dung mua hàng *' ||
        header.techName !== 'Thành viên phụ trách yêu cầu kỹ thuật *' ||
        header.techLeadName !== 'Người duyệt yêu cầu kỹ thuật *' ||
        header.timeTechDate !== '1.Thời hạn thiết lập các yêu cầu kỹ thuật *' ||
        header.timePriceDate !== '2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *' ||
        header.timeCheckTechDate !== '5.Thời hạn đánh giá các yêu cầu kỹ thuật *' ||
        header.timeCheckPriceDate !== '6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *' ||
        header.percentTech !== 'Tỉ lệ phần trăm kỹ thuật *' ||
        header.percentTrade !== 'Tỉ lệ phần trăm ĐKTM *' ||
        header.percentPrice !== 'Tỉ lệ phần trăm giá *' ||
        header.serviceInvite !== 'Mô tả nội dung mời thầu *' ||
        header.isShowHomePage !== 'Cho phép hiển thị thông tin mời thầu ở trang tin đấu thầu' ||
        header.isSendEmailInviteBid !== 'Gửi thông báo mời thầu cho NCC'
        // header.isAutoBid !== 'Tự động chọn NCC thắng thầu và kết thúc thầu'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(`File không đúng template import bid`)
        return
      }

      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có gói thầu cần import!`)
        return
      }

      jsonData.forEach((row) => {
        if (typeof row.acceptEndDate === 'number') row.acceptEndDate = this.excelDateToJSDate(row.acceptEndDate)
        if (typeof row.submitEndDate === 'number') row.submitEndDate = this.excelDateToJSDate(row.submitEndDate)
        if (typeof row.timeTechDate === 'number') row.timeTechDate = this.excelDateToJSDate(row.timeTechDate)
        if (typeof row.timePriceDate === 'number') row.timePriceDate = this.excelDateToJSDate(row.timePriceDate)
        if (typeof row.timeCheckTechDate === 'number') row.timeCheckTechDate = this.excelDateToJSDate(row.timeCheckTechDate)
        if (typeof row.timeCheckPriceDate === 'number') row.timeCheckPriceDate = this.excelDateToJSDate(row.timeCheckPriceDate)
        if (typeof row.isShowHomePage == 'string' && row.isShowHomePage.toLowerCase() == 'có') row.isShowHomePage = true
        else if (typeof row.isShowHomePage == 'string' && row.isShowHomePage.toLowerCase() == 'không') row.isShowHomePage = false
        if (typeof row.isSendEmailInviteBid == 'string' && row.isSendEmailInviteBid.toLowerCase() == 'có') row.isSendEmailInviteBid = true
        else if (typeof row.isSendEmailInviteBid == 'string' && row.isSendEmailInviteBid.toLowerCase() == 'không') row.isSendEmailInviteBid = false
        if (typeof row.isAutoBid == 'string' && row.isAutoBid.toLowerCase() == 'có') row.isAutoBid = true
        else if (typeof row.isAutoBid == 'string' && row.isAutoBid.toLowerCase() == 'không') row.isAutoBid = false
      })

      jsonData = jsonData.filter((c) => c.zenId != null && c.zenId !== '' && (c.zenId > 0 || c.zenId.trim() !== ''))
      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có gói thầu cần import. Lưu ý, chỉ dòng có STT mới được import!`)
        return
      }

      this.apiService.post(this.apiService.BID.IMPORT_BID, { lstData: jsonData }).then((res) => {
        if (res) {
          // Lỗi khi kiểm tra trước lúc vào transaction (không import)
          if (res.isCheckError) {
            this.notifyService.showError(`${res.message}`)
            this.lstErrorImport = res.lstError
            this.isVisibleError = true
          }
          // Lỗi khi vào transaction (import 1 phần)
          else if (res.lstError.length > 0) {
            this.notifyService.showError(`${res.message}`)
            this.lstErrorImport = res.lstError
            this.isVisibleError = true
            this.searchData(true)
          }
          // import 100%
          else {
            this.notifyService.showSuccess(`${res.message}`)
            this.searchData(true)
            this.lstErrorImport = []
          }
        }
      })
    }
  }
  //#endregion

  //#region Sao chép gói thầu
  checkButton() {
    this.isShowCopyBid = !this.isShowCopyBid
  }

  clickCopy(bid: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.COPY_BID, { bidId: bid.id }).then((res) => {
      if (res) {
        this.notifyService.showSuccess(res.message)
        this.searchData()
      }
    })
  }
  //#endregion

  //#region Xóa gói thầu
  clickRequestDelete(bid: any) {
    this.notifyService.showloading()
    this.apiService.put(this.apiService.BID.REQUEST_DELETE_BID(bid.id), {}).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.searchData()
    })
  }
  clickApproveDelete(bid: any) {
    this.notifyService.showloading()
    this.apiService.put(this.apiService.BID.DELETE_BID(bid.id), {}).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.searchData()
    })
  }
  //#endregion

  //#region Nhập hồ sơ gói thầu cho NCC
  biddingFromAdminToSupplier(object: any) {
    this.dialog.open(BiddingAdminComponent, { disableClose: false, data: object })
  }
  //#endregion
}
