import { Component, Inject, OnInit, Optional } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Subscription } from 'rxjs'
import { enumData } from '../../../core'
import { User } from '../../../models'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { BidPrintComponent } from '../bid-print/bid-print.component'

@Component({ templateUrl: './add-or-edit-bid.component.html' })
export class AddOrEditBidComponent implements OnInit {
  modalTitle = 'Thêm mới gói thầu'
  modalTitleView = 'Thông tin gói thầu'
  currentUser!: User
  validateForm!: FormGroup
  dataEmployee: any[] = []
  dataBidType: any[] = []
  dataCompanyInvite: any[] = []
  dataAddressSubmit: any[] = []
  dataMasterBidGuarantee: any[] = []
  dataObject: any = { listItem: [] }
  dataItem: any = {}
  today = new Date()
  yesterday = new Date()
  dataService: any[] = []
  isAllowChange = false
  dataPr: any[] = []
  headerListItem = 'Danh sách Item'

  maxSizeUpload = enumData.maxSizeUpload
  language_key: any
  subscriptions: Subscription = new Subscription()
  isChooseAll: any
  enumProject: any
  enumRole: any
  action: any
  service: any
  enumRolePr: any
  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private dialogRef: MatDialogRef<AddOrEditBidComponent>,
    public authenticationService: AuthenticationService,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.enumRolePr = this.enumProject.Features.BID_002.code
    this.yesterday.setDate(this.yesterday.getDate() - 1)
    this.validateForm = this.fb.group({
      name: [null, [Validators.required, Validators.maxLength(250)]],
      moneyGuarantee: [null, [Validators.min(0)]],
      timeGuarantee: [null, [Validators.min(0)]],
      bidTypeId: [null, [Validators.required]],
      serviceInvite: [null, [Validators.required, Validators.maxLength(250)]],
      mpoId: [null, [Validators.required]],
      mpoLeadId: [null, [Validators.required]],
      techId: [null, [Validators.required]],
      techLeadId: [null, [Validators.required]],
      anotherRoleIds: [null, [Validators.required]],
      otherRoleIds: [null],
      companyInvite: [null, [Validators.required]],
      addressSubmit: [null],
      listAddress: [null, [Validators.maxLength(250)]],
      acceptEndDate: [null, [Validators.required]],
      submitEndDate: [null, [Validators.required]],
      timeserving: [null, [Validators.required, Validators.min(0)]],
      // scoreDLC: [null, [Validators.required, Validators.min(0)]],
      masterBidGuaranteeId: [null],
      serviceId: [null, [Validators.required]],
      timeTechDate: [null, [Validators.required]],
      timePriceDate: [null, [Validators.required]],
      timeCheckTechDate: [null, [Validators.required]],
      timeCheckPriceDate: [null, [Validators.required]],
      isShowHomePage: [null, [Validators.required]],
      hiddenScore: [null, [Validators.required]],
      isSkipEnd: [null, [Validators.required]],
      isNotImportFromAdmin: [null, [Validators.required]],
      isSendEmailInviteBid: [null, [Validators.required]],
      watchProfile: [null, [Validators.required]],
      isAutoBid: [null],
      prId: [null],
      // percentTech: [null, [Validators.required, Validators.min(0)]],
      // percentTrade: [null, [Validators.required, Validators.min(0)]],
      // percentPrice: [null, [Validators.required, Validators.min(0)]],
      fileDrawing: [null, [Validators.maxLength(500)]],
      fileJD: [null, [Validators.maxLength(500)]],
      fileKPI: [null, [Validators.maxLength(500)]],
      fileRule: [null, [Validators.maxLength(500)]],
      fileDocument: [null, [Validators.maxLength(500)]],
      fileAnother: [null, [Validators.maxLength(500)]],
    })

    this.loadAllSelect()
  }

  async loadAllSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.BID_TYPE.FIND, {}), // 0
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.company }), // 1
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.address }), // 2
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.masterBidGuarantee }), // 3
      this.apiService.post(this.apiService.EMPLOYEE.FIND, {}), // 4
      this.apiService.post(this.apiService.MATERIAL.FIND, {}), // 5
      this.apiService.post(this.apiService.PR.LOAD_PR_CREATE_BID, { bidId: this.data?.id }), // 6
      this.apiService.post(this.apiService.SERVICE.FIND, {}), // 7
    ]).then(async (res) => {
      this.dataBidType = res[0]
      this.dataCompanyInvite = res[1]
      this.dataAddressSubmit = res[2]
      this.dataMasterBidGuarantee = res[3]
      this.dataEmployee = res[4]
      this.dataService = res[5]
      this.dataPr = res[6] || []
      this.service = res[7] || []

      //#region Check data
      let flagCheckErr = false
      if (this.dataBidType.length === 0) {
        this.notifyService.showError('Vui lòng liên hệ IT thiết lập hình thức đấu thầu trước!')
        flagCheckErr = true
      }
      if (this.dataCompanyInvite.length === 0) {
        this.notifyService.showError('Vui lòng liên hệ IT thiết lập công ty mời thầu trước!')
        flagCheckErr = true
      }
      if (this.dataMasterBidGuarantee.length === 0) {
        this.notifyService.showError('Vui lòng liên hệ IT thiết lập hình thức bảo lãnh dự thầu trước!')
        flagCheckErr = true
      }
      if (this.dataEmployee.length === 0) {
        this.notifyService.showError('Vui lòng liên hệ IT thiết lập nhân viên trước!')
        flagCheckErr = true
      }
      if (flagCheckErr) {
        this.closeDialog(false)
        return
      }
      //#endregion

      if (this.data && this.data.id) {
        this.modalTitle = 'Chỉnh sửa gói thầu'
        await this.apiService.post(this.apiService.BID.FIND_DETAIL_EDIT, { id: this.data.id }).then((res) => {
          this.dataObject = res

          // if (res?.__service__?.parentId) {
          //   if (res?.__service__?.__parent__?.parentId) {
          //     this.dataObject.serviceId = [res.__service__.__parent__.parentId, res.__service__.parentId, res.serviceId]
          //   } else {
          //     this.dataObject.serviceId = [res.__service__.parentId, res.serviceId]
          //   }
          // } else {
          //   this.dataObject.serviceId = [res.serviceId]
          // }

          setTimeout(() => {
            this.isAllowChange = true
          }, 200)
        })
      } else {
        this.isAllowChange = true
        this.dataObject.isAllowEditBid = true
        this.dataObject.isShowHomePage = false
        this.dataObject.hiddenScore = false
        this.dataObject.isSkipEnd = false
        this.dataObject.isNotImportFromAdmin = false
        this.dataObject.watchProfile = false
        this.dataObject.isSendEmailInviteBid = true
        this.dataObject.isAutoBid = false
        this.dataObject.scoreDLC = 100
        this.dataObject.mpoId = this.currentUser.employeeId
        this.dataObject.isMPO = true
      }
      this.notifyService.hideloading()
    })
  }

  async submitForm(): Promise<void> {
    this.notifyService.showloading()
    // tslint:disable-next-line: forin
    for (const i in this.validateForm.controls) {
      this.validateForm.controls[i].markAsDirty()
      this.validateForm.controls[i].updateValueAndValidity()
    }
    if (!this.validateForm.valid) {
      this.notifyService.showError('Có lỗi xảy ra, vui lòng kiểm tra lại.')
      return
    }

    //#region Check data
    this.dataObject.timeTechDate = new Date(this.dataObject.timeTechDate)
    this.dataObject.timeTechDate.setMilliseconds(0)

    this.dataObject.timePriceDate = new Date(this.dataObject.timePriceDate)
    this.dataObject.timePriceDate.setMilliseconds(0)

    this.dataObject.acceptEndDate = new Date(this.dataObject.acceptEndDate)
    this.dataObject.acceptEndDate.setMilliseconds(0)

    this.dataObject.submitEndDate = new Date(this.dataObject.submitEndDate)
    this.dataObject.submitEndDate.setMilliseconds(0)

    this.dataObject.timeCheckTechDate = new Date(this.dataObject.timeCheckTechDate)
    this.dataObject.timeCheckTechDate.setMilliseconds(0)

    this.dataObject.timeCheckPriceDate = new Date(this.dataObject.timeCheckPriceDate)
    this.dataObject.timeCheckPriceDate.setMilliseconds(0)

    if (this.dataObject.timeTechDate <= this.today) {
      this.notifyService.showError('Ngày (1) phải trễ hơn ngày hiện tại.')
      return
    }
    if (this.dataObject.timePriceDate <= this.today) {
      this.notifyService.showError('Ngày (2) phải trễ hơn ngày hiện tại.')
      return
    }
    if (this.dataObject.timeTechDate >= this.dataObject.acceptEndDate) {
      this.notifyService.showError('Ngày (1) phải sớm hơn ngày (3).')
      return
    }
    if (this.dataObject.timePriceDate >= this.dataObject.acceptEndDate) {
      this.notifyService.showError('Ngày (2) phải sớm hơn ngày (3).')
      return
    }
    if (this.dataObject.acceptEndDate >= this.dataObject.submitEndDate) {
      this.notifyService.showError('Ngày (3) phải sớm hơn ngày (4).')
      return
    }
    if (this.dataObject.submitEndDate >= this.dataObject.timeCheckTechDate) {
      this.notifyService.showError('Ngày (4) phải sớm hơn ngày (5).')
      return
    }
    if (this.dataObject.submitEndDate >= this.dataObject.timeCheckPriceDate) {
      this.notifyService.showError('Ngày (4) sớm hơn ngày (6).')
      return
    }
    //#endregion

    //#region Lưu bid
    if (!this.dataObject.id) {
      this.apiService.post(this.apiService.BID.CREATE_BID, this.dataObject).then((res) => {
        if (res) {
          this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
          this.closeDialog(true)
        }
      })
    } else {
      this.apiService.post(this.apiService.BID.UPDATE_BID, this.dataObject).then((res) => {
        if (res) {
          this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
          this.closeDialog(true)
        }
      })
    }
    //#endregion
  }

  //#region DS Item

  /** Chọn lại hoặc bỏ chọn PR ở loại gói thầu 3 */
  dataPrChange(prId: string) {
    if (!this.isAllowChange) return
    this.dataObject.isChangeItem = true
    const prChoose = this.dataPr.find((c) => c.id == prId)
    this.headerListItem = 'Danh sách Item'
    if (!prChoose) {
      // Trường hợp bỏ chọn PR
      if (this.dataObject.listItem.length > 0) {
        this.notifyService.showInfo('Thực hiện xóa danh sách Item của Pr trước đó.')
      }

      setTimeout(() => {
        this.dataObject.listItem = []
      }, 1000)

      return
    }
    this.apiService.post(this.apiService.PR.FINDDETAIL, prChoose).then((result) => {
      prChoose.lstItem = result.lstItem
      for (let data of prChoose.lstItem) {
        data.itemName = `${data.__service__.code} - ${data.__service__.name}`
        data.prItemId = data.id
        data.itemId = data.itemId
        data.quantityCreatedBid = data.quantity - data.quantityBid
      }
    })
    this.notifyService.showInfo(`Thực hiện load danh sách Item của PR có mã [${prChoose.code}]`)
    this.headerListItem = `Danh sách Item theo PR [${prChoose.code}]`

    setTimeout(() => {
      // load item của pr ra
      this.dataObject.listItem = prChoose.lstItem || []
      this.dataObject.listItem.forEach((c: any) => (c.isNew = true))
    }, 1000)
  }

  changeEmployee() {
    if (!this.isAllowChange) return
    this.dataObject.isChangeEmployee = true
  }

  log(data: any) {
    console.log(data)
  }
  /** Thêm Item ở loại gói thầu 2 */
  onAddItem() {
    this.notifyService.showloading()
    if (!this.dataItem.serviceId) {
      this.notifyService.showError('Vui lòng chọn LVMH!')
      return
    }

    for (const item of this.dataObject.listItem)
      if (item.itemId === this.dataItem.serviceId) {
        this.notifyService.showError('Item đã được thêm, vui lòng chọn Item khác!')
        return
      }

    if (this.dataItem.quantityItem === null) {
      this.notifyService.showError('Vui lòng nhập số lượng LVMH!')
      return
    }
    if (this.dataItem.quantityItem <= 0) {
      this.notifyService.showError('Vui lòng nhập số lượng LVMH hợp lệ!')
      return
    }
    if (this.dataObject.listItem.some((c: any) => c.serviceId === this.dataItem.serviceId)) {
      this.notifyService.showError('LVMH đã được thêm, vui lòng chọn LVMH khác!')
      return
    }

    const serviceChoose = this.dataService.find((c: any) => c.id == this.dataItem.serviceId)
    if (!serviceChoose) {
      this.notifyService.showError('Không tìm thấy LVMH, vui lòng thử lại!')
      return
    }

    this.dataObject.isChangeItem = true

    const objItem: any = {
      itemId: this.dataItem.serviceChoose,
      percentTech: this.dataItem.percentTech,
      percentTrade: this.dataItem.percentTrade,
      percentPrice: this.dataItem.percentPrice,
      itemName: `${serviceChoose.code + ' - ' + serviceChoose.name}`,
      quantityItem: 0,
      quantityCreatedBid: this.dataItem.quantityItem,
      isNew: true,
    }
    this.dataObject.listItem = [...this.dataObject.listItem, objItem]

    // reset các biến
    setTimeout(() => {
      this.dataObject.parent2 = null
      // this.dataObject.serviceId = null
      this.dataObject.quantityItem = 0
      this.notifyService.hideloading()
    }, 1000)
  }

  /** Xóa Item đã chọn */
  onDeleteItemChoose() {
    this.dataObject.listItem = this.dataObject.listItem.filter((c: any) => !c.isChoose)
    this.dataObject.isChangeItem = true
  }

  /** Giữ lại Item đã chọn */
  onKeepItemChoose() {
    this.dataObject.listItem = this.dataObject.listItem.filter((c: any) => c.isChoose)
    this.dataObject.isChangeItem = true
  }

  isChangeChooseAll() {
    for (const item of this.dataObject.listItem) {
      item.isChoose = this.isChooseAll
    }
  }
  //#endregion

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  async onPrintBid() {
    this.dialog.open(BidPrintComponent, { disableClose: false, data: this.dataObject })
  }

  disabledDate = (current: Date): boolean => {
    return current < this.yesterday
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    // for (const item of this.service) {
    //   if (item.id == 'ba624abe-92d6-4640-aaab-59e6e7fc469d') {
    //     console.log('day ne')
    //   }
    // }
    return new Promise<void>((resolve) => {
      if (index < 0) {
        // if index less than 0 it is root node
        node.children = this.service
          .filter((c: any) => c.level === 1)
          .map((c: any) => {
            return { value: c.id, label: c.name, isLeaf: c.isLast }
          })
      } else {
        node.children = this.service
          .filter((c: any) => c.parentId === node.value)
          .map((c: any) => {
            return { value: c.id, label: c.name, isLeaf: c.isLast }
          })
      }
      resolve()
    })
  }

  onChangeService = () => {
    this.dataItem.serviceId = null

    if (this.dataItem.serviceChoose && this.dataItem.serviceChoose.length > 0) {
      this.dataItem.serviceId = this.dataItem.serviceChoose

      // Nếu gói thầu đang tạo mới thì lấy theo service
      if (!this.dataItem.id) {
        let objService = this.dataService.find((c: any) => c.id == this.dataItem.serviceId)
        if (objService) {
          this.dataItem.percentTech = objService.percentTech
          this.dataItem.percentTrade = objService.percentTrade
          this.dataItem.percentPrice = objService.percentPrice
        }
      }
    }
  }

  onRefreshItem() {
    this.dataPrChange(this.dataObject.prId)
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }

  sendMPOLeaderCheckBid = () => {
    if (this.dataObject.id && this.dataObject.status == enumData.BidStatus.GoiThauTam.code && this.dataObject.isMPO) {
      this.notifyService.showloading()
      this.apiService.put(this.apiService.BID.SEND_MPOLEADER_CHECK_BID(this.dataObject.id), {}).then(() => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
    }
  }

  async acceptBid() {
    if (this.dataObject.id && this.dataObject.status == enumData.BidStatus.ChoDuyetGoiThauTam.code && this.dataObject.isMPOLeader) {
      this.notifyService.showloading()
      this.apiService.put(this.apiService.BID.MPOLEADER_ACCEPT_BID(this.dataObject.id), {}).then(() => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
    }
  }

  async acceptBidQuick() {
    if (this.dataObject.id && this.dataObject.status == enumData.BidStatus.ChoDuyetGoiThauTam.code && this.dataObject.isMPOLeader) {
      this.notifyService.showloading()
      this.apiService.put(this.apiService.BID.MPOLEADER_ACCEPT_BID_QUICK(this.dataObject.id), {}).then(() => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
    }
  }

  async rejectBid() {
    if (this.dataObject.id && this.dataObject.status == enumData.BidStatus.ChoDuyetGoiThauTam.code && this.dataObject.isMPOLeader) {
      this.notifyService.showloading()
      this.apiService.put(this.apiService.BID.MPOLEADER_REJECT_BID(this.dataObject.id), {}).then(() => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
    }
  }

  deleteBid() {
    if (
      this.dataObject.id &&
      (this.dataObject.status == enumData.BidStatus.GoiThauTam.code || this.dataObject.status == enumData.BidStatus.ChoDuyetGoiThauTam.code) &&
      this.dataObject.isMPO
    ) {
      this.notifyService.showloading()
      this.apiService.put(this.apiService.BID.DELETE_BID(this.dataObject.id), {}).then(() => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
    }
  }
}
