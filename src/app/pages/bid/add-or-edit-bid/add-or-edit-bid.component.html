<form nz-form [formGroup]="validateForm" *ngIf="dataObject.isMPO">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-collapse nzBordered="false">
      <nz-collapse-panel [nzHeader]="language_key?.GENERAL_INFO || 'Thông Tin Chung'" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="companyInvite" nzRequired class="text-left"> Công ty mời thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn công ty mời thầu!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn công ty mời thầu"
                  [(ngModel)]="dataObject.companyInvite"
                  formControlName="companyInvite"
                  id="companyInvite"
                >
                  <nz-option *ngFor="let item of dataCompanyInvite" [nzLabel]="item.name" [nzValue]="item.name"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="addressSubmit" class="text-left"> Địa chỉ nộp hồ sơ thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn địa chỉ nộp hồ sơ thầu!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn địa chỉ nộp hồ sơ thầu"
                  [(ngModel)]="dataObject.addressSubmit"
                  formControlName="addressSubmit"
                  id="addressSubmit"
                >
                  <nz-option *ngFor="let item of dataAddressSubmit" [nzLabel]="item.name" [nzValue]="item.name"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="listAddress" class="text-left"> Các địa điểm thực hiện gói thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập các địa điểm thực hiện gói thầu (1-250 kí tự)!">
                <input nz-input formControlName="listAddress" id="listAddress" [(ngModel)]="dataObject.listAddress" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="anotherRoleIds" nzRequired class="text-left"> Các thành viên hội đồng xét thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thành viên hội đồng xét thầu!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzMode="multiple"
                  nzPlaceHolder="Chọn thành viên hội đồng xét thầu"
                  [(ngModel)]="dataObject.anotherRoleIds"
                  (ngModelChange)="changeEmployee()"
                  formControlName="anotherRoleIds"
                  id="anotherRoleIds"
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="otherRoleIds" class="text-left"> Các thành viên khác </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thành viên khác!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzMode="multiple"
                  nzPlaceHolder="Chọn thành viên khác"
                  [(ngModel)]="dataObject.otherRoleIds"
                  (ngModelChange)="changeEmployee()"
                  formControlName="otherRoleIds"
                  id="otherRoleIds"
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse nzBordered="false" class="mt-2">
      <nz-collapse-panel nzHeader="Thông tin gói thầu" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row *ngIf="data && data.id">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" class="text-left">PR</nz-form-label>
              <nz-form-control nzSpan="8">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  (ngModelChange)="dataPrChange($event)"
                  [(ngModel)]="dataObject.prId"
                  formControlName="prId"
                  nzPlaceHolder="Chọn PR"
                >
                  <nz-option *ngFor="let item of dataPr" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="24">
            <nz-form-label nzSpan="24" nzFor="serviceChoose" nzRequired class="text-left"> LVMH </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn LVMH!">
              <nz-cascader
                nzPlaceHolder="Chọn LVMH"
                [(ngModel)]="dataObject.serviceId"
                id="serviceId"
                formControlName="serviceId"
                [nzLoadData]="loadDataService"
              >
              </nz-cascader>
            </nz-form-control>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8" *ngIf="!data?.id">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" class="text-left">PR </nz-form-label>
              <nz-form-control>
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  (ngModelChange)="dataPrChange($event)"
                  [(ngModel)]="dataObject.prId"
                  formControlName="prId"
                  nzPlaceHolder="Chọn PR"
                >
                  <nz-option *ngFor="let item of dataPr" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="24">
            <nz-form-label nzSpan="24" nzFor="serviceChoose" nzRequired class="text-left"> LVMH </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn LVMH!">
              <nz-cascader
                nzPlaceHolder="Chọn LVMH"
                [(ngModel)]="dataObject.serviceId"
                id="serviceId"
                formControlName="serviceId"
                [nzLoadData]="loadDataService"
              >
              </nz-cascader>
            </nz-form-control>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8" class="mt-2">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="name" nzRequired class="text-left"> Tên gói thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên gói thầu (1-250 kí tự)!">
                <input nz-input formControlName="name" id="name" [(ngModel)]="dataObject.name" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="bidTypeId" nzRequired class="text-left"> Hình thức đấu thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn hình thức đấu thầu!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn hình thức đấu thầu"
                  [(ngModel)]="dataObject.bidTypeId"
                  formControlName="bidTypeId"
                  id="bidTypeId"
                >
                  <nz-option *ngFor="let item of dataBidType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="timeserving" nzRequired class="text-left"> Hiệu lực hợp đồng (tháng) </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập hiệu lực hợp đồng (tháng)!">
                <input
                  class="input-rtl"
                  nz-input
                  formControlName="timeserving"
                  id="timeserving"
                  [(ngModel)]="dataObject.timeserving"
                  type="number"
                  min="0"
                />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <hr class="dash" />
        <!-- Thời hạn -->
        <nz-row nzGutter="8">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label
                nzSpan="24"
                nzFor="timeTechDate"
                nzRequired
                class="text-left h40"
                nz-tooltip
                nzTooltipTitle="Sau thời điểm hiện tại và trước ngày (3)"
              >
                1.Thời hạn thiết lập các yêu cầu kỹ thuật
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thời hạn thiết lập các yêu cầu kỹ thuật!">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy HH:mm"
                  formControlName="timeTechDate"
                  id="timeTechDate"
                  [(ngModel)]="dataObject.timeTechDate"
                  [nzDisabledDate]="disabledDate"
                  [nzShowTime]="{ nzDefaultOpenValue: today }"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label
                nzSpan="24"
                nzFor="timePriceDate"
                nzRequired
                class="text-left h40"
                nz-tooltip
                nzTooltipTitle="Sau thời điểm hiện tại và trước ngày (3)"
              >
                2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thời hạn thiết lập các hạng mục báo giá, cơ cấu giá và điều kiện thương mại!">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy HH:mm"
                  formControlName="timePriceDate"
                  id="timePriceDate"
                  [(ngModel)]="dataObject.timePriceDate"
                  [nzDisabledDate]="disabledDate"
                  [nzShowTime]="{ nzDefaultOpenValue: today }"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label
                nzSpan="24"
                nzFor="acceptEndDate"
                nzRequired
                class="text-left h40"
                nz-tooltip
                nzTooltipTitle="Sau ngày (1, 2) và trước ngày (4)"
              >
                3.Ngày hết hạn xác nhận tham gia đấu thầu
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn ngày hết hạn xác nhận tham gia đấu thầu!">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy HH:mm"
                  formControlName="acceptEndDate"
                  id="acceptEndDate"
                  [(ngModel)]="dataObject.acceptEndDate"
                  [nzDisabledDate]="disabledDate"
                  [nzShowTime]="{ nzDefaultOpenValue: today }"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
        <nz-row nzGutter="8">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label
                nzSpan="24"
                nzFor="submitEndDate"
                nzRequired
                class="text-left h40"
                nz-tooltip
                nzTooltipTitle="Sau ngày (3) và trước ngày (5,6)"
              >
                4.Ngày hết hạn nộp hồ sơ thầu
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn ngày hết hạn nộp hồ sơ thầu!">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy HH:mm"
                  formControlName="submitEndDate"
                  id="submitEndDate"
                  [(ngModel)]="dataObject.submitEndDate"
                  [nzDisabledDate]="disabledDate"
                  [nzShowTime]="{ nzDefaultOpenValue: today }"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="timeCheckTechDate" nzRequired class="text-left h40" nz-tooltip nzTooltipTitle="Sau ngày (4)">
                5.Thời hạn đánh giá các yêu cầu kỹ thuật
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thời hạn đánh giá các yêu cầu kỹ thuật!">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy HH:mm"
                  formControlName="timeCheckTechDate"
                  id="timeCheckTechDate"
                  [(ngModel)]="dataObject.timeCheckTechDate"
                  [nzDisabledDate]="disabledDate"
                  [nzShowTime]="{ nzDefaultOpenValue: today }"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="timeCheckPriceDate" nzRequired class="text-left h40" nz-tooltip nzTooltipTitle="Sau ngày (4)">
                6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại
              </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thời hạn đánh giá kết quả chào giá và điều kiện thương mại!">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy HH:mm"
                  formControlName="timeCheckPriceDate"
                  id="timeCheckPriceDate"
                  [(ngModel)]="dataObject.timeCheckPriceDate"
                  [nzDisabledDate]="disabledDate"
                  [nzShowTime]="{ nzDefaultOpenValue: today }"
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
        <!-- End Thời hạn -->

        <hr class="dash" />

        <nz-row nzGutter="8">
          <!-- Bảo lãnh dự thầu -->
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="masterBidGuaranteeId" class="text-left"> Hình thức bảo lãnh dự thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn hình thức bảo lãnh dự thầu!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn hình thức bảo lãnh dự thầu"
                  [(ngModel)]="dataObject.masterBidGuaranteeId"
                  formControlName="masterBidGuaranteeId"
                  id="masterBidGuaranteeId"
                >
                  <nz-option *ngFor="let item of dataMasterBidGuarantee" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="moneyGuarantee" class="text-left"> Số tiền bảo lãnh dự thầu (VNĐ) </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số tiền bảo lãnh dự thầu (VNĐ)!">
                <input nz-input currencyMask formControlName="moneyGuarantee" id="moneyGuarantee" [(ngModel)]="dataObject.moneyGuarantee" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="timeGuarantee" class="text-left"> Thời hạn bảo lãnh dự thầu (tháng) </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập thời hạn bảo lãnh dự thầu (tháng)!">
                <input
                  class="input-rtl"
                  nz-input
                  type="number"
                  formControlName="timeGuarantee"
                  id="timeGuarantee"
                  [(ngModel)]="dataObject.timeGuarantee"
                  min="0"
                />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <hr class="dash" />

        <nz-row nzGutter="8">
          <!-- Phân quyền -->
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="mpoId" nzRequired class="text-left"> Thành viên phụ trách mua hàng </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thành viên phụ trách mua hàng!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn thành viên phụ trách mua hàng"
                  [(ngModel)]="dataObject.mpoId"
                  (ngModelChange)="changeEmployee()"
                  formControlName="mpoId"
                  id="mpoId"
                  [nzDisabled]="true"
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="mpoLeadId" nzRequired class="text-left"> Người duyệt nội dung mua hàng </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người duyệt nội dung mua hàng!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn người duyệt nội dung mua hàng"
                  [(ngModel)]="dataObject.mpoLeadId"
                  (ngModelChange)="changeEmployee()"
                  formControlName="mpoLeadId"
                  id="mpoLeadId"
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="techId" nzRequired class="text-left"> Thành viên phụ trách yêu cầu kỹ thuật </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thành viên phụ trách yêu cầu kỹ thuật!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn thành viên phụ trách yêu cầu kỹ thuật"
                  [(ngModel)]="dataObject.techId"
                  (ngModelChange)="changeEmployee()"
                  formControlName="techId"
                  id="techId"
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="techLeadId" nzRequired class="text-left"> Người duyệt yêu cầu kỹ thuật </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người duyệt yêu cầu kỹ thuật!">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn người duyệt yêu cầu kỹ thuật"
                  [(ngModel)]="dataObject.techLeadId"
                  (ngModelChange)="changeEmployee()"
                  formControlName="techLeadId"
                  id="techLeadId"
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <!-- 3 File dòng 1 -->
        <nz-row nzGutter="8">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileDrawing" class="text-left"> Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ </nz-form-label>
              <nz-form-control nzSpan="24">
                <label for="fileDrawing" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input class="hidden" type="file" formControlName="fileDrawing" id="fileDrawing" (change)="handleFileInput($event, 'fileDrawing')" />
                <div class="tooltip" *ngIf="dataObject.fileDrawing?.length > 0">
                  <a href="{{ dataObject.fileDrawing }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileJD" class="text-left"> Phạm vi công việc </nz-form-label>
              <nz-form-control nzSpan="24">
                <label for="fileJD" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input class="hidden" type="file" formControlName="fileJD" id="fileJD" (change)="handleFileInput($event, 'fileJD')" />
                <div class="tooltip" *ngIf="dataObject.fileJD?.length > 0">
                  <a href="{{ dataObject.fileJD }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileKPI" class="text-left"> Tiêu chuẩn đánh giá KPI </nz-form-label>
              <nz-form-control nzSpan="24">
                <label for="fileKPI" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input class="hidden" type="file" formControlName="fileKPI" id="fileKPI" (change)="handleFileInput($event, 'fileKPI')" />
                <div class="tooltip" *ngIf="dataObject.fileKPI?.length > 0">
                  <a href="{{ dataObject.fileKPI }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <!-- 3 File dòng 2 -->
        <nz-row nzGutter="8">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileRule" class="text-left"> Các quy định về nội quy gói thầu </nz-form-label>
              <nz-form-control nzSpan="24">
                <label for="fileRule" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input class="hidden" type="file" formControlName="fileRule" id="fileRule" (change)="handleFileInput($event, 'fileRule')" />
                <div class="tooltip" *ngIf="dataObject.fileRule?.length > 0">
                  <a href="{{ dataObject.fileRule }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileDocument" class="text-left"> Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...) </nz-form-label>
              <nz-form-control nzSpan="24">
                <label for="fileDocument" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input
                  class="hidden"
                  type="file"
                  formControlName="fileDocument"
                  id="fileDocument"
                  (change)="handleFileInput($event, 'fileDocument')"
                />
                <div class="tooltip" *ngIf="dataObject.fileDocument?.length > 0">
                  <a href="{{ dataObject.fileDocument }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="fileAnother" class="text-left"> Khác </nz-form-label>
              <nz-form-control nzSpan="24">
                <label for="fileAnother" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                <input class="hidden" type="file" formControlName="fileAnother" id="fileAnother" (change)="handleFileInput($event, 'fileAnother')" />
                <div class="tooltip" *ngIf="dataObject.fileAnother?.length > 0">
                  <a href="{{ dataObject.fileAnother }}" target="_blank"> Xem file </a>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="serviceInvite" nzRequired class="text-left"> Mô tả nội dung mời thầu </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mô tả nội dung mời thầu (1-250 kí tự)!">
                <textarea nz-input formControlName="serviceInvite" id="serviceInvite" [(ngModel)]="dataObject.serviceInvite" rows="5" auto></textarea>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.isShowHomePage" formControlName="isShowHomePage" id="isShowHomePage">
                  Cho phép hiển thị thông tin mời thầu ở trang tin đấu thầu
                </label>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.isSendEmailInviteBid" formControlName="isSendEmailInviteBid" id="isSendEmailInviteBid">
                  Gửi thông báo mời thầu cho NCC
                </label>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.hiddenScore" formControlName="hiddenScore" id="hiddenScore">Ẩn cột điểm hệ thống </label>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.isSkipEnd" formControlName="isSkipEnd" id="isSkipEnd">Không duyệt kết thúc thầu</label>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.isNotImportFromAdmin" formControlName="isNotImportFromAdmin" id="isNotImportFromAdmin"
                  >Không nhập hồ sơ ở trang admin</label
                >
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.watchProfile" formControlName="watchProfile" id="watchProfile"
                  >Không xe hồ sơ dự thầu trước khi mở thầu</label
                >
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <!-- <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-control nzSpan="24">
                <label nz-checkbox [(ngModel)]="dataObject.isAutoBid" formControlName="isAutoBid" id="isAutoBid">
                  Tự động chọn NCC thắng thầu và kết thúc thầu
                </label>
              </nz-form-control>
            </nz-form-item>
          </nz-col> -->
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse nzBordered="false" class="mt-2">
      <nz-collapse-panel [nzHeader]="headerListItem" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row nzGutter="8" *ngIf="!dataObject.prId">
          <nz-col nzSpan="16">
            <nz-form-label nzSpan="24" nzFor="serviceChoose" nzRequired class="text-left"> Item </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Item!">
              <!-- <nz-cascader
                nzPlaceHolder="Chọn Item"
                [(ngModel)]="dataItem.serviceChoose"
                [ngModelOptions]="{ standalone: true }"
                id="serviceChoose"
                (ngModelChange)="onChangeService()"
                [nzLoadData]="loadDataService"
              >
              </nz-cascader> -->

              <nz-select
                nzShowSearch
                nzAllowClear
                [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn Item'"
                [(ngModel)]="dataItem.serviceChoose"
                name="serviceChoose"
                id="serviceChoose"
                [ngModelOptions]="{ standalone: true }"
                (ngModelChange)="onChangeService()"
              >
                <nz-option *ngFor="let item of this.dataService" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-col>
          <nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzRequired class="text-left"> Số lượng </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số lượng!">
                <input
                  class="input-rtl"
                  nz-input
                  [(ngModel)]="dataItem.quantityItem"
                  [ngModelOptions]="{ standalone: true }"
                  type="number"
                  min="0"
                  autocomplete="off"
                />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="24" class="text-center">
            <button
              nz-button
              (click)="onAddItem()"
              [disabled]="!dataItem.serviceId || dataItem.quantityItem == null || dataItem.quantityItem <= 0"
              nzType="primary"
            >
              <span nz-icon nzType="plus"></span>Thêm Item
            </button>
          </nz-col>
        </nz-row>
        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <button nz-button (click)="onDeleteItemChoose()" [disabled]="dataObject.listItem?.length == 0" nzType="primary" nzDanger class="mr-2">
              <span nz-icon nzType="delete"></span>Xóa dòng đã chọn
            </button>
            <button nz-button (click)="onKeepItemChoose()" [disabled]="dataObject.listItem?.length == 0" nzType="primary" class="mr-2">
              <span nz-icon nzType="check-square"></span>Giữ lại dòng đã chọn
            </button>
            <button nz-button (click)="onRefreshItem()" *ngIf="dataObject.prId" nzType="primary">
              <span nz-icon nzType="reload" nzTheme="outline"></span>Làm mới
            </button>
          </nz-col>
          <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" [nzData]="['']" [nzScroll]="{ y: '400px' }" class="mt-2" nzBordered>
            <thead>
              <tr>
                <th nzWidth="50px">
                  <label nz-checkbox [(ngModel)]="isChooseAll" (ngModelChange)="isChangeChooseAll()" [ngModelOptions]="{ standalone: true }"></label>
                </th>
                <th nzWidth="80px">STT</th>
                <th *ngIf="dataObject.prId">Tên LVKD</th>
                <th *ngIf="!dataObject.prId">Tên hàng hóa</th>
                <th>Số lượng cần đặt</th>
                <!-- <th>Số lượng tạo thầu</th> -->
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let dataItem of dataObject.listItem; let i = index">
                <td>
                  <label nz-checkbox [(ngModel)]="dataItem.isChoose" [ngModelOptions]="{ standalone: true }"></label>
                </td>
                <td>{{ i + 1 }}</td>
                <td>{{ dataItem.itemName }}</td>
                <!-- <td class="text-right" *ngIf="dataObject.prId">
                  {{ dataItem.quantity - dataItem.quantityBid - dataItem.quantityCreatedBid + dataItem.quantityItem
                  | number }}
                </td> -->
                <td class="text-right">
                  <input
                    nz-input
                    currencyMask
                    [(ngModel)]="dataItem.quantityCreatedBid"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="dataObject.isChangeItem = true"
                    autocomplete="off"
                  />
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button
        nz-button
        *ngIf="!dataObject.id && authenticationService.checkPermission([enumRole], action.Create.code)"
        [disabled]="!validateForm.valid"
        nzType="primary"
        class="mr-2"
        (click)="submitForm()"
      >
        Tạo gói thầu
      </button>
      <button
        nz-button
        *ngIf="dataObject.id && dataObject.isAllowEditBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        [disabled]="!validateForm.valid"
        nzType="primary"
        class="mr-2"
        (click)="submitForm()"
      >
        Lưu thông tin
      </button>
      <button
        *ngIf="dataObject.isShowDeleteBid && authenticationService.checkPermission([enumRole], action.Delete.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn xóa gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="deleteBid()"
        nz-button
        nzDanger
        class="mr-2"
      >
        Xóa gói thầu
      </button>
      <button
        nz-button
        *ngIf="dataObject.isShowSendMPOLeadAccept && authenticationService.checkPermission([enumRole], action.Update.code)"
        class="ant-btn-blue mr-2"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn gửi yêu cầu phê duyệt gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="sendMPOLeaderCheckBid()"
      >
        Gửi yêu cầu phê duyệt
      </button>
      <button
        *ngIf="dataObject.isShowAcceptBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn duyệt gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="acceptBid()"
        nz-button
        class="ant-btn-blue mr-2"
      >
        Duyệt
      </button>
      <button
        *ngIf="dataObject.isShowAcceptBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn chào giá cạnh tranh cho gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="acceptBidQuick()"
        nz-button
        class="ant-btn-orange mr-2"
      >
        Chào giá cạnh tranh
      </button>
      <button
        *ngIf="dataObject.isShowAcceptBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn từ chối gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="rejectBid()"
        nz-button
        nzDanger
        class="mr-2"
      >
        Yêu cầu kiểm tra lại
      </button>
      <button
        nz-button
        *ngIf="dataObject.isAllowPrintBid && authenticationService.checkPermission([enumRole], action.Print.code)"
        nzType="dashed"
        (click)="onPrintBid()"
      >
        In thông tin gói thầu
      </button>
    </nz-col>
  </nz-row>
</form>

<div *ngIf="!dataObject.isMPO && dataObject.isMPOLeader">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitleView | uppercase }}
    </nz-col>
  </nz-row>
  <nz-row matDialogContent>
    <app-bid-basic-detail [bidId]="dataObject.id"></app-bid-basic-detail>
  </nz-row>
  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button
        *ngIf="dataObject.isShowAcceptBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn duyệt gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="acceptBid()"
        nz-button
        class="ant-btn-blue mr-2"
      >
        Duyệt
      </button>
      <button
        *ngIf="dataObject.isShowAcceptBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn chào giá cạnh tranh cho gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="acceptBidQuick()"
        nz-button
        class="ant-btn-orange mr-2"
      >
        Chào giá cạnh tranh
      </button>
      <button
        *ngIf="dataObject.isShowAcceptBid && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn từ chối gói thầu tạm?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="rejectBid()"
        nz-button
        nzDanger
        class="mr-2"
      >
        Yêu cầu kiểm tra lại
      </button>
      <button
        nz-button
        *ngIf="dataObject.isAllowPrintBid && authenticationService.checkPermission([enumRole], action.Print.code)"
        nzType="dashed"
        (click)="onPrintBid()"
      >
        In thông tin gói thầu
      </button>
    </nz-col>
  </nz-row>
</div>
