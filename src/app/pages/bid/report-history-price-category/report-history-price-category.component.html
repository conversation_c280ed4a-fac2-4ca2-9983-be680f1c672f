<nz-collapse nzBordered="false">
    <nz-collapse-panel nzHeader="Bộ tìm kiếm dữ liệu báo cáo" [(nzActive)]="isCollapseFilter">
        <nz-row nzGutter="8">
            <nz-col nzSpan="8">
                <input nz-input [(ngModel)]="dataSearch.category" placeholder="Tìm theo tên hạng mục chào giá" />
            </nz-col>
            <nz-col nzSpan="8">
                <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
                    [nzLoadData]="loadDataService">
                </nz-cascader>
            </nz-col>
            <nz-col nzSpan="8">
                <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo tên gó<PERSON> thầu" />
            </nz-col>
        </nz-row>

        <nz-row class="mt-2" nzGutter="12">
            <nz-col nzSpan="8">
                <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom"
                    nzPlaceHolder="Từ ngày đăng tải">
                </nz-date-picker>
            </nz-col>
            <nz-col nzSpan="8">
                <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
                </nz-date-picker>
            </nz-col>
        </nz-row>

        <nz-row class="mt-3" nzGutter="8">
            <nz-col nzSpan="24" class="text-center">
                <button nz-button (click)="searchData(true)" class="mr-2">
                    <span nz-icon nzType="search"></span>
                    {{ language_key?.SEARCH || 'Tìm kiếm' }}
                </button>
                <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)"
                    (click)="clickExportExcel()">
                    <span nz-icon nzType="download"></span>Xuất excel
                </button>
            </nz-col>
        </nz-row>
    </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable id="report-table" [nzData]="listOfData"
        [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
        <thead>
            <tr class="text-nowrap">
                <th>Tên hạng mục</th>
                <th>Mã TBMT</th>
                <th>Tên gói thầu</th>
                <th>Item</th>
                <th>{{ language_key?.INTERPRISE || 'Doanh nghiệp' }}</th>
                <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th>{{ language_key?.PRICE || 'Đơn giá' }}</th>
                <th>Giá</th>
                <th>Ngày chào giá</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of ajaxTable.data">
                <td (click)="clickView(data)">{{ data.name }}</td>
                <td (click)="clickView(data)">{{ data.bidCode }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ data.bidName }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ data.itemName }}</td>
                <td class="mw-25" (click)="clickView(data)">{{ '(' + data.supplierCode + ') ' + data.supplierName }}
                </td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.number | number }}</td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.unitPrice | number }}</td>
                <td class="text-nowrap text-right" (click)="clickView(data)">{{ data.price | number }}</td>
                <td class="text-nowrap" (click)="clickView(data)">{{ data.dateSubmitPrice | date: 'dd/MM/yyyy HH:mm' }}
                </td>
            </tr>
        </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
        {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
</nz-row>