<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tìm kiếm thông tin gói thầu" [(nzActive)]="isCollapseFilter">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
          [nzLoadData]="loadDataService">
        </nz-cascader>
      </nz-col>
      <nz-col nzSpan="6">
        <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo mã hoặc tên gói thầu" />
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Từ ngày đăng tải">
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="12">
        <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
          [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
          <nz-option *ngFor="let item of listBidStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2 text-center">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)" class="mr-2">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
          (click)="clickExportExcel()">
          <span nz-icon nzType="download"></span>Xuất excel
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" id="report-table" [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Mã TBMT</th>
        <th>Tên gói thầu</th>
        <th>Phụ trách kỹ thuật</th>
        <th>Phụ trách mua hàng</th>
        <th>Thời gian đăng tải</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData" [ngClass]="{ 'highlight-ape': data.isHighlight }">
        <td (click)="clickView(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.name }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.techName }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.mpoName }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.publicDate | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
        <td class="text-nowrap" (click)="clickView(data)">{{ data.statusName }}</td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>