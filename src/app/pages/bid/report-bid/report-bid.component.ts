import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { BidDetailComponent } from '../bid-detail/bid-detail.component'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './report-bid.component.html' })
export class ReportBidComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  listBidStatus = [
    enumData.BidStatus.DangNhanBaoGia,
    enumData.BidStatus.DangDanhGia,
    enumData.BidStatus.DangDuyetDanhGia,
    enumData.BidStatus.HoanTatDanhGia,
    enumData.BidStatus.HoanTat,
  ]
  // dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  bidStatus = enumData.BidStatus
  listOfData: any[] = []
  isCollapseFilter = true
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.status && this.dataSearch.status.length > 0) {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }

    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }

    this.loading = true
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.REPORT.GET_REPORT_BID, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        for (const item of this.listOfData) {
          item.isShowPrint = item.isMPO || item.isMPOLeader
        }
      }
    })
  }

  clickView(object: any) {
    this.dialog.open(BidDetailComponent, { disableClose: false, data: object })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  clickExportExcel() {
    const tbl = document.getElementById('report-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Báo cáo gói thầu.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
