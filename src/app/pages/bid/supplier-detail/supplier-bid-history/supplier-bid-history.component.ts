import { Component, OnInit, Input } from '@angular/core'
import { ApiService } from '../../../../services'
import { enumData } from '../../../../core'

@Component({
  selector: 'app-supplier-bid-history',
  templateUrl: './supplier-bid-history.component.html',
})
export class SupplierBidHistoryComponent implements OnInit {
  pageSize = enumData.Page.pageSize
  pageIndex = enumData.Page.pageIndex
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  @Input()
  public supplierId!: string
  constructor(private apiService: ApiService) {}

  ngOnInit() {
    this.searchData()
  }

  searchData() {
    if (!this.supplierId) return

    this.loading = true
    const dataSearch: any = {
      where: { supplierId: this.supplierId },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.SUPPLIER.PAGINATION_BID_HISTORY, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.listOfData = data[0]
        this.total = data[1]
      }
    })
  }
}
