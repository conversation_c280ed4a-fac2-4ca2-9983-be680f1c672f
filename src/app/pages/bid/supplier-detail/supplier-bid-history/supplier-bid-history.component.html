<nz-row>
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Mã gói thầu</th>
        <th>Tên gói thầu</th>
        <th>Trạng thái gói thầu</th>
        <th>Trạng thái đấu thầu</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25">{{ data.bidCode }}</td>
        <td class="mw-25">{{ data.bidName }}</td>
        <td class="mw-25">{{ data.bidStatus }}</td>
        <td class="mw-25">{{ data.statusName }}</td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData()" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>