<nz-row *ngIf="dataCapacity">
  <nz-col nzSpan="24">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
      (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span> Xuất excel
    </button>
  </nz-col>

  <nz-table nz-col nzSpan="24" class="mt-2" id='capacity-table' #expandTable [nzData]="dataCapacity"
    [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Tiêu chí</th>
        <th>Tỉ trọng(%)</th>
        <th>Nội dung mới đăng ký/điều chỉnh</th>
        <th>Nội dung trước khi điều chỉnh</th>
        <th>Điể<PERSON> năng lực ( {{ sum | number: '1.0-2' }})</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let data of expandTable.data">
        <tr>
          <td class="mw-25">{{ data.name }}</td>
          <td class="text-right">{{data.percent > 0 ? (data.percent | number: '1.0-2') : ''}}</td>
          <td>
            <div *ngIf="!data.isChangeByYear; else changeByYear1">
              <span *ngIf="data.type === dataType.String.code">{{ data.value }}</span>
              <span class="text-right" *ngIf="data.type === dataType.Number.code">{{ data.value | number }}</span>
              <span *ngIf="data.type === dataType.Date.code">{{ data.value | date: 'dd/MM/yyyy' }}</span>
              <span *ngIf="data.type === dataType.List.code">
                {{ coreService.convertTypeList(data.__supplierCapacityListDetails__) }}
              </span>
              <span *ngIf="data.type === dataType.File.code && data.value">
                <a href="{{ data.value }}" target="_blank">
                  <span nz-icon nzType="file-text"></span> Xem file đính kèm
                </a>
              </span>
            </div>
            <ng-template #changeByYear1>
              <span nz-popover [nzPopoverContent]="contentTemplate">
                Chi tiết
              </span>
              <ng-template #contentTemplate>
                <div *ngFor="let itemYear of data.__supplierCapacityYearValues__">
                  <p *ngIf="data.type === dataType.String.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value }}
                  </p>
                  <p class="text-right" *ngIf="data.type === dataType.Number.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                  </p>
                  <p *ngIf="data.type === dataType.File.code && itemYear.value">
                    <a href="{{ itemYear.value }}" target="_blank">
                      Năm {{ itemYear.year }} -
                      <span nz-icon nzType="file-text"></span> Xem file đính kèm
                    </a>
                  </p>
                </div>
              </ng-template>
            </ng-template>
          </td>
          <td>
            <div *ngIf="!data.isChangeByYear; else changeByYear2">
              <span *ngIf="data.type === dataType.String.code">{{ getHistory(data) }}</span>
              <span class="text-right" *ngIf="data.type === dataType.Number.code">{{ getHistory(data) | number }}</span>
              <span *ngIf="data.type === dataType.Date.code">{{ getHistory(data) | date: 'dd/MM/yyyy' }}</span>
              <span *ngIf="data.type === dataType.List.code">
                {{ getHistory(data) }}
              </span>
              <span *ngIf="data.type === dataType.File.code">
                <a *ngIf="getHistory(data) !== ''" href="{{ getHistory(data) }}" target="_blank">
                  <span nz-icon nzType="file-text"></span> Xem file đính kèm
                </a>
              </span>
            </div>
            <ng-template #changeByYear2>
              <div *ngIf="getYearHistory(data).length > 0">
                <span nz-popover [nzPopoverContent]="contentTemplate">
                  Chi tiết
                </span>
                <ng-template #contentTemplate>
                  <div *ngFor="let itemYear of getYearHistory(data)">
                    <p *ngIf="data.type === dataType.String.code">
                      Năm {{ itemYear.year }} - {{ itemYear.value }}
                    </p>
                    <p class="text-right" *ngIf="data.type === dataType.Number.code">
                      Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                    </p>
                    <p *ngIf="data.type === dataType.File.code && itemYear.value">
                      <a href="{{ itemYear.value }}" target="_blank">
                        Năm {{ itemYear.year }} -
                        <span nz-icon nzType="file-text"></span> Xem file đính kèm
                      </a>
                    </p>
                  </div>
                </ng-template>
              </div>
            </ng-template>
          </td>
          <td class="text-right">
            <span *ngIf="data.percentRule || data.type === dataType.List.code || data.__childs__.length > 0">
              {{ data.score | number: '1.0-2' }}
            </span>
          </td>
        </tr>
        <ng-container>
          <tr *ngFor="let item of data.__childs__">
            <td [nzIndentSize]="10">{{ item.name }}</td>
            <td class="text-right">{{item.percent > 0 ? (item.percent | number: '1.0-2') : ''}}</td>
            <td>
              <div *ngIf="!item.isChangeByYear; else changeByYear1">
                <span *ngIf="item.type === dataType.String.code">{{ item.value }}</span>
                <span class="text-right" *ngIf="item.type === dataType.Number.code">{{ item.value | number }}</span>
                <span *ngIf="item.type === dataType.Date.code">{{ item.value | date: 'dd/MM/yyyy' }}</span>
                <span *ngIf="item.type === dataType.List.code">
                  {{ coreService.convertTypeList(item.__supplierCapacityListDetails__) }}
                </span>
                <span *ngIf="item.type === dataType.File.code && item.value">
                  <a href="{{ item.value }}" target="_blank">
                    <span nz-icon nzType="file-text"></span> Xem file đính kèm
                  </a>
                </span>
              </div>
              <ng-template #changeByYear1>
                <span nz-popover [nzPopoverContent]="contentTemplateC">
                  Chi tiết
                </span>
                <ng-template #contentTemplateC>
                  <div *ngFor="let itemYear of item.__supplierCapacityYearValues__">
                    <p *ngIf="item.type === dataType.String.code">
                      Năm {{ itemYear.year }} - {{ itemYear.value }}
                    </p>
                    <p class="text-right" *ngIf="item.type === dataType.Number.code">
                      Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                    </p>
                    <p *ngIf="item.type === dataType.File.code && itemYear.value">
                      <a href="{{ itemYear.value }}" target="_blank">
                        Năm {{ itemYear.year }} -
                        <span nz-icon nzType="file-text"></span> Xem file đính kèm
                      </a>
                    </p>
                  </div>
                </ng-template>
              </ng-template>
            </td>
            <td>
              <div *ngIf="!item.isChangeByYear; else changeByYear2">
                <span *ngIf="item.type === dataType.String.code">{{ getHistoryChild(data, item) }}</span>
                <span class="text-right" *ngIf="item.type === dataType.Number.code">{{ getHistoryChild(data, item) |
                  number }}</span>
                <span *ngIf="item.type === dataType.Date.code">{{
                  getHistoryChild(data, item) | date: 'dd/MM/yyyy'
                  }}</span>
                <div *ngIf="item.type === dataType.List.code">
                  <span>{{ getHistoryChild(data, item) }}</span>
                </div>
                <span *ngIf="item.type === dataType.File.code"><a *ngIf="getHistoryChild(data, item) !== ''"
                    href="{{ getHistoryChild(data, item) }}" target="_blank">
                    <span nz-icon nzType="file-text"></span> Xem file đính kèm
                  </a>
                </span>
              </div>
              <ng-template #changeByYear2>
                <div *ngIf="getYearHistoryChild(data, item).length > 0">
                  <span nz-popover [nzPopoverContent]="contentTemplateC">
                    Chi tiết
                  </span>
                  <ng-template #contentTemplateC>
                    <div *ngFor="let itemYear of getYearHistoryChild(data, item)">
                      <p *ngIf="item.type === dataType.String.code">
                        Năm {{ itemYear.year }} - {{ itemYear.value }}
                      </p>
                      <p class="text-right" *ngIf="item.type === dataType.Number.code">
                        Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                      </p>
                      <p *ngIf="item.type === dataType.File.code && itemYear.value">
                        <a href="{{ itemYear.value }}" target="_blank">
                          Năm {{ itemYear.year }} -
                          <span nz-icon nzType="file-text"></span> Xem file đính kèm
                        </a>
                      </p>
                    </div>
                  </ng-template>
                </div>
              </ng-template>
            </td>
            <td></td>
          </tr>
        </ng-container>
      </ng-container>
    </tbody>
  </nz-table>
</nz-row>