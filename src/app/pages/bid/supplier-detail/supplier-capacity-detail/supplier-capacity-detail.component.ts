import { Component, OnInit, Input } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService } from '../../../../services'
import * as moment from 'moment'
import * as XLSX from 'xlsx'

@Component({
  selector: 'app-supplier-capacity-detail',
  templateUrl: './supplier-capacity-detail.component.html',
})
export class SupplierCapacityDetailComponent implements OnInit {
  loading = false
  pageSize = enumData.Page.pageSizeMax
  dataCapacity: any
  dataHistory: any[] = []
  sum = 0
  @Input()
  public data: any
  dataType = enumData.DataType
  currentUser: any
  enumProject: any
  enumRole: any
  action: any
  constructor(private apiService: ApiService, public coreService: CoreService, public authenticationService: AuthenticationService) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    this.searchData()
    this.loadHistory()
  }

  searchData(reset = false) {
    this.loading = true
    this.apiService.get(this.apiService.SUPPLIER.GET_SUPPLIER_SERVICE_CAPACITY(this.data.supplierServiceId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataCapacity = this.coreService.dynamicSort(data, 'sort')
        const length = data.length
        for (let i = 0; i < length; i++) {
          const item = data[i]
          item.score = this.coreService.calScore(item)
          item.__childs__ = item.__childs__.sort((a: any, b: any) => a.score - b.score)
          this.sum += item.score
        }
      }
    })
  }

  loadHistory() {
    if (this.data.supplierServiceId) {
      this.loading = true
      this.apiService.get(this.apiService.SUPPLIER_REVIEWAL.GET_HISTORY_CAPACITIES(this.data.supplierServiceId), {}).then((data) => {
        if (data) {
          this.loading = false
          this.dataHistory = data.dataJson
        }
      })
    }
  }

  getHistory(obj: any) {
    let result = ''
    const findData = this.dataHistory.find((p) => p.id === obj.id)
    if (findData) {
      if (!obj.isChangeByYear) {
        if (obj.type === enumData.DataType.List.code) {
          const check1 = this.coreService.convertTypeList(obj.__supplierCapacityListDetails__)
          const check2 = this.coreService.convertTypeList(findData.__supplierCapacityListDetails__)
          if (check1 !== check2) {
            result = check2
          }
        } else {
          if (obj.value !== findData.value) {
            if (obj.type === enumData.DataType.List.code) {
              result = this.coreService.convertTypeList(findData.__supplierCapacityListDetails__)
            } else {
              result = findData.value
            }
          }
        }
      } else {
      }
    }
    return result
  }

  getHistoryChild(obj: any, child: any) {
    let result = ''
    const findData = this.dataHistory.find((p) => p.id === obj.id)
    if (findData && findData.__childs__) {
      const findDataChild = findData.__childs__.find((p: any) => p.id === child.id)
      if (findDataChild) {
        if (!child.isChangeByYear) {
          if (child.type === enumData.DataType.List.code) {
            const check1 = this.coreService.convertTypeList(child.__supplierCapacityListDetails__)
            const check2 = this.coreService.convertTypeList(findDataChild.__supplierCapacityListDetails__)
            if (check1 !== check2) {
              result = check2
            }
          } else {
            if (child.value !== findDataChild.value) {
              if (child.type === enumData.DataType.List.code) {
                result = this.coreService.convertTypeList(findDataChild.__supplierCapacityListDetails__)
              } else {
                result = findDataChild.value
              }
            }
          }
        }
      }
    }
    return result
  }

  getYearHistory(obj: any) {
    const findData = this.dataHistory.find((p) => p.id === obj.id)
    if (findData) {
      if (obj.isChangeByYear) {
        const check = this.checkDifferentArrayYear(obj.__supplierCapacityYearValues__, findData.__supplierCapacityYearValues__)
        if (check) {
          return findData.__supplierCapacityYearValues__
        }
      }
    }
    return []
  }

  getYearHistoryChild(obj: any, child: any) {
    const findData = this.dataHistory.find((p) => p.id === obj.id)
    if (findData && findData.__childs__) {
      const findDataChild = findData.__childs__.find((p: any) => p.id === child.id)
      if (findDataChild) {
        if (child.isChangeByYear) {
          const check = this.checkDifferentArrayYear(child.__supplierCapacityYearValues__, findDataChild.__supplierCapacityYearValues__)
          if (check) {
            return findDataChild.__supplierCapacityYearValues__
          }
        }
      }
    }
    return []
  }

  checkDifferentArrayYear(parent: string | any[], child: any[]) {
    if (parent.length !== child.length) {
      return true
    }
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < parent.length; i++) {
      const itemP = parent[i]
      const itemC = child.find((p: { year: any }) => p.year === itemP.year)
      if (!itemC) {
        return true
      } else if (itemP.value !== itemC.value) {
        return true
      }
    }
    return false
  }

  public compareObject(obj1: any, obj2: any) {
    for (const key in obj1) {
      if (!obj2[key]) {
        return false
      } else {
        if (obj1[key] !== obj2[key]) {
          return false
        }
      }
    }
    return true
  }

  clickExportExcel() {
    const tbl = document.getElementById('capacity-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Thông tin năng lực.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
