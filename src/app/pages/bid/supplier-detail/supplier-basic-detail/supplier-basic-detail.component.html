<nz-row>
  <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="['']" [(nzPageSize)]="pageSize" [nzLoading]="loading"
    [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Trường thông tin</th>
        <th>Nhà cung cấp đã đăng ký</th>
        <th *ngIf="isEnableBtnHistory">Lịch sử</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>{{ language_key?.INTERPRISE_CODE || 'Mã doanh nghiệp' }}</td>
        <td> {{ dataObject.code}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('code')}}</span>
        </td>
      </tr>

      <tr>
        <td>Tê<PERSON> ch<PERSON></td>
        <td>{{ dataObject.name}}</td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('name')}}</span>
        </td>
      </tr>

      <tr>
        <td>Tên giao dịch</td>
        <td> {{ dataObject.dealName}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('dealName')}}</span>
        </td>
      </tr>

      <tr>
        <td>Địa chỉ trụ sở</td>
        <td> {{ dataObject.address}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('address')}}</span>
        </td>
      </tr>

      <tr>
        <td>Địa chỉ giao dịch</td>
        <td> {{ dataObject.dealAddress}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('dealAddress')}}</span>
        </td>
      </tr>

      <tr>
        <td>Giấy phép ĐKKD</td>
        <td>
          <a *ngIf="dataObject.fileMST" href="{{dataObject.fileMST}}" target="_blank">
            <span nz-icon nzType="file-text"></span> Xem file đính kèm
          </a>
        </td>
        <td *ngIf="isEnableBtnHistory">
          <span *ngIf="getHistory('fileMST') !== ''" nz-typography nzType="warning">
            <a href="{{ getHistory('fileMST')}}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </span>
        </td>
      </tr>

      <tr>
        <td>Đại diện pháp luật</td>
        <td> {{ dataObject.represen}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('represen')}}</span>
        </td>
      </tr>

      <tr>
        <td>Tên giám đốc</td>
        <td> {{ dataObject.chief}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('chief')}}</span>
        </td>
      </tr>

      <tr>
        <td>Số tài khoản</td>
        <td> {{ dataObject.bankNumber}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('bankNumber')}}</span>
        </td>
      </tr>

      <tr>
        <td>Ngân hàng</td>
        <td> {{ dataObject.bankname}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('bankname')}}</span>
        </td>
      </tr>

      <tr>
        <td> {{ language_key?.BRANCH || 'Chi nhánh' }} </td>
        <td> {{ dataObject.bankBrand}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('bankBrand')}}</span>
        </td>
      </tr>

      <tr>
        <td>Thông báo mở TK</td>
        <td>
          <a *ngIf="dataObject.fileAccount" href="{{dataObject.fileAccount}}" target="_blank">
            <span nz-icon nzType="file-text"></span>
            {{dataObject.fileAccount ? 'Xem file đính kèm' : 'Không có'}}
          </a>
        </td>
        <td *ngIf="isEnableBtnHistory">
          <span *ngIf="getHistory('fileAccount') !== ''" nz-typography nzType="warning">
            <a href="{{ getHistory('fileAccount')}}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </span>
        </td>
      </tr>

      <tr>
        <td>Người liên hệ</td>
        <td> {{ dataObject.contactName}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('contactName')}}</span>
        </td>
      </tr>

      <tr>
        <td>Email</td>
        <td> {{ dataObject.email}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('email')}}</span>
        </td>
      </tr>

      <tr>
        <td>Điện thoại</td>
        <td> {{ dataObject.phone}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('phone')}}</span>
        </td>
      </tr>

      <tr>
        <td>Năm thành lập</td>
        <td> {{ dataObject.createYear}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('createYear')}}</span>
        </td>
      </tr>

      <tr>
        <td>Vốn điều lệ (tỷ đồng)</td>
        <td> {{ dataObject.capital}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('capital')}}</span>
        </td>
      </tr>

      <tr>
        <td>Tài sản cố định (tỷ đồng)</td>
        <td> {{ dataObject.assets}} </td>
        <td *ngIf="isEnableBtnHistory">
          <span nz-typography nzType="warning">{{ getHistory('assets')}}</span>
        </td>
      </tr>

      <tr>
        <td>HĐ mẫu/phiếu thu/biên lai</td>
        <td>
          <a *ngIf="dataObject.fileBill" href="{{dataObject.fileBill}}" target="_blank">
            <span nz-icon nzType="file-text"></span> Xem file đính kèm
          </a>
        </td>
        <td *ngIf="isEnableBtnHistory">
          <span *ngIf="getHistory('fileBill') !== ''" nz-typography nzType="warning">
            <a href="{{ getHistory('fileBill')}}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </span>
        </td>
      </tr>

      <tr>
        <td>Thông tin phát hành HĐ</td>
        <td>
          <a *ngIf="dataObject.fileInfoBill" href="{{dataObject.fileInfoBill ? dataObject.fileInfoBill : '#'}}"
            target="_blank">
            <span nz-icon nzType="file-text"></span>{{dataObject.fileInfoBill ? 'Xem file đính kèm' : 'Không có'}}
          </a>
        </td>
        <td *ngIf="isEnableBtnHistory">
          <span *ngIf="getHistory('fileInfoBill') !== ''" nz-typography nzType="warning">
            <a href="{{ getHistory('fileInfoBill')}}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </span>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>