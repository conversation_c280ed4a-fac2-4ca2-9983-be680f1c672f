import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-supplier-basic-detail',
  templateUrl: './supplier-basic-detail.component.html',
})
export class SupplierBasicDetailComponent implements OnInit {
  dataObject: any = {}
  dataHistory: any
  pageSize = enumData.Page.pageSizeMax
  loading = false
  isEnableBtnHistory = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  public supplierId!: string
  constructor(private apiService: ApiService, public coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.supplierId) return
    this.loadDetail()
    this.loadHistory()
  }

  loadDetail() {
    this.loading = true
    this.apiService.post(this.apiService.SUPPLIER.FIND_DETAIL, { id: this.supplierId }).then((data) => {
      this.loading = false
      this.dataObject = data
    })
  }

  loadHistory() {
    this.loading = true
    this.apiService.get(this.apiService.SUPPLIER_REVIEWAL.GET_HISTORY_LAW(this.supplierId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataHistory = data.dataJson
        this.checkIsEnableBtnHistory()
      }
    })
  }

  getHistory(param: any) {
    if (this.dataHistory && typeof this.dataHistory[param] !== 'undefined') {
      if (this.dataObject[param] !== this.dataHistory[param]) {
        return `${this.dataHistory[param]}`
      }
    }
    return ''
  }

  checkIsEnableBtnHistory() {
    this.isEnableBtnHistory = !this.compareObject(this.dataHistory, this.dataObject)
  }

  public compareObject(obj1: any, obj2: any) {
    for (const key in obj1) {
      if (!obj2[key]) return false
      if (obj1[key] !== obj2[key]) return false
    }
    return true
  }
}
