<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tìm kiếm thông tin gói thầu" [(nzActive)]="isCollapseFilter">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
          [nzLoadData]="loadDataService"> </nz-cascader>
      </nz-col>
      <nz-col nzSpan="6">
        <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo mã số hoặc tên gói thầu" />
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Từ ngày đăng tải">
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="12">
        <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
          [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
          <nz-option *ngFor="let item of listBidStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSpan="12">
        <label nz-checkbox [(ngModel)]="dataSearch.isGetBidNeedApprove" name="isGetBidNeedApprove"> Lấy gói thầu cần
          duyệt </label>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
      (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus-square"></span> Tạo mới gói thầu
    </button>
    <button nz-button (click)="checkButton()"
      *ngIf="authenticationService.checkPermission([enumRole], action.View.code)" class="mr-2">
      <span nz-icon nzType="copy"></span>
      {{ isShowCopyBid ? 'Tắt sao chép gói thầu' : 'Bật sao chép gói thầu' }}
    </button>
    <button class="mr-2" nz-button (click)="clickExportExcelTemplate()"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)">
      <span nz-icon nzType="download"></span>Xuất template excel
    </button>
    <input class="hidden" type="file" id="fileBid"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" (change)="clickImportExcel($event)"
      onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
    <label for="fileBid" class="lable-custom-file mr-2"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)">
      <span nz-icon nzType="upload"></span>
      {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
    </label>
    <button *ngIf="lstErrorImport?.length" nz-button (click)="isVisibleError = true" nzType="primary" nzDanger>
      <span nz-icon nzType="warning"></span>Danh sách lỗi import
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Mã TBMT</th>
        <th>Tên gói thầu</th>
        <th>Phụ trách kỹ thuật</th>
        <th>Phụ trách mua hàng</th>
        <th>Thời gian đăng tải</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th class="text-nowrap">{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of ajaxTable.data">
        <td (click)="clickView(item)">{{ item.code }}</td>
        <td class="mw-25" (click)="clickView(item)">{{ item.name }}</td>
        <td class="mw-25" (click)="clickView(item)">{{ item.techName }}</td>
        <td class="mw-25" (click)="clickView(item)">{{ item.mpoName }}</td>
        <td class="text-nowrap" (click)="clickView(item)">{{ item.publicDate | date : 'dd/MM/yyyy HH:mm:ss' }}</td>
        <td class="text-nowrap" (click)="clickView(item)">{{ item.statusName }}</td>
        <td class="text-nowrap">
          <button
            *ngIf="isShowCopyBid && item.isShowCopy && authenticationService.checkPermission([enumRole], action.View.code)"
            nz-tooltip nzTooltipTitle="Sao chép gói thầu" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn sao chép gói thầu này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickCopy(item)" class="mr-2" nz-button nzType="dashed">
            <span nz-icon nzType="copy"></span>
          </button>
          <button *ngIf="item.isShowEditBid && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Chỉnh sửa thông tin chung" (click)="clickEdit(item)" class="mr-2" nz-button
            nzType="dashed">
            <span nz-icon nzType="form"></span>
          </button>
          <button
            *ngIf="item.isShowEditSettingRate && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Chỉnh sửa thông tin chung sau duyệt" (click)="clickEditOther(item)" class="mr-2"
            nz-button nzType="dashed">
            <span nz-icon nzType="form"></span>
          </button>
          <button *ngIf="item.isShowDelete && authenticationService.checkPermission([enumRole], action.Delete.code)"
            nz-tooltip nzTooltipTitle="Hủy gói thầu" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn hủy gói thầu này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickRequestDelete(item)" class="mr-2" nz-button nzDanger>
            <span nz-icon nzType="delete"></span>
          </button>
          <button
            *ngIf="item.isShowApproveDelete && authenticationService.checkPermission([enumRole], action.Delete.code)"
            nz-tooltip nzTooltipTitle="Phê duyệt hủy gói thầu" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn phê duyệt hủy gói thầu này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickApproveDelete(item)" class="mr-2" nz-button nzType="primary" nzDanger>
            <span nz-icon nzType="delete"></span>
          </button>
          <button *ngIf="item.isShowBidTech && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Thiết lập yêu cầu kỹ thuật" (click)="bidTech(item)"
            class="mr-2 ant-btn-{{ item.techType }}" nz-button>
            <span nz-icon nzType="robot"></span>
          </button>
          <button *ngIf="item.isShowBidPrice && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Thiết lập các hạng mục chào giá, cơ cấu giá" (click)="bidPrice(item)"
            class="mr-2 ant-btn-{{ item.priceType }}" nz-button>
            <span nz-icon nzType="dollar"></span>
          </button>
          <button *ngIf="item.isShowBidTrade && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Thiết lập điều kiện thương mại" (click)="bidTrade(item)"
            class="mr-2 ant-btn-{{ item.tradeType }}" nz-button>
            <span nz-icon nzType="shopping"></span>
          </button>
          <button
            *ngIf="item.isShowChoseSupplier && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Thông tin mời thầu" (click)="chooseSupplier(item)"
            class="mr-2 ant-btn-{{ item.chooseSupplierType }}" nz-button>
            <span nz-icon nzType="home"></span>
          </button>
          <button
            *ngIf="item.isShowBiddingFromAdminToSupplier && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Nhập dữ liệu gói thầu" (click)="biddingFromAdminToSupplier(item)"
            class="mr-2 ant-btn-{{ item.chooseSupplierType }}" nz-button>
            <span nz-icon nzType="profile"></span>
          </button>
          <button *ngIf="item.isShowSendEmail && authenticationService.checkPermission([enumRole2], action.View.code)"
            nz-tooltip nzTooltipTitle="Gửi email nội bộ và NCC được mời tham gia thầu" (click)="sendEmailBid(item)"
            class="ant-btn-blue" nz-button>
            <span nz-icon nzType="mail"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Gửi email nội bộ và NCC được mời tham gia thầu"
  (nzOnCancel)="handleCancel()" [nzWidth]="'60vw'" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Nội dung email:</h4>
      <nz-col nzSpan="24" class="text-center">
        <textarea nz-input rows="5" auto placeholder="Nhập nội dung gửi"
          [(ngModel)]="dataEmail.emailContent"></textarea>
      </nz-col>
    </nz-row>
    <nz-row class="mt-3">
      <h4>Thành viên nội bộ:</h4>
      <nz-col nzSpan="24" class="text-center">
        <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataEmail.lstEmployeeId"
          nzPlaceHolder="Chọn nhân viên nội bộ">
          <nz-option *ngFor="let item of lstBidEmployee" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <nz-row class="mt-3">
      <h4>Nhà cung cấp:</h4>
      <nz-col nzSpan="24" class="text-center">
        <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataEmail.lstSupplierId"
          nzPlaceHolder="Chọn nhà cung cấp">
          <nz-option *ngFor="let item of lstSupplier" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button *ngIf="authenticationService.checkPermission([enumRole2], action.View.code)" nz-popconfirm
          nzPopconfirmTitle="Bạn có chắc muốn gửi email thông báo?" nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="onSendEmail()" class="ant-btn-blue">
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleError" [nzWidth]="'60vw'" nzTitle="Danh sách lỗi nhập excel"
  (nzOnCancel)="isVisibleError = false" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="lstErrorImport" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false"
        nzBordered>
        <thead>
          <tr>
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Nội dung lỗi</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of lstErrorImport">
            <td>{{ data.zenId }}</td>
            <td>{{ data.errorMessage }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>