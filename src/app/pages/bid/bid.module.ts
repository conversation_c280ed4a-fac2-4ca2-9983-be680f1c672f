import { NgModule } from '@angular/core'
import { BidRoutingModule } from './bid-routing.module'
import { BidComponent } from './bid.component'
import { SupplierServiceToExpertiseComponent } from './supplier-service-to-expertise/supplier-service-to-expertise.component'
import { CommonModule } from '@angular/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { CurrencyMaskModule, CURRENCY_MASK_CONFIG } from 'ng2-currency-mask'
// ------------ ANT -------------------
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NgxPrintModule } from 'ngx-print'
// ------------- Create Component ---------------------
import { SupplierCapacityComponent } from './supplier-capacity/supplier-capacity.component'
import { SupplierDetailComponent } from './supplier-detail/supplier-detail.component'
import { SupplierBasicDetailComponent } from './supplier-detail/supplier-basic-detail/supplier-basic-detail.component'
import { SupplierCapacityDetailComponent } from './supplier-detail/supplier-capacity-detail/supplier-capacity-detail.component'
import { SupplierCapacityCheckDetailComponent } from './supplier-expertise/supplier-expertise-detail-model/supplier-capacity-check-detail/supplier-capacity-check-detail.component'
import { SupplierBidHistoryComponent } from './supplier-detail/supplier-bid-history/supplier-bid-history.component'
import { AddOrEditBidComponent } from './add-or-edit-bid/add-or-edit-bid.component'
import { BidRateComponent } from './bid-rate/bid-rate.component'
import { BidDetailComponent } from './bid-detail/bid-detail.component'
import { BidBasicDetailComponent } from './bid-detail/bid-basic-detail/bid-basic-detail.component'
import { BidHistoryDetailComponent } from './bid-detail/bid-history-detail/bid-history-detail.component'
import { SupplierExpertiseComponent } from './supplier-expertise/supplier-expertise.component'
import { SupplierCapacityModalComponent } from './supplier-capacity/supplier-capacity-modal/supplier-capacity-modal.component'
import { MaterialModule } from '../../app.module'
import { SupplierExpertiseDetailComponent } from './supplier-expertise/supplier-expertise-detail-model/supplier-expertise-detail.component'
import { SupplierBasicCheckDetailComponent } from './supplier-expertise/supplier-expertise-detail-model/supplier-basic-check-detail/supplier-basic-check-detail.component'
import { BidTechComponent } from './bid-tech/bid-tech.component'
import { BidTechListDetailComponent } from './bid-tech/bid-tech-list-detail/bid-tech-list-detail.component'
import { BidTradeComponent } from './bid-trade/bid-trade.component'
import { BidTradeListDetailComponent } from './bid-trade/bid-trade-list-detail/bid-trade-list-detail.component'
import { BidPriceComponent } from './bid-price/bid-price.component'
import { BidPriceListDetailComponent } from './bid-price/bid-price-list-detail/bid-price-list-detail.component'
import { BidChooseSupplierComponent } from './bid-choose-supplier/bid-choose-supplier.component'
import { BidPrintComponent } from './bid-print/bid-print.component'
import { SupplierExpertisePrintComponent } from './supplier-expertise/supplier-expertise-print/supplier-expertise-print.component'
import { BidSupplierTechRateComponent } from './bid-rate/bid-rate-tech/bid-rate-tech.component'
import { BidSupplierTradeRateComponent } from './bid-rate/bid-rate-trade/bid-rate-trade.component'
import { BidRatePriceComponent } from './bid-rate/bid-rate-price/bid-rate-price.component'
import { BidRatePriceDetailComponent } from './bid-rate/bid-rate-price/bid-rate-price-detail/bid-rate-price-detail.component'
import { BidRateTechDetailComponent } from './bid-rate/bid-rate-tech/bid-rate-tech-detail/bid-rate-tech-detail.component'
import { BidDealComponent } from './bid-rate/bid-item/bid-deal/bid-deal.component'
import { BidRateTradeDetailComponent } from './bid-rate/bid-rate-trade/bid-rate-trade-detail/bid-rate-trade-detail.component'
import { BidAuctionComponent } from './bid-rate/bid-item/bid-auction/bid-auction.component'
import { BidSupplierDetailComponent } from './bid-supplier-detail/bid-supplier-detail.component'
import { BidCheckSupplierComponent } from './bid-check-supplier/bid-check-supplier.component'
import { BidSupplierPriceDetailComponent } from './bid-detail/bid-supplier-price-detail/bid-supplier-price-detail.component'
import { BidResultComponent } from './bid-detail/bid-result/bid-result.component'
import { BidResultTechComponent } from './bid-detail/bid-result/bid-result-tech/bid-result-tech.component'
import { BidResultTradeComponent } from './bid-detail/bid-result/bid-result-trade/bid-result-trade.component'
import { BidResultPriceComponent } from './bid-detail/bid-result/bid-result-price/bid-result-price.component'
import { BidResultDetailComponent } from './bid-detail/bid-result/bid-result-detail/bid-result-detail.component'
import { BidResultDealComponent } from './bid-detail/bid-result/bid-result-deal/bid-result-deal.component'
import { BidResultDealDetailComponent } from './bid-detail/bid-result/bid-result-deal-detail/bid-result-deal-detail.component'
import { BidResultDealDetailSupplierComponent } from './bid-detail/bid-result/bid-result-deal-detail-supplier/bid-result-deal-detail-supplier.component'
import { BidEvaluationComponent } from './bid-rate/bid-evaluation/bid-evaluation.component'
import { BidResultAuctionComponent } from './bid-detail/bid-result/bid-result-auction/bid-result-auction.component'
import { BidResultAuctionDetailComponent } from './bid-detail/bid-result/bid-result-auction-detail/bid-result-auction-detail.component'
import { BidResultAuctionSupplierComponent } from './bid-detail/bid-result/bid-result-auction-supplier/bid-result-auction-supplier.component'
import { BidSupplierTechDetailComponent } from './bid-detail/bid-supplier-tech-detail/bid-supplier-tech-detail.component'
import { BidSupplierTradeDetailComponent } from './bid-detail/bid-supplier-trade-detail/bid-supplier-trade-detail.component'
import { AddOrEditBidTechComponent } from './bid-tech/add-or-edit-bid-tech/add-or-edit-bid-tech.component'
import { AddOrEditBidTradeComponent } from './bid-trade/add-or-edit-bid-trade/add-or-edit-bid-trade.component'
import { AddOrEditBidPriceComponent } from './bid-price/add-or-edit-bid-price/add-or-edit-bid-price.component'
import { BidDetailTechComponent } from './bid-detail/bid-detail-tech/bid-detail-tech.component'
import { BidDetailPriceComponent } from './bid-detail/bid-detail-price/bid-detail-price.component'
import { BidDetailTradeComponent } from './bid-detail/bid-detail-trade/bid-detail-trade.component'
import { BidDetailTechListComponent } from './bid-detail/bid-detail-tech-list/bid-detail-tech-list.component'
import { BidDetailTradeListComponent } from './bid-detail/bid-detail-trade-list/bid-detail-trade-list.component'
import { BidDetailPriceListComponent } from './bid-detail/bid-detail-price-list/bid-detail-price-list.component'
import { BidInfoComponent } from './bid-info/bid-info.component'
import { BidResultCustomPriceComponent } from './bid-detail/bid-result/bid-result-custom-price/bid-result-custom-price.component'
import { SupplierCapacityCheckDetailPrintComponent } from './supplier-expertise/supplier-expertise-print/supplier-capacity-check-detail-print/supplier-capacity-check-detail-print.component'
import { SupplierBasicCheckDetailPrintComponent } from './supplier-expertise/supplier-expertise-print/supplier-basic-check-detail-print/supplier-basic-check-detail-print.component'
import { BidBasicDetailPrintComponent } from './bid-detail/bid-basic-detail-print/bid-basic-detail-print.component'
import { ReportSupplierComponent } from './report-supplier/report-supplier.component'
import { ReportExpertiseComponent } from './report-expertise/report-expertise.component'
import { ReportBidComponent } from './report-bid/report-bid.component'
import { BidPriceColComponent } from './bid-price/bid-price-col/bid-price-col.component'
import { BidSettingRateComponent } from './bid-setting-rate/bid-setting-rate.component'
import { BidRateReportComponent } from './bid-rate/bid-result-report/bid-result-report.component'
import { ReportResultComponent } from './bid-rate/bid-result-report/report-result/report-result.component'
import { ReportCapacityComponent } from './bid-rate/bid-result-report/report-capacity/report-capacity.component'
import { ReportTechComponent } from './bid-rate/bid-result-report/report-tech/report-tech.component'
import { ReportTradeComponent } from './bid-rate/bid-result-report/report-trade/report-trade.component'
import { ReportPriceComponent } from './bid-rate/bid-result-report/report-price/report-price.component'
import { SupplierManageComponent } from './supplier-manage/supplier-manage.component'
import { BidResetPriceComponent } from './bid-rate/bid-reset-price/bid-reset-price.component'
import { BidRankBySumPriceComponent } from './bid-rate/bid-rank-by-sum-price/bid-rank-by-sum-price.component'
import { BidRankByMinPriceComponent } from './bid-rate/bid-rank-by-min-price/bid-rank-by-min-price.component'
import { BidResultCapacityComponent } from './bid-detail/bid-result/bid-result-capacity/bid-result-capacity.component'
import { BidHistoryPriceComponent } from './bid-detail/bid-result/bid-history-price/bid-history-price.component'
import { ReportHistoryPriceSupplierComponent } from './report-history-price-supplier/report-history-price-supplier.component'
import { ReportHistoryPriceServiceComponent } from './report-history-price-service/report-history-price-service.component'
import { ReportHistoryPriceCategoryComponent } from './report-history-price-category/report-history-price-category.component'
import { AddOrEditSupplierComponent } from './supplier-manage/add-or-edit-supplier/add-or-edit-supplier.component'
import { SupplierServiceCapacityComponent } from './supplier-manage/supplier-service/supplier-service-capacity/supplier-service-capacity.component'
import { BiddingComponent } from './bidding/bidding.component'
import { BiddingTradeComponent } from './bidding/bidding-trade/bidding-trade.component'
import { BiddingTechComponent } from './bidding/bidding-tech/bidding-tech.component'
import { BiddingPriceComponent } from './bidding/bidding-price/bidding-price.component'
import { BiddingCustomPriceComponent } from './bidding/bidding-custom-price/bidding-custom-price.component'
import { BiddingPriceListDetailComponent } from './bidding/bidding-price-list-detail/bidding-price-list-detail.component'
import { AddBiddingPriceComponent } from './bidding/add-bidding-price/add-bidding-price.component'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCardModule } from 'ng-zorro-antd/card'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzStepsModule } from 'ng-zorro-antd/steps'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzDividerModule } from 'ng-zorro-antd/divider'
import { NzDrawerModule } from 'ng-zorro-antd/drawer'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzTypographyModule } from 'ng-zorro-antd/typography'
import { ReportHistoryBidSupplierComponent } from './report-history-bid-supplier/report-history-bid-supplier.component'
import { ReportHistoryBuyerComponent } from './report-history-buyer/report-history-buyer.component'
import { BidResultPrintComponent } from './bid-info/bid-result-print/bid-result-print.component'
import { ProtocolOpenBidComponent } from './bid-check-supplier/protocol-open-bid/protocol-open-bid.component'
import { ReportTotalComponent } from './bid-rate/bid-result-report/report-total/report-total.component'
import { BiddingAdminComponent } from './bidding-admin/bidding-admin.component'
import { CustomCurrencyMaskConfig } from '../setting/setting.module'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { BidItemComponent } from './bid-rate/bid-item/bid-item.component'
import { SupplierServiceComponent } from './supplier-manage/supplier-service/supplier-service.component'
import { AuctionComponent } from './auction/auction.component'
import { AuctionDetailComponent } from './auction/auction-detail/auction-detail.component'
import { AddOrEditAuctionComponent } from './auction/add-or-edit-auction/add-or-edit-auction.component'
import { AddSupplierAuctionComponent } from './auction/add-supplier-auction/add-supplier-auction.component'

@NgModule({
  imports: [
    CurrencyMaskModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BidRoutingModule,
    NzButtonModule,
    NzTableModule,
    NzDividerModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDescriptionsModule,
    NzListModule,
    NzDatePickerModule,
    NzStatisticModule,
    NzPaginationModule,
    NzCollapseModule,
    NzCascaderModule,
    NzPopoverModule,
    NzTypographyModule,
    MaterialModule,
    NzPopconfirmModule,
    NgxPrintModule,
    NzDrawerModule,
    NzCardModule,
    NzRadioModule,
    NzStepsModule,
    NzTagModule,
  ],
  declarations: [
    BidComponent,
    SupplierServiceToExpertiseComponent,
    SupplierCapacityComponent,
    SupplierDetailComponent,
    SupplierBasicDetailComponent,
    SupplierCapacityDetailComponent,
    SupplierCapacityCheckDetailComponent,
    SupplierCapacityCheckDetailPrintComponent,
    SupplierBidHistoryComponent,
    AddOrEditBidComponent,
    BidRateComponent,
    BidDetailComponent,
    BidBasicDetailComponent,
    BidHistoryDetailComponent,
    SupplierExpertiseComponent,
    SupplierCapacityModalComponent,
    SupplierExpertiseDetailComponent,
    SupplierBasicCheckDetailComponent,
    SupplierBasicCheckDetailPrintComponent,
    BidTechComponent,
    BidTechListDetailComponent,
    BidTradeComponent,
    BidTradeListDetailComponent,
    BidPriceComponent,
    BidPriceColComponent,
    BidPriceListDetailComponent,
    BidChooseSupplierComponent,
    BidPrintComponent,
    SupplierExpertisePrintComponent,
    BidSupplierTechRateComponent,
    BidSupplierTradeRateComponent,
    BidRatePriceComponent,
    BidRateTechDetailComponent,
    BidItemComponent,
    BidDealComponent,
    BidRateTradeDetailComponent,
    BidRatePriceDetailComponent,
    BidAuctionComponent,
    BidSupplierDetailComponent,
    BidCheckSupplierComponent,
    BidSupplierPriceDetailComponent,
    BidResultComponent,
    BidResultCapacityComponent,
    BidResultTechComponent,
    BidResultTradeComponent,
    BidResultPriceComponent,
    BidHistoryPriceComponent,
    BidResultDetailComponent,
    BidResultDealComponent,
    BidResultDealDetailComponent,
    BidResultDealDetailSupplierComponent,
    BidEvaluationComponent,
    BidResultAuctionComponent,
    BidResultAuctionDetailComponent,
    BidResultAuctionSupplierComponent,
    BidSupplierTechDetailComponent,
    BidSupplierTradeDetailComponent,
    AddOrEditBidTechComponent,
    AddOrEditBidTradeComponent,
    AddOrEditBidPriceComponent,
    BidDetailTechComponent,
    BidDetailPriceComponent,
    BidDetailTradeComponent,
    BidDetailTechListComponent,
    BidDetailTradeListComponent,
    BidDetailPriceListComponent,
    BidInfoComponent,
    AuctionComponent,
    AddOrEditAuctionComponent,
    AddSupplierAuctionComponent,
    AuctionDetailComponent,
    BidResultCustomPriceComponent,
    BidRankByMinPriceComponent,
    BidRankBySumPriceComponent,
    BidResultPrintComponent,
    BidBasicDetailPrintComponent,
    ReportSupplierComponent,
    ReportExpertiseComponent,
    ReportBidComponent,
    BidSettingRateComponent,
    BidRateReportComponent,
    ReportResultComponent,
    ReportCapacityComponent,
    ReportTechComponent,
    ReportTradeComponent,
    ReportTotalComponent,
    ReportPriceComponent,
    SupplierManageComponent,
    SupplierServiceComponent,
    BidResetPriceComponent,
    ReportHistoryPriceSupplierComponent,
    ReportHistoryPriceServiceComponent,
    ReportHistoryPriceCategoryComponent,
    AddOrEditSupplierComponent,
    SupplierServiceCapacityComponent,
    BiddingComponent,
    BiddingTradeComponent,
    BiddingTechComponent,
    BiddingPriceComponent,
    BiddingCustomPriceComponent,
    BiddingPriceListDetailComponent,
    AddBiddingPriceComponent,
    ReportHistoryBidSupplierComponent,
    ReportHistoryBuyerComponent,
    ProtocolOpenBidComponent,
    BiddingAdminComponent,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  exports: [BidComponent, SupplierCapacityDetailComponent],
})
export class BidModule {}
