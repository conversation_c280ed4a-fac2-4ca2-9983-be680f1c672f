<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-tabset>
    <nz-tab nzTitle="Thông tin chung">
      <nz-row nzGutter="12" class="mt-3">
        <nz-col nzSpan="8">
          <label>Tiêu đề:</label>
          <input nz-input [(ngModel)]="dataObject.title" readonly />
        </nz-col>
        <nz-col nzSpan="8">
          <label>Giá khởi điểm:</label>
          <input nz-input currencyMask [(ngModel)]="dataObject.price" readonly />
        </nz-col>
        <nz-col nzSpan="8">
          <label>Gi<PERSON> thấp nhất:</label>
          <input nz-input currencyMask [(ngModel)]="dataObject.minPrice" readonly />
        </nz-col>
      </nz-row>
      <nz-row nzGutter="12" class="mt-3">
        <nz-col nzSpan="8">
          <label>Thời điểm bắt đầu:</label>
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.dateStart" nzDisabled>
          </nz-date-picker>
        </nz-col>
        <nz-col nzSpan="8">
          <label>Thời điểm kết thúc:</label>
          <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.dateEnd" nzDisabled>
          </nz-date-picker>
        </nz-col>
        <nz-col nzSpan="8">
          <label>Gói thầu:</label>
          <input nz-input [(ngModel)]="dataObject.bidName" readonly />
        </nz-col>
      </nz-row>

      <nz-row nzGutter="8" class="mt-3">
        <nz-col nzSpan="24">
          <label>Ghi chú:</label>
          <textarea nz-input rows="5" [(ngModel)]="dataObject.description" readonly></textarea>
        </nz-col>
      </nz-row>
    </nz-tab>
    <nz-tab nzTitle="Danh sách nhà cung cấp tham gia">
      <nz-row nzGutter="8" class="mt-2">
        <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" [nzData]="['']"
          [nzScroll]="dataObject.lstSupplier && dataObject.lstSupplier.length > 10 ? { y: '400px' } : { y: null } "
          nzBordered>
          <thead>
            <tr>
              <th>Thứ hạng</th>
              <th>Tên NCC</th>
              <th>Giá đấu</th>
              <th>Ngày nộp đấu giá</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of dataObject.lstSupplier">
              <td>{{ dataItem.rank | number }}</td>
              <td>{{ dataItem.supplierName }}</td>
              <td>{{ dataItem.submitPrice | number }}</td>
              <td>{{ dataItem.submitDate | date: 'dd/MM/yyyy HH:mm' }} </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-tab>
    <nz-tab nzTitle="Lịch sử đấu giá">
      <nz-row nzGutter="8" class="mt-2">
        <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" [nzData]="['']"
          [nzScroll]="dataObject.lstHistory && dataObject.lstHistory.length > 10 ? { y: '400px' } : { y: null } "
          nzBordered>
          <thead>
            <tr>
              <th>Thời gian thao tác</th>
              <th>Nội dung thao tác</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of dataObject.lstHistory">
              <td>{{ dataItem.createdAt | date: 'dd/MM/yyyy HH:mm' }}</td>
              <td>{{ dataItem.description }} </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-tab>
  </nz-tabset>
</div>