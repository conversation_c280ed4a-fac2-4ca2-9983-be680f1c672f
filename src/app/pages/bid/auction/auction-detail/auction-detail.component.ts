import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../services'
import { Subscription } from 'rxjs'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'

@Component({ templateUrl: './auction-detail.component.html' })
export class AuctionDetailComponent implements OnInit {
  dataObject: any = {}
  loading = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  modalTitle = 'Thông tin phiên đấu giá'

  constructor(
    private apiService: ApiService,
    private coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDetail()
  }

  loadDetail() {
    this.loading = true
    this.apiService.post(this.apiService.AUCTION.DETAIL, { id: this.data.id }).then((res) => {
      if (res) {
        this.loading = false
        this.dataObject = res
      }
    })
  }
}
