import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { AuctionDetailComponent } from './auction-detail/auction-detail.component'
import { AddOrEditAuctionComponent } from './add-or-edit-auction/add-or-edit-auction.component'
import { AddSupplierAuctionComponent } from './add-supplier-auction/add-supplier-auction.component'

@Component({ templateUrl: './auction.component.html' })
export class AuctionComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  constructor(
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private notifyService: NotifyService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.title && this.dataSearch.title !== '') where.title = this.dataSearch.title
    if (this.dataSearch.dateStart) where.dateStart = this.dataSearch.dateStart
    if (this.dataSearch.dateEnd) where.dateEnd = this.dataSearch.dateEnd

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    this.loading = true
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.AUCTION.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditAuctionComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditAuctionComponent, { disableClose: false, data: { ...object } })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) this.searchData()
      })
  }

  clickCancel(object: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.AUCTION.CANCEL, { id: object.id, reasonCancel: '' }).then((res) => {
      this.notifyService.showSuccess(res?.message || enumData.Constants.Message_Action_Success)
      this.searchData()
    })
  }

  clickAddSupplier(object: any) {
    this.dialog
      .open(AddSupplierAuctionComponent, { disableClose: false, data: { ...object } })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) this.searchData()
      })
  }

  clickView(object: any) {
    this.dialog.open(AuctionDetailComponent, { disableClose: false, data: object })
  }
}
