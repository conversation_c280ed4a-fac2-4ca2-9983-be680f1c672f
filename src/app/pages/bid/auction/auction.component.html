<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tìm kiếm phiên đấu giá">
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <input nz-input [(ngModel)]="dataSearch.title" placeholder="Tìm theo tiêu đề" />
      </nz-col>
      <nz-col nzSpan="8">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateStart" nzPlaceHolder="Từ ngày đăng tải">
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateEnd" nzPlaceHolder="Đến ngày đăng tả<PERSON>">
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-1">
  <button nz-button nzType="primary" (click)="clickAdd()">
    <span nz-icon nzType="plus"></span> Thêm mới
  </button>
</nz-row>

<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Tiêu đề</th>
        <th>Gói thầu</th>
        <th>Giá khởi điểm</th>
        <th>Giá đấu thấp nhất</th>
        <th>Thời điểm bắt đầu</th>
        <th>Thời điểm kết thúc</th>
        <th>Trạng thái</th>
        <th>Thao tác</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td (click)="clickView(data)">{{ data.title }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.bidName }}</td>
        <td class="mw-25 text-right" (click)="clickView(data)">{{ data.price | number }}</td>
        <td class="mw-25 text-right" (click)="clickView(data)">{{ data.minPrice | number }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.dateStart | date: 'dd/MM/yyyy HH:mm' }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.dateEnd | date: 'dd/MM/yyyy HH:mm' }}</td>
        <td class="mw-25" (click)="clickView(data)">{{ data.statusName }}</td>
        <td class="text-nowrap">
          <button *ngIf="data.isShowEdit && authenticationService.checkPermissionUpdate('BID_014')" nz-tooltip
            nzTooltipTitle="Chỉnh sửa" (click)="clickEdit(data)" class="mr-2" nz-button nzType="dashed">
            <span nz-icon nzType="form"></span>
          </button>

          <button *ngIf="data.isShowEdit && authenticationService.checkPermissionUpdate('BID_014')" nz-tooltip
            nzTooltipTitle="Mời thêm NCC" (click)="clickAddSupplier(data)" class="mr-2" nz-button nzType="default">
            <span nz-icon nzType="user-add"></span>
          </button>

          <button *ngIf="data.isShowCancel && authenticationService.checkPermissionUpdate('BID_014')" nz-tooltip
            nzTooltipTitle="Hủy phiên đấu giá" nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hủy phiên đấu giá này?"
            nzPopconfirmPlacement="bottom" (nzOnConfirm)="clickCancel(data)" class="mr-2" nz-button nzDanger>
            <span nz-icon nzType="delete"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>