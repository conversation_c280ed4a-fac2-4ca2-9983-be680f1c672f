import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { User } from '../../../../models'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './add-supplier-auction.component.html' })
export class AddSupplierAuctionComponent implements OnInit {
  currentUser!: User
  modalTitle = 'MỜI THÊM NHÀ CUNG CẤP'
  dataObject: any = { lstSupplierId: [], lstSupplierAddId: [] }
  dataSupplier: any[] = []
  dataSupplierAdd: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddSupplierAuctionComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    await this.loadAllDataSelect()

    this.loadDetail()
  }

  loadDetail() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.AUCTION.DETAIL, { id: this.data.id }).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
      this.dataSupplierAdd = this.dataSupplier.filter((c) => !this.dataObject.lstSupplierId.includes(c.id))
      this.dataObject.lstSupplierAddId = []
      if (this.dataSupplierAdd.length == 0) {
        if (this.dataObject.bidName) this.notifyService.showWarning(`Đã mời toàn bộ NCC trong gói thầu "${this.dataObject.bidName}"!`)
        else this.notifyService.showWarning(`Đã mời toàn bộ NCC trong hệ thống!`)
      }
    })
  }

  onSave() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.AUCTION.ADD_SUPPLIER, { id: this.dataObject.id, lstSupplierId: this.dataObject.lstSupplierAddId })
      .then((res) => {
        this.notifyService.showSuccess(res?.message || enumData.Constants.Message_Action_Success)
        this.closeDialog(true)
      })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  async loadAllDataSelect() {
    if (!this.data.bidId) {
      this.apiService.post(this.apiService.SUPPLIER.FIND, {}).then((res) => {
        this.dataSupplier = res || []
      })
    } else {
      this.apiService.post(this.apiService.BID.FIND_BID_SUPPLIER, { bidId: this.data.bidId }).then((res) => {
        this.dataSupplier = res || []
      })
    }
  }
}
