import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { User } from '../../../../models'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-auction.component.html' })
export class AddOrEditAuctionComponent implements OnInit {
  currentUser!: User
  modalTitle = 'THÊM MỚI PHIÊN ĐẤU GIÁ'
  dateFormat = 'dd-MM-yyyy HH:mm'
  dataObject: any = {}
  dataBid: any[] = []
  dataSupplier: any[] = []
  dataSupplierSrc: any[] = []
  isEdit = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditAuctionComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    await this.loadAllDataSelect()

    this.dataObject.lstSupplierId = []
    if (this.data?.id) {
      this.isEdit = true
      this.modalTitle = 'CHỈNH SỬA PHIÊN ĐẤU GIÁ'
      this.loadDetail()
    }
  }

  loadDetail() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.AUCTION.DETAIL, { id: this.data.id }).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (!this.isEdit) {
      this.apiService.post(this.apiService.AUCTION.CREATE, this.dataObject).then((res) => {
        this.notifyService.showSuccess(res?.message || enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      })
    } else {
      this.apiService.post(this.apiService.AUCTION.UPDATE, this.dataObject).then((res) => {
        this.notifyService.showSuccess(res?.message || enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      })
    }
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  async loadAllDataSelect() {
    Promise.all([this.apiService.post(this.apiService.BID.FIND, {}), this.apiService.post(this.apiService.SUPPLIER.FIND, {})]).then((res) => {
      this.dataBid = res[0]
      this.dataSupplier = res[1]
      this.dataSupplierSrc = this.dataSupplier
    })
  }

  onChangeBid() {
    this.dataSupplier = this.dataSupplierSrc
    this.dataObject.lstSupplierId = []
    if (!this.dataObject.bidId) return
    this.apiService.post(this.apiService.BID.FIND_BID_SUPPLIER, { bidId: this.dataObject.bidId }).then((res) => {
      this.dataSupplier = res || []
      this.dataObject.lstSupplierId = this.dataSupplier.map((c) => c.id)
    })
  }
}
