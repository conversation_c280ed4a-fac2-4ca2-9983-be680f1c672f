<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiêu đề</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tiêu đề ">
            <input nz-input placeholder="Nhập Tiêu đề" [(ngModel)]="dataObject.title" name="title" required
              [disabled]="isEdit" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Gi<PERSON> khởi điểm</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Giá khởi điểm ">
            <input nz-input currencyMask placeholder="Nhập Giá khởi điểm" [(ngModel)]="dataObject.price" name="price" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời điểm bắt đầu</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Thời điểm bắt đầu">
            <nz-date-picker [nzFormat]="dateFormat" nzShowTime name="dateStart" nzPlaceHolder="Thời điểm bắt đầu"
              [(ngModel)]="dataObject.dateStart" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời điểm kết thúc</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Thời điểm kết thúc">
            <nz-date-picker [nzFormat]="dateFormat" nzShowTime name="dateEnd" nzPlaceHolder="Thời điểm kết thúc"
              [(ngModel)]="dataObject.dateEnd" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Gói thầu
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.bidId" name="bidId"
              nzPlaceHolder="Chọn Gói thầu" (ngModelChange)="onChangeBid()" [disabled]="isEdit">
              <nz-option *ngFor="let item of dataBid" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24" nzRequired>Nhà cung cấp tham gia
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataObject.lstSupplierId"
              name="lstSupplierId" nzPlaceHolder="Chọn nhà cung cấp tham gia" required [disabled]="isEdit">
              <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ghi chú
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <textarea nz-input [(ngModel)]="dataObject.description" name="description" placeholder="Nhập Ghi chú"
              rows="2" auto></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid || dataObject.lstSupplierId.length === 0" nzType="primary"
        (click)="onSave()">
        <span nz-icon nzType="save"></span> {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>