import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { BidResultDetailComponent } from '../bid-detail/bid-result/bid-result-detail/bid-result-detail.component'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-check-supplier.component.html' })
export class BidCheckSupplierComponent implements OnInit {
  modalTitle = 'Thông tin mở thầu'
  modalTitle2 = ''
  pageSizeMax = enumData.Page.pageSizeMax
  loading = false
  listOfData: any[] = []
  note: any
  listEmployee: any[] = []
  lstEmployeeId: any[] = []
  isShowPopupOpenBid = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  supplierChoose: any
  isVisibleListItem = false
  bid: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidCheckSupplierComponent>,
    public drawerService: NzDrawerService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { id: string; name: string },
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.modalTitle += ` gói thầu [${this.data.name}]`
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.loadEmployee()
    this.searchData()
  }

  loadEmployee() {
    this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((res) => {
      this.listEmployee = res || []
    })
  }

  searchData() {
    this.loading = true
    this.apiService.post(this.apiService.BID.LOAD_BID_SUPPLIER_OPEN_BID, { bidId: this.data.id }).then((res) => {
      if (res) {
        this.loading = false
        this.bid = res
      }
    })
  }

  //#region Đánh giá hồ sơ các Item
  rateListItem(object: any) {
    this.supplierChoose = object
    this.modalTitle2 = `Đánh giá hồ sơ các Item của nhà cung cấp [${object.supplierName}]`
    this.isVisibleListItem = true
  }

  hideRateListItem() {
    this.isVisibleListItem = false
    this.supplierChoose = undefined
  }
  //#endregion

  async openBid() {
    let strErr = ''
    this.bid.listBidSupplier.forEach((bidSupplier: any) => {
      let numValid = 0
      for (const item of bidSupplier.listItem) {
        if (!item.isValid && !item.note) {
          strErr += `Hồ sơ doanh nghiệp [${bidSupplier.supplierName}] Item [${item.itemName}] không hợp lệ, vui lòng ghi chú!<br>`
        }
        if (item.isValid) numValid++
      }
      bidSupplier.statusSubmitValid = `${numValid}/${bidSupplier.listItem.length} Hồ sơ`
    })
    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      return
    }

    this.isShowPopupOpenBid = true
  }

  confirmOpenBid() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BID.OPEN_BID, {
        bidId: this.bid.id,
        lstData: this.bid.listBidSupplier,
        lstEmployeeId: this.lstEmployeeId,
      })
      .then((result) => {
        this.isShowPopupOpenBid = false
        if (result && !result.error) {
          this.notifyService.showSuccess(result.message)
        }
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  showBidResultDetail(data: { bidId: string; supplierId: string }) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết doanh nghiệp',
      nzContent: BidResultDetailComponent,
      nzContentParams: data,
      nzWidth: '640',
    })
  }
}
