<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row *ngIf="bid">
    <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="bid.listBidSupplier" [(nzPageSize)]="pageSizeMax"
      [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
      <thead>
        <tr class="text-nowrap">
          <th nzWidth="70px">STT</th>
          <th>Mã nhà cung cấp</th>
          <th>Tên nhà cung cấp</th>
          <th>Trạng thái tham gia</th>
          <th>Trạng thái nộ<PERSON> hồ sơ</th>
          <th>Trạng thá<PERSON> hồ sơ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataRow of bid.listBidSupplier; let i = index">
          <td class="text-center">{{ i + 1 }}</td>
          <td>
            <span nz-tooltip nzTooltipTitle="Chi tiết" (click)="showBidResultDetail(dataRow)" nz-icon
              nzType="info-circle"></span>
            {{ dataRow.supplierCode }}
          </td>
          <td class="mw-25">{{ dataRow.supplierName }}</td>
          <td>{{ dataRow.statusConfirmJoinBid }}</td>
          <td>{{ dataRow.statusSubmitBid }}</td>
          <td>
            <button nz-tooltip nzTooltipTitle="Đánh giá hồ sơ các Item"
              *ngIf=" authenticationService.checkPermission([enumRole], action.Update.code)"
              (click)="rateListItem(dataRow)" nz-button nzType="primary">
              <span nz-icon nzType="profile"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>

  <nz-row class="mt-3">
    <h4>Hội đồng mở thầu:</h4>
    <nz-select nz-col nzSpan="24" nzShowSearch nzAllowClear nzMode="multiple"
      nzPlaceHolder="Chọn các nhân viên trong hội đồng mở thầu" [(ngModel)]="lstEmployeeId">
      <nz-option *ngFor="let item of listEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
        [nzValue]="item.id"></nz-option>
    </nz-select>
  </nz-row>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-button nzType="primary"
      (click)="openBid()">Xác nhận mở thầu</button>
  </nz-col>
</nz-row>

<!-- Modal Đánh giá hồ sơ các Item  -->
<nz-modal [(nzVisible)]="isVisibleListItem" [nzWidth]="'80vw'" [nzTitle]="modalTitle2" (nzOnCancel)="hideRateListItem()"
  [nzFooter]="null" *ngIf="isVisibleListItem && supplierChoose">
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="supplierChoose.listItem" [(nzPageSize)]="pageSizeMax"
        [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th nzWidth="50px">STT</th>
            <th>Tên Item</th>
            <th>Trạng thái nộp hồ sơ</th>
            <th>Trạng thái hồ sơ</th>
            <th>Ghi chú</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data1 of supplierChoose.listItem; let i = index">
            <tr>
              <td class="text-center">{{ i + 1 }}</td>
              <td>{{ data1.itemName }}</td>
              <td>{{ data1.statusName }}</td>
              <td class="text-center">
                <label nz-checkbox [(ngModel)]="data1.isValid" name="isValid">
                  Hợp lệ
                </label>
              </td>
              <td class="w-25">
                <textarea nz-input rows="1" auto placeholder="Nhập ghi chú {{data1.isValid ? '' : '(bắt buộc)'}}"
                  [(ngModel)]="data1.note"></textarea>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>

<!-- Biên bản mở thầu -->
<nz-modal [(nzVisible)]="isShowPopupOpenBid" [nzWidth]="'800px'" nzTitle="Biên bản mở hồ sơ thầu"
  (nzOnCancel)="isShowPopupOpenBid=false" [nzFooter]="null" *ngIf="isShowPopupOpenBid">
  <ng-container *nzModalContent>
    <app-protocol-open-bid [bid]="bid"></app-protocol-open-bid>
    <nz-row class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button *ngIf=" authenticationService.checkPermission([enumRole], action.Update.code)"
          (click)="confirmOpenBid()" nz-button class="ant-btn-blue">Đồng ý mở thầu</button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>