import { Component, Input, OnInit } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { Workbook } from 'exceljs'
import { Subscription } from 'rxjs'
import * as moment from 'moment'
import * as fs from 'file-saver'

@Component({
  selector: 'app-protocol-open-bid',
  templateUrl: './protocol-open-bid.component.html',
})
export class ProtocolOpenBidComponent implements OnInit {
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  public data!: { id: string }
  @Input()
  public bid!: any
  enumRole: any
  enumProject: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.BID_005.code
    if (!this.bid) {
      this.notifyService.showloading()
      this.apiService.post(this.apiService.BID.LOAD_BID_SUPPLIER_OPEN_BID, { bidId: this.data.id }).then((res) => {
        if (res) {
          this.notifyService.hideloading()
          this.bid = res
        }
      })
    }
  }

  clickExportExcel() {
    this.notifyService.showloading()
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')

    //#region Header

    //title 1
    worksheet.getCell('A1').value = 'BIÊN BẢN MỞ HỒ SƠ THẦU'
    worksheet.getCell('A1').style = {
      font: { name: 'Calibri', family: 4, size: 16, bold: true, color: { argb: '426EB4' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
    }
    worksheet.mergeCells('A1:F2')

    //title 3
    worksheet.getCell('A3').value = `Gói thầu: ${this.bid.name}`
    worksheet.getCell('A3').style = {
      font: { name: 'Calibri', family: 4, size: 14, bold: true, color: { argb: 'BA0303' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
    }
    worksheet.mergeCells('A3:F3')
    //#endregion

    //#region Body
    worksheet.addRow([])
    const bidOpenDateStr = this.coreService.date.to_ddMMyyyy(new Date(this.bid.bidOpenDate))
    const bidOpenDateStrLong = this.coreService.date.to_HHmmddMMyyyyLong(new Date(this.bid.bidOpenDate))

    const row5 = worksheet.addRow([
      `Biên bản mở thầu ngày ${bidOpenDateStr} tại Văn phòng ${this.bid.companyInvite}${
        this.bid.addressSubmit ? ', ' + this.bid.addressSubmit : ''
      }.`,
    ])
    const row6 = worksheet.addRow([`Nội dung như sau:`])
    const row7 = worksheet.addRow([])
    const row8 = worksheet.addRow(['I.', 'Thông tin chung gói thầu:'])
    row8.eachCell((cell) => {
      cell.font = { name: 'Segoe UI', family: 4, size: 11, bold: true }
      cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    })
    const row9 = worksheet.addRow(['', 'Công ty mời thầu:', null, null, this.bid.companyInvite])
    const acceptEndDateStr = this.coreService.date.to_ddMMyyyyHHmmss(new Date(this.bid.acceptEndDate))
    const row10 = worksheet.addRow(['', 'Ngày hết hạn xác nhận tham gia đấu thầu:', null, null, acceptEndDateStr])
    const submitEndDateStr = this.coreService.date.to_ddMMyyyyHHmmss(new Date(this.bid.submitEndDate))
    const row11 = worksheet.addRow(['', 'Ngày hết hạn nộp hồ sơ thầu:', null, null, submitEndDateStr])
    // const row12 = worksheet.addRow(['', 'Tỉ trọng đánh giá:', null, '- Yêu cầu kỹ thuật:', this.bid.percentTech + ' %'])
    // const row13 = worksheet.addRow(['', null, null, '- Điều kiện thương mại:', this.bid.percentTrade + ' %'])
    // const row14 = worksheet.addRow(['', null, null, '- Bảng giá:', this.bid.percentPrice + ' %'])
    const row15 = worksheet.addRow([])
    const row16 = worksheet.addRow(['II.', 'Thành phần:'])
    row16.eachCell((cell) => {
      cell.font = { name: 'Segoe UI', family: 4, size: 11, bold: true }
      cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    })
    const row17 = worksheet.addRow(['', 'HĐXT ủy quyền cho phòng mua hàng (MPO) đại diện bấm nút mở thầu trên hệ thống.'])
    row17.eachCell((cell) => {
      cell.font = { name: 'Segoe UI', family: 4, size: 11, bold: true }
      cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    })
    const row18 = worksheet.addRow(['', 'Danh sách HĐXT bao gồm:'])
    row18.eachCell((cell) => {
      cell.font = { name: 'Segoe UI', family: 4, size: 11, bold: true }
      cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    })
    const row19 = worksheet.addRow(['', this.language_key?.NO || 'STT', 'Họ và tên', 'Phòng ban', 'Email'])
    row19.eachCell((cell, colNumber) => {
      if (colNumber > 1) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFF' },
        }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: true }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
      }
    })
    let sort = 0
    for (const item of this.bid.listAccess) {
      sort++
      const rowItem = worksheet.addRow(['', sort, item.employeeName, item.departmentName, item.employeeEmail])

      rowItem.eachCell((cell, colNumber) => {
        if (colNumber > 1) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFFF' },
          }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
          cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
          if (colNumber == 2) cell.alignment = { horizontal: 'center', vertical: 'middle' }
        }
      })
    }
    const row23 = worksheet.addRow([])

    const row24 = worksheet.addRow(['III.', 'Kết quả:'])
    row24.eachCell((cell) => {
      cell.font = { name: 'Segoe UI', family: 4, size: 11, bold: true }
      cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    })
    const row25 = worksheet.addRow([
      '',
      this.language_key?.NO || 'STT',
      this.language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp',
      'Xác nhận tham gia',
      'Nộp hồ sơ',
      'Hợp lệ',
    ])
    row25.eachCell((cell, colNumber) => {
      if (colNumber > 1) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFF' },
        }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: true }
        cell.alignment = { horizontal: 'center', vertical: 'middle' }
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 3
          break
        case 2:
          worksheet.getColumn(colNumber).width = 6
          break
        default:
          worksheet.getColumn(colNumber).width = 25
          break
      }
    })
    sort = 0
    for (const item of this.bid.listBidSupplier) {
      sort++
      const rowItem = worksheet.addRow(['', sort, item.supplierName, item.statusConfirmJoinBid, item.statusSubmitBid, item.statusSubmitValid])

      rowItem.eachCell((cell, colNumber) => {
        if (colNumber > 1) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFFF' },
          }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
          cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
          if (colNumber == 2) cell.alignment = { horizontal: 'center', vertical: 'middle' }
        }
      })
    }
    const row28 = worksheet.addRow(['', 'Tổng', this.bid.numOfSupplier, this.bid.numOfSupplierConfirmJoin, this.bid.numOfSupplierSubmitBid, ''])
    row28.eachCell((cell, colNumber) => {
      if (colNumber > 1) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFF' },
        }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: true }
        if (colNumber == 2) cell.alignment = { horizontal: 'center', vertical: 'middle' }
      }
    })
    const row29 = worksheet.addRow([])

    const row30 = worksheet.addRow(['IV.', 'Kết luận:'])
    row30.eachCell((cell) => {
      cell.font = { name: 'Segoe UI', family: 4, size: 11, bold: true }
      cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    })
    const row31 = worksheet.addRow(['', `Biên bản mở thầu hoàn thành vào lúc ${bidOpenDateStrLong}`])

    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Biên bản mở gói thầu ${this.bid.name}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }
}
