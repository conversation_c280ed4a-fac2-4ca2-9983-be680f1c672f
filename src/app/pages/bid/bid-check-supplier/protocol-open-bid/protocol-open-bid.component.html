<div class="mb-5" *ngIf="bid">
  <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)"  [useExistingCss]="true" printSectionId="print-section" ngxPrint class="mr-2">
    In biên bản
  </button>
  <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" (click)="clickExportExcel()">
    <span nz-icon nzType="download"></span>
    Xuất excel
  </button>
</div>

<div id="print-section" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;" *ngIf="bid">
  <img src="../../../../../assets/img/logoform.jpg" alt="logo" height="50" width="150" />
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BIÊN BẢN MỞ HỒ SƠ THẦU</div>
    <div>Gói thầu: {{ bid.name | uppercase }}</div>
  </div>

  <!-- Thông tin chung -->
  <div style="margin-top: 20px; padding: 0px 20px;">
    Biên bản mở thầu ngày {{bid.bidOpenDate | date: 'dd/MM/yyyy'}} tại Văn phòng
    {{bid.companyInvite}}{{bid.addressSubmit ? ', ' + bid.addressSubmit : ''}}. Nội dung như sau:
  </div>

  <div style="margin-top: 20px; padding: 0px 20px;">
    <table>
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px;">I.</th>
          <th style="width: 60%;">Thông tin chung gói thầu:</th>
          <th style="width: 40%;"></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td>Công ty mời thầu:</td>
          <td>{{bid.companyInvite}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Ngày hết hạn xác nhận tham gia đấu thầu:</td>
          <td>{{bid.acceptEndDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
        <tr>
          <td></td>
          <td>Ngày hết hạn nộp hồ sơ thầu:</td>
          <td>{{bid.submitEndDate | date: 'dd/MM/yyyy HH:mm'}}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <div style="margin-top: 20px; padding: 0px 20px;">
    <table style="border-spacing: 0;">
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px;">II.</th>
          <th colspan="4" style="width: 100%;">Thành phần:</th>
        </tr>
        <tr style="text-align: left;">
          <th></th>
          <th colspan="4">HĐXT ủy quyền cho phòng mua hàng (MPO) đại diện bấm nút mở thầu trên hệ thống.</th>
        </tr>
        <tr style="text-align: left;">
          <th></th>
          <th colspan="4">Danh sách HĐXT bao gồm:</th>
        </tr>
        <tr style="text-align: center;">
          <th></th>
          <th style="width: 30px; border: darkgrey 1px solid; padding: 5px;">
            {{ language_key?.NO || 'STT' }}
          </th>
          <th style="width: 33%; border: darkgrey 1px solid; padding: 5px;">Họ và tên</th>
          <th style="width: 33%; border: darkgrey 1px solid; padding: 5px;">Phòng ban</th>
          <th style="width: 33%; border: darkgrey 1px solid; padding: 5px;">Email</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of bid.listAccess; let i = index;">
          <td></td>
          <td align="center" style="border: darkgrey 1px solid; padding: 5px;">{{i+1}}.</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.employeeName}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.departmentName}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.employeeEmail}}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <div style="margin-top: 20px; padding: 0px 20px;">
    <table style="border-spacing: 0;">
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px;">III.</th>
          <th colspan="6" style="width: 100%;">Kết quả:</th>
        </tr>
        <tr style="text-align: center;">
          <th></th>
          <th style="width: 30px; border: darkgrey 1px solid; padding: 5px;">
            {{ language_key?.NO || 'STT' }}
          </th>
          <th style="width: 24%; border: darkgrey 1px solid; padding: 5px;">
            {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}
          </th>
          <th style="width: 24%; border: darkgrey 1px solid; padding: 5px;">Xác nhận<br>tham gia</th>
          <th style="width: 24%; border: darkgrey 1px solid; padding: 5px;">Nộp hồ sơ</th>
          <th style="width: 24%; border: darkgrey 1px solid; padding: 5px;">Hợp lệ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of bid.listBidSupplier; let i = index;">
          <td></td>
          <td align="center" style="border: darkgrey 1px solid; padding: 5px;">{{i+1}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.supplierName}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.statusConfirmJoinBid}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.statusSubmitBid}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{item.statusSubmitValid}}</td>
        </tr>
        <tr style="text-align: right;">
          <td></td>
          <td align="center" style="border: darkgrey 1px solid; padding: 5px;">Tổng</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{bid.numOfSupplier}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{bid.numOfSupplierConfirmJoin}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;">{{bid.numOfSupplierSubmitBid}}</td>
          <td style="border: darkgrey 1px solid; padding: 5px;"></td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Kết quả đánh giá -->
  <div style="margin-top: 20px; padding: 0px 20px;">
    <table>
      <thead>
        <tr style="text-align: left;">
          <th style="width: 30px;">IV.</th>
          <th style="width: 100%;">Kết luận:</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td>
            Biên bản mở thầu hoàn thành vào lúc {{ bid.bidOpenDate | date: 'HH giờ mm' }} phút,
            ngày {{ bid.bidOpenDate | date: 'dd'}}
            tháng {{ bid.bidOpenDate | date: 'MM'}}
            năm {{ bid.bidOpenDate | date: 'yyyy' }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>