<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="data">
  <nz-row class="mt-2" *ngIf="bid">
    <nz-table nz-col nzSpan="24" [nzData]="bid.listBidSupplier" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false"
      nzBordered>
      <thead>
        <tr>
          <th nzWidth="70px">STT</th>
          <th>Mã nhà cung cấp</th>
          <th>Tên nhà cung cấp</th>
          <th>Trạng thái nộp hồ sơ</th>
          <th nzWidth="170px">Tùy chọn</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data1 of bid.listBidSupplier; let i = index">
          <td class="text-center">{{ i + 1 }}</td>
          <td>{{ data1.supplierCode }}</td>
          <td>{{ data1.supplierName }}</td>
          <td>{{ data1.statusSubmitBid }}</td>
          <td class="text-center">
            <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-tooltip
              nzTooltipTitle="Chọn Item nhập hồ sơ thầu" (click)="chooseSupplier(data1)" nz-button nzType="primary">
              <span nz-icon nzType="profile"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>

<!-- Modal Chọn Item để nhập hồ sơ gói thầu  -->
<nz-modal [(nzVisible)]="isVisibleChooseItem" [nzWidth]="'80vw'" [nzTitle]="modalTitle2"
  (nzOnCancel)="isVisibleChooseItem=false;supplierChoose=null" [nzFooter]="null" *ngIf="isVisibleChooseItem">
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-col nzSpan="24">
        <nz-table [nzData]="supplierChoose.listItem" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th nzWidth="50px">STT</th>
              <th>Tên Item</th>
              <th>Trạng thái</th>
              <th nzWidth="170px">Nhập hồ sơ thầu</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data1 of supplierChoose.listItem; let i = index">
              <td class="text-center">{{ i + 1 }}</td>
              <td>{{ data1.itemName }}</td>
              <td>{{ data1.statusName }}</td>
              <td class="text-center">
                <button nz-tooltip *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
                  nzTooltipTitle="Nhập hồ sơ thầu" (click)="chooseItem(data1)" nz-button nzType="primary">
                  <span nz-icon nzType="profile"></span>
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>

    </nz-row>
  </ng-container>
</nz-modal>