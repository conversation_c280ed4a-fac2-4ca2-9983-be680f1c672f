import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { BiddingComponent } from '../bidding/bidding.component'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, NotifyService } from '../../../services'

@Component({ templateUrl: './bidding-admin.component.html' })
export class BiddingAdminComponent implements OnInit {
  modalTitle = ''
  modalTitle2 = ''
  pageSizeMax = enumData.Page.pageSizeMax
  bid: any
  isVisibleChooseItem = false
  supplierChoose: any
  enumProject: any
  enumRole: any
  action: any

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private dialogRef: MatDialogRef<BiddingAdminComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.modalTitle = `Chọn nhà cung cấp cần nộp hồ sơ [${this.data.code}]`
    this.loadData()
  }

  /** Load thông tin các NCC */
  loadData() {
    this.notifyService.showloading()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.apiService.post(this.apiService.BID.LOAD_BID_SUPPLIER, { bidId: this.data.id }).then((res) => {
      this.notifyService.hideloading()
      this.bid = res

      // nếu đang mở modal chọn item thì load lại data cho modal
      if (this.isVisibleChooseItem) {
        this.supplierChoose = this.bid.listBidSupplier.find((c: any) => c.id == this.supplierChoose.id)
      }
    })
  }

  closeDialog() {
    this.dialogRef.close()
  }

  /** Khi chọn NCC thì mở màn hình chọn Item */
  chooseSupplier(data: any) {
    this.supplierChoose = data
    this.modalTitle2 = `Chọn Item để nhập hồ sơ gói thầu [${this.data.code}] cho nhà cung cấp [${data.supplierName}]`
    if (data.listItem.length == 1) {
      // có 1 item thì không cần chọn item
      const noChooseItem = true
      this.chooseItem(data.listItem[0], noChooseItem)
    } else if (data.listItem.length > 1) {
      this.isVisibleChooseItem = true
    }
  }

  /** Khi chọn Item thì màn hình nộp hồ sơ gói thầu */
  chooseItem(data: any, noChooseItem = false) {
    /* Ẩn modal chọn Item */
    this.isVisibleChooseItem = false
    this.dialog
      .open(BiddingComponent, {
        disableClose: false,
        data: {
          bidId: data.bidId,
          supplierId: data.supplierId,
          supplierName: this.supplierChoose.supplierName,
          bidCode: this.data.code,
          itemName: data.itemName,
        },
      })
      .afterClosed()
      .subscribe(() => {
        /* Mở lại modal chọn Item */
        if (!noChooseItem) {
          this.isVisibleChooseItem = true
        }
        this.loadData()
      })
  }
}
