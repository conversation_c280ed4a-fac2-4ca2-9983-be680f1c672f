import { Component, Input, OnInit } from '@angular/core'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({
  selector: 'app-report-tech',
  templateUrl: './report-tech.component.html',
})
export class ReportTechComponent implements OnInit {
  loading = false
  pageSize = enumData.Page.pageSizeMax
  isVisibleDetail = false
  titleDetail = ''
  bidSupplier: any
  todate = new Date()
  temp = 'Đang đánh giá'
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(public coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankTech - b.rankTech)
    }
  }

  clickExportExcel() {
    this.notifyService.showInfo('Chức năng đang phát triển')
    return

    setTimeout(() => {
      const tbl = document.getElementById('tech-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bao cao so sanh danh gia tong ho so ky thuat.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  showDetail(bidSupplier: any) {
    this.isVisibleDetail = true
    this.titleDetail = `Báo cáo chi tiết kỹ thuật doanh nghiệp ${bidSupplier.supplierName}`
    this.bidSupplier = bidSupplier
  }

  handleCancel() {
    this.isVisibleDetail = false
    this.bidSupplier = null
  }
}
