import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './bid-result-report.component.html' })
export class BidRateReportComponent implements OnInit {
  dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public bidId: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.bidId) {
      this.notifyService.showloading()
      this.apiService.get(this.apiService.BID_RATE.GET_DATA_REPORT(this.bidId), {}).then(async (res) => {
        this.notifyService.hideloading()
        this.dataReport = res
      })
    }
  }
}
