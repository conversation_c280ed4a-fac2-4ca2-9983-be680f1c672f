import { Component, Input, OnInit } from '@angular/core'
import { enumData } from '../../../../../core'

import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({
  selector: 'app-report-total',
  templateUrl: './report-total.component.html',
})
export class ReportTotalComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  loading = false
  temp = 'Đang đánh giá'
  listOfData: any[] = []
  todate = new Date()
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(private coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankTotalScore - b.rankTotalScore)
    }
  }

  clickExportExcel() {
    this.notifyService.showInfo('Chức năng đang phát triển')
    return

    setTimeout(() => {
      const tbl = document.getElementById('total-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bao cao so sanh tong diem cac tieu chi.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
