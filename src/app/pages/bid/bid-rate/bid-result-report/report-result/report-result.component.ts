import { Component, Input, OnInit } from '@angular/core'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({
  selector: 'app-report-result',
  templateUrl: './report-result.component.html',
})
export class ReportResultComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  loading = false
  listOfData: any[] = []
  temp = 'Đang đánh giá'
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(private coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankTotalScore - b.rankTotalScore)
    }
  }

  clickExportExcel() {
    this.notifyService.showInfo('Chức năng đang phát triển')
    return

    setTimeout(() => {
      const tbl = document.getElementById('result-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Kết quả đánh giá gói thầu.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
