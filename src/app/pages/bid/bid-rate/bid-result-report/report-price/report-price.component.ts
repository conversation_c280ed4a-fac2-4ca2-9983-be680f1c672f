import { Component, Input, OnInit } from '@angular/core'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({
  selector: 'app-report-price',
  templateUrl: './report-price.component.html',
})
export class ReportPriceComponent implements OnInit {
  loading = false
  pageSize = enumData.Page.pageSizeMax
  isVisibleDetail = false
  titleDetail = ''
  bidSupplier: any
  todate = new Date()
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(public coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankPrice - b.rankPrice)
    }
  }

  clickExportExcel() {
    this.notifyService.showInfo('Chức năng đang phát triển')
    return

    setTimeout(() => {
      const tbl = document.getElementById('price-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bao cao so sanh tong hop bang chao gia.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  showDetail(bidSupplier: any) {
    this.isVisibleDetail = true
    this.titleDetail = `Báo cáo bảng chào giá và lịch sử nộp giá doanh nghiệp ${bidSupplier.supplierName}`
    this.bidSupplier = bidSupplier
  }

  handleCancel() {
    this.isVisibleDetail = false
    this.bidSupplier = null
  }
}
