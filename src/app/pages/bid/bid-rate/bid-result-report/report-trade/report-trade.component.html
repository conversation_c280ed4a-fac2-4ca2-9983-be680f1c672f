<div id="print-section" *ngIf="dataReport" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150">
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BÁO CÁO SO SÁNH ĐÁNH GIÁ TỔNG ĐIỀU KIỆN THƯƠNG MẠI</div>
    <div>TÊN GÓI THẦU: {{dataReport.bidName}}</div>
    <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px;" *ngFor="let bidChild of dataReport.listItem">
    <div style="margin-bottom: 10px;">Item: <b>{{bidChild.itemName}}</b></div>
    <table id="trade-table" style="margin-bottom: 10px; border-spacing: 0;" align="center">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 40px; border: darkgrey 1px solid;">{{ language_key?.NO || 'STT' }}</th>
          <th style="width: 300px; border: darkgrey 1px solid;">Tên điều kiện thương mại</th>
          <th style="width: 100px; border: darkgrey 1px solid;">Loại dữ liệu</th>
          <th style="width: 100px; border: darkgrey 1px solid;">Tỉ trọng<br>({{bidChild.totalPercentTrade}}%)</th>
          <th style="width: 120px; border: darkgrey 1px solid; cursor: pointer;"
            *ngFor="let bidSupplier of bidChild.lstBidSupplier" (click)="showDetail(bidSupplier)">
            {{bidSupplier.supplierName}}
          </th>
        </tr>
        <tr style="font-weight: 700;">
          <td colspan="4" style="border: darkgrey 1px solid; text-align: left;">&nbsp;Xếp loại</td>
          <td align="right" style="border: darkgrey 1px solid;"
            *ngFor="let bidSupplier of bidChild.lstBidSupplier; let i=index;">
            {{bidSupplier.rankTrade || i+1}}&nbsp;
          </td>
        </tr>
        <tr style="font-weight: 700;">
          <td colspan="4" style="border: darkgrey 1px solid; text-align: left;">&nbsp;Tổng điểm</td>
          <td align="right" style="border: darkgrey 1px solid;" *ngFor="let bidSupplier of bidChild.lstBidSupplier">
            {{bidSupplier.scoreTrade === -1 ? temp : (bidSupplier.scoreTrade | number: '1.0-2')}}&nbsp;
          </td>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of bidChild.bidTrades">
          <tr>
            <td style="border: darkgrey 1px solid;">&nbsp;{{ data.sort > 0 ? data.sort : '' }}</td>
            <td style="border: darkgrey 1px solid;">&nbsp;{{ data.name }}</td>
            <td align="center" style="border: darkgrey 1px solid;">{{ data.type }}</td>
            <td align="center" style="border: darkgrey 1px solid;">{{ data.percent }}</td>
            <td align="right" style="border: darkgrey 1px solid;" *ngFor="let bidSupplier of bidChild.lstBidSupplier">
              {{ data[bidSupplier.id] | number: '1.0-2' }}&nbsp;
            </td>
          </tr>
          <ng-container *ngIf="data.__childs__?.length">
            <tr *ngFor="let item of data.__childs__">
              <td style="border: darkgrey 1px solid;">&emsp;&nbsp;{{ item.sort > 0 ? item.sort : '' }}</td>
              <td style="border: darkgrey 1px solid;">&nbsp;{{ item.name }}</td>
              <td align="center" style="border: darkgrey 1px solid;">{{ item.type }}</td>
              <td align="center" style="border: darkgrey 1px solid;">{{ item.percent }}</td>
              <td align="right" style="border: darkgrey 1px solid;" *ngFor="let bidSupplier of bidChild.lstBidSupplier">
                {{ item[bidSupplier.id] | number: '1.0-2' }}&nbsp;
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px;">
    <div style="text-align: right; font-weight: 700;" *ngIf="dataReport.approveChooseSupplierWinDate">
      Ngày {{dataReport.approveChooseSupplierWinDate | date: 'dd'}}
      tháng {{dataReport.approveChooseSupplierWinDate | date: 'MM'}}
      năm {{dataReport.approveChooseSupplierWinDate | date: 'yyyy'}}
    </div>
    <div style="text-align: right; font-weight: 700;" *ngIf="!dataReport.approveChooseSupplierWinDate">
      Ngày .....
      tháng .....
      năm .....
    </div>
    <table style="margin: 10px 0 100px 0;">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 30px;"></th>
          <th style="width:50%;">ĐỀ XUẤT</th>
          <th style="width:50%;">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-5 text-center" *ngIf="!dataReport.isHidePrint">
  <!-- <button nz-button (click)="clickExportExcel()" class="mr-2">
    <span nz-icon nzType="download"></span>Xuất excel
  </button> -->
  <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section" ngxPrint>
    <span nz-icon nzType="printer"></span> In báo cáo
  </button>
</div>

<nz-modal [(nzVisible)]="isVisibleDetail" [nzTitle]="titleDetail" (nzOnCancel)="handleCancel()" [nzWidth]="'70vw'"
  [nzFooter]="null" *ngIf="bidSupplier">
  <ng-container *nzModalContent>
    <app-bid-result-trade [bidId]="bidSupplier.bidId" [supplierId]="bidSupplier.supplierId"></app-bid-result-trade>
  </ng-container>
</nz-modal>