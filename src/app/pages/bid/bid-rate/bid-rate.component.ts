import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { NzModalService } from 'ng-zorro-antd/modal'
import { Subscription } from 'rxjs'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { BidCheckSupplierComponent } from '../bid-check-supplier/bid-check-supplier.component'
import { BidDetailComponent } from '../bid-detail/bid-detail.component'
import { BidResultPrintComponent } from '../bid-info/bid-result-print/bid-result-print.component'
import { BidEvaluationComponent } from './bid-evaluation/bid-evaluation.component'
import { BidItemComponent } from './bid-item/bid-item.component'
import { BidRankByMinPriceComponent } from './bid-rank-by-min-price/bid-rank-by-min-price.component'
import { BidRankBySumPriceComponent } from './bid-rank-by-sum-price/bid-rank-by-sum-price.component'
import { BidRatePriceComponent } from './bid-rate-price/bid-rate-price.component'
import { BidSupplierTechRateComponent } from './bid-rate-tech/bid-rate-tech.component'
import { BidSupplierTradeRateComponent } from './bid-rate-trade/bid-rate-trade.component'
import { BidResetPriceComponent } from './bid-reset-price/bid-reset-price.component'
import { BidRateReportComponent } from './bid-result-report/bid-result-report.component'

@Component({ templateUrl: './bid-rate.component.html' })
export class BidRateComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  maxSizeUpload = enumData.maxSizeUpload
  loading = true
  showAnalysis = false
  showReport = false
  isVisible1 = false
  isVisible2 = false
  dataSearch: any = {}
  listBidStatus = [
    enumData.BidStatus.GoiThauTam,
    enumData.BidStatus.DangNhanBaoGia,
    enumData.BidStatus.DangDanhGia,
    enumData.BidStatus.DangDuyetDanhGia,
    enumData.BidStatus.HoanTatDanhGia,
    enumData.BidStatus.DongDauGia,
    enumData.BidStatus.DongDamPhanGia,
    enumData.BidStatus.DongThau,
    enumData.BidStatus.DuyetNCCThangThau,
    enumData.BidStatus.DangDuyetKetThucThau,
  ]
  // dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  bidStatus = enumData.BidStatus
  listOfData: any[] = []
  isCollapseFilter = false

  bidPriceMinListItem: any
  bidChoose: any
  screenWidth: any
  isShowPopupRequestFinishBid = false
  modalTitle: string = ''
  isShowProtocolOpenBid = false
  isShowCopyBid = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  enumRole4: any
  enumRole6: any
  enumRole7: any
  enumRole011: any
  enumRole008: any
  enumRole009: any
  enumRole010: any
  enumRole013: any

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    private modal: NzModalService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.BID_005.code
    this.enumRole4 = this.enumProject.Features.BID_004.code
    this.enumRole6 = this.enumProject.Features.BID_006.code
    this.enumRole7 = this.enumProject.Features.BID_007.code
    this.enumRole011 = this.enumProject.Features.BID_011.code
    this.enumRole008 = this.enumProject.Features.BID_008.code
    this.enumRole009 = this.enumProject.Features.BID_009.code
    this.enumRole010 = this.enumProject.Features.BID_010.code
    this.enumRole013 = this.enumProject.Features.BID_013?.code
    this.action = this.enumProject.Action
    this.searchData()
    this.screenWidth = window.screen.width
  }

  async searchData(reset = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.status && this.dataSearch.status.length > 0) {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }

    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }

    // Lấy gói thầu cần duyệt
    if (this.dataSearch.isGetBidNeedApprove) {
      where.isGetBidNeedApprove = this.dataSearch.isGetBidNeedApprove
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.BID_RATE.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        this.showAnalysis = this.listOfData.some((c) => c.isShowAnalysis)
        this.showReport = this.listOfData.some((c) => c.isShowReport)
      }
    })
  }

  clickView(object: any) {
    this.dialog.open(BidDetailComponent, { data: object })
  }

  openBid(object: { id: string; name: string }) {
    this.dialog
      .open(BidCheckSupplierComponent, { data: { id: object.id, name: object.name } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidSupplierTechRate(object: any) {
    this.dialog
      .open(BidSupplierTechRateComponent, { data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidSupplierTradeRate(object: any) {
    this.dialog
      .open(BidSupplierTradeRateComponent, { data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidSupplierPriceRate(object: any) {
    this.dialog
      .open(BidRatePriceComponent, { data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidDealAuction(object: { id: string; name: string }) {
    this.dialog
      .open(BidItemComponent, { data: { id: object.id, name: object.name } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  /** Tự động chọn NCC thắng thầu và kết thúc thầu */
  // bidAutoChoose(object: any) {
  //   this.modal.confirm({
  //     nzTitle: '<i>Bạn có chắc muốn tự động chọn NCC thắng thầu và kết thúc thầu?</i>',
  //     nzContent: `
  //     <b>Khi xác nhận, hệ thống sẽ chọn NCC thắng thầu cho từng Item của gói thầu dựa trên kết quả đánh giá các doanh nghiệp. Đồng thời cũng hoàn tất gói thầu, mà không qua các bước xét duyệt khác.</b><br>
  //     <b>Bạn cần chắc chắn các thay đổi trước khi xác nhận.</b>`,
  //     nzOnOk: () => {
  //       this.notifyService.showloading()
  //       this.apiService.post(this.apiService.BID_EVALUATION.AUTO_BID, { bidId: object.id }).then(() => {
  //         this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
  //         this.searchData()
  //       })
  //     },
  //   })
  // }

  /** Chọn NCC thắng thầu */
  bidEvaluation(object: any) {
    const data = {
      bidId: object.id,
      isShowEnd: object.isShowEnd,
      isShowAcceptEnd: object.isShowAcceptEnd,
    }
    this.dialog
      .open(BidEvaluationComponent, { data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  //#region Phân tích giá

  /** Load các hạng mục giá của gói thầu */
  private async loadPrice(bidId: string) {
    this.notifyService.showloading()
    await this.apiService.get(this.apiService.BID.GET_PRICE(bidId), {}).then((res) => {
      this.notifyService.hideloading()
      this.bidPriceMinListItem = res
    })
  }

  /** Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục) */
  async rankByMinPrice(object: any) {
    await this.loadPrice(object.id)
    this.isVisible1 = true
    this.bidChoose = object
  }

  /** Xếp hạng theo tổng giá (Giá theo từng NCC) */
  async rankBySumPrice(object: any) {
    await this.loadPrice(object.id)
    this.isVisible2 = true
    this.bidChoose = object
  }

  hidePopupAnalysPrice() {
    this.isVisible1 = this.isVisible2 = false
    this.bidChoose = undefined
  }

  analysisData(item: any) {
    let lstBidPriceId = item.listPrice.filter((c: any) => c.isChoose).map((c: any) => c.id)
    if (lstBidPriceId.length == 0) lstBidPriceId = item.listPrice.map((c: any) => c.id)

    if (this.isVisible1) {
      this.dialog.open(BidRankByMinPriceComponent, {
        disableClose: false,
        data: { bid: item, lstId: lstBidPriceId },
      })
    } else {
      this.dialog.open(BidRankBySumPriceComponent, {
        disableClose: false,
        data: { bid: item, lstId: lstBidPriceId },
      })
    }

    this.isVisible1 = this.isVisible2 = false
  }

  onChangeChooseAll(item: any) {
    for (const price of item.listPrice) {
      price.isChoose = item.isChooseAll
    }
  }

  //#endregion

  //#region Báo cáo kết quả đánh giá

  /** Báo cáo kết quả đánh giá */
  reportRateBid(object: any) {
    this.dialog.open(BidRateReportComponent, { data: object.id })
  }
  //#endregion

  //#region Hiệu chỉnh bảng giá

  /** Cấu hình lại bảng giá */

  bidResetPrice(object: any) {
    if (
      object.statusResetPrice == enumData.BidResetPriceStatus.ChuaTao.code ||
      object.statusResetPrice == enumData.BidResetPriceStatus.KetThuc.code
    ) {
      this.modal.confirm({
        nzTitle: '<i>Bạn có chắc muốn cấu hình lại bảng giá?</i>',
        nzContent: `
        <b>Khi xác nhận, hệ thống sẽ xóa thông tin chào giá, đấu giá và đàm phán giá mà các doanh nghiệp đã nộp.</b><br>
        <b>Bạn cần chắc chắn các thay đổi trước khi xác nhận.</b>`,
        nzOnOk: () => {
          // call api thay đổi trạng thái gói thầu về lúc đánh giá giá và xóa thông tin các NCC nộp chào giá cũ

          let check = this.authenticationService.checkPermission([this.enumRole008], this.action.Update.code)
          if (check) {
            this.notifyService.showloading()
            this.apiService.put(this.apiService.BID.RESET_PRICE(object.id), {}).then((res) => {
              this.notifyService.hideloading()
              this.dialog
                .open(BidResetPriceComponent, { data: object })
                .afterClosed()
                .subscribe((res) => {
                  if (res) this.searchData()
                })
            })
          } else {
            this.notifyService.showWarning('Bạn không có quyền để thực hiện chức năng này')
          }
        },
      })
    } else {
      this.dialog
        .open(BidResetPriceComponent, { data: object })
        .afterClosed()
        .subscribe((res) => {
          if (res) this.searchData()
        })
    }
  }

  /** Kết thúc nộp chào giá hiệu chỉnh */
  bidEndResetPrice(object: any) {
    if (object.status == enumData.BidStatus.DangDanhGia.code && object.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code) {
      this.notifyService.showloading()
      // api lấy ds các ncc đã nộp chào giá hiệu chỉnh
      this.apiService.get(this.apiService.BID.BID_SUPPLIER_JOIN_RESET_PRICE(object.id), {}).then((res) => {
        this.notifyService.hideloading()

        let isShowWarning = false
        if (object.resetPriceEndDate) {
          const todate = new Date()
          const endDate = new Date(object.resetPriceEndDate)
          if (endDate > todate) {
            isShowWarning = true
          }
        }

        this.modal.confirm({
          nzTitle: '<i>Bạn có chắc muốn kết thúc nộp chào giá hiệu chỉnh?</i>',
          nzContent:
            `
          <span>Đã có ${res.length} doanh nghiệp nộp chào giá hiệu chỉnh.</span><br>
          <span>Khi xác nhận, hệ thống sẽ cho phép đánh giá lại thông tin chào giá mới và <b>ngưng nhận nộp chào giá hiệu chỉnh từ các doanh nghiệp.</b></span><br>` +
            (isShowWarning ? `<b>Gói thầu chưa hết hạn nộp chào giá hiệu chỉnh.</b><br>` : ``) +
            `<span>Bạn cần chắc chắn các thay đổi trước khi xác nhận.</span>`,
          nzOnOk: () => {
            this.notifyService.showloading()
            this.apiService.put(this.apiService.BID.END_RESET_PRICE(object.id), {}).then((res) => {
              this.notifyService.hideloading()
              this.searchData()
            })
          },
        })
      })
    }
  }

  //#endregion

  //#region Phê duyệt kết thúc thầu

  /** In hồ sơ thầu */

  /** In hồ sơ thầu */
  bidPrint(object: any) {
    this.dialog.open(BidResultPrintComponent, { data: object })
  }

  /** Gửi yêu cầu phê duyệt kết thúc thầu */
  sendRequestFinishBid(object: any) {
    this.bidChoose = object
    if (object.status == enumData.BidStatus.DuyetNCCThangThau.code && object.isMPO) {
      this.isShowPopupRequestFinishBid = true
      this.modalTitle = `Gửi yêu cầu phê duyệt kết thúc gói thầu [${this.bidChoose.name}]`
    }
  }

  /** Phê duyệt kết thúc thầu */
  approveFinishBid(object: any) {
    this.bidChoose = object
    if (object.status == enumData.BidStatus.DangDuyetKetThucThau.code && object.isMPOLeader) {
      this.isShowPopupRequestFinishBid = true
      this.modalTitle = `Phê duyệt kết thúc gói thầu [${this.bidChoose.name}]`
    }
  }

  /** Đóng popup Gửi yêu cầu phê duyệt kết thúc thầu/Phê duyệt kết thúc thầu */
  hidePopupRequestFinishBid() {
    this.isShowPopupRequestFinishBid = false
    this.bidChoose = undefined
  }

  /** Gửi phê duyệt kết thúc thầu */
  onSendRequestFinishBid() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BID_RATE.SEND_REQUEST_FINISH_BID, {
        id: this.bidChoose.id,
        fileScan: this.bidChoose.fileScan,
        noteFinishBidMPO: this.bidChoose.noteFinishBidMPO,
      })
      .then((res) => {
        this.notifyService.hideloading()
        this.hidePopupRequestFinishBid()
        this.searchData()
      })
  }

  /** Phê duyệt kết thúc thầu */
  onApproveRequestFinishBid() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID_RATE.APPROVE_FINISH_BID, { id: this.bidChoose.id }).then((res) => {
      this.notifyService.hideloading()
      this.hidePopupRequestFinishBid()
      this.searchData()
    })
  }

  async handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) {
          this.bidChoose[fieldName] = res[0]
        }
      })
    }
  }
  //#endregion

  //#region Biên bản mở thầu
  showProtocolOpenBid(item: any) {
    this.bidChoose = item
    this.isShowProtocolOpenBid = true
  }

  hidePopupOpenBid() {
    this.isShowProtocolOpenBid = false
    this.bidChoose = undefined
  }
  //#endregion

  //#region Sao chép gói thầu
  clickCopy(bid: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.COPY_BID, { bidId: bid.id }).then((res) => {
      if (res) {
        this.notifyService.showSuccess(res.message)
        this.searchData()
      }
    })
  }
  //#endregion

  //#region Hủy gói thầu
  clickDelete(bid: any) {
    this.notifyService.showloading()
    this.apiService.put(this.apiService.BID.REQUEST_DELETE_BID(bid.id), {}).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.searchData()
    })
  }
  clickApproveDelete(bid: any) {
    this.notifyService.showloading()
    this.apiService.put(this.apiService.BID.DELETE_BID(bid.id), {}).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.searchData()
    })
  }
  //#endregion
}
