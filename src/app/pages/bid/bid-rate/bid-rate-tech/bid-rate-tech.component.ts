import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { BidRateTechDetailComponent } from './bid-rate-tech-detail/bid-rate-tech-detail.component'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-rate-tech.component.html' })
export class BidSupplierTechRateComponent implements OnInit {
  modalTitle = 'Thông tin đánh giá yêu cầu kỹ thuật các doanh nghiệp dự thầu'
  pageSize = enumData.Page.pageSizeMax
  dataObject: any = {}
  loading = false
  isNeedConfirm = false
  isNeedApprove = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidSupplierTechRateComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    const lstStatus1 = [enumData.BidTechRateStatus.DangTao.code, enumData.BidTechRateStatus.TuChoi.code]

    this.isNeedApprove = this.data.isTechLeader && this.data.statusRateTech === enumData.BidTechRateStatus.DaTao.code
    this.isNeedConfirm = this.data.isTech && lstStatus1.includes(this.data.statusRateTech)

    this.loadData()

    this.enumRole = this.enumProject.Features.BID_005.code
    this.action = this.enumProject.Action
  }

  /** Hàm lấy ds ncc tham gia thầu và tính điểm */
  loadData() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.BID_RATE.LOAD_TECH_RATE(this.data.id), {}).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  viewDetail(item: any, data: any) {
    const itemName = this.dataObject.listItem.map((obj: any) => obj.itemName);
    const object = {
      data,
      template: item.lstBidTech,
      isNeedConfirm: this.isNeedConfirm,
      isNeedApprove: this.isNeedApprove,
      itemName
    }
    this.dialog.open(BidRateTechDetailComponent, { disableClose: false, data: object })
  }

  createBidTechRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID_RATE.CREATE_TECH_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }

  approveBidTechRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID_RATE.APPROVE_TECH_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }

  rejectBidTechRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID_RATE.REJECT_TECH_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }
}
