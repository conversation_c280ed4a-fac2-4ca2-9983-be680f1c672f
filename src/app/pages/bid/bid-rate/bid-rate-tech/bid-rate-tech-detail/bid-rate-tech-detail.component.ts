import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { BidSupplierTechDetailComponent } from '../../../bid-detail/bid-supplier-tech-detail/bid-supplier-tech-detail.component'
import { NzDrawerService } from 'ng-zorro-antd/drawer'

@Component({ templateUrl: './bid-rate-tech-detail.component.html' })
export class BidRateTechDetailComponent implements OnInit {
  modalTitle = 'Bảng chi tiết đánh giá yêu cầu kỹ thuật'
  loading = false
  listOfData: any[] = []
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidRateTechDetailComponent>,
    private drawerService: NzDrawerService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadBestData()
  }

  loadBestData() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BID_RATE.LOAD_BEST_TECH_VALUE, { bidId: this.data.data.bidId, bidSupplierId: this.data.data.id })
      .then((res) => {
        this.listOfData = res
        this.notifyService.hideloading()
      })
  }

  onValidBidSupplierTech() {
    this.data.data.isTechValid = true
    this.closeDialog(this.data)
  }

  onInvalidBidSupplierTech() {
    this.data.data.isTechValid = false
    this.closeDialog(this.data)
  }

  showModalBidSupplierTechDetail(bidTech: any) {
    this.drawerService.create({
      nzTitle: `Bảng xếp hạng đánh giá các NCC tiêu chí [${bidTech.name}]`,
      nzContent: BidSupplierTechDetailComponent,
      nzContentParams: { bidTechId: bidTech.id },
      nzWidth: '640',
    })
  }

  closeDialog(data: any) {
    this.dialogRef.close(data)
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('tech-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng đánh giá năng lực, yêu cầu kỹ thuật Item [${
        this.data.itemName
      }] của nhà thầu ${this.data.data.supplierName}.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
