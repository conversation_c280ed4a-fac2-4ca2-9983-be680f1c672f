import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { BidSupplierTradeDetailComponent } from '../../../bid-detail/bid-supplier-trade-detail/bid-supplier-trade-detail.component'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { NzDrawerService } from 'ng-zorro-antd/drawer'

@Component({ templateUrl: './bid-rate-trade-detail.component.html' })
export class BidRateTradeDetailComponent implements OnInit {
  modalTitle = 'Bảng chi tiết đánh giá điều kiện thương mại'
  loading = false
  listOfData: any[] = []
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidRateTradeDetailComponent>,
    private drawerService: NzDrawerService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadBestData()
  }

  loadBestData() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BID_RATE.LOAD_BEST_TRADE_VALUE, { bidId: this.data.data.bidId, bidSupplierId: this.data.data.id })
      .then((res) => {
        this.listOfData = res
        this.notifyService.hideloading()
      })
  }

  onValidBidSupplierTrade() {
    this.data.data.isTradeValid = true
    this.closeDialog(this.data)
  }

  onInvalidBidSupplierTrade() {
    this.data.data.isTradeValid = false
    this.closeDialog(this.data)
  }

  showModalBidSupplierTradeDetail(bidTrade: any) {
    this.drawerService.create({
      nzTitle: `Bảng xếp hạng đánh giá các NCC tiêu chí [${bidTrade.name}]`,
      nzContent: BidSupplierTradeDetailComponent,
      nzContentParams: { bidTradeId: bidTrade.id },
      nzWidth: '640',
    })
  }

  closeDialog(data: any) {
    this.dialogRef.close(data)
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('trade-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng đánh giá điều kiện thương mại của nhà thầu ${
        this.data.data.supplierName
      }.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
