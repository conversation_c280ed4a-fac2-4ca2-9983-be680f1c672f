<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="dataObject.id">
  <nz-collapse nzBordered="false" *ngFor="let item of dataObject.listItem">
    <nz-collapse-panel [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite">
      <nz-row>
        <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="item.lstBidSupplier" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>{{ language_key?.INTERPRISE || 'doanh nghiệp' }}</th>
              <th class="text-nowrap">Trạng th<PERSON><PERSON> hồ sơ</th>
              <th><PERSON><PERSON><PERSON><PERSON></th>
              <th class="text-nowrap"><PERSON><PERSON><PERSON><PERSON> HĐXT ĐKTM</th>
              <th class="text-nowrap">Trạng thái đánh giá</th>
              <th class="text-nowrap">Xác nhận hợp lệ</th>
              <th>Ghi chú người đánh giá</th>
              <th>Ghi chú người duyệt</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataRow of item.lstBidSupplier">
              <td class="w-25" (click)="viewDetail(item, dataRow)">{{ dataRow.supplierName }}</td>
              <td (click)="viewDetail(item, dataRow)">{{ dataRow.statusFileName }}</td>
              <td (click)="viewDetail(item, dataRow)" class="text-right text-nowrap">
                {{ dataRow.scoreTrade | number: '1.0-2' }} - {{ dataRow.rankABCD }}
              </td>
              <td>
                <input nz-input [(ngModel)]="dataRow.scoreManualTrade" type="number" min="0" max="100"
                  [disabled]="!isNeedConfirm" />
                <span class="text-danger text-nowrap"
                  *ngIf="dataRow.scoreManualTrade < 0 || dataRow.scoreManualTrade > 100">Vui lòng chấm
                  điểm từ 0 đến 100</span>
              </td>
              <td (click)="viewDetail(item, dataRow)">{{ dataRow.statusTradeName }}</td>
              <td>
                <label nz-checkbox [(ngModel)]="dataRow.isTradeValid" name="isTradeValid" [disabled]="!isNeedConfirm">
                  Hợp lệ
                </label>
              </td>
              <td class="w-25">
                <textarea nz-input rows="1" auto [(ngModel)]="dataRow.noteTrade" [disabled]="!isNeedConfirm"></textarea>
              </td>
              <td class="w-25">
                <textarea nz-input rows="1" auto [(ngModel)]="dataRow.noteMPOLeader"
                  [disabled]="!isNeedApprove"></textarea>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="createTradeRate()" class="ant-btn-blue mr-2" *ngIf="isNeedConfirm && authenticationService.checkPermission([enumRole], action.Update.code)">
      Gửi yêu cầu phê duyệt kết quả đánh giá điều kiện thương mại
    </button>
    <button nz-button (click)="approveTradeRate()" class="ant-btn-blue mr-2" *ngIf="isNeedApprove && authenticationService.checkPermission([enumRole], action.Update.code)">
      Duyệt kết quả đánh giá
    </button>
    <button nz-button (click)="rejectTradeRate()" nzDanger *ngIf="isNeedApprove && authenticationService.checkPermission([enumRole], action.Update.code)">
      Yêu cầu kiểm tra lại
    </button>
  </nz-col>
</nz-row>