import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../services'

@Component({ templateUrl: './bid-rank-by-min-price.component.html' })
export class BidRankByMinPriceComponent implements OnInit {
  lstTitleLv1: any[] = []
  pageSize = enumData.Page.pageSizeMax
  listOfData: any[] = []
  bidPriceCol: any[] = []
  todate = new Date()
  isShowPriceDiff = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public dialogRef: MatDialogRef<BidRankByMinPriceComponent>,
    public dialog: MatDialog,
    @Optional()
    @Inject(MAT_DIALOG_DATA)
    public data: {
      bid: any
      lstId: string[]
    },
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_006.code
  }

  searchData() {
    const dataFilter = {
      bidId: this.data.bid.id,
      lstId: this.data.lstId,
    }
    this.apiService.post(this.apiService.BID_RATE.LOAD_RANK_BY_MIN_PRICE, dataFilter).then((res) => {
      this.listOfData = res[0]
      this.bidPriceCol = res[1]
      this.lstTitleLv1 = res[2]
    })
  }

  clickExportExcel() {
    const tbl = document.getElementById('test-html-table')
    const wb = XLSX.utils.table_to_book(tbl)

    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng phân tích theo giá thấp nhất của từng hạng mục gói thầu ${
      this.data.bid.name
    }.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
