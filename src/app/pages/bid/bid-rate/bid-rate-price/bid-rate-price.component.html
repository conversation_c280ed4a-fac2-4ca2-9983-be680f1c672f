<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="dataObject.id">
  <nz-collapse nzBordered="false" *ngFor="let item of dataObject.listItem">
    <nz-collapse-panel [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite">
      <nz-row>
        <nz-table nz-col nzSpan="24" [nzData]="item.lstBidSupplier" [(nzPageSize)]="pageSize" [nzLoading]="loading"
          [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>{{ language_key?.INTERPRISE || 'Doanh nghiệp' }}</th>
              <th class="text-nowrap">Trạng th<PERSON><PERSON> hồ s<PERSON></th>
              <th><PERSON><PERSON><PERSON><PERSON></th>
              <th class="text-nowrap"><PERSON>i<PERSON><PERSON> HĐXT chào giá</th>
              <th class="text-nowrap">Trạng thái đánh giá</th>
              <th class="text-nowrap">Xác nhận hợp lệ</th>
              <th>Ghi chú người đánh giá</th>
              <th>Ghi chú người duyệt</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataRow of item.lstBidSupplier">
              <td class="w-25" (click)="viewDetail(item, dataRow)">{{ dataRow.supplierName }}</td>
              <td (click)="viewDetail(item, dataRow)">{{ dataRow.statusFileName }}</td>
              <td (click)="viewDetail(item, dataRow)" class="text-right text-nowrap">
                {{ dataRow.scorePrice | number: '1.0-2' }} - {{ dataRow.rankABCD }}
              </td>
              <td>
                <input nz-input [(ngModel)]="dataRow.scoreManualPrice" type="number" min="0" max="100"
                  [disabled]="!isNeedConfirm" />
                <span class="text-danger text-nowrap"
                  *ngIf="dataRow.scoreManualPrice < 0 || dataRow.scoreManualPrice > 100">
                  Vui lòng chấm điểm từ 0 đến 100
                </span>
              </td>
              <td (click)="viewDetail(item, dataRow)">{{ dataRow.statusPriceName }}</td>
              <td>
                <label nz-checkbox [(ngModel)]="dataRow.isPriceValid" name="isPriceValid" [disabled]="!isNeedConfirm">
                  Hợp lệ
                </label>
              </td>
              <td class="w-25">
                <textarea nz-input rows="1" auto [(ngModel)]="dataRow.notePrice" [disabled]="!isNeedConfirm"></textarea>
              </td>

              <td class="w-25">
                <textarea nz-input rows="1" auto [(ngModel)]="dataRow.noteMPOLeader" [disabled]="true"></textarea>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="createBidPriceRate()" class="ant-btn-blue" *ngIf="isNeedConfirm && authenticationService.checkPermission([enumRole], action.Create.code)">
      Gửi yêu cầu phê duyệt kết quả đánh giá chào giá
    </button>
  </nz-col>
</nz-row>