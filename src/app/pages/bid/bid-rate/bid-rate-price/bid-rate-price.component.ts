import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { BidRatePriceDetailComponent } from './bid-rate-price-detail/bid-rate-price-detail.component'

@Component({ templateUrl: './bid-rate-price.component.html' })
export class BidRatePriceComponent implements OnInit {
  modalTitle = 'Thông tin đánh giá bảng chào giá, cơ cấu giá các doanh nghiệp dự thầu'
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataObject: any = {}
  isNeedConfirm = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidRatePriceComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.isNeedConfirm =
      this.data.isMPO &&
      (this.data.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code ||
        this.data.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code)

    this.loadData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
  }

  /** Hàm lấy ds ncc tham gia thầu và tính điểm */
  loadData() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.BID_RATE.LOAD_PRICE_RATE(this.data.id), {}).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  viewDetail(item: any, data: any) {
    const object = { data, template: item.lstBidPrice, isNeedConfirm: this.isNeedConfirm }
    this.dialog.open(BidRatePriceDetailComponent, { disableClose: false, data: object })
  }

  createBidPriceRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID_RATE.CREATE_PRICE_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }
}
