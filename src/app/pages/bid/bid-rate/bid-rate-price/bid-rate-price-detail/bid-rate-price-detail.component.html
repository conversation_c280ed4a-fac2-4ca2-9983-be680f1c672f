<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-collapse nzBordered="false">
    <nz-collapse-panel nzHeader="Bảng đánh giá bảng chào giá" class="ant-bg-antiquewhite" nzActive="true">
      <nz-row class="mt-2">
        <button nz-button (click)="clickExportExcelPrice()">
          <span nz-icon nzType="download"></span>Xuất excel
        </button>
      </nz-row>
      <nz-row class="mt-1">
        <nz-table nz-col nzSpan="24" id="price-table" [nzData]="listOfData" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>Tên tiêu chí</th>
              <th *ngFor="let col of bidPriceCol"
                [ngClass]="{ 'dynamic-col-mpo': col.colType===mpoType, 'dynamic-col-supplier': col.colType===supType}">
                {{ col.name }}
              </th>
              <th class="text-right">Thông tin cung cấp</th>
              <th class="text-right">Điểm đạt được ({{ data.data.scorePrice | number: '1.0-2' }})</th>
              <th>Xếp hạng</th>
              <th class="text-right">Giá trị thấp nhất</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let data1 of listOfData">
              <tr>
                <td class="mw-25">{{ data1.name }}</td>
                <td *ngFor="let col of bidPriceCol">{{ data1[col.id] }}</td>
                <td class="text-right">{{ data1.value | number }}</td>
                <td class="text-right">{{ data1.score > 0 ? (data1.score | number: '1.0-2') : '' }}</td>
                <td>{{ data1.rank}}</td>
                <td class="text-right">
                  <div *ngIf="data1.bestScore">
                    {{ data1.bestValue > 0 ? (data1.bestValue | number: '1.0-2') : '' }}
                    <span (click)="showModalBidSupplierPriceDetail(data1)" nz-popover
                      [nzPopoverContent]="contentTemplate" nz-icon nzType="info-circle"></span>
                    <ng-template #contentTemplate>
                      <p>NCC: {{ data1.bestSupplierName}}</p>
                      <p>Điểm: {{ data1.bestScore | number: '1.0-2' }}</p>
                    </ng-template>
                  </div>
                </td>
              </tr>
              <ng-container *ngFor="let data2 of data1.__childs__">
                <tr>
                  <td [nzIndentSize]="5" class="mw-25">{{ data2.name }}</td>
                  <td *ngFor="let col of bidPriceCol">{{ data2[col.id] }}</td>
                  <td class="text-right">{{ data2.value | number }}</td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
                <ng-container *ngFor="let data3 of data2.__childs__">
                  <tr>
                    <td [nzIndentSize]="30" class="mw-25">{{ data3.name }}</td>
                    <td *ngFor="let col of bidPriceCol">{{ data3[col.id] }}</td>
                    <td class="text-right">{{ data3.value | number }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                </ng-container>
              </ng-container>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse nzBordered="false" class="mt-3">
    <nz-collapse-panel nzHeader="Bảng đánh giá thông tin cơ cấu giá" class="ant-bg-antiquewhite" nzActive="true">
      <nz-row class="mt-2">
        <button nz-button (click)="clickExportExcelCustomPrice()">
          <span nz-icon nzType="download"></span>Xuất excel
        </button>
      </nz-row>
      <nz-row class="mt-1">
        <nz-table nz-col nzSpan="24" id="custom-price-table" [nzData]="listOfDataCustom" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>Tên hạng mục</th>
              <th>Đơn vị tính</th>
              <th>Đơn vị tiền tệ</th>
              <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
              <th class="text-right">Thông tin cung cấp</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let dataRow of listOfDataCustom">
              <tr>
                <td class="mw-25">{{ dataRow.name }}</td>
                <td class="mw-25">{{ dataRow.unit }}</td>
                <td class="mw-25">{{ dataRow.currency }}</td>
                <td class="mw-25">{{ dataRow.number | number }}</td>
                <td class="text-right">{{ dataRow.value > 0 ? (dataRow.value | number: '1.0-2') : '' }}</td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-row class="mt-3">
    <h4>Điểm HĐXT chào giá:</h4>
    <nz-col nzSpan="24" class="text-center">
      <input nz-input placeholder="Nhập điểm HĐXT chào giá (0-100)" [(ngModel)]="data.data.scoreManualPrice"
        type="number" min="0" max="100" [disabled]="!data.isNeedConfirm" />
      <div class="text-danger text-left" *ngIf="data.data.scoreManualPrice < 0 || data.data.scoreManualPrice > 100">Vui
        lòng chấm điểm từ 0 đến 100</div>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <h4>Ghi chú từ người đánh giá:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.data.notePrice" [disabled]="!data.isNeedConfirm"></textarea>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <h4>Ghi chú từ người duyệt:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.data.noteMPOLeader" [disabled]="true"></textarea>
    </nz-col>
  </nz-row>
</div>

<nz-row matDialogActions *ngIf="data.isNeedConfirm">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button nzType="primary" class="mr-2" (click)="onValidBidSupplierPrice()">
      Xác nhận hợp lệ
    </button>
    <button nz-button nzDanger (click)="onInvalidBidSupplierPrice()">
      Không hợp lệ
    </button>
  </nz-col>
</nz-row>