<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tìm kiếm thông tin gói thầu" [(nzActive)]="isCollapseFilter">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
          [nzLoadData]="loadDataService"> </nz-cascader>
      </nz-col>
      <nz-col nzSpan="6">
        <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo mã số hoặc tên gói thầu" />
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Từ ngày đăng tải">
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày đăng tải">
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="12">
        <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
          [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
          <nz-option *ngFor="let item of listBidStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSpan="12">
        <label nz-checkbox [(ngModel)]="dataSearch.isGetBidNeedApprove" name="isGetBidNeedApprove"> Lấy gói thầu cần
          duyệt </label>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button *ngIf="authenticationService.checkPermission([enumRole4], action.View.code)" nz-button
      (click)="isShowCopyBid = !isShowCopyBid">
      <span nz-icon nzType="copy"></span>
      {{ isShowCopyBid ? 'Tắt sao chép gói thầu' : 'Bật sao chép gói thầu' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Mã TBMT</th>
        <th>Tên gói thầu</th>
        <th>Phụ trách kỹ thuật</th>
        <th>Phụ trách mua hàng</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th *ngIf="showReport">Báo cáo</th>
        <th *ngIf="showAnalysis">Phân tích giá</th>
        <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of ajaxTable.data">
        <td (click)="clickView(item)">{{ item.code }}</td>
        <td class="mw-25" (click)="clickView(item)">{{ item.name }}</td>
        <td class="mw-25" (click)="clickView(item)">{{ item.techName }}</td>
        <td class="mw-25" (click)="clickView(item)">{{ item.mpoName }}</td>
        <td class="text-nowrap" (click)="clickView(item)">{{ item.statusName }}</td>
        <td *ngIf="showReport" class="text-nowrap">
          <button *ngIf="item.isShowReport && authenticationService.checkPermission([enumRole011], action.View.code)"
            nz-tooltip nzTooltipTitle="Báo cáo kết quả đánh giá" (click)="reportRateBid(item)" nz-button
            nzType="primary">
            <span nz-icon nzType="file-text"></span>
          </button>
        </td>
        <td *ngIf="showAnalysis" class="text-nowrap">
          <button *ngIf="item.isShowAnalysis && authenticationService.checkPermission([enumRole6], action.View.code)"
            nz-tooltip nzTooltipTitle="Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)"
            (click)="rankByMinPrice(item)" class="mr-2" nz-button nzType="primary">
            <span nz-icon nzType="deployment-unit"></span>
          </button>

          <button *ngIf="item.isShowAnalysis && authenticationService.checkPermission([enumRole7], action.View.code)"
            nz-tooltip nzTooltipTitle="Xếp hạng theo tổng giá (Giá theo từng NCC)" (click)="rankBySumPrice(item)"
            nz-button class="ant-btn-success">
            <span nz-icon nzType="fork"></span>
          </button>
        </td>
        <td class="text-nowrap">
          <button
            *ngIf="isShowCopyBid && item.isShowCopy && authenticationService.checkPermission([enumRole4], action.View.code)"
            nz-tooltip nzTooltipTitle="Sao chép gói thầu" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn sao chép gói thầu này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickCopy(item)" class="mr-2" nz-button nzType="dashed">
            <span nz-icon nzType="copy"></span>
          </button>
          <button *ngIf="item.isShowDelete && authenticationService.checkPermission([enumRole], action.Delete.code)"
            nz-tooltip nzTooltipTitle="Hủy gói thầu" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn hủy gói thầu này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickDelete(item)" class="mr-2" nz-button nzDanger>
            <span nz-icon nzType="delete"></span>
          </button>
          <button
            *ngIf="item.isShowApproveDelete && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Phê duyệt hủy gói thầu" nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn phê duyệt hủy gói thầu này?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickApproveDelete(item)" class="mr-2" nz-button nzType="primary" nzDanger>
            <span nz-icon nzType="delete"></span>
          </button>
          <button *ngIf="item.isShowOpenBid && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Mở thầu" (click)="openBid(item)" nz-button nzType="primary">
            <span nz-icon nzType="audit"></span>
          </button>
          <button
            *ngIf="item.isShowProtocolOpenBid && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Biên bản mở thầu" (click)="showProtocolOpenBid(item)" class="mr-2" nz-button
            nzType="dashed">
            <span nz-icon nzType="profile"></span>
          </button>

          <button
            *ngIf="item.isShowEndResetPrice && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Kết thúc nộp chào giá hiệu chỉnh" (click)="bidEndResetPrice(item)" class="mr-2"
            nz-button nzDanger>
            <span nz-icon nzType="check-square"></span>
          </button>

          <button *ngIf="item.isShowTechRate && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Đánh giá năng lực, kỹ thuật" (click)="bidSupplierTechRate(item)"
            class="mr-2 ant-btn-{{ item.techType }}" nz-button>
            <span nz-icon nzType="robot"></span>
          </button>
          <button *ngIf="item.isShowPriceRate && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Đánh giá bảng chào giá, cơ cấu giá" (click)="bidSupplierPriceRate(item)"
            class="mr-2 ant-btn-{{ item.priceType }}" nz-button>
            <span nz-icon nzType="dollar"></span>
          </button>
          <button *ngIf="item.isShowTradeRate && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Đánh giá điều kiện thương mại" (click)="bidSupplierTradeRate(item)"
            class="mr-2 ant-btn-{{ item.tradeType }}" nz-button>
            <span nz-icon nzType="shopping"></span>
          </button>

          <!-- <button *ngIf="item.isShowResetPrice && authenticationService.checkPermission([enumRole], action.View.code)"
            nz-tooltip nzTooltipTitle="Cấu hình lại bảng giá" (click)="bidResetPrice(item)" class="mr-2" nz-button
            nzDanger>
            <span nz-icon nzType="rollback"></span>
          </button> -->

          <!-- <button
            *ngIf="item.isShowEnd && item.isAutoBid && authenticationService.checkPermission([enumRole013], action.View.code)"
            nz-tooltip nzTooltipTitle="Tự động chọn NCC thắng thầu và kết thúc thầu" (click)="bidAutoChoose(item)"
            class="mr-2" nz-button nzType="primary">
            <span nz-icon nzType="fast-forward"></span>
          </button> -->

          <button *ngIf="item.isShowEnd && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Chọn NCC thắng thầu" (click)="bidEvaluation(item)" class="mr-2" nz-button
            nzType="primary">
            <span nz-icon nzType="audit"></span>
          </button>
          <button *ngIf="item.isShowAcceptEnd && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Phê duyệt NCC thắng thầu" (click)="bidEvaluation(item)" class="mr-2" nz-button
            nzType="primary">
            <span nz-icon nzType="audit"></span>
          </button>
          <button
            *ngIf="item.isShowDealAuction && authenticationService.checkPermission([enumRole009, enumRole010], action.View.code)"
            nz-tooltip nzTooltipTitle="Đàm phán / Đấu giá" (click)="bidDealAuction(item)" class="mr-2" nz-button
            nzType="primary">
            <span nz-icon nzType="dollar"></span>
          </button>
          <!-- <button *ngIf="item.isShowDeal && authenticationService.checkPermission([enumRole009], action.View.code)"
            nz-tooltip nzTooltipTitle="Đàm phán giá" (click)="bidDeal(item)" class="mr-2" nz-button nzType="primary"
            >
            <span nz-icon nzType="bank"></span>
          </button>
          <button *ngIf="item.isShowAuction && authenticationService.checkPermission([enumRole010], action.View.code)"
            nz-tooltip nzTooltipTitle="Đấu giá" (click)="bidAuction(item)" class="mr-2" nz-button nzType="primary"
            >
            <span nz-icon nzType="dollar"></span>
          </button> -->
          <button *ngIf="item.isShowPrint && authenticationService.checkPermission([enumRole], action.Print.code)"
            nz-tooltip nzTooltipTitle="In hồ sơ thầu" (click)="bidPrint(item)" class="mr-2" nz-button nzType="dashed">
            <span nz-icon nzType="printer"></span>
          </button>

          <button
            *ngIf="item.isShowSendRequestFinishBid && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Gửi yêu cầu phê duyệt kết thúc thầu" (click)="sendRequestFinishBid(item)"
            class="mr-2" nz-button nzType="primary">
            <span nz-icon nzType="right-circle"></span>
          </button>

          <button
            *ngIf="item.isShowApproveFinishBid && authenticationService.checkPermission([enumRole], action.Update.code)"
            nz-tooltip nzTooltipTitle="Phê duyệt kết thúc thầu" (click)="approveFinishBid(item)"
            class="ant-btn-blue mr-2" nz-button>
            <span nz-icon nzType="check-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>

<!-- Chọn hạng mục giá cần phân tích -->
<nz-modal [nzVisible]="isVisible1 || isVisible2" [nzWidth]="'70vw'" nzTitle="Chọn hạng mục giá cần phân tích"
  (nzOnCancel)="hidePopupAnalysPrice()" *ngIf="(isVisible1 || isVisible2) && bidChoose" [nzFooter]="modalFooter">
  <ng-container *nzModalContent>
    <nz-collapse nzBordered="false" *ngFor="let item of bidPriceMinListItem.listItem" class="mt-2">
      <nz-collapse-panel [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite" [nzExtra]='extraTpl'>
        <nz-row class="mt-2">
          <nz-col nzSpan="24">
            <ng-template #extraTpl>
              <button nz-button nzType="primary" (click)="analysisData(item)">
                Phân tích
              </button>
            </ng-template>
          </nz-col>
          <nz-table nz-col nzSpan="24" [nzData]="item.listPrice" [nzPageSize]="20" nzBordered
            [nzShowPagination]="false">
            <thead>
              <tr>
                <th nzWidth="50px">
                  <label nz-checkbox [(ngModel)]="item.isChooseAll" (ngModelChange)="onChangeChooseAll(item)"></label>
                </th>
                <th nzWidth="100px">{{ language_key?.NO || 'STT' }}</th>
                <th nzWidth="300px">Tên hạng mục</th>
                <th nzWidth="130px" *ngFor="let col of item.listPriceCol" class="dynamic-col-mpo">{{ col.name }}</th>
                <th nzWidth="120px">Đơn vị tính</th>
                <th nzWidth="150px">Đơn vị tiền tệ</th>
                <th nzWidth="120px">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th nzWidth="120px">Bắt buộc?</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let data1 of item.listPrice">
                <tr>
                  <td>
                    <label nz-checkbox [(ngModel)]="data1.isChoose"></label>
                  </td>
                  <td>{{ data1.sort > 0 ? data1.sort : '' }}</td>
                  <td class="mw-25">{{ data1.name }}</td>
                  <td *ngFor="let col of item.listPriceCol">{{ data1[col.id] }}</td>
                  <td>{{ data1.unit }}</td>
                  <td>{{ data1.currency }}</td>
                  <td class="text-right">{{ data1.number | number }}</td>
                  <td>{{ data1.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                </tr>
              </ng-container>
            </tbody>
          </nz-table>
          <i>* Lưu ý: Khi không chọn hạng mục nào thì mặc định là tất cả</i>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>
  </ng-container>
</nz-modal>


<ng-template #modalFooter>
  <nz-row class="mt-2" nzJustify="center">

    <button nz-button (click)="hidePopupAnalysPrice()">Đóng</button>
  </nz-row>
</ng-template>

<!-- Gửi yêu cầu phê duyệt kết thúc thầu/Phê duyệt kết thúc thầu -->
<nz-modal [(nzVisible)]="isShowPopupRequestFinishBid" [nzWidth]="'50vw'" [nzTitle]="modalTitle"
  (nzOnCancel)="hidePopupRequestFinishBid()" [nzFooter]="null" *ngIf="bidChoose && isShowPopupRequestFinishBid">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>File scan:</h4>
      <nz-col nzSpan="24">
        <label for="fileScan" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
        <input *ngIf="bidChoose.isShowSendRequestFinishBid" class="hidden" type="file" name="fileScan" id="fileScan"
          (change)="handleFileInput($event, 'fileScan')" />
        <div class="tooltip" *ngIf="bidChoose.fileScan?.length > 0">
          <a href="{{ bidChoose.fileScan }}" target="_blank"> Xem file </a>
        </div>
        <div class="tooltip" *ngIf="!bidChoose.fileScan">
          <i>Không có file</i>
        </div>
      </nz-col>
    </nz-row>
    <nz-row class="mt-3">
      <h4>{{ language_key?.NOTE || 'Ghi chú' }} :</h4>
      <nz-col nzSpan="24">
        <textarea nz-input rows="5" auto placeholder="Nhập nội dung ghi chú thêm"
          [(ngModel)]="bidChoose.noteFinishBidMPO" [disabled]="bidChoose.isShowApproveFinishBid"></textarea>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn gửi yêu cầu phê duyệt kết thúc thầu?"
          nzPopconfirmPlacement="bottom" (nzOnConfirm)="onSendRequestFinishBid()" nz-button nzType="primary"
          *ngIf="bidChoose.isShowSendRequestFinishBid && authenticationService.checkPermission([enumRole], action.Update.code)">
          Gửi phê duyệt kết thúc thầu
        </button>
        <button class="ant-btn-blue" nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn phê duyệt kết thúc thầu?"
          nzPopconfirmPlacement="bottom" (nzOnConfirm)="onApproveRequestFinishBid()" nz-button
          *ngIf="bidChoose.isShowApproveFinishBid && authenticationService.checkPermission([enumRole], action.Update.code)">
          Phê duyệt kết thúc thầu
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>

<!-- Biên bản mở thầu -->
<nz-modal [(nzVisible)]="isShowProtocolOpenBid" [nzWidth]="'800px'" nzTitle="Biên bản mở hồ sơ thầu"
  (nzOnCancel)="hidePopupOpenBid()" [nzFooter]="null" *ngIf="isShowProtocolOpenBid && bidChoose">
  <ng-container *nzModalContent>
    <app-protocol-open-bid [data]="{ id: bidChoose.id }"></app-protocol-open-bid>
  </ng-container>
</nz-modal>