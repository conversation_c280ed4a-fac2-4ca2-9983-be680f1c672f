<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" #itemTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th nzWidth="50px">STT</th>
          <th>LVMH</th>
          <th>Số lượng</th>
          <th>Trạng thái</th>
          <th>Tùy chọn</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of itemTable.data; let i = index">
          <td class="text-center">{{ i + 1 + (pageIndex - 1)*pageSize }}</td>
          <td>{{ item.itemName }}</td>
          <td>{{ item.quantityItem | number: '1.0-2' }}</td>
          <td>
            <nz-tag class="tag-status" [nzColor]="item.statusColor"> {{ item.statusName}}</nz-tag>
          </td>
          <td class="text-nowrap">
            <button *ngIf="authenticationService.checkPermission([enumRole009], action.View.code)" nz-tooltip
              nzTooltipTitle="Đàm phán giá" (click)="bidDeal(item)" class="mr-2" nz-button nzType="primary">
              <span nz-icon nzType="bank"></span>
            </button>
            <button *ngIf="authenticationService.checkPermission([enumRole010], action.View.code)" nz-tooltip
              nzTooltipTitle="Đấu giá" (click)="bidAuction(item)" class="mr-2" nz-button nzType="primary">
              <span nz-icon nzType="dollar"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-row>
</div>