import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs/internal/Subscription'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { BidAuctionComponent } from './bid-auction/bid-auction.component'
import { BidDealComponent } from './bid-deal/bid-deal.component'

@Component({ templateUrl: './bid-item.component.html' })
export class BidItemComponent implements OnInit {
  modalTitle = 'Đàm phán/ đấu giá'
  total = enumData.Page.total
  pageSize = enumData.Page.pageSize
  pageIndex = enumData.Page.pageIndex
  loading = false
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  action: any
  enumRole009: any
  enumRole010: any

  constructor(
    private coreService: CoreService,
    private notifyService: NotifyService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { id: string; name: string },
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole009 = this.enumProject.Features.BID_009.code
    this.enumRole010 = this.enumProject.Features.BID_010.code

    this.modalTitle += ` gói thầu [${this.data.name}]`
    this.searchData(true)
  }

  searchData(reset = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    const dataSearch: any = {
      where: { bidId: this.data.id },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.BID_RATE.ITEM_PAGINATION, dataSearch).then((res) => {
      if (res) {
        this.listOfData = res[0]
        this.total = res[1]
        this.loading = false
      }
    })
  }

  checkStatus(status: string) {
    if (status == enumData.BidStatus.DangDamPhanGia.code) {
      this.notifyService.showWarning(`Item ${enumData.BidStatus.DangDamPhanGia.name}, không thể đàm phán/ đấu giá.`)
      return false
    }
    if (status == enumData.BidStatus.DangDauGia.code) {
      this.notifyService.showWarning(`Item ${enumData.BidStatus.DangDauGia.name}, không thể đàm phán/ đấu giá.`)
      return false
    }
    if (status == enumData.BidStatus.DongDauGia.code) {
      this.notifyService.showWarning(`Item ${enumData.BidStatus.DongDauGia.name}, không thể đàm phán/ đấu giá.`)
      return false
    }
    return true
  }

  bidDeal(item: { id: string; status: string; itemName: string }) {
    if (!this.checkStatus(item.status)) return
    this.dialog
      .open(BidDealComponent, { data: { id: item.id, itemName: item.itemName, bidName: this.data.name } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidAuction(item: { id: string; status: string; itemName: string }) {
    if (!this.checkStatus(item.status)) return
    this.dialog
      .open(BidAuctionComponent, { data: { id: item.id, itemName: item.itemName, bidName: this.data.name } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }
}
