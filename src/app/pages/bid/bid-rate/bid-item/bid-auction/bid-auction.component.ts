import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidResultDetailComponent } from '../../../bid-detail/bid-result/bid-result-detail/bid-result-detail.component'
import { BidSupplierPriceDetailComponent } from '../../../bid-detail/bid-supplier-price-detail/bid-supplier-price-detail.component'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs/internal/Subscription'
import { BidHistoryPriceComponent } from '../../../bid-detail/bid-result/bid-history-price/bid-history-price.component'

@Component({ templateUrl: './bid-auction.component.html' })
export class BidAuctionComponent implements OnInit {
  modalTitle = 'Đấu giá'
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataStatus = this.coreService.convertObjToArray(enumData.BidSupplierFileStatus)

  dataPrices: any
  listOfData: any[] = []

  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  isChooseAll = false
  dataObject: any = {
    bidId: '',
    endDate: new Date(),
  }
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public dialogRef: MatDialogRef<BidAuctionComponent>,
    public dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { id: string; itemName: string; bidName: string },
    private drawerService: NzDrawerService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.modalTitle += ` Item [${this.data.itemName}] - [${this.data.bidName}]`
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_010.code

    this.loadData()
  }

  loadData() {
    this.dataObject.bidId = this.data.id
    this.notifyService.showloading()
    this.loading = true
    Promise.all([
      this.apiService.get(this.apiService.BID_AUCTION.GET_PRICE(this.data.id), {}),
      this.apiService.post(this.apiService.BID_AUCTION.LOAD_SUPPLIER_DATA, { bidId: this.data.id }),
    ]).then((res) => {
      this.loading = false
      this.dataPrices = res[0]
      this.listOfData = res[1]
      this.notifyService.hideloading()
    })
  }

  onSave() {
    this.notifyService.showloading()
    const lstSupplierChoose = this.listOfData.filter((c: any) => c.isChoose)
    if (lstSupplierChoose.length === 0) {
      this.notifyService.showWarning(`Vui lòng chọn nhà cung cấp tham gia đấu giá.`)
      return
    }
    if (lstSupplierChoose.length === 1) {
      this.notifyService.showError('Vui lòng chọn nhiều hơn một nhà cung cấp!')
      return
    }
    if (!this.dataObject.endDate) {
      this.notifyService.showWarning('Vui lòng chọn thời điểm kết thúc đấu giá!')
      return
    }

    const lstPrice: any[] = []
    for (const item of this.dataPrices) {
      lstPrice.push({
        bidPriceId: item.id,
        maxPrice: item.maxPrice,
      })
    }

    this.apiService
      .post(this.apiService.BID_AUCTION.SAVE_BID_AUCTION, {
        ...this.dataObject,
        lstSupplierChoose,
        lstPrice,
      })
      .then((result) => {
        this.notifyService.hideloading()
        if (!result?.error) {
          this.closeDialog(true)
          this.notifyService.showSuccess(result.message)
        }
      })
  }

  // Chọn tất cả
  checkAll() {
    for (const item of this.listOfData) {
      item.isChoose = this.isChooseAll
    }
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  /** Chi tiết nhà cung cấp */
  showDetail(data: { bidId: string; supplierId: string; supplierName: string }) {
    this.drawerService.create({
      nzTitle: (this.language_key?.INTERPRISE_DETAIL || 'Chi tiết nhà cung cấp') + ` ${data.supplierName}`,
      nzContent: BidResultDetailComponent,
      nzContentParams: data,
      nzWidth: '640',
    })
  }

  /** Lịch sử nộp giá nhà cung cấp */
  showHistory(data: { bidId: string; supplierId: string; supplierName: string }) {
    this.drawerService.create({
      nzTitle: `Lịch sử nộp giá nhà cung cấp ${data.supplierName}`,
      nzContent: BidHistoryPriceComponent,
      nzContentParams: data,
      nzWidth: '640',
    })
  }

  showValue(bidPriceId: string) {
    this.drawerService.create({
      nzTitle: 'Giá trị hạng mục theo nhà cung cấp',
      nzContent: BidSupplierPriceDetailComponent,
      nzContentParams: { bidPriceId },
      nzWidth: '640',
    })
  }

  //#region excel

  clickExportExcel() {
    const tbl = document.getElementById('test-html-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Thông tin đấu giá.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['id', 'name', 'number', 'valueTop1', 'supplierNameTop1', 'value'],
      })

      for (const item of this.dataPrices) {
        const itemExcel = jsonData.find((c: any) => c.id === item.id)
        if (itemExcel) {
          item.maxPrice = itemExcel.value
        }
      }
    }
  }

  //#endregion
}
