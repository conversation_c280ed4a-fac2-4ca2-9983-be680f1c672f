import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidResultDetailComponent } from '../../bid-detail/bid-result/bid-result-detail/bid-result-detail.component'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-evaluation.component.html' })
export class BidEvaluationComponent implements OnInit {
  modalTitle = 'Chọn nhà cung cấp thắng thầu'
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataSearch: any = {}

  language_key: any
  subscriptions: Subscription = new Subscription()
  dataObject: any = {}
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public dialogRef: MatDialogRef<BidEvaluationComponent>,
    public dialog: MatDialog,
    @Optional()
    @Inject(MAT_DIALOG_DATA)
    public data: {
      bidId: string
      isShowEnd: boolean
      isShowAcceptEnd: boolean
    },
    private drawerService: NzDrawerService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_005.code
    this.searchData()
  }

  async searchData() {
    this.loading = true
    this.apiService.post(this.apiService.BID_EVALUATION.LOAD_SUPPLIER_DATA, { bidId: this.data.bidId }).then((res) => {
      this.loading = false
      this.dataObject = res
      this.dataObject.isShowEnd = this.data.isShowEnd || false
      this.dataObject.isShowAcceptEnd = this.data.isShowAcceptEnd || false
    })
  }

  async onAcceptBidSupplier() {
    this.notifyService.showloading()
    for (const item of this.dataObject.listItem) {
      const lstSupplierChoose = item.lstBidSupplier.filter((c: any) => c.isChoose)
      if (lstSupplierChoose.length === 0) {
        this.notifyService.showError(`Vui lòng chọn nhà cung cấp trúng thầu cho Item [${item.itemName}].`)
        return
      }
    }
    this.apiService
      .post(this.apiService.BID_EVALUATION.EVALUATION_BID_SUPPLIER, {
        bidId: this.dataObject.id,
        listItem: this.dataObject.listItem,
        comment: this.dataObject.noteCloseBidMPO,
      })
      .then((res) => {
        this.notifyService.showSuccess(res.message)
        this.closeDialog(true)
      })
  }

  async onAccept() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BID_EVALUATION.APPROVE_SUPPLIER_WIN_BID, {
        bidId: this.dataObject.id,
        comment: this.dataObject.noteCloseBidMPOLeader,
      })
      .then((data) => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
  }

  async onReject() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.BID_EVALUATION.REJECT_SUPPLIER_WIN_BID, {
        bidId: this.dataObject.id,
        comment: this.dataObject.noteCloseBidMPOLeader,
      })
      .then((data) => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
  }

  // Chọn tất cả
  checkAll(item: any) {
    for (const bidSup of item.lstBidSupplier) {
      bidSup.isChoose = item.isChooseAll
    }
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  showBidResultDetail(data: { bidId: string; supplierId: string }) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết nhà cung cấp',
      nzContent: BidResultDetailComponent,
      nzContentParams: data,
      nzWidth: '640',
    })
  }
}
