<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="dataObject.id">
  <nz-collapse nzBordered="false" *ngFor="let item of dataObject.listItem" class="mt-2">
    <nz-collapse-panel [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite">
      <nz-row nzGutter="8" class="mt-3">
        <nz-table nz-col nzSpan="24" [nzScroll]="{ x: '1250px' }" [nzData]="item.lstBidSupplier"
          [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th nzLeft nzWidth="50px">
                <label nz-checkbox [(ngModel)]="item.isChooseAll" [nzDisabled]="dataObject.isShowAcceptEnd"
                  (ngModelChange)="checkAll(item)">
                </label>
              </th>
              <th nzWidth="300px">Ghi chú cho NCC thắng thầu</th>
              <th nzWidth="180px">{{ language_key?.INTERPRISE_NAME || 'Tên nhà cung cấp' }}</th>
              <th nzWidth="150px">Tổng điểm</th>
              <th nzWidth="150px">Tổng điểm HĐXT</th>
              <th nzWidth="100px">Thứ hạng</th>
              <th nzWidth="150px">Điểm năng lực</th>
              <th nzWidth="180px">Điểm HĐXT năng lực</th>
              <th nzWidth="150px">Điểm báo giá</th>
              <th nzWidth="170px">Điểm HĐXT báo giá</th>
              <th nzWidth="150px">Điểm ĐKTM</th>
              <th nzWidth="170px">Điểm HĐXT ĐKTM</th>
              <th nzWidth="150px">Trạng thái hồ sơ</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let rowData of item.lstBidSupplier">
              <td nzLeft>
                <label nz-checkbox [(ngModel)]="rowData.isChoose" [nzDisabled]="dataObject.isShowAcceptEnd"></label>
              </td>
              <td>
                <textarea *ngIf="rowData.isChoose" [disabled]="dataObject.isShowAcceptEnd" nz-input rows="1" auto
                  placeholder="Nhập ghi chú cho NCC thắng thầu" [(ngModel)]="rowData.noteSuccessBid">
                </textarea>
              </td>
              <td>
                <span nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Chi tiết"
                  (click)="showBidResultDetail(rowData)"></span>
                {{ rowData.supplierName }}
              </td>
              <td [ngClass]="{'text-right': rowData.scoreTotal !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreTotal) }}
              </td>
              <td [ngClass]="{'text-right': rowData.scoreManualTotal !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreManualTotal) }}
              </td>
              <td>{{ rowData.rank }}</td>
              <td [ngClass]="{'text-right': rowData.scoreTech !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreTech) }}
              </td>
              <td [ngClass]="{'text-right': rowData.scoreManualTech !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreManualTech) }}
              </td>
              <td [ngClass]="{'text-right': rowData.scorePrice !== -1}">
                {{ coreService.scoreRankABCD(rowData.scorePrice) }}
              </td>
              <td [ngClass]="{'text-right': rowData.scoreManualPrice !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreManualPrice) }}
              </td>
              <td [ngClass]="{'text-right': rowData.scoreTrade !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreTrade) }}
              </td>
              <td [ngClass]="{'text-right': rowData.scoreManualTrade !== -1}">
                {{ coreService.scoreRankABCD(rowData.scoreManualTrade) }}
              </td>
              <td>{{ rowData.statusFileName }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-row class="mt-3">
    <h4>Người phụ trách đánh giá:</h4>
    <textarea nz-input rows="2" [disabled]="dataObject.isShowAcceptEnd" auto placeholder="Người phụ trách đánh giá"
      [(ngModel)]="dataObject.noteCloseBidMPO" name="noteCloseBidMPO">
    </textarea>
    <h4 class="mt-3">Người duyệt đánh giá:</h4>
    <textarea nz-input rows="2" [disabled]="dataObject.isShowEnd" auto placeholder="Người duyệt đánh giá"
      [(ngModel)]="dataObject.noteCloseBidMPOLeader" name="noteCloseBidMPOLeader">
    </textarea>
  </nz-row>
</div>

<nz-row matDialogActions *ngIf="dataObject.id">
  <nz-col nzSpan="24" class="text-center">
    <button *ngIf="dataObject.isShowEnd && authenticationService.checkPermission([enumRole], action.Update.code)"
      nz-button nzType="primary" class="mr-2" nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn xác nhận nhà cung cấp thắng thầu?" nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="onAcceptBidSupplier()">
      Xác nhận nhà cung cấp thắng thầu
    </button>
    <button *ngIf="dataObject.isShowAcceptEnd && authenticationService.checkPermission([enumRole], action.Update.code)"
      nz-button nzType="primary" class="mr-2" nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn phê duyệt NCC thắng thầu?" nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="onAccept()">
      Phê duyệt kết quả
    </button>
    <button *ngIf="dataObject.isShowAcceptEnd && authenticationService.checkPermission([enumRole], action.Update.code)"
      nz-button nzType="primary" nzDanger nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn yêu cầu đánh giá và chọn lại NCC thắng thầu?" nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="onReject()">
      Yêu cầu kiểm tra lại
    </button>
  </nz-col>
</nz-row>