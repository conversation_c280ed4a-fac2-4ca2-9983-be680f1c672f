<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<nz-row nzGutter="2" class="px-3 pt-2">
  <nz-col nzSpan="6">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24" nzRequired> Chọn thời điểm kết thúc nộp chào giá hiệu chỉnh </nz-form-label>
      <nz-form-control nzSpan="24" nzErrorTip="Chọn thời điểm kết thúc nộp chào giá hiệu chỉnh">
        <nz-date-picker
          nzFormat="dd-MM-yyyy HH:mm"
          [(ngModel)]="resetPriceEndDate"
          name="resetPriceEndDate"
          [nzShowTime]="{ nzDefaultOpenValue: timeDefaultValue }"
        >
        </nz-date-picker>
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="24">
    <nz-form-item>
      <nz-form-control nzSpan="24">
        <label nz-checkbox [(ngModel)]="isRequireFileTechDetail" name="isRequireFileTechDetail"> Bắt buộc File chi tiết kỹ thuật </label>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
</nz-row>

<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Thông tin các hạng mục chào giá" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row class="mt-3" *ngIf="data.isMPO">
      <nz-col nzSpan="24">
        <button
          class="mr-2"
          *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
          nz-button
          (click)="clickAdd()"
          nzType="primary"
        >
          {{ language_key?.ADD || 'Thêm mới' }}
        </button>
        <button
          nz-button
          *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
          (click)="settingPriceCol()"
          nzType="dashed"
          class="mr-2"
        >
          <span nz-icon nzType="setting"></span> Cấu hình cột
        </button>
        <button
          nz-button
          *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
          (click)="settingFomularCalValue()"
          nzType="dashed"
          class="mr-2"
        >
          <span nz-icon nzType="calculator"></span> Cấu hình công thức tính đơn giá
        </button>
        <button
          class="mr-2"
          *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
          nz-button
          nz-popconfirm
          nzPopconfirmTitle="Bạn có chắc muốn xoá tất cả hạng mục chào giá?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="clickDeleteAll()"
          nzDanger
        >
          Xoá tất cả hạng mục chào giá
        </button>
        <button
          class="ant-btn-blue mr-2"
          *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)"
          nz-button
          (click)="loadPrice()"
        >
          Tải các hạng mục chào giá từ template
        </button>
        <button
          class="mr-2"
          nz-button
          (click)="clickExportExcelPrice()"
          *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
        >
          <span nz-icon nzType="download"></span>Xuất excel
        </button>
        <input
          class="hidden"
          type="file"
          id="file"
          *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
          (change)="clickImportExcelPrice($event)"
          onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <label for="file" *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="lable-custom-file">
          <span nz-icon nzType="upload"></span>
          {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
        </label>
      </nz-col>
    </nz-row>

    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th style="width: 120px">{{ language_key?.NO || 'STT' }}</th>
            <th>Tên hạng mục</th>
            <th *ngFor="let col of bidPriceCol" class="dynamic-col-mpo">{{ col.name }}</th>
            <th>Đơn vị tính</th>
            <th>Đơn vị tiền tệ</th>
            <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
            <th>Bắt buộc?</th>
            <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
          </tr>
        </thead>
        <tbody>
          <!-- level 1 -->
          <ng-container *ngFor="let data1 of listOfData">
            <tr>
              <td (click)="clickEdit(data1)">{{ data1.sort > 0 ? data1.sort : '' }}</td>
              <td (click)="clickEdit(data1)" class="mw-25">{{ data1.name }}</td>
              <td *ngFor="let col of bidPriceCol">{{ data1[col.id] }}</td>
              <td (click)="clickEdit(data1)">{{ data1.unit }}</td>
              <td (click)="clickEdit(data1)">{{ data1.currency }}</td>
              <td (click)="clickEdit(data1)" class="text-right">{{ data1.number | number }}</td>
              <td (click)="clickEdit(data1)">{{ data1.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td>
                <button
                  *ngIf="data.isMPO && authenticationService.checkPermission([enumRole], action.Delete.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục giá này?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="clickDelete(data1)"
                  nz-tooltip
                  nzTooltipTitle="Xoá hạng mục"
                  class="mr-2"
                  nz-button
                  nzDanger
                >
                  <span nz-icon nzType="delete"></span>
                </button>
                <button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
                  nz-tooltip
                  nzTooltipTitle="Thiết lập các thông tin mở rộng"
                  class="mr-2"
                  nz-button
                  [nzType]="data1.__bidPriceListDetails__ && data1.__bidPriceListDetails__.length > 0 ? 'default' : 'dashed'"
                  (click)="settingExInfo(data1)"
                >
                  <span nz-icon nzType="plus-circle"></span>
                </button>
              </td>
            </tr>
            <!-- level 2 -->
            <ng-container *ngFor="let data2 of data1.__childs__">
              <tr>
                <td (click)="clickEdit(data2)" [nzIndentSize]="5">{{ data2.sort > 0 ? data2.sort : '' }}</td>
                <td class="mw-25" (click)="clickEdit(data2)">{{ data2.name }}</td>
                <td *ngFor="let col of bidPriceCol">{{ data2[col.id] }}</td>
                <td (click)="clickEdit(data2)">{{ data2.unit }}</td>
                <td (click)="clickEdit(data2)">{{ data2.currency }}</td>
                <td class="text-right" (click)="clickEdit(data2)">{{ data2.number | number }}</td>
                <td (click)="clickEdit(data2)">{{ data2.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                <td>
                  <button
                    *ngIf="data.isMPO && authenticationService.checkPermission([enumRole], action.Delete.code)"
                    nz-popconfirm
                    nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục giá này?"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="clickDelete(data2)"
                    nz-tooltip
                    nzTooltipTitle="Xoá hạng mục"
                    class="mr-2"
                    nz-button
                    nzDanger
                  >
                    <span nz-icon nzType="delete"></span>
                  </button>
                  <button
                    *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
                    nz-tooltip
                    nzTooltipTitle="Thiết lập các thông tin mở rộng"
                    class="mr-2"
                    nz-button
                    [nzType]="data2.__bidPriceListDetails__ && data2.__bidPriceListDetails__.length > 0 ? 'default' : 'dashed'"
                    (click)="settingExInfo(data2)"
                  >
                    <span nz-icon nzType="plus-circle"></span>
                  </button>
                </td>
              </tr>
              <!-- level 3 -->
              <ng-container *ngFor="let data3 of data2.__childs__">
                <tr>
                  <td (click)="clickEdit(data3)" [nzIndentSize]="30">{{ data3.sort > 0 ? data3.sort : '' }}</td>
                  <td class="mw-25" (click)="clickEdit(data3)">{{ data3.name }}</td>
                  <td *ngFor="let col of bidPriceCol">{{ data3[col.id] }}</td>
                  <td (click)="clickEdit(data3)">{{ data3.unit }}</td>
                  <td (click)="clickEdit(data3)">{{ data3.currency }}</td>
                  <td class="text-right" (click)="clickEdit(data3)">{{ data3.number | number }}</td>
                  <td (click)="clickEdit(data3)">{{ data3.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                  <td>
                    <button
                      *ngIf="data.isMPO && authenticationService.checkPermission([enumRole], action.Delete.code)"
                      nz-popconfirm
                      nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục giá này?"
                      nzPopconfirmPlacement="bottom"
                      (nzOnConfirm)="clickDelete(data3)"
                      nz-tooltip
                      nzTooltipTitle="Xoá hạng mục"
                      class="mr-2"
                      nz-button
                      nzDanger
                    >
                      <span nz-icon nzType="delete"></span>
                    </button>
                    <button
                      *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
                      nz-tooltip
                      nzTooltipTitle="Thiết lập các thông tin mở rộng"
                      class="mr-2"
                      nz-button
                      [nzType]="data3.__bidPriceListDetails__ && data3.__bidPriceListDetails__.length > 0 ? 'default' : 'dashed'"
                      (click)="settingExInfo(data3)"
                    >
                      <span nz-icon nzType="plus-circle"></span>
                    </button>
                  </td>
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Chọn doanh nghiệp nộp lại chào giá" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row class="mt-2">
      <nz-table
        nz-col
        nzSpan="24"
        [nzData]="listOfDataSupplier"
        [(nzPageSize)]="pageSize"
        [nzLoading]="loading2"
        [nzShowPagination]="false"
        nzBordered
      >
        <thead>
          <tr>
            <th>
              <label nz-checkbox [(ngModel)]="isChooseAll" (ngModelChange)="isChangeChooseAll()"></label>
            </th>
            <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
            <th class="text-nowrap">Trạng thái hồ sơ</th>
            <th>Điểm</th>
            <th class="text-nowrap">Điểm HĐXT chào giá</th>
            <th class="text-nowrap">Trạng thái đánh giá</th>
            <th class="text-nowrap">Xác nhận hợp lệ</th>
            <th>Ghi chú từ người đánh giá</th>
            <th>Ghi chú từ người duyệt</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dataRow of listOfDataSupplier">
            <td>
              <label nz-checkbox [(ngModel)]="dataRow.isChoose"></label>
            </td>
            <td class="w-25">
              {{ dataRow.supplierName }}
            </td>
            <td>{{ dataRow.statusFileName }}</td>
            <td class="text-right text-nowrap">
              {{ coreService.scoreRankABCD(dataRow.scorePrice) }}
            </td>
            <td>
              {{ coreService.scoreRankABCD(dataRow.scoreManualPrice) }}
            </td>
            <td>{{ dataRow.statusPriceName }}</td>
            <td>
              <label nz-checkbox [(ngModel)]="dataRow.isPriceValid" name="isPriceValid" [disabled]="true"> Hợp lệ </label>
            </td>
            <td class="w-25">{{ dataRow.notePrice }}</td>
            <td class="w-25">{{ dataRow.noteMPOLeader }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-2 text-center" style="justify-content: center">
  <button
    nz-button
    (click)="saveResetPrice()"
    class="ant-btn-blue mr-2 mb-2"
    *ngIf="data.isShowResetPrice && authenticationService.checkPermission([enumRole], action.Create.code)"
  >
    {{ language_key?.SAVE || 'Lưu' }}
  </button>
</nz-row>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Thiết lập công thức tính đơn giá" (nzOnCancel)="handleCancel()" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Công thức:</h4>
      <nz-col nzSpan="24" class="text-center">
        <input nz-input placeholder="Nhập công thức tính đơn giá" [(ngModel)]="data.fomular" />
      </nz-col>
    </nz-row>
    <nz-row class="mt-2 text-center">
      <button
        *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
        nz-popconfirm
        nzPopconfirmTitle="Xác nhận lưu công thức?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="handleOk()"
        nz-button
        class="ant-btn-blue"
      >
        Đồng ý
      </button>
    </nz-row>
  </ng-container>
</nz-modal>
