<nz-row nzGutter="8">
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">Chọ<PERSON> lĩnh vực mua hàng</nz-form-label>
      <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceChose"
        [nzLoadData]="loadDataService">
      </nz-cascader>
    </nz-form-item>
  </nz-col>
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">
        {{ language_key?.INTERPRISE_CODENAME || 'Mã hoặc tên doanh nghiệp' }}
      </nz-form-label>
      <input nz-input [placeholder]="language_key?.INTERPRISE_CODENAME || 'Mã hoặc tên doanh nghiệp'"
        [(ngModel)]="dataSearch.supplierName" name="supplierName" />
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">Thông tin pháp lý</nz-form-label>
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.lawStatus" name="lawStatus"
        nzPlaceHolder="Trạng thái pháp lý">
        <nz-option *ngFor="let item of dataLawStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
      </nz-select>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8" *ngIf="false">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">Trạng thái thẩm định</nz-form-label>
      <nz-select nzShowSearch nzAllowClear nzMode="multiple" [(ngModel)]="dataSearch.expertiseStatus"
        name="expertiseStatus" nzPlaceHolder="Trạng thái thẩm định">
        <nz-option *ngFor="let item of dataExpertiseStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
      </nz-select>
    </nz-form-item>
  </nz-col>
</nz-row>
<nz-row nzGutter="8" class="mt-2">

  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">Thông tin năng lực</nz-form-label>
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.capacityStatus" name="lawStatus"
        nzPlaceHolder="Trạng thái năng lực">
        <nz-option *ngFor="let item of dataCapacityStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
      </nz-select>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">Từ ngày thẩm định</nz-form-label>
      <nz-form-control nzSpan="24" nzErrorTip="Từ ngày thẩm định">
        <nz-date-picker [(ngModel)]="dataSearch.fromDate" name="dataSearch.fromDate"> </nz-date-picker>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label class="text-left" nzSpan="24">Đến ngày thẩm định</nz-form-label>
      <nz-form-control nzSpan="24" nzErrorTip="Đến ngày thẩm định">
        <nz-date-picker [(ngModel)]="dataSearch.toDate" name="dataSearch.toDate"> </nz-date-picker>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
</nz-row>

<nz-row class="mt-2">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="searchData(true)" class="mr-2">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
    <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)"
      (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>
      Xuất excel
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #basicTable id="reprot-html-table" [nzData]="listOfData"
    [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
        <th>Lĩnh vực kinh doanh</th>
        <th>Phụ trách pháp lý</th>
        <th>Phụ trách mua hàng</th>
        <th>Thông tin pháp lý</th>
        <th>Thông tin năng lực</th>
        <th>Ngày thẩm định</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td class="mw-25" (click)="showModalDetail(data)">{{ data.supplierName }}</td>
        <td (click)="showModalDetail(data)">{{ data.itemName }}</td>
        <td (click)="showModalDetail(data)">{{ data.approvedLawName }}</td>
        <td (click)="showModalDetail(data)">{{ data.approvedCapacityName }}</td>
        <td (click)="showModalDetail(data)">{{ data.statusLawName }}</td>
        <td (click)="showModalDetail(data)">{{ data.statusCapacityName }}</td>
        <td (click)="showModalDetail(data)">{{ data.changeDate | date: 'dd/MM/yyyy' }}</td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>