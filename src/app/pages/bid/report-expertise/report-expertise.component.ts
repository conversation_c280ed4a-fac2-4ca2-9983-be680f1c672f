import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import * as moment from 'moment'
import { SupplierExpertiseDetailComponent } from '../supplier-expertise/supplier-expertise-detail-model/supplier-expertise-detail.component'
import * as XLSX from 'xlsx'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './report-expertise.component.html' })
export class ReportExpertiseComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataServiceChild: any[] = []
  dataServiceChildLast: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  dataCapacityStatus = this.coreService.convertObjToArray(enumData.SupplierExpertiseCapacityStatus)
  dataLawStatus = this.coreService.convertObjToArray(enumData.SupplierExpertiseLawStatus)
  dataExpertiseStatus = this.coreService.convertObjToArray(enumData.SupplierExpertiseStatus)
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1

    const where: any = {}
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.expertiseStatus && this.dataSearch.expertiseStatus !== '') {
      where.expertiseStatus = this.dataSearch.expertiseStatus
    }

    if (this.dataSearch.lawStatus && this.dataSearch.lawStatus !== '') {
      where.lawStatus = this.dataSearch.lawStatus
    }

    if (this.dataSearch.capacityStatus && this.dataSearch.capacityStatus !== '') {
      where.capacityStatus = this.dataSearch.capacityStatus
    }

    if (this.dataSearch.supplierName && this.dataSearch.supplierName !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.loading = true
    this.apiService.post(this.apiService.REPORT.GET_REPORT_EXPERTISE, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve()
    })
  }

  showModalDetail(item: any) {
    this.dialog
      .open(SupplierExpertiseDetailComponent, { data: { supplierExpertiseId: item.id } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData(true)
      })
  }

  clickExportExcel() {
    const tbl = document.getElementById('reprot-html-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Báo cáo phân tích thẩm định nhà cung cấp.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
