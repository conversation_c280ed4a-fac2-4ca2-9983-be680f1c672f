<div *ngIf="bidId && data">
  <div nzBordered="false" *ngFor="let item of data.listItem" class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="item.listTech" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th>{{ language_key?.NO || 'STT' }}</th>
          <th>Tên tiêu chí</th>
          <th>Tỉ trọng(%)</th>
          <th>Giá trị đạt</th>
          <th>Kiểu dữ liệu</th>
          <th>Bắt buộc?</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of item.listTech">
          <tr>
            <td>{{ data.sort > 0 ? data.sort : '' }}</td>
            <td class="mw-25">
              {{ data.name }}
            </td>
            <td class="text-right">{{ data.percent }}</td>
            <td class="text-right">{{ data.percentRule | number }}</td>
            <td>{{ data.type }}</td>
            <td>{{ data.isRequired ? 'Bắt buộc' : 'Không' }}</td>
            <td>
              <button *ngIf="data.type === dataType.List.code" nz-tooltip nzTooltipTitle="Chi tiết danh sách"
                class="mr-3" nz-button [nzType]="data.__bidTechListDetails__.length > 0 ? 'default' : 'dashed'"
                (click)="settingList(data)">
                <span nz-icon nzType="unordered-list"></span>
              </button>
            </td>
          </tr>
          <ng-container>
            <td [nzIndentSize]="10" colspan="8" scope="colgroup" *ngIf="data.sumPercent < 100 && data.sumPercent >= 0"
              class="text-orange">
              Tổng tỉ trọng trong mục này đạt {{ data.sumPercent }}%, chưa đủ 100%
            </td>
            <tr *ngFor="let item of data.__childs__">
              <td [nzIndentSize]="10">{{ item.sort > 0 ? item.sort : '' }}</td>
              <td class="mw-25">
                {{ item.name }}
              </td>
              <td class="text-right">{{ item.percent }}</td>
              <td class="text-right">{{ item.percentRule | number }}</td>
              <td>{{ item.type }}</td>
              <td>{{ item.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td>
                <button *ngIf="item.type === dataType.List.code" nz-tooltip nzTooltipTitle="Chi tiết danh sách"
                  class="mr-3" nz-button [nzType]="item.__bidTechListDetails__.length > 0 ? 'default' : 'dashed'"
                  (click)="settingList(item)">
                  <span nz-icon nzType="unordered-list"></span>
                </button>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>

  </div>
</div>