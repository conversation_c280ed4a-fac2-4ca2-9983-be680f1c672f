<!-- Thông tin chung -->
<nz-collapse nzBordered="false">
  <nz-collapse-panel [nzHeader]="language_key?.GENERAL_INFO || 'Thông Tin Chung'" class="ant-bg-antiquewhite"
    nzActive="true">
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24">
        <label>Công ty mời thầu:</label>
        <input nz-input [(ngModel)]="dataObject.companyInvite" readonly />
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24">
        <label>Đ<PERSON><PERSON> chỉ nộ<PERSON> hồ sơ thầu:</label>
        <input nz-input [(ngModel)]="dataObject.addressSubmit" readonly />
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24">
        <label><PERSON><PERSON><PERSON> đị<PERSON> đi<PERSON>m thực hiện gói thầu:</label>
        <input nz-input [(ngModel)]="dataObject.listAddress" readonly />
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24">
        <label>Các thành viên hội đồng xét thầu:</label>
        <input nz-input [(ngModel)]="dataObject.listMember" readonly />
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24">
        <label>Các thành viên khác:</label>
        <input nz-input [(ngModel)]="dataObject.listOther" readonly />
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<!-- Thông tin gói thầu  -->
<nz-collapse nzBordered="false" class="mt-2">
  <nz-collapse-panel nzHeader="Thông tin gói thầu" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row nzGutter="8" class="mt-3" *ngIf="authenticationService.checkPermission([enumRole], action.View.code)">
      <nz-col nzSpan="8">
        <label>PR:</label>
        <input nz-input [(ngModel)]="dataObject.prCode" readonly />
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="8">
        <label>Tên gói thầu:</label>
        <input nz-input [(ngModel)]="dataObject.name" readonly />
      </nz-col>
      <nz-col nzSpan="8">
        <label>Mã TBMT:</label>
        <input nz-input [(ngModel)]="dataObject.code" readonly />
      </nz-col>
      <nz-col nzSpan="8">
        <label>Trạng thái gói thầu:</label>
        <input nz-input [(ngModel)]="dataObject.statusName" readonly />
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="8">
        <label>Hình thức đấu thầu:</label>
        <input nz-input [(ngModel)]="dataObject.bidTypeName" readonly />
      </nz-col>
      <nz-col nzSpan="8">
        <label>Hiệu lực hợp đồng (tháng):</label>
        <input nz-input [(ngModel)]="dataObject.timeserving" readonly />
      </nz-col>
      <nz-col nzSpan="8">
        <label>Thời gian đăng tải:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.publicDate" nzDisabled>
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="8">
        <label>Hình thức bảo lãnh dự thầu:</label>
        <input nz-input [(ngModel)]="dataObject.masterBidGuaranteeName" readonly />
      </nz-col>
      <nz-col nzSpan="8">
        <label>Số tiền bảo lãnh dự thầu (VNĐ):</label>
        <input nz-input currencyMask [(ngModel)]="dataObject.moneyGuarantee" readonly />
      </nz-col>
      <nz-col nzSpan="8">
        <label>Thời hạn bảo lãnh dự thầu (tháng):</label>
        <input nz-input [(ngModel)]="dataObject.timeGuarantee" readonly />
      </nz-col>
    </nz-row>
    <hr class="dash" />

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="12">
        <label>Thành viên phụ trách mua hàng:</label>
        <input nz-input [(ngModel)]="dataObject.mpo" readonly />
      </nz-col>
      <nz-col nzSpan="12">
        <label>Người duyệt nội dung mua hàng:</label>
        <input nz-input [(ngModel)]="dataObject.mpoLeader" readonly />
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="12">
        <label>Thành viên phụ trách yêu cầu kỹ thuật:</label>
        <input nz-input [(ngModel)]="dataObject.tech" readonly />
      </nz-col>
      <nz-col nzSpan="12">
        <label>Người duyệt yêu cầu kỹ thuật:</label>
        <input nz-input [(ngModel)]="dataObject.techLeader" readonly />
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="8">
        <label>1.Thời hạn thiết lập các yêu cầu kỹ thuật:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm:ss" [(ngModel)]="dataObject.timeTechDate" nzDisabled>
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="8">
        <label>2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm:ss" [(ngModel)]="dataObject.timePriceDate" nzDisabled>
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="8">
        <label>3.Ngày hết hạn xác nhận tham gia đấu thầu:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.acceptEndDate" nzDisabled>
        </nz-date-picker>
      </nz-col>

      <nz-col nzSpan="8">
        <label>4.Ngày hết hạn nộp hồ sơ thầu:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" [(ngModel)]="dataObject.submitEndDate" nzDisabled>
        </nz-date-picker>
      </nz-col>

      <nz-col nzSpan="8">
        <label>5.Thời hạn đánh giá các yêu cầu kỹ thuật:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm:ss" [(ngModel)]="dataObject.timeCheckTechDate" nzDisabled>
        </nz-date-picker>
      </nz-col>
      <nz-col nzSpan="8">
        <label>6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại:</label>
        <nz-date-picker nzFormat="dd-MM-yyyy HH:mm:ss" [(ngModel)]="dataObject.timeCheckPriceDate" nzDisabled>
        </nz-date-picker>
      </nz-col>
    </nz-row>

    <!-- File -->
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="8">
        <label>Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ:</label><br>
        <a *ngIf="dataObject.fileDrawing && dataObject.fileDrawing.length > 0" href="{{ dataObject.fileDrawing }}"
          target="_blank"> Xem file </a>
        <i *ngIf="!dataObject.fileDrawing">Không có file</i>
      </nz-col>
      <nz-col nzSpan="8">
        <label>Phạm vi công việc:</label><br>
        <a *ngIf="dataObject.fileJD && dataObject.fileJD.length > 0" href="{{ dataObject.fileJD }}" target="_blank">
          Xem file </a>
        <i *ngIf="!dataObject.fileJD">Không có file</i>
      </nz-col>
      <nz-col nzSpan="8">
        <label>Tiêu chuẩn đánh giá KPI:</label><br>
        <a *ngIf="dataObject.fileKPI && dataObject.fileKPI.length > 0" href="{{ dataObject.fileKPI }}" target="_blank">
          Xem file </a>
        <i *ngIf="!dataObject.fileKPI">Không có file</i>
      </nz-col>
    </nz-row>


    <!-- File -->
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="8">
        <label>Các quy định về nội quy gói thầu:</label><br>
        <a *ngIf="dataObject.fileRule && dataObject.fileRule.length > 0" href="{{ dataObject.fileRule }}"
          target="_blank"> Xem file </a>
        <i *ngIf="!dataObject.fileRule">Không có file</i>
      </nz-col>
      <nz-col nzSpan="8">
        <label>Tài liệu mẫu(mẫu báo giá, mẫu hợp đồng,...):</label><br>
        <a *ngIf="dataObject.fileDocument && dataObject.fileDocument.length > 0" href="{{ dataObject.fileDocument }}"
          target="_blank"> Xem file </a>
        <i *ngIf="!dataObject.fileDocument">Không có file</i>
      </nz-col>
      <nz-col nzSpan="8">
        <label>Khác:</label><br>
        <a *ngIf="dataObject.fileAnother && dataObject.fileAnother.length > 0" href="{{ dataObject.fileAnother }}"
          target="_blank"> Xem file </a>
        <i *ngIf="!dataObject.fileAnother">Không có file</i>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24">
        <label>Mô tả nội dung mời thầu:</label>
        <textarea nz-input rows="5" [(ngModel)]="dataObject.serviceInvite" readonly></textarea>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="8">
        <label>Cho phép hiển thị thông tin mời thầu ở trang tin đấu thầu:</label>
        <p>{{dataObject.isShowHomePage ? 'Có' : 'Không'}}</p>
      </nz-col>
      <nz-col nzSpan="8">
        <label>Gửi thông báo mời thầu cho NCC:</label>
        <p>{{dataObject.isSendEmailInviteBid ? 'Có' : 'Không'}}</p>
      </nz-col>
      <!-- <nz-col nzSpan="8">
        <label>Tự động chọn NCC thắng thầu và kết thúc thầu:</label>
        <p>{{dataObject.isAutoBid ? 'Có' : 'Không'}}</p>
      </nz-col> -->
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-collapse nzBordered="false" class="mt-2">
  <nz-collapse-panel [nzHeader]="headerListItem" class="ant-bg-antiquewhite" nzActive="true">
    <nz-row nzGutter="8" class="mt-2">
      <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" [nzData]="['']"
        [nzScroll]="dataObject.listItem && dataObject.listItem.length > 10 ? { y: '400px' } : { y: null } " nzBordered>
        <thead>
          <tr>
            <th nzWidth="80px">STT</th>
            <th>LVMH</th>
            <th *ngIf="dataObject.prId">Tên hàng hóa</th>
            <th>Số lượng tạo thầu</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dataItem of dataObject.listItem; let i = index">
            <td>{{ i + 1 }}</td>
            <td>{{ dataItem.itemName }}</td>
            <td *ngIf="dataObject.prId">{{ dataItem.productName }}</td>
            <td class="text-right"> {{ dataItem.quantityItem | number }} </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>