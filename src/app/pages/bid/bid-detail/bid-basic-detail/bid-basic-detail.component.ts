import { Component, OnInit, Input } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../services'
import { Subscription } from 'rxjs'
import { MatDialog } from '@angular/material/dialog'
@Component({
  selector: 'app-bid-basic-detail',
  templateUrl: './bid-basic-detail.component.html',
})
export class BidBasicDetailComponent implements OnInit {
  dataObject: any = {}
  headerListItem = 'Danh sách Item'
  loading = false
  @Input()
  public bidId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  currentUser: any
  constructor(
    private apiService: ApiService,
    private coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService,
    private dialog: MatDialog
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.BID_002.code
    this.action = this.enumProject.Action
    this.loadDetail()
  }

  loadDetail() {
    if (this.bidId) {
      this.loading = true
      this.apiService.post(this.apiService.BID_DETAIL.FIND_DETAIL, { id: this.bidId }).then((res) => {
        if (res) {
          this.loading = false
          this.dataObject = res
          if (this.dataObject.prId) this.headerListItem = `Danh sách Item theo PR [${this.dataObject.prCode}]`
        }
      })
    }
  }
}
