import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { BidSupplierDetailComponent } from '../../bid-supplier-detail/bid-supplier-detail.component'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-supplier-price-detail.component.html' })
export class BidSupplierPriceDetailComponent implements OnInit {
  pageSize = enumData.Page.pageSize
  pageIndex = enumData.Page.pageIndex
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  @Input()
  public bidPriceId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private apiService: ApiService,
    private drawerService: NzDrawerService,
    public coreService: CoreService,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    if (this.bidPriceId) {
      const dataSearch: any = {
        skip: (this.pageIndex - 1) * this.pageSize,
        take: this.pageSize,
      }
      this.loading = true
      this.apiService.get(this.apiService.BID_DETAIL.GET_SUPPLIER_PRICE_BY_BID_PRICE_ID(this.bidPriceId), dataSearch).then((data) => {
        if (data) {
          this.loading = false
          this.listOfData = data[0]
          this.total = data[1]

          let sort = 1
          for (const item of this.listOfData) {
            item.sort = sort
            sort++
          }
        }
      })
    }
  }

  showSupplierDetail(data: any) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết doanh nghiệp' + ` [${data.supplierName}]`,
      nzContent: BidSupplierDetailComponent,
      nzContentParams: { supplierId: data.supplierId },
      nzWidth: '640',
      nzPlacement: 'left',
    })
  }
}
