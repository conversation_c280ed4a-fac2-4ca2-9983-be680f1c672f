import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-bid-basic-detail-print',
  templateUrl: './bid-basic-detail-print.component.html',
})
export class BidBasicDetailPrintComponent implements OnInit {
  dataObject: any = {}
  headerListItem = 'Danh sách Item'
  loading = false
  @Input()
  public bidId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(private apiService: ApiService, private coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.bidId) return
    this.loadDetail()
  }

  loadDetail() {
    this.loading = true
    this.apiService.post(this.apiService.BID_DETAIL.FIND_DETAIL, { id: this.bidId }).then((data) => {
      if (data) {
        this.loading = false
        this.dataObject = data
        if (this.dataObject.prId) this.headerListItem = `Danh sách Item theo PR [${this.dataObject.prCode}]`
      }
    })
  }
}
