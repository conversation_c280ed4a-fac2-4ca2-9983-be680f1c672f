import { Component, OnInit, Input } from '@angular/core'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { BidSupplierDetailComponent } from '../../bid-supplier-detail/bid-supplier-detail.component'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-supplier-trade-detail.component.html' })
export class BidSupplierTradeDetailComponent implements OnInit {
  pageSize = enumData.Page.pageSize
  pageIndex = enumData.Page.pageIndex
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  dataType = enumData.DataType
  @Input()
  public bidTradeId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private drawerService: NzDrawerService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    if (this.bidTradeId) {
      const dataSearch: any = {
        skip: (this.pageIndex - 1) * this.pageSize,
        take: this.pageSize,
      }
      this.loading = true

      this.apiService.get(this.apiService.BID_DETAIL.GET_SUPPLIER_TRADE_BY_BID_TRADE_ID(this.bidTradeId), dataSearch).then((data) => {
        if (data) {
          this.loading = false
          this.listOfData = data[0]
          this.total = data[1]

          let sort = 1
          for (const item of this.listOfData) {
            item.sort = sort
            if (item.__bidTrade__.type === enumData.DataType.List.code) {
              const objList = item.__bidTrade__.__bidTradeListDetails__.find((c: any) => c.id === item.value)
              item.value = objList ? objList.name : ''
            }
            sort++
          }
        }
      })
    }
  }

  showSupplierDetail(data: any) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết doanh nghiệp' + ` [${data.supplierName}]`,
      nzContent: BidSupplierDetailComponent,
      nzContentParams: { supplierId: data.supplierId },
      nzWidth: '640',
      nzPlacement: 'left',
    })
  }
}
