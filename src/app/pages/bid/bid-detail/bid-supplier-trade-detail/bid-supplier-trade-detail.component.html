<nz-row>
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>{{ language_key?.NO || 'STT' }}</th>
        <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
        <th>Thông tin cung cấp</th>
        <th>Điểm đạt đượ<PERSON></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td>{{ data.sort > 0 ? data.sort : '' }}</td>
        <td class="mw-25">
          <span nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Chi tiết" (click)="showSupplierDetail(data)">
          </span>
          {{ data.supplierName }}
        </td>
        <td class="mw-25" [ngClass]="{ 'text-right': data.__bidTrade__.type === dataType.Number.code }">
          <span *ngIf="data.__bidTrade__.type === dataType.String.code">{{ data.value }}</span>
          <span *ngIf="data.__bidTrade__.type === dataType.Number.code">{{ data.value > 0 ? (data.value | number:
            '1.0-2') : '' }}</span>
          <span *ngIf="data.__bidTrade__.type === dataType.Date.code">{{ data.value | date: 'dd/MM/yyyy HH:mm'
            }}</span>
          <span *ngIf="data.__bidTrade__.type === dataType.List.code">{{ data.value }}</span>
          <span *ngIf="data.__bidTrade__.type === dataType.File.code && data.value">
            <a href="{{ data.value }}" target="_blank">
              <span nz-icon nzType="file-text"></span> Xem file đính kèm
            </a>
          </span>
        </td>
        <td class="text-right">{{ data.score | number: '1.0-2' }}</td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>