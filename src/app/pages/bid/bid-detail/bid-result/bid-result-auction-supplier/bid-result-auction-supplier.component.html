<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" [nzData]="dataObject.bidPrices" [nzLoading]="loading" [(nzPageSize)]="pageSize"
    [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tên tiêu chí</th>
        <th>Gi<PERSON> trị</th>
        <th><PERSON><PERSON><PERSON><PERSON></th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let data of dataObject.bidPrices">
        <tr>
          <td class="mw-25">{{ data.name }}</td>
          <td>
            {{ getSupplierValue(data.id) | number }}
          </td>
          <td>{{ getSupplierScore(data.id) | number: '1.0-2' }}</td>
        </tr>
      </ng-container>
    </tbody>
  </nz-table>
</nz-row>