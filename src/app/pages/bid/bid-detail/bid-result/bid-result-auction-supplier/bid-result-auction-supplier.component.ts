import { Component, OnInit, Input } from '@angular/core'
import { ApiService } from '../../../../../services'
import { enumData } from '../../../../../core'
@Component({ templateUrl: './bid-result-auction-supplier.component.html' })
export class BidResultAuctionSupplierComponent implements OnInit {
  loading = false
  dataObject: any = { bidPrices: [] }
  pageSize = enumData.Page.pageSizeMax

  @Input()
  public bidAuctionSupplierId!: string
  constructor(private apiService: ApiService) {}

  ngOnInit() {
    if (!this.bidAuctionSupplierId) return
    this.loadDetail()
  }

  loadDetail() {
    this.loading = true
    this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_AUCTION_SUPPLIER_DETAIL(this.bidAuctionSupplierId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataObject = data
      }
    })
  }

  getSupplierScore(bidPriceId: any) {
    const find = this.dataObject.bidAuctionSupplierPrice.find((p: any) => p.bidPriceId === bidPriceId)
    if (find) {
      return find.score
    }
    return ''
  }

  getSupplierValue(bidPriceId: any) {
    const find = this.dataObject.bidAuctionSupplierPrice.find((p: any) => p.bidPriceId === bidPriceId)
    if (find) {
      return find.value
    }
    return ''
  }
}
