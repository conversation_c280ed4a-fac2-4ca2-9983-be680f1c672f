<div id="print-section2" *ngIf="dataObject" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150">
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BÁO CÁO ĐÁNH GIÁ HỒ SƠ NĂNG LỰC</div>
    <div>CÔNG TY: {{dataObject.supplierName}}</div>
    <div>TÊN GÓI THẦU: {{dataObject.bidName}}</div>
    <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px;">
    <table id="capacity-detail-table" style="margin-bottom: 10px; border-spacing: 0;" align="center">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 40px; border: darkgrey 1px solid;">
            {{ language_key?.NO || 'STT' }}
          </th>
          <th style="width: 30%; border: darkgrey 1px solid;">Tên tiêu chí</th>
          <th style="width: 15%; border: darkgrey 1px solid;">Tỉ trọng<br>(100%)</th>
          <th style="width: 15%; border: darkgrey 1px solid;">Giá trị đạt</th>
          <th style="width: 20%; border: darkgrey 1px solid;">Thông tin cung cấp</th>
          <th style="width: calc(20%-40px); border: darkgrey 1px solid;">Điểm đánh giá</th>
        </tr>
        <tr style="text-align: center;">
          <th style="border: darkgrey 1px solid;"></th>
          <th style="border: darkgrey 1px solid;">Điểm đánh giá</th>
          <th style="border: darkgrey 1px solid;">100</th>
          <th style="border: darkgrey 1px solid;"></th>
          <th style="border: darkgrey 1px solid;"></th>
          <th style="border: darkgrey 1px solid;">{{ sum | number: '1.0-2' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of dataObject.bidCapacitys">
          <tr style="text-align: center;">
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.sort > 0 ? data.sort : '' }}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.name }}</td>
            <td style="border: darkgrey 1px solid;">{{ data.percent }}</td>
            <td style="border: darkgrey 1px solid;">{{ data.percentRule | number }}</td>

            <td *ngIf="!data.isChangeByYear" style="border: darkgrey 1px solid;">
              <span *ngIf="data.type === dataType.String.code">{{ data.value }}</span>
              <span *ngIf="data.type === dataType.Number.code">{{ data.value | number }}</span>
              <span *ngIf="data.type === dataType.Date.code">{{ data.value | date: 'dd/MM/yyyy' }}</span>
              <span *ngIf="data.type === dataType.List.code">
                {{ coreService.convertTypeList(data.__supplierCapacityListDetails__) }}
              </span>
              <span *ngIf="data.type === dataType.File.code && data.value">
                <a href="{{ data.value }}" target="_blank">
                  <span nz-icon nzType="file-text"></span>
                  Xem file đính kèm
                </a>
              </span>
            </td>
            <td *ngIf="data.isChangeByYear" style="border: darkgrey 1px solid;">
              <span nz-popover [nzPopoverContent]="contentTemplate">
                Chi tiết
              </span>
              <ng-template #contentTemplate>
                <div *ngFor="let itemYear of data.__supplierCapacityYearValues__">
                  <p *ngIf="data.type === dataType.String.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value }}
                  </p>
                  <p *ngIf="data.type === dataType.Number.code">
                    Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                  </p>
                  <p *ngIf="data.type === dataType.File.code && itemYear.value">
                    <a href="{{ itemYear.value }}" target="_blank">
                      Năm {{ itemYear.year }} -
                      <span nz-icon nzType="file-text"></span> Xem file đính kèm
                    </a>
                  </p>
                </div>
              </ng-template>
            </td>
            <td style="border: darkgrey 1px solid;">
              <span *ngIf="data.percentRule || data.type === dataType.List.code || data.__childs__.length > 0">
                {{ data.score | number: '1.0-2' }}</span>
            </td>
          </tr>
          <ng-container *ngFor="let item of data.__childs__">
            <tr style="text-align: center;">
              <td align="left" style="border: darkgrey 1px solid;">&ensp;&nbsp;{{ item.sort > 0 ? item.sort : '' }}</td>
              <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ item.name }}</td>
              <td style="border: darkgrey 1px solid;">{{ item.percent }}</td>
              <td style="border: darkgrey 1px solid;">{{ item.percentRule | number }}</td>

              <td *ngIf="!item.isChangeByYear" style="border: darkgrey 1px solid;">
                <span *ngIf="item.type === dataType.String.code">{{ item.value }}</span>
                <span *ngIf="item.type === dataType.Number.code">{{ item.value | number }}</span>
                <span *ngIf="item.type === dataType.Date.code">{{ item.value | date: 'dd/MM/yyyy' }}</span>
                <span *ngIf="item.type === dataType.List.code">
                  {{ coreService.convertTypeList(item.__supplierCapacityListDetails__) }}
                </span>
                <span *ngIf="item.type === dataType.File.code && item.value">
                  <a href="{{ item.value }}" target="_blank">
                    <span nz-icon nzType="file-text"></span>
                    Xem file đính kèm
                  </a>
                </span>
              </td>
              <td *ngIf="item.isChangeByYear" style="border: darkgrey 1px solid;">
                <span nz-popover [nzPopoverContent]="contentTemplate">
                  Chi tiết
                </span>
                <ng-template #contentTemplate>
                  <div *ngFor="let itemYear of item.__supplierCapacityYearValues__">
                    <p *ngIf="item.type === dataType.String.code">
                      Năm {{ itemYear.year }} - {{ itemYear.value }}
                    </p>
                    <p *ngIf="item.type === dataType.Number.code">
                      Năm {{ itemYear.year }} - {{ itemYear.value | number }}
                    </p>
                    <p *ngIf="item.type === dataType.File.code && itemYear.value">
                      <a href="{{ itemYear.value }}" target="_blank">
                        Năm {{ itemYear.year }} -
                        <span nz-icon nzType="file-text"></span> Xem file đính kèm
                      </a>
                    </p>
                  </div>
                </ng-template>
              </td>
              <td style="border: darkgrey 1px solid;">
                <span *ngIf="item.percentRule || item.type === dataType.List.code">
                  {{ item.score | number: '1.0-2' }}</span>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px;">
    <div style="text-align: right; font-weight: 700;" *ngIf="dataObject.approveChooseSupplierWinDate">
      Ngày {{dataObject.approveChooseSupplierWinDate | date: 'dd'}}
      tháng {{dataObject.approveChooseSupplierWinDate | date: 'MM'}}
      năm {{dataObject.approveChooseSupplierWinDate | date: 'yyyy'}}
    </div>
    <div style="text-align: right; font-weight: 700;" *ngIf="!dataObject.approveChooseSupplierWinDate">
      Ngày .....
      tháng .....
      năm .....
    </div>
    <table style="margin: 10px 0 100px 0;">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 30px;"></th>
          <th style="width:50%;">ĐỀ XUẤT</th>
          <th style="width:50%;">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-5 text-center">
  <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)"
    (click)="clickExportExcel()" class="mr-2">
    <span nz-icon nzType="download"></span>Xuất excel
  </button>
  <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)"
    [useExistingCss]="true" printSectionId="print-section2" ngxPrint>
    In báo cáo
  </button>
</div>