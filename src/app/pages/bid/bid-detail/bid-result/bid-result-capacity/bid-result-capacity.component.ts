import { Component, OnInit, Input } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-bid-result-capacity',
  templateUrl: './bid-result-capacity.component.html',
})
export class BidResultCapacityComponent implements OnInit {
  dataObject: any = {}
  loading = false
  listOfData: any[] = []
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax
  sum = 0
  todate = new Date()
  @Input()
  public bidId!: string
  @Input()
  public supplierId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
    this.loadDetail()
  }

  loadDetail() {
    if (this.bidId) {
      this.loading = true
      this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_CAPACITY(this.bidId), { supplierId: this.supplierId }).then((data) => {
        if (data) {
          this.loading = false
          this.dataObject = data
          for (const item of this.dataObject.bidCapacitys) {
            item.score = this.coreService.calScore(item)
            this.sum += item.score
            for (const child of item.__childs__) {
              child.score = this.coreService.calScore(child)
            }
          }
        }
      })
    }
  }

  convertTypeList(bidCapacity: any) {
    if (this.dataObject.__bidSupplierCapacityValue__) {
      const list = bidCapacity.__bidCapacityListDetails__
      const find = this.dataObject.__bidSupplierCapacityValue__.find((p: any) => p.bidCapacityId === bidCapacity.id)
      if (find && list) {
        const data = list.find((p: any) => p.id === find.value)
        if (data) {
          return data.name
        }
      }
      return ''
    } else {
      return ''
    }
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('capacity-detail-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Kết quả đánh giá năng lực doanh nghiệp ${this.dataObject.supplierName}.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
