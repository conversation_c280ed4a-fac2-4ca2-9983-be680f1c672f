<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th>Item</th>
        <th><PERSON><PERSON><PERSON> kết thúc</th>
        <th><PERSON><PERSON><PERSON> tạo</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td (click)="showDetail(data.id)" class="mw-25">
          {{ data.statusName }}
        </td>
        <td (click)="showDetail(data.id)" class="mw-25">
          {{ data.itemName }}
        </td>
        <td (click)="showDetail(data.id)" class="mw-25">
          {{ data.endDate | date: 'dd/MM/yyyy HH:mm' }}
        </td>
        <td (click)="showDetail(data.id)" class="mw-25">
          {{ data.createdAt | date: 'dd/MM/yyyy HH:mm' }}
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>