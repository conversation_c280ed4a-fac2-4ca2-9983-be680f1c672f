import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { enumData } from '../../../../core'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidResultDetailComponent } from './bid-result-detail/bid-result-detail.component'
import { Subscription } from 'rxjs'

/** <PERSON>ết quả đánh giá gói thầu */
@Component({
  selector: 'app-bid-result',
  templateUrl: './bid-result.component.html',
})
export class BidResultComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataObject: any = {}
  dataSearch: any = {}
  dataStatus = this.coreService.convertObjToArray(enumData.BidSupplierFileStatus)
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  public bidId!: string
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private drawerService: NzDrawerService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.searchData()
  }

  searchData() {
    if (!this.bidId) return

    this.loading = true
    const where: any = { bidId: this.bidId }
    if (this.dataSearch.name && this.dataSearch.name !== '') where.name = this.dataSearch.name
    if (this.dataSearch.statusFile && this.dataSearch.statusFile.length > 0) where.statusFile = this.dataSearch.statusFile

    this.apiService.post(this.apiService.BID_DETAIL.LOAD_BID_RESULT, where).then((res) => {
      this.loading = false
      this.dataObject = res
    })
  }

  showBidResultDetail(data: { bidId: string; supplierId: string }) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết doanh nghiệp',
      nzContent: BidResultDetailComponent,
      nzContentParams: data,
      nzWidth: '640',
    })
  }
}
