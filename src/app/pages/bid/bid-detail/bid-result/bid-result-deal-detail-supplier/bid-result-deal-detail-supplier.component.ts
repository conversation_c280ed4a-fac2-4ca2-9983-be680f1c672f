import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-result-deal-detail-supplier.component.html' })
export class BidResultDealDetailSupplierComponent implements OnInit {
  loading = false
  dataObject: any = { bidPrices: [] }
  pageSize = enumData.Page.pageSizeMax
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  public bidDealSupplierId!: string
  constructor(private apiService: ApiService, private coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.bidDealSupplierId) return
    this.searchData()
  }

  searchData() {
    this.loading = true
    this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_DEAL_SUPPLIER_DETAIL(this.bidDealSupplierId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataObject = data
      }
    })
  }

  getSupplierValue(bidPriceId: any) {
    const find = this.dataObject.bidDealSupplierPrice.find((p: any) => p.bidPriceId === bidPriceId)
    if (find) return find.value

    return ''
  }
}
