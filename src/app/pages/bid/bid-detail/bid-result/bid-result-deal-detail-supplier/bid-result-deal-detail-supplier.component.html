<nz-row>
  <nz-table nz-col nzSpan="24" [nzData]="dataObject.bidPrices" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tê<PERSON> hạ<PERSON> mụ<PERSON></th>
        <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
        <th><PERSON><PERSON><PERSON> đà<PERSON> phán</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let data of dataObject.bidPrices">
        <tr>
          <td class="mw-25">{{ data.name }}</td>
          <td class="mw-25">{{ data.number | number }}</td>
          <td>{{ getSupplierValue(data.id) | number }}</td>
        </tr>
      </ng-container>
    </tbody>
  </nz-table>
</nz-row>