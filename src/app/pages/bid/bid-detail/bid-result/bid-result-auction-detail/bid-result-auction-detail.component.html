<nz-collapse nzBordered="false" *ngFor="let child of dataObject.listChild" class="mt-2">
  <nz-collapse-panel [nzHeader]="'Item ' + child.itemName" class="ant-bg-antiquewhite"
    [nzActive]="!dicActiveCollapse[child.id]" (nzActiveChange)="dicActiveCollapse[child.id] = $event">
    <nz-collapse [nzBordered]="true" class="mt-3">
      <nz-collapse-panel nzHeader="Danh sách nhà cung cấp" class="ant-bg-lightblue" nzActive="true">
        <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="child.__bidAuctionSupplier__" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>Nhà cung cấp</th>
              <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
              <th>Điểm</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of child.__bidAuctionSupplier__">
              <td class="mw-25">
                <span nz-tooltip nzTooltipTitle="Chi tiết" (click)="showSupplierDetail(data.supplierId)" nz-icon
                  nzType="info-circle"></span>
                {{ data.supplierName }}
              </td>
              <td> {{ data.statusName }} </td>
              <td>
                <span nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Chi tiết" (click)="showDetail(data.id)">
                </span>
                {{ data.score }}
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-collapse-panel>
    </nz-collapse>
  </nz-collapse-panel>
</nz-collapse>