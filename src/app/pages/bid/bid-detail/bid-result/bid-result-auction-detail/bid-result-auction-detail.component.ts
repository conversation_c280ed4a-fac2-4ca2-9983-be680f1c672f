import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../../services'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidResultAuctionSupplierComponent } from '../bid-result-auction-supplier/bid-result-auction-supplier.component'
import { BidSupplierDetailComponent } from '../../../bid-supplier-detail/bid-supplier-detail.component'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'
@Component({ templateUrl: './bid-result-auction-detail.component.html' })
export class BidResultAuctionDetailComponent implements OnInit {
  loading = false
  dataObject: any = { listChild: [] }
  pageSize = enumData.Page.pageSizeMax
  language_key: any
  subscriptions: Subscription = new Subscription()
  dicActiveCollapse: any = {}

  @Input()
  public bidAuctionId!: string
  constructor(
    private apiService: ApiService,
    private coreService: CoreService,
    private storageService: StorageService,
    private drawerService: NzDrawerService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (!this.bidAuctionId) return
    this.loadDetail()
  }

  loadDetail() {
    this.loading = true
    this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_AUCTION(this.bidAuctionId), {}).then((data) => {
      if (data) {
        this.loading = false
        this.dataObject = data
      }
    })
  }

  showDetail(bidAuctionSupplierId: any) {
    this.drawerService.create({
      nzTitle: 'Chi tiết đấu giá',
      nzContent: BidResultAuctionSupplierComponent,
      nzContentParams: { bidAuctionSupplierId },
      nzWidth: '640',
      nzPlacement: 'left',
    })
  }

  showSupplierDetail(supplierId: any) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết doanh nghiệp',
      nzContent: BidSupplierDetailComponent,
      nzContentParams: { supplierId },
      nzWidth: '640',
      nzPlacement: 'left',
    })
  }
}
