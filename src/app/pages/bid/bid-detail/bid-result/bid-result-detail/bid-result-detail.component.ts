import { Component, OnInit, Input } from '@angular/core'
import { enumData } from '../../../../../core'
import { ApiService } from '../../../../../services'

/** <PERSON> tiết doanh nghiệp */
@Component({ templateUrl: './bid-result-detail.component.html' })
export class BidResultDetailComponent implements OnInit {
  @Input()
  public bidId!: string
  @Input()
  public supplierId!: string

  isShowTab = false
  constructor(private apiService: ApiService) {}

  ngOnInit() {
    if (!this.bidId) return
    // get bid
    this.apiService.get(this.apiService.BID.GET_BID_STATUS(this.bidId), {}).then((res) => {
      const lstStatus = [
        enumData.BidStatus.DangDanhGia.code,
        enumData.BidStatus.DangDuyetDanhGia.code,
        enumData.BidStatus.HoanTatDanhGia.code,
        enumData.BidStatus.DangDamPhanGia.code,
        enumData.BidStatus.DongDamPhanGia.code,
        enumData.BidStatus.DangDauGia.code,
        enumData.BidStatus.DongDauGia.code,
        enumData.BidStatus.DongThau.code,
        enumData.BidStatus.DuyetNCCThangThau.code,
        enumData.BidStatus.DangDuyetKetThucThau.code,
        enumData.BidStatus.HoanTat.code,
      ]
      this.isShowTab = lstStatus.includes(res.status)
    })
  }
}
