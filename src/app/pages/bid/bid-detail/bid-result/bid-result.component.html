<nz-row nzGutter="12" class="mt-3">
  <nz-col nzSpan="8">
    <input nz-input [(ngModel)]="dataSearch.name" placeholder="T<PERSON><PERSON> kiếm theo tên doanh nghiệp" />
  </nz-col>

  <nz-col nzSpan="8">
    <nz-select nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusFile" name="statusFile"
      nzPlaceHolder="Trạng thá<PERSON> hồ sơ">
      <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
    </nz-select>
  </nz-col>

  <nz-col nzSpan="8">
    <button nz-button (click)="searchData()">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || '<PERSON><PERSON><PERSON> kiếm' }}
    </button>
  </nz-col>
</nz-row>

<div *ngIf="dataObject.id" class="mt-3">
  <nz-collapse nzBordered="false" *ngFor="let item of dataObject.listItem">
    <nz-collapse-panel [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite">
      <nz-row class="mt-3">
        <nz-table nz-col nzSpan="24" [nzData]="item.lstBidSupplier" [(nzPageSize)]="pageSize" [nzLoading]="loading"
          [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}</th>
              <th>Tổng điểm</th>
              <th>Tổng điểm HĐXT</th>
              <th>Điểm năng lực</th>
              <th>Điểm HĐXT năng lực</th>
              <th>Điểm báo giá</th>
              <th>Điểm HĐXT báo giá</th>
              <th>Điểm ĐKTM</th>
              <th>Điểm HĐXT ĐKTM</th>
              <th>Trạng thái hồ sơ</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of item.lstBidSupplier">
              <td class="mw-25">
                <span nz-tooltip nzTooltipTitle="Chi tiết" (click)="showBidResultDetail(data)" nz-icon
                  nzType="info-circle"></span> {{ data.supplierName }}
              </td>
              <td [ngClass]="{'text-right': data.scoreTotal !== -1}">
                {{ coreService.scoreRankABCD(data.scoreTotal) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualTotal !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualTotal) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreTech !== -1}">
                {{ coreService.scoreRankABCD(data.scoreTech) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualTech !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualTech) }}
              </td>
              <td [ngClass]="{'text-right': data.scorePrice !== -1}">
                {{ coreService.scoreRankABCD(data.scorePrice) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualPrice !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualPrice) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreTrade !== -1}">
                {{ coreService.scoreRankABCD(data.scoreTrade) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualTrade !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualTrade) }}
              </td>
              <td class="mw-25">{{ data.statusFileName }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
</div>