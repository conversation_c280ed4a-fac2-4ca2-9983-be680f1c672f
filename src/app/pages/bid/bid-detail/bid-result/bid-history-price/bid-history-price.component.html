<nz-tabset *ngIf="bidSupplier" matDialogContent>
  <nz-tab nzTitle="BÁO CÁO BẢNG CHÀO GIÁ">
    <nz-row>
      <h4>T<PERSON><PERSON> chọn in:</h4>
      <nz-col nzSpan="24">
        <label nz-checkbox [(ngModel)]="isShowListBidDeal">
          Báo c<PERSON>o lịch sử nộp giá
        </label>
      </nz-col>
    </nz-row>
    <nz-row class="mb-5">
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
        (click)="clickExportExcel()" class="mr-2">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)" nz-button nzType="primary"
        [useExistingCss]="true" printSectionId="print-section2" ngxPrint>
        In báo cáo
      </button>
    </nz-row>
    <div id="print-section2" *ngIf="bidSupplier && dataPrice"
      style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
      <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150">
      <div style="text-align: center; font-weight: 700; font-size: 18px;">
        <div>BÁO CÁO BẢNG CHÀO GIÁ</div>
        <div>{{bidSupplier.supplierName}}</div>
        <div>TÊN GÓI THẦU: {{bid.name}}</div>
        <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
      </div>
      <!-- Body -->
      <div style="margin-top: 20px;">
        <table id="price-detail-table" align="center" style="border-spacing: 0;">
          <thead>
            <tr style="text-align: center;">
              <th style="width: 40px; border: darkgrey 1px solid;">
                {{ language_key?.NO || 'STT' }}
              </th>
              <th style="width: 200px; border: darkgrey 1px solid;">Tên hạng mục</th>
              <th style="width: 120px; border: darkgrey 1px solid;" *ngFor="let col of dataPrice.lstPriceCol">
                {{ col.name }}
              </th>
              <th style="width: 120px; border: darkgrey 1px solid;">Đơn vị tính</th>
              <th style="width: 120px; border: darkgrey 1px solid;">Đơn vị tiền tệ</th>
              <th style="width: 100px; border: darkgrey 1px solid;">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
              <th style="width: 120px; border: darkgrey 1px solid;">{{ language_key?.PRICE || 'Đơn giá' }}</th>
              <th style="width: 120px; border: darkgrey 1px solid;">Thành tiền</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let data1 of dataPrice.lstPrice">
              <tr style="text-align: center;">
                <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data1.sort > 0 ? data1.sort : '' }}</td>
                <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data1.name }}</td>
                <td style="border: darkgrey 1px solid;" *ngFor="let col of dataPrice.lstPriceCol">
                  {{ data1[col.id] }}
                </td>
                <td style="border: darkgrey 1px solid;">{{ data1.unit}}</td>
                <td style="border: darkgrey 1px solid;">{{ data1.currency}}</td>
                <td style="border: darkgrey 1px solid;">{{ data1.number | number }}</td>
                <td style="border: darkgrey 1px solid;">{{ data1.value | number }}</td>
                <td style="border: darkgrey 1px solid;">{{ data1.price | number }}</td>
              </tr>
              <ng-container *ngFor="let data2 of data1.__childs__">
                <tr style="text-align: center;">
                  <td align="left" style="border: darkgrey 1px solid;">&ensp;&nbsp;{{ data2.sort > 0 ? data2.sort : ''
                    }}
                  </td>
                  <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data2.name }}</td>
                  <td style="border: darkgrey 1px solid;" *ngFor="let col of dataPrice.lstPriceCol">
                    {{ data2[col.id] }}
                  </td>
                  <td style="border: darkgrey 1px solid;">{{ data2.unit}}</td>
                  <td style="border: darkgrey 1px solid;">{{ data2.currency}}</td>
                  <td style="border: darkgrey 1px solid;">{{ data2.number | number }}</td>
                  <td style="border: darkgrey 1px solid;">{{ data2.value | number }}</td>
                  <td style="border: darkgrey 1px solid;">{{ data2.price | number }}</td>
                </tr>
                <ng-container *ngFor="let data3 of data2.__childs__">
                  <tr style="text-align: center;">
                    <td align="left" style="border: darkgrey 1px solid;">&emsp;&nbsp;{{ data3.sort > 0 ? data3.sort : ''
                      }}
                    </td>
                    <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data3.name }}</td>
                    <td style="border: darkgrey 1px solid;" *ngFor="let col of dataPrice.lstPriceCol">
                      {{ data3[col.id] }}
                    </td>
                    <td style="border: darkgrey 1px solid;">{{ data3.unit}}</td>
                    <td style="border: darkgrey 1px solid;">{{ data3.currency}}</td>
                    <td style="border: darkgrey 1px solid;">{{ data3.number | number }}</td>
                    <td style="border: darkgrey 1px solid;">{{ data3.value | number }}</td>
                    <td style="border: darkgrey 1px solid;">{{ data3.price | number }}</td>
                  </tr>
                </ng-container>
              </ng-container>
            </ng-container>
          </tbody>
          <tfoot>
            <tr style="text-align: center;">
              <td></td>
              <td align="left">&nbsp;Tổng cộng</td>
              <td *ngFor="let col of dataPrice.lstPriceCol"></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td>{{dataPrice.totalPrice | number}}</td>
            </tr>
          </tfoot>
        </table>
      </div>
      <div style="margin-top: 20px;" *ngIf="isShowListBidDeal">
        <table id="history-table" align="center">
          <thead>
            <tr>
              <th style="width: 60px; border: darkgrey 1px solid;">&nbsp;{{ language_key?.NO || 'STT' }}</th>
              <th style="width: 500px; border: darkgrey 1px solid;">&nbsp;Lịch sử nộp giá</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let result of lstResult; let i = index;">
              <td align="center" style="border: darkgrey 1px solid;">{{ i + 1 }}</td>
              <td style="border: darkgrey 1px solid;">&nbsp;{{ result.name }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Footer -->
      <div style="margin-top: 30px; padding: 0px 5px;">
        <div style="text-align: right; font-weight: 700;" *ngIf="bid.approveChooseSupplierWinDate">
          Ngày {{bid.approveChooseSupplierWinDate | date: 'dd'}}
          tháng {{bid.approveChooseSupplierWinDate | date: 'MM'}}
          năm {{bid.approveChooseSupplierWinDate | date: 'yyyy'}}
        </div>
        <div style="text-align: right; font-weight: 700;" *ngIf="!bid.approveChooseSupplierWinDate">
          Ngày .....
          tháng .....
          năm .....
        </div>
        <table style="margin: 10px 0 100px 0;">
          <thead>
            <tr style="text-align: center;">
              <th style="width: 30px;"></th>
              <th style="width:50%;">ĐỀ XUẤT</th>
              <th style="width:50%;">PHÊ DUYỆT</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </nz-tab>
  <nz-tab nzTitle="BÁO CÁO LỊCH SỬ NỘP GIÁ">
    <div style="text-align: center; font-weight: 700; font-size: 18px;">
      <div>BÁO CÁO LỊCH SỬ NỘP GIÁ</div>
      <div>{{bidSupplier.supplierName}}</div>
      <div>TÊN GÓI THẦU: {{bidSupplier.bidName}}</div>
      <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
    </div>
    <div>
      <b class="bg-antiquewhite">Nhận xét:</b> {{ bidSupplier.notePrice }}
    </div>
    <div class="mt-2" *ngFor="let result of lstResult; let i = index">
      <nz-collapse nzBordered="false">
        <nz-collapse-panel [nzHeader]="result.name" class="ant-bg-antiquewhite" [nzActive]="i == 0">
          <nz-row *ngIf="result.type == 0" class="mt-2">
            <nz-descriptions [nzColumn]="2">
              <nz-descriptions-item nzSpan="1" nzTitle="File chi tiết giá"
                *ngIf="result.filePriceDetail && result.filePriceDetail.length > 0">
                <a href="{{ result.filePriceDetail }}" target="_blank"> Xem file</a>
              </nz-descriptions-item>
              <nz-descriptions-item nzSpan="1" nzTitle="File chi tiết kỹ thuật"
                *ngIf="result.fileTechDetail && result.fileTechDetail.length > 0">
                <a href="{{ result.fileTechDetail }}" target="_blank"> Xem file </a>
              </nz-descriptions-item>
            </nz-descriptions>
          </nz-row>

          <nz-row *ngIf="result.type == 1" class="mt-2">
            <nz-descriptions [nzColumn]="2">
              <nz-descriptions-item nzSpan="1" nzTitle="File chi tiết giá"
                *ngIf="result.filePriceDetail && result.filePriceDetail.length > 0">
                <a href="{{ result.filePriceDetail }}" target="_blank"> Xem file</a>
              </nz-descriptions-item>
              <nz-descriptions-item nzSpan="1" nzTitle="File chi tiết kỹ thuật"
                *ngIf="result.fileTechDetail && result.fileTechDetail.length > 0">
                <a href="{{ result.fileTechDetail }}" target="_blank"> Xem file </a>
              </nz-descriptions-item>
            </nz-descriptions>

            <nz-descriptions [nzColumn]="1">
              <nz-descriptions-item nzSpan="1" nzTitle="Link driver các file bổ sung"
                *ngIf="result.linkDriver && result.linkDriver.length > 0">
                {{ result.linkDriver }}
              </nz-descriptions-item>
            </nz-descriptions>
          </nz-row>
          <nz-row class="mt-3">
            <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
              (click)="clickExportExcelByTable('price-table-'+i)">
              <span nz-icon nzType="download"></span>Xuất excel
            </button>
          </nz-row>
          <nz-row class="mt-1">
            <nz-table nz-col nzSpan="24" [id]="'price-table-'+i" [nzData]="result.lstPrice" [(nzPageSize)]="pageSize"
              [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
              <thead>
                <tr>
                  <th>Tên hạng mục</th>
                  <th *ngFor="let col of result.lstPriceCol" class="dynamic-col-mpo">
                    {{ col.name }}
                  </th>
                  <th>Đơn vị tính</th>
                  <th>Đơn vị tiền tệ</th>
                  <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                  <th>Thông tin cung cấp</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let data1 of result.lstPrice">
                  <tr>
                    <td class="mw-25">
                      {{ data1.name }}
                    </td>
                    <td *ngFor="let col of result.lstPriceCol">
                      {{ data1[col.id] }}
                    </td>
                    <td class="text-right">{{ data1.unit}}</td>
                    <td class="text-right">{{ data1.currency}}</td>
                    <td class="text-right">{{ data1.number | number }}</td>
                    <td class="text-right">{{ data1.value | number }}</td>
                  </tr>
                  <ng-container *ngFor="let data2 of data1.__childs__">
                    <tr>
                      <td [nzIndentSize]="5" class="mw-25">{{ data2.name }}</td>
                      <td *ngFor="let col of result.lstPriceCol">
                        {{ data2[col.id] }}
                      </td>
                      <td class="text-right">{{ data2.unit}}</td>
                      <td class="text-right">{{ data2.currency}}</td>
                      <td class="text-right">{{ data2.number | number }}</td>
                      <td class="text-right">{{ data2.value | number }}</td>
                    </tr>
                    <ng-container *ngFor="let data3 of data2.__childs__">
                      <tr>
                        <td [nzIndentSize]="30" class="mw-25">{{ data3.name }}</td>
                        <td *ngFor="let col of result.lstPriceCol">{{ data3[col.id] }}</td>
                        <td class="text-right">{{ data3.unit}}</td>
                        <td class="text-right">{{ data3.currency}}</td>
                        <td class="text-right">{{ data3.number | number }}</td>
                        <td class="text-right">{{ data3.value | number }}</td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>
    </div>
  </nz-tab>
</nz-tabset>