<div id="print-section2" *ngIf="dataObject" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150">
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BÁO CÁO BẢNG CHÀO GIÁ</div>
    <div>CÔNG TY: {{dataObject.supplierName}}</div>
    <div>TÊN GÓI THẦU: {{dataObject.bidName}}</div>
    <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px;">
    <table id="price-detail-table" style="margin-bottom: 10px; border-spacing: 0;" align="center">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 40px; border: darkgrey 1px solid;">
            {{ language_key?.NO || 'STT' }}
          </th>
          <th style="width: 200px; border: darkgrey 1px solid;">Tên hạng mục</th>
          <th style="width: 120px; border: darkgrey 1px solid;" *ngFor="let col of dataObject.bidPriceCols">{{
            col.name }}
          </th>
          <th style="width: 120px; border: darkgrey 1px solid;">Đơn vị tính</th>
          <th style="width: 120px; border: darkgrey 1px solid;">Đơn vị tiền tệ</th>
          <th style="width: 120px; border: darkgrey 1px solid;">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th style="width: 120px; border: darkgrey 1px solid;">{{ language_key?.PRICE || 'Đơn giá' }}</th>
          <th style="width: 120px; border: darkgrey 1px solid;">Thành tiền</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data1 of dataObject.bidPrices">
          <tr style="text-align: center;">
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data1.sort > 0 ? data1.sort : '' }}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data1.name }}</td>
            <td align="left" style="border: darkgrey 1px solid;" *ngFor="let col of dataObject.bidPriceCols">
              {{data1[col.id] }}
            </td>
            <td style="border: darkgrey 1px solid;">{{ data1.unit}}</td>
            <td style="border: darkgrey 1px solid;">{{ data1.currency}}</td>
            <td style="border: darkgrey 1px solid;">{{ data1.number | number }}</td>
            <td style="border: darkgrey 1px solid;">{{ data1.value | number }}</td>
            <td style="border: darkgrey 1px solid;">{{ data1.price | number }}</td>
          </tr>
          <ng-container *ngFor="let data2 of data1.__childs__">
            <tr style="text-align: center;">
              <td align="left" style="border: darkgrey 1px solid;">
                &ensp;&nbsp;{{ data2.sort > 0 ? data2.sort : ''}}
              </td>
              <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data2.name }}</td>
              <td align="left" style="border: darkgrey 1px solid;" *ngFor="let col of dataObject.bidPriceCols">
                {{ data2[col.id] }}</td>
              <td style="border: darkgrey 1px solid;">{{ data2.unit}}</td>
              <td style="border: darkgrey 1px solid;">{{ data2.currency}}</td>
              <td style="border: darkgrey 1px solid;">{{ data2.number | number }}</td>
              <td style="border: darkgrey 1px solid;">{{ data2.value | number }}</td>
              <td style="border: darkgrey 1px solid;">{{ data2.price | number }}</td>
            </tr>
            <ng-container *ngFor="let data3 of data2.__childs__">
              <tr style="text-align: center;">
                <td align="left" style="border: darkgrey 1px solid;">
                  &emsp;&nbsp;{{ data3.sort > 0 ? data3.sort : ''}}
                </td>
                <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data3.name }}</td>
                <td align="left" style="border: darkgrey 1px solid;" *ngFor="let col of dataObject.bidPriceCols">
                  {{ data3[col.id] }}</td>
                <td style="border: darkgrey 1px solid;">{{ data3.unit}}</td>
                <td style="border: darkgrey 1px solid;">{{ data3.currency}}</td>
                <td style="border: darkgrey 1px solid;">{{ data3.number | number }}</td>
                <td style="border: darkgrey 1px solid;">{{ data3.value | number }}</td>
                <td style="border: darkgrey 1px solid;">{{ data3.price | number }}</td>
              </tr>
            </ng-container>
          </ng-container>
        </ng-container>
      </tbody>
      <tfoot>
        <tr style="text-align: center;">
          <td></td>
          <td align="left">&nbsp;Tổng cộng</td>
          <td *ngFor="let col of dataObject.bidPriceCols"></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td>{{dataObject.totalPrice | number}}</td>
        </tr>
      </tfoot>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px;">
    <div style="text-align: right; font-weight: 700;" *ngIf="dataObject.approveChooseSupplierWinDate">
      Ngày {{dataObject.approveChooseSupplierWinDate | date: 'dd'}}
      tháng {{dataObject.approveChooseSupplierWinDate | date: 'MM'}}
      năm {{dataObject.approveChooseSupplierWinDate | date: 'yyyy'}}
    </div>
    <div style="text-align: right; font-weight: 700;" *ngIf="!dataObject.approveChooseSupplierWinDate">
      Ngày .....
      tháng .....
      năm .....
    </div>
    <table style="margin: 10px 0 100px 0;">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 30px;"></th>
          <th style="width:50%;">ĐỀ XUẤT</th>
          <th style="width:50%;">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="text-center">
  <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button (click)="clickExportExcel()" class="mr-2">
    <span nz-icon nzType="download"></span>Xuất excel</button>
  <button *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)" nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section2" ngxPrint>
    In báo cáo
  </button>
</div>