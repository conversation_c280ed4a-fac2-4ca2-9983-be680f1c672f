import { Component, OnInit, Input } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-bid-result-price',
  templateUrl: './bid-result-price.component.html',
})
export class BidResultPriceComponent implements OnInit {
  dataObject: any
  loading = false
  pageSize = enumData.Page.pageSizeMax
  todate = new Date()
  @Input()
  public bidId!: string
  @Input()
  public supplierId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
    if (this.bidId) {
      this.loadDataList()
    }
  }

  loadDataList() {
    this.loading = true
    this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_PRICE(this.bidId), { supplierId: this.supplierId }).then((res) => {
      this.loading = false
      this.dataObject = res
      this.dataObject.totalPrice = 0

      if (this.dataObject.bidPrices.length > 0) {
        for (const data1 of this.dataObject.bidPrices) {
          data1.price = data1.number * +data1.value
          this.dataObject.totalPrice += data1.price
          for (const data2 of data1.__childs__) {
            data2.price = data2.number * +data2.value
            for (const data3 of data2.__childs__) {
              data3.price = data3.number * +data3.value
            }
          }
        }
      }
    })
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('price-detail-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Kết quả đánh giá bảng chào giá doanh nghiệp ${this.dataObject.supplierName}.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
