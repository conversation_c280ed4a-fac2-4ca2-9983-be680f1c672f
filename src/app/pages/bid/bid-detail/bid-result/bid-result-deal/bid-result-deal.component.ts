import { Component, OnInit, Input } from '@angular/core'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, StorageService } from '../../../../../services'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidResultDealDetailComponent } from '../bid-result-deal-detail/bid-result-deal-detail.component'
import { Subscription } from 'rxjs'

/** Kết quả đàm phán */
@Component({
  selector: 'app-bid-result-deal',
  templateUrl: './bid-result-deal.component.html',
})
export class BidResultDealComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  @Input()
  public bidId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private drawerService: NzDrawerService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  searchData(reset = false) {
    if (reset) this.pageIndex = 1

    this.loading = true
    const dataSearch: any = {
      where: { bidId: this.bidId },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.loading = true
    this.apiService.post(this.apiService.BID_DETAIL.GET_LIST_BID_RESULT_DEAL, dataSearch).then((res) => {
      if (res) {
        this.loading = false
        this.listOfData = res[0]
        this.total = res[1]
      }
    })
  }

  showDetail(bidDealId: string) {
    this.drawerService.create({
      nzTitle: 'Chi tiết kết quả đàm phán',
      nzContent: BidResultDealDetailComponent,
      nzContentParams: { bidDealId },
      nzWidth: '640',
    })
  }
}
