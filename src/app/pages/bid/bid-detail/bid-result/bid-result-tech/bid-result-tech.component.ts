import { Component, OnInit, Input } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
@Component({
  selector: 'app-bid-result-tech',
  templateUrl: './bid-result-tech.component.html',
})
export class BidResultTechComponent implements OnInit {
  dataObject: any
  loading = false
  listOfData: any[] = []
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax
  todate = new Date()
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  public bidId!: string
  @Input()
  public supplierId!: string
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
    this.loadDataList()
  }

  loadDataList() {
    if (this.bidId) {
      this.loading = true
      this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_TECH(this.bidId), { supplierId: this.supplierId }).then((data) => {
        if (data) {
          this.loading = false
          this.dataObject = data
        }
      })
    }
  }

  getSupplierScore(bidTechId: string) {
    const bidTech = this.dataObject.bidTechs.find((p: any) => p.id === bidTechId)
    if (bidTech && bidTech.percent > 0 && bidTech.__childs__?.length > 0) {
      let temp = 0
      for (const child of bidTech.__childs__) {
        const find = this.dataObject.__bidSupplierTechValue__.find((p: any) => p.bidTechId === child.id)
        if (find && find.score > 0) {
          temp += find.score
        }
      }
      temp = (temp * bidTech.percent) / 100
      return temp
    } else if (this.dataObject.__bidSupplierTechValue__) {
      const find = this.dataObject.__bidSupplierTechValue__.find((p: any) => p.bidTechId === bidTechId)
      if (find) {
        return find.score
      }
      return ''
    } else {
      return ''
    }
  }

  getSupplierValue(bidTechId: string) {
    if (this.dataObject.__bidSupplierTechValue__) {
      const find = this.dataObject.__bidSupplierTechValue__.find((p: any) => p.bidTechId === bidTechId)
      if (find) {
        return find.value
      }
      return ''
    } else {
      return ''
    }
  }

  convertTypeList(bidTech: any) {
    if (this.dataObject.__bidSupplierTechValue__) {
      const list = bidTech.__bidTechListDetails__
      const find = this.dataObject.__bidSupplierTechValue__.find((p: any) => p.bidTechId === bidTech.id)
      if (find && list) {
        const data = list.find((p: any) => p.id === find.value)
        if (data) {
          return data.name
        }
      }
      return ''
    } else {
      return ''
    }
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('tech-detail-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Kết quả đánh giá yêu cầu kỹ thuật doanh nghiệp ${
        this.dataObject.supplierName
      }.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
