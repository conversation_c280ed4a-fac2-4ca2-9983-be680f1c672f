<div id="print-section2" *ngIf="dataObject" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150">
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BÁO CÁO BẢNG CƠ CẤU GIÁ</div>
    <div>CÔNG TY: {{ dataObject.supplierName }}</div>
    <div>TÊN GÓI THẦU: {{ dataObject.bidName }}</div>
    <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px;">
    <table id="custom-price-table" style="margin-bottom: 10px; border-spacing: 0;" align="center">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 40px; border: darkgrey 1px solid;">
            {{ language_key?.NO || 'STT' }}
          </th>
          <th style="width: 30%; border: darkgrey 1px solid;">Tên hạng mục</th>
          <th style="width: 15%; border: darkgrey 1px solid;">Đơn vị tính</th>
          <th style="width: 15%; border: darkgrey 1px solid;">Đơn vị tiền tệ</th>
          <th style="width: 15%; border: darkgrey 1px solid;">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th style="width: cllc(25%-40px); border: darkgrey 1px solid;">Thông tin cung cấp</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of dataObject.__bidSupplierCustomPriceValue__">
          <tr style="text-align: center;">
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.sort > 0 ? data.sort : '' }}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.name }}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.unit }}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.currency }}</td>
            <td style="border: darkgrey 1px solid;">{{ data.number | number }}</td>
            <td style="border: darkgrey 1px solid;">{{ data.value | number }}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px;">
    <div style="text-align: right; font-weight: 700;" *ngIf="dataObject.approveChooseSupplierWinDate">
      Ngày {{dataObject.approveChooseSupplierWinDate | date: 'dd'}}
      tháng {{dataObject.approveChooseSupplierWinDate | date: 'MM'}}
      năm {{dataObject.approveChooseSupplierWinDate | date: 'yyyy'}}
    </div>
    <div style="text-align: right; font-weight: 700;" *ngIf="!dataObject.approveChooseSupplierWinDate">
      Ngày .....
      tháng .....
      năm .....
    </div>
    <table style="margin: 10px 0 100px 0;">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 30px;"></th>
          <th style="width:50%;">ĐỀ XUẤT</th>
          <th style="width:50%;">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-5 text-center">
  <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" (click)="clickExportExcel()" class="mr-2">
    <span nz-icon nzType="download"></span>Xuất excel
  </button>
  <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)" [useExistingCss]="true" printSectionId="print-section2" ngxPrint>
    In báo cáo
  </button>
</div>