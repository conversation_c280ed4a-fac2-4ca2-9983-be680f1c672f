import { Component, OnInit, Input } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-bid-result-custom-price',
  templateUrl: './bid-result-custom-price.component.html',
})
export class BidResultCustomPriceComponent implements OnInit {
  dataObject: any
  loading = false
  pageSize = enumData.Page.pageSizeMax
  todate = new Date()
  @Input()
  public bidId!: string
  @Input()
  public supplierId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_011.code
    this.loadDataList()
  }

  loadDataList() {
    if (this.bidId) {
      this.loading = true
      this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_CUSTOM_PRICE(this.bidId), { supplierId: this.supplierId }).then((res) => {
        this.loading = false
        this.dataObject = res
      })
    }
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('custom-price-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Kết quả đánh giá thông tin cơ cấu giá doanh nghiệp ${
        this.dataObject.supplierName
      }.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
