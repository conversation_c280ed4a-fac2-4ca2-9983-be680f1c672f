<div id="print-section2" *ngIf="dataObject" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150">
  <div style="text-align: center; font-weight: 700; font-size: 18px;">
    <div>BÁO CÁO ĐÁNH GIÁ ĐIỀU KIỆN THƯƠNG MẠI</div>
    <div>CÔNG TY: {{dataObject.supplierName}}</div>
    <div>TÊN GÓI THẦU: {{dataObject.bidName}}</div>
    <div style="font-size: 15px;">Ngày in: {{todate | date: 'dd/MM/yyyy'}}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px;">
    <table id="trade-detail-table" style="margin-bottom: 10px; border-spacing: 0;" align="center">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 40px; border: darkgrey 1px solid;">{{ language_key?.NO || 'STT' }}</th>
          <th style="width: 30%; border: darkgrey 1px solid;">Tên tiêu chí</th>
          <th style="width: 15%; border: darkgrey 1px solid;">Tỉ trọng<br>(100%)</th>
          <th style="width: 15%; border: darkgrey 1px solid;">Giá trị đạt</th>
          <th style="width: 20%; border: darkgrey 1px solid;">Thông tin cung cấp</th>
          <th style="width: calc(20%-40px); border: darkgrey 1px solid;">Điểm đánh giá</th>
        </tr>
        <tr style="text-align: center;">
          <th style="border: darkgrey 1px solid;"></th>
          <th style="border: darkgrey 1px solid;">Điểm đánh giá</th>
          <th style="border: darkgrey 1px solid;">100</th>
          <th style="border: darkgrey 1px solid;"></th>
          <th style="border: darkgrey 1px solid;"></th>
          <th style="border: darkgrey 1px solid;">{{ dataObject.scoreTrade | number: '1.0-2' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of dataObject.bidTrades">
          <tr style="text-align: center;">
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.sort > 0 ? data.sort : '' }}</td>
            <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ data.name }}</td>
            <td style="border: darkgrey 1px solid;">{{ data.percent }}</td>
            <td style="border: darkgrey 1px solid;">{{ data.percentRule | number }}</td>
            <td style="border: darkgrey 1px solid;">
              <span *ngIf="data.type === dataType.String.code">{{ getSupplierValue(data.id) }}</span>
              <span *ngIf="data.type === dataType.Number.code">{{ getSupplierValue(data.id) | number }}</span>
              <span *ngIf="data.type === dataType.Date.code">{{
                getSupplierValue(data.id) | date: 'dd/MM/yyyy'
                }}</span>
              <span *ngIf="data.type === dataType.List.code">
                {{ convertTypeList(data) }}
              </span>
              <span *ngIf="data.type === dataType.File.code && getSupplierValue(data.id)">
                <a href="{{ getSupplierValue(data.id) }}" target="_blank">
                  <span nz-icon nzType="file-text"></span> Xem file đính kèm
                </a>
              </span>
            </td>
            <td style="border: darkgrey 1px solid;">
              {{ getSupplierScore(data.id) | number: '1.0-2' }}
            </td>
          </tr>
          <ng-container *ngFor="let item of data.__childs__">
            <tr style="text-align: center;">
              <td align="left" style="border: darkgrey 1px solid;">&ensp;&nbsp;{{ item.sort > 0 ? item.sort : '' }}</td>
              <td align="left" style="border: darkgrey 1px solid;">&nbsp;{{ item.name }}</td>
              <td style="border: darkgrey 1px solid;">{{ item.percent }}</td>
              <td style="border: darkgrey 1px solid;">{{ item.percentRule | number }}</td>
              <td style="border: darkgrey 1px solid;">
                <span *ngIf="item.type === dataType.String.code">{{ getSupplierValue(item.id) }}</span>
                <span *ngIf="item.type === dataType.Number.code">{{ getSupplierValue(item.id) | number }}</span>
                <span *ngIf="item.type === dataType.Date.code">{{
                  getSupplierValue(item.id) | date: 'dd/MM/yyyy'
                  }}</span>
                <span *ngIf="item.type === dataType.List.code">
                  {{ convertTypeList(item) }}
                </span>
                <span *ngIf="item.type === dataType.File.code && getSupplierValue(item.id)">
                  <a href="{{ getSupplierValue(item.id) }}" target="_blank">
                    <span nz-icon nzType="file-text"></span> Xem file đính kèm
                  </a>
                </span>
              </td>
              <td style="border: darkgrey 1px solid;">
                {{ getSupplierScore(item.id) | number: '1.0-2' }}
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px;">
    <div style="text-align: right; font-weight: 700;" *ngIf="dataObject.approveChooseSupplierWinDate">
      Ngày {{dataObject.approveChooseSupplierWinDate | date: 'dd'}}
      tháng {{dataObject.approveChooseSupplierWinDate | date: 'MM'}}
      năm {{dataObject.approveChooseSupplierWinDate | date: 'yyyy'}}
    </div>
    <div style="text-align: right; font-weight: 700;" *ngIf="!dataObject.approveChooseSupplierWinDate">
      Ngày .....
      tháng .....
      năm .....
    </div>
    <table style="margin: 10px 0 100px 0;">
      <thead>
        <tr style="text-align: center;">
          <th style="width: 30px;"></th>
          <th style="width:50%;">ĐỀ XUẤT</th>
          <th style="width:50%;">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-5 text-center">
  <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button (click)="clickExportExcel()" class="mr-2">
    <span nz-icon nzType="download"></span>Xuất excel
  </button>
  <button *ngIf="authenticationService.checkPermission([enumRole], action.Print.code)" nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section2" ngxPrint>
    In báo cáo
  </button>
</div>