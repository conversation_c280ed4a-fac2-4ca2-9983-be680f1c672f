import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService } from '../../../../../services'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidResultDealDetailSupplierComponent } from '../bid-result-deal-detail-supplier/bid-result-deal-detail-supplier.component'
import { BidSupplierDetailComponent } from '../../../bid-supplier-detail/bid-supplier-detail.component'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-result-deal-detail.component.html' })
export class BidResultDealDetailComponent implements OnInit {
  loading = false
  dataObject: any = { listChild: [] }
  pageSize = enumData.Page.pageSizeMax
  language_key: any
  subscriptions: Subscription = new Subscription()
  dicActiveCollapse: any = {}

  @Input()
  public bidDealId!: string
  constructor(
    private apiService: ApiService,
    private coreService: CoreService,
    private storageService: StorageService,
    private drawerService: NzDrawerService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    if (!this.bidDealId) return
    this.searchData()
  }

  searchData() {
    this.loading = true
    this.apiService.get(this.apiService.BID_DETAIL.GET_BID_RESULT_DEAL(this.bidDealId), {}).then((res) => {
      if (res) {
        this.loading = false
        this.dataObject = res
      }
    })
  }

  showDetail(data: any) {
    this.drawerService.create({
      nzTitle: 'Chi tiết đàm phán giá',
      nzContent: BidResultDealDetailSupplierComponent,
      nzContentParams: { bidDealSupplierId: data.id },
      nzWidth: '640',
      nzPlacement: 'left',
    })
  }

  showSupplierDetail(supplierId: any) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết doanh nghiệp',
      nzContent: BidSupplierDetailComponent,
      nzContentParams: { supplierId },
      nzWidth: '640',
      nzPlacement: 'left',
    })
  }
}
