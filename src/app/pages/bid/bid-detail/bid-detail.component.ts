import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../services'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './bid-detail.component.html' })
export class BidDetailComponent implements OnInit {
  dataObject: any = {}
  bidId: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public bid: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDetail()
  }

  loadDetail() {
    if (this.bid.id) {
      this.notifyService.showloading()
      this.apiService.get(this.apiService.BID_DETAIL.GET_CHECK_DEAL_AND_AUCTION(this.bid.id), {}).then((data) => {
        if (data) {
          this.notifyService.hideloading()
          this.dataObject = data
          this.bidId = this.bid.id
        }
      })
    }
  }
}
