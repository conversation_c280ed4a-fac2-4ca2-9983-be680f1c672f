import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService } from '../../../../services'
import { enumData } from '../../../../core'

@Component({
  selector: 'app-bid-history-detail',
  templateUrl: './bid-history-detail.component.html',
})
export class BidHistoryDetailComponent implements OnInit {
  pageSize = enumData.Page.pageSize
  pageIndex = enumData.Page.pageIndex
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  @Input()
  public bidId!: string
  constructor(private apiService: ApiService, public coreService: CoreService) {}

  ngOnInit() {
    this.searchData()
  }

  searchData() {
    if (!this.bidId) return

    const dataSearch: any = {
      where: { bidId: this.bidId },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.loading = true
    this.apiService.post(this.apiService.BID_DETAIL.PAGINATION_HISTORY, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.listOfData = data[0]
        this.total = data[1]
      }
    })
  }
}
