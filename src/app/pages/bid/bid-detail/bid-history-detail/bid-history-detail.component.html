<nz-row *ngIf="bidId">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Ngư<PERSON><PERSON> cập nhật</th>
        <th>Nội dung cập nhật</th>
        <th>Th<PERSON><PERSON> gian cập nhật</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25">{{ data.employeeName }}</td>
        <td class="mw-25">{{ data.statusName }}</td>
        <td class="mw-25">{{ data.createdAt | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData()" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>