<div *ngIf="bidId && data">
  <div nzBordered="false" *ngFor="let item of data.listItem" class="mt-2">

    <nz-collapse nzBordered="false" class="mt-2">
      <nz-collapse-panel nzHeader="Thông tin các hạng mục chào giá" class="ant-bg-lightblue" nzActive="true">
        <nz-row>
          <nz-table nz-col nzSpan="24" class="mt-3" [nzData]="item.listPrice" [(nzPageSize)]="pageSize"
            [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th style="width:120px">{{ language_key?.NO || 'STT' }}</th>
                <th>Tên hạng mục</th>
                <th *ngFor="let col of item.listPriceCol;" class="dynamic-col-mpo">
                  {{ col.name }}
                </th>
                <th><PERSON><PERSON><PERSON> vị t<PERSON></th>
                <th>Đ<PERSON><PERSON> vị tiền tệ</th>
                <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th>Bắt buộc?</th>
                <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let data1 of item.listPrice">
                <tr>
                  <td>{{ data1.sort > 0 ? data1.sort : '' }}</td>
                  <td class="mw-25">{{ data1.name }}</td>
                  <td *ngFor="let col of item.listPriceCol">{{ data1[col.id] }}</td>
                  <td>{{ data1.unit }}</td>
                  <td>{{ data1.currency }}</td>
                  <td class="text-right">{{ data1.number | number }}</td>
                  <td>{{ data1.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                  <td>
                    <button nz-tooltip nzTooltipTitle="Thông tin mở rộng" nz-button
                      [nzType]="data1.__bidPriceListDetails__.length > 0 ? 'default' : 'dashed'"
                      (click)="settingExInfo(data1)">
                      <span nz-icon nzType="unordered-list"></span>
                    </button>
                  </td>
                </tr>
                <ng-container *ngFor="let data2 of data1.__childs__">
                  <tr>
                    <td [nzIndentSize]="5">{{ data2.sort > 0 ? data2.sort : '' }}</td>
                    <td class="mw-25">{{ data2.name }}</td>
                    <td *ngFor="let col of item.listPriceCol">{{ data2[col.id] }}</td>
                    <td>{{ data2.unit }}</td>
                    <td>{{ data2.currency }}</td>
                    <td class="text-right">{{ data2.number | number }}</td>
                    <td>{{ data2.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                    <td>
                      <button nz-tooltip nzTooltipTitle="Thông tin mở rộng" nz-button
                        [nzType]="data2.__bidPriceListDetails__.length > 0 ? 'default' : 'dashed'"
                        (click)="settingExInfo(data2)">
                        <span nz-icon nzType="unordered-list"></span>
                      </button>
                    </td>
                  </tr>
                  <ng-container *ngFor="let data3 of data2.__childs__">
                    <tr>
                      <td [nzIndentSize]="30">{{ data3.sort > 0 ? data3.sort : '' }}</td>
                      <td class="mw-25">{{ data3.name }}</td>
                      <td *ngFor="let col of item.listPriceCol">{{ data3[col.id] }}</td>
                      <td>{{ data3.unit }}</td>
                      <td>{{ data3.currency }}</td>
                      <td class="text-right">{{ data3.number | number }}</td>
                      <td>{{ data3.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                      <td>
                        <button nz-tooltip nzTooltipTitle="Thông tin mở rộng" nz-button
                          [nzType]="data3.__bidPriceListDetails__.length > 0 ? 'default' : 'dashed'"
                          (click)="settingExInfo(data3)">
                          <span nz-icon nzType="unordered-list"></span>
                        </button>
                      </td>
                    </tr>
                  </ng-container>
                </ng-container>
              </ng-container>
            </tbody>
          </nz-table>
        </nz-row>

      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse nzBordered="false" class="mt-3">
      <nz-collapse-panel nzHeader="Thông tin các hạng mục cơ cấu giá" class="ant-bg-lightblue" nzActive="true">
        <nz-row>
          <nz-table nz-col nzSpan="24" class="mt-3" [nzData]="item.listCustomPrice" [(nzPageSize)]="pageSize"
            [nzLoading]="loading2" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>{{ language_key?.NO || 'STT' }}</th>
                <th>Tên hạng mục</th>
                <th>Đơn vị tính</th>
                <th>Đơn vị tiền tệ</th>
                <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th>Bắt buộc?</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let data of item.listCustomPrice">
                <tr>
                  <td>
                    {{ data.sort > 0 ? data.sort : '' }}
                  </td>
                  <td class="mw-25">{{ data.name }}</td>
                  <td>{{ data.unit }}</td>
                  <td>{{ data.currency }}</td>
                  <td class="text-right">{{ data.number | number }}</td>
                  <td>{{ data.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                </tr>
              </ng-container>
            </tbody>
          </nz-table>
        </nz-row>

      </nz-collapse-panel>
    </nz-collapse>

  </div>
</div>