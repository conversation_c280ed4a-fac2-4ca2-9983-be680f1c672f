import { Component, OnInit, Input } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { MatDialog } from '@angular/material/dialog'
import { BidDetailPriceListComponent } from '../bid-detail-price-list/bid-detail-price-list.component'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-bid-detail-price',
  templateUrl: './bid-detail-price.component.html',
})
export class BidDetailPriceComponent implements OnInit {
  loading = false
  loading2 = false
  pageSize = enumData.Page.pageSizeMax
  @Input()
  public bidId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  data: any

  constructor(private apiService: ApiService, public coreService: CoreService, private storageService: StorageService, private dialog: MatDialog) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  searchData() {
    this.loading = true
    this.loading2 = true
    this.apiService.get(this.apiService.BID.GET_PRICE(this.bidId), {}).then((res) => {
      if (res) {
        this.loading = false
        this.loading2 = false
        this.data = res
      }
    })
  }

  settingExInfo(object: any) {
    this.dialog.open(BidDetailPriceListComponent, { disableClose: false, data: object })
  }
}
