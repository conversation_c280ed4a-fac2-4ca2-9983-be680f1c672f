import { Component, OnInit, Input } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, CoreService, StorageService } from '../../../../services'
import { BidDetailTradeListComponent } from '../bid-detail-trade-list/bid-detail-trade-list.component'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-bid-detail-trade',
  templateUrl: './bid-detail-trade.component.html',
})
export class BidDetailTradeComponent implements OnInit {
  loading = false
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax
  @Input()
  public bidId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  data: any

  constructor(private apiService: ApiService, public coreService: CoreService, private storageService: StorageService, private dialog: MatDialog) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  async searchData() {
    this.loading = true
    await this.apiService.get(this.apiService.BID.GET_TRADE(this.bidId), {}).then(async (res) => {
      this.loading = false
      this.data = res
    })
  }

  settingList(object: any) {
    this.dialog
      .open(BidDetailTradeListComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => this.searchData())
  }
}
