<div *ngIf="bidId && data">
  <div nzBordered="false" *ngFor="let item of data.listItem" class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="item.listTrade" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th>{{ language_key?.NO || 'STT' }}</th>
          <th>Tên tiêu chí</th>
          <th>Tỉ trọng(%)</th>
          <th>Giá trị đạt</th>
          <th>Kiểu dữ liệu</th>
          <th>Bắt buộc?</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data1 of item.listTrade">
          <tr>
            <td>{{ data1.sort > 0 ? data1.sort : '' }}</td>
            <td class="mw-25">
              {{ data1.name }}
            </td>
            <td class="text-right">{{ data1.percent }}</td>
            <td class="text-right">{{ data1.percentRule | number }}</td>
            <td>{{ data1.type }}</td>
            <td>{{ data1.isRequired ? 'Bắt buộc' : 'Không' }}</td>
            <td>
              <button *ngIf="data1.type === dataType.List.code" nz-tooltip nzTooltipTitle="Chi tiết danh sách" nz-button
                [nzType]="data1.__bidTradeListDetails__.length > 0 ? 'default' : 'dashed'" (click)="settingList(data1)">
                <span nz-icon nzType="unordered-list"></span>
              </button>
            </td>
          </tr>
          <ng-container>
            <td [nzIndentSize]="10" colspan="8" scope="colgroup" *ngIf="data1.sumPercent < 100 && data1.sumPercent >= 0"
              class="text-orange">
              Tổng tỉ trọng trong mục này đạt {{ data1.sumPercent }}%, chưa đủ 100%
            </td>
            <tr *ngFor="let child of data1.__childs__">
              <td [nzIndentSize]="10">{{ child.sort > 0 ? child.sort : '' }}</td>
              <td class="mw-25">
                {{ child.name }}
              </td>
              <td class="text-right">{{ child.percent }}</td>
              <td class="text-right">{{ child.percentRule | number }}</td>
              <td>{{ child.type }}</td>
              <td>{{ child.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td>
                <button *ngIf="child.type === dataType.List.code" nz-tooltip nzTooltipTitle="Chi tiết danh sách"
                  nz-button [nzType]="child.__bidTradeListDetails__.length > 0 ? 'default' : 'dashed'"
                  (click)="settingList(child)">
                  <span nz-icon nzType="unordered-list"></span>
                </button>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </div>
</div>