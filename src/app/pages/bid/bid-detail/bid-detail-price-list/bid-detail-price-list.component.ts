import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService } from '../../../../services'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core'

@Component({ templateUrl: './bid-detail-price-list.component.html' })
export class BidDetailPriceListComponent implements OnInit {
  constructor(private apiService: ApiService, @Optional() @Inject(MAT_DIALOG_DATA) public data: any) {}

  loading = true
  listOfData: any[] = []
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax

  ngOnInit() {
    this.searchData()
  }

  async searchData() {
    this.apiService.get(this.apiService.BID.BIDPRICELISTDETAIL_LIST(this.data.id), {}).then((res) => {
      this.loading = false
      this.listOfData = res
    })
  }
}
