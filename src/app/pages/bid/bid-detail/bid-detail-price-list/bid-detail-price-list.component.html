<nz-row style="margin:1.5rem">
  <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
    [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tên</th>
        <th>Kiểu dữ liệu</th>
        <th>Giá trị</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData">
        <td class="mw-25">{{ data.name }}</td>
        <td>{{ data.type }}</td>
        <td>
          <p *ngIf="data.type == dataType.String.code || data.type == dataType.Address.code">
            {{ data.value }}
          </p>
          <p class="text-right" *ngIf="data.type == dataType.Km.code || data.type == dataType.Time.code">
            {{ data.value | number: '1.0-2'}}
          </p>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>