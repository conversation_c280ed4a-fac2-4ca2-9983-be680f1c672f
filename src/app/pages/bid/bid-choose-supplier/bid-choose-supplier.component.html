<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="12" *ngIf="data.isMPO">
    <nz-col nzSpan="6">
      <input nz-input placeholder="Tìm theo tên nhà cung cấp" [(ngModel)]="dataSearch.supplierName" />
    </nz-col>
    <nz-col nzSpan="6">
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.lstStatus" nzMode="multiple"
        [nzPlaceHolder]="language_key?.CHOOSE_STATUS_EXPERTISE || 'Chọn trạng thái thẩm định'">
        <nz-option *ngFor="let item of lstStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-select nzShowSearch [(ngModel)]="dataSearch.typeGetData" nzPlaceHolder="Chọn phạm vi lấy">
        <nz-option *ngFor="let item of lstTypeGetData" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="6">
      <button nz-button (click)="loadData()">
        <span nz-icon nzType="search"></span>
        {{ language_key?.SEARCH || 'Tìm kiếm' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3" nzGutter="12">
    <nz-col nzSpan="24">
      <button nz-button nzType="primary"
        *ngIf="!isShowChooseSupplier && data.isMPO  && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn chọn lại nhà cung cấp mời thầu?"
        nzPopconfirmPlacement="bottom" (nzOnConfirm)="reChooseSupplier()">
        <span nz-icon nzType="redo"></span> Chọn lại NCC mời thầu
      </button>
    </nz-col>

  </nz-row>

  <nz-row class="mt-1">
    <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th *ngIf="isShowChooseSupplier" nzShowCheckbox [(nzChecked)]="isAllDisplayDataChecked"
            [nzIndeterminate]="isIndeterminate" (nzCheckedChange)="checkAll($event)" width="20px"></th>
          <th>{{ language_key?.INTERPRISE_CODE || 'Mã nhà cung cấp' }}</th>
          <th>{{ language_key?.INTERPRISE_NAME || 'Tên nhà cung cấp' }}</th>
          <th>Trạng thái NCC</th>
          <th>Ngày đăng ký</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataRow of listOfData">
          <td *ngIf="isShowChooseSupplier" nzShowCheckbox [(nzChecked)]="dataRow.isChosen"
            (nzCheckedChange)="refreshStatus()"></td>
          <td (click)="showSupplierInfo(dataRow)">
            <span nz-tooltip nzTooltipTitle="Chi tiết" nz-icon nzType="info-circle"></span>
            {{ dataRow.supplierCode }}
          </td>
          <td class="mw-25">{{ dataRow.supplierName }}</td>
          <td>{{ dataRow.statusName }}</td>
          <td>{{ dataRow.createdAt | date: 'dd/MM/yyyy' }}</td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>

  <nz-row class="mt-3">
    <h4>Người duyệt ghi chú:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.noteMPOLeader" [disabled]="!isShowAcceptChooseSupplier"></textarea>
    </nz-col>
  </nz-row>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button *ngIf="isShowChooseSupplier && authenticationService.checkPermission([enumRole], action.Update.code)"
      [disabled]="numberOfChecked === 0" nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn mời các nhà cung cấp đã chọn tham gia thầu?" nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="chooseSupplier()" nz-button nzType="primary" class="mr-2">
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
    <button
      *ngIf="isShowSendMPOLeaderCheck && data.isMPO  && authenticationService.checkPermission([enumRole], action.Update.code)"
      [disabled]="numberOfChecked === 0" nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn gửi yêu cầu phê duyệt bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu?"
      nzPopconfirmPlacement="bottom" (nzOnConfirm)="sendMPOLeaderCheck()" nz-button class="ant-btn-blue mr-2">
      Gửi yêu cầu phê duyệt
    </button>
    <button *ngIf="isShowAcceptChooseSupplier  && authenticationService.checkPermission([enumRole], action.Update.code)"
      nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn duyệt bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu?"
      nzPopconfirmPlacement="bottom" (nzOnConfirm)="acceptAll()" nz-button class="ant-btn-blue mr-2">
      Duyệt
    </button>
    <button *ngIf="isShowAcceptChooseSupplier  && authenticationService.checkPermission([enumRole], action.Delete.code)"
      nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn từ chối bảng chào giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu?"
      nzPopconfirmPlacement="bottom" (nzOnConfirm)="rejectAll()" nz-button nzDanger class="mr-2">
      Yêu cầu kiểm tra lại
    </button>
  </nz-col>
</nz-row>