import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { BidSupplierDetailComponent } from '../bid-supplier-detail/bid-supplier-detail.component'
import { Subscription } from 'rxjs'
@Component({ templateUrl: './bid-choose-supplier.component.html' })
export class BidChooseSupplierComponent implements OnInit {
  modalTitle = 'Danh sách nhà cung cấp mời thầu'
  pageSize = enumData.Page.pageSizeMax
  lstStatus = [enumData.SupplierServiceExpertiseStatus.DaThamDinh, enumData.SupplierServiceExpertiseStatus.ChuaThamDinh]
  lstTypeGetData = [
    { value: 1, name: 'Chỉ chọn nhà cung cấp trong LVMH' },
    { value: 2, name: 'Chọn cả nhà cung cấp ngoài LVMH' },
  ]
  loading = false
  dataSearch: any = {
    typeGetData: 1,
    supplierName: '',
    lstStatus: [],
  }
  listOfData: any[] = []
  isAllDisplayDataChecked = false
  isIndeterminate = false
  numberOfChecked = 0
  isShowChooseSupplier = false
  isShowSendMPOLeaderCheck = false
  isShowAcceptChooseSupplier = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BidChooseSupplierComponent>,
    private drawerService: NzDrawerService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.BID_001.code
    this.loadData()
  }

  async loadData() {
    const where: any = { bidId: this.data.id }
    if (this.dataSearch.supplierName && this.dataSearch.supplierName !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    if (this.dataSearch.lstStatus && this.dataSearch.lstStatus.length > 0) {
      where.lstStatus = this.dataSearch.lstStatus
    }
    this.dataSearch.typeGetData = this.dataSearch.typeGetData || 1
    where.typeGetData = this.dataSearch.typeGetData

    this.loading = true
    this.isShowChooseSupplier = false
    this.isShowSendMPOLeaderCheck = false
    this.isShowAcceptChooseSupplier = false

    await this.apiService.post(this.apiService.BID.LOAD_SUPPLIER_INVITE, where).then((res) => {
      this.loading = false
      this.modalTitle = `Danh sách nhà cung cấp mời thầu [${res.bidName}]`
      this.listOfData = res.lstData || []
      this.isShowChooseSupplier = res.isShowChooseSupplier
      this.isShowSendMPOLeaderCheck = res.isShowSendMPOLeaderCheck
      this.isShowAcceptChooseSupplier = res.isShowAcceptChooseSupplier

      this.refreshStatus()
    })
  }

  // load lại danh sách hiển thị mới
  refreshStatus() {
    this.isAllDisplayDataChecked = this.listOfData.every((item) => item.isChosen)
    this.isIndeterminate = this.listOfData.some((item) => item.isChosen) && !this.isAllDisplayDataChecked
    this.numberOfChecked = this.listOfData.filter((item) => item.isChosen).length
  }

  // Chọn tất cả
  checkAll(value: boolean) {
    this.listOfData.forEach((item) => (item.isChosen = value))
    this.refreshStatus()
  }

  async chooseSupplier() {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.BID.BID_CHOOSE_SUPPLIER, { bidId: this.data.id, lstData: this.listOfData }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.loadData()
    })
  }

  async reChooseSupplier() {
    this.notifyService.showloading()
    await this.apiService.put(this.apiService.BID.BID_RECHOOSE_SUPPLIER(this.data.id), {}).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.loadData()
    })
  }

  async sendMPOLeaderCheck() {
    this.notifyService.showloading()
    await this.apiService.put(this.apiService.BID.BID_SEND_MPOLEADER_CHECK(this.data.id), {}).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.loadData()
    })
  }

  acceptAll() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.ACCEPT_ALL, { id: this.data.id, noteMPOLeader: this.data.noteMPOLeader }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.closeDialog(true)
    })
  }

  rejectAll() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.REJECT_ALL, { id: this.data.id, noteMPOLeader: this.data.noteMPOLeader }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.closeDialog(true)
    })
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  showSupplierInfo(data: any) {
    this.drawerService.create({
      nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết nhà cung cấp',
      nzContent: BidSupplierDetailComponent,
      nzContentParams: data,
      nzWidth: '640',
    })
  }
}
