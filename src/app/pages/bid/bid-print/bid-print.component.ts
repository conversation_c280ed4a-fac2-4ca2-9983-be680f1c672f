import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { CoreService, StorageService } from '../../../services'

@Component({ templateUrl: './bid-print.component.html' })
export class BidPrintComponent implements OnInit {
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(public coreService: CoreService, private storageService: StorageService, @Optional() @Inject(MAT_DIALOG_DATA) public data: any) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
  }
}
