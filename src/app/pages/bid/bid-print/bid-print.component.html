<div matDialogContent id="print-section" *ngIf="data">
  <div style="text-align: center;">
    <h3>THÔNG TIN GÓI THẦU {{ data.name | uppercase }}</h3>
  </div>

  <div style="margin-top: 30px;">
    <h4>{{ language_key?.GENERAL_INFO || 'Thông Tin Chung' }}</h4>
    <app-bid-basic-detail-print [bidId]="data.id"></app-bid-basic-detail-print>
  </div>
  <div style="margin-top: 30px;">
    <h4>Thông tin thiết lập yêu cầu kỹ thuật</h4>
    <app-bid-detail-tech [bidId]="data.id"></app-bid-detail-tech>
  </div>
  <div style="margin-top: 30px;">
    <h4>Thông tin các hạng mục chào giá, cơ cấu giá</h4>
    <app-bid-detail-price [bidId]="data.id"></app-bid-detail-price>
  </div>
  <div style="margin-top: 30px;">
    <h4>Thông tin điều kiện thương mại</h4>
    <app-bid-detail-trade [bidId]="data.id"></app-bid-detail-trade>
  </div>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section" ngxPrint>
      In gói thầu
    </button>
  </nz-col>
</nz-row>