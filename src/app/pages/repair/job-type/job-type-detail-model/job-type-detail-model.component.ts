import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { NzImageService } from 'ng-zorro-antd/image'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './job-type-detail-model.component.html' })
export class JobTypeDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT LOẠI CÔNG VIỆC'
  dataObject: any = {}

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private dialogRef: MatDialogRef<JobTypeDetailModelComponent>,
    private nzImageService: NzImageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.FIND_DETAIL, {
      id: this.data.id,
    })
    if (this.dataObject.lstJobTypeImages && this.dataObject.lstJobTypeImages.length > 0) {
      for (const img of this.dataObject.lstJobTypeImages) {
        if (!img.src) img.src = img.url
      }
    }
    this.notifySerivce.hideloading()
  }

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
