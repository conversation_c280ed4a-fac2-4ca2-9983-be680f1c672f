<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <button nzShape="round" class="ml-2 mr-2" nz-button nzType="primary" (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  <!-- <button nzShape="round" *ngIf="coreService.checkPermission(role.Download.code)" class="mr-2" nz-button
    (click)="onDownloadTemplateExcel()"><span nz-icon nzType="download"></span>Tải
    Template Excel</button>
  <input class="hidden" type="file" id="file" (change)="clickImportExcel($event)" placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
  <label *ngIf="coreService.checkPermission(role.Upload.code)" nz-button for="file"
    class="ant-btn lable-custom-file-custom">
    <span nz-icon nzType="upload"></span> Nhập Excel
  </label> -->

  <!-- <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã sửa chữa
            </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.code" name="dataSearch.code" placeholder="Nhập mã sửa chữa" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên sửa chữa
            </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.name" name="dataSearch.name" placeholder="Nhập tên sửa chữa" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng thái
            </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.isDeleted" name="isDeleted"
                nzPlaceHolder="Chọn trạng thái">
                <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name"
                  [nzValue]="item.value"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse> -->
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="['']"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th class="text-center">Ngày áp dụng</th>
        <th class="text-center">Ngày tạo</th>
        <th class="text-center">Trạng thái</th>
        <th class="text-center">Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData">
        <td class="text-center">{{ data.effectiveDateFrom | date : 'dd/MM/yyyy' }} - {{ data.effectiveDateTo | date : 'dd/MM/yyyy' }}</td>
        <td class="text-center">{{ data.createdAt | date : 'dd/MM/yyyy' }}</td>
        <td class="text-center">{{ data.statusName }}</td>
        <td class="text-center text-nowrap">
          <button class="mr-1" (click)="onShowDetail(data)" nz-tooltip nzTooltipTitle="Chi tiết" nz-button nzType="primary" nzGhost nzShape="circle">
            <i nz-icon nzType="eye"></i>
          </button>
          <button
            class="mr-1"
            *ngIf="data.status === enumDataSC.RepairServiceFeeStandardStatus.NEW.code && !data.isDeleted"
            (click)="onShowEdit(data)"
            nz-tooltip
            nzTooltipTitle="Chỉnh Sửa"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <i nz-icon nzType="edit"></i>
          </button>
          <button
            *ngIf="data.status === enumDataSC.RepairServiceFeeStandardStatus.NEW.code"
            class="mr-1"
            (click)="acceptStatus(data)"
            nz-tooltip
            nzTooltipTitle="Duyệt"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <span nz-icon nzType="check-circle" nzTheme="outline"></span>
          </button>
          <button
            *ngIf="data.status === enumDataSC.RepairServiceFeeStandardStatus.NEW.code"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn Huỷ?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="destroyStatus(data)"
            nz-tooltip
            nzTooltipTitle="Huỷ thiết lập điểm thưởng"
            nz-button
            nzType="primary"
            nzDanger
            nzShape="circle"
          >
            <i nz-icon nzType="delete"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
