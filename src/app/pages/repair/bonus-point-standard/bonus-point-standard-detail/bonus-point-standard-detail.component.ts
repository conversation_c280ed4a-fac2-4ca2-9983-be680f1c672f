import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './bonus-point-standard-detail.component.html' })
export class BonusPointStandardDetailComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT THIẾT LẬP ĐIỂM THƯỞNG'
  dataObject: any = {}

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private dialogRef: MatDialogRef<BonusPointStandardDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.FIND_DETAIL, {
      id: this.data.id,
    })
    this.notifySerivce.hideloading()
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
