<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<div nz-row class="mt-4">
  <div nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON>y áp dụng </nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.effectiveDateFrom | date:'dd/MM/yyyy' }} - {{ dataObject.effectiveDateTo| date:'dd/MM/yyyy'
          }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdAt | date:'dd/MM/yyyy' }} </b>
      </nz-form-control>
    </nz-form-item>
  </div>



  <nz-col nzSpan="24" *ngIf="dataObject.lstDetail.length > 0">
    <nz-row class="mt-2">
      <span style="color: red">
        Điểm phạt: Số tiền phạt trên 1 điểm là {{ dataObject.lstDetail[0]?.money | currency:'VND':'symbol':'1.0-0' }}
      </span>
    </nz-row>
    <div nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" [nzData]="['']" [nzShowPagination]="false" class="mb-3" #ajaxTable
        [nzFrontPagination]="false" nzTemplateMode nzBordered>
        <thead>
          <tr>
            <th class="text-center">Điểm</th>
            <th class="text-center">Tiền dịch vụ<i>(VND)</i></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataObject.lstDetail">
            <td>
              {{ item.point | number }}
            </td>
            <td class="text-center" nzWidth="80px">
              {{ item.money | number }}
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-col>
</div>