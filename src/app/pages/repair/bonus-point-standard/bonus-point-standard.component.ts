import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditBonusPointStandardComponent } from './add-or-edit-bonus-point-standrad/add-or-edit-bonus-point-standrad.component'
import { BonusPointStandardDetailComponent } from './bonus-point-standard-detail/bonus-point-standard-detail.component'
import { ApiScService } from 'src/app/services/apiSc.service'
import { enumData } from 'src/app/core'

@Component({
  selector: 'app-bonus-point-standard',
  templateUrl: './bonus-point-standard.component.html',
})
export class BonusPointStandardComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  enumDataSC = enumData
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataSearch = {}
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false

        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  onAddNew() {
    let newData = {
      point: 0,
      money: 0,
    }
    this.listOfData.push(newData)
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditBonusPointStandardComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditBonusPointStandardComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(BonusPointStandardDetailComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onSave() {
    const isCheck = this.listOfData.some((e: any) => e.point === 0 || e.money === 0)
    if (isCheck) {
      this.notifyService.showError('Vui lòng nhập điểm và tiền dịch vụ lớn hơn 0!')
      return
    }

    const pointValue = this.listOfData.map((e: any) => e.point)

    let isDuplicate = pointValue.some((item: any, index: any) => pointValue.indexOf(item) !== index)
    if (isDuplicate) {
      this.notifyService.showError('Vui lòng nhập điểm không trùng nhau!')
      return
    }

    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.CREATE, this.listOfData).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(result.message)
        this.searchData()
      }
    })
  }

  destroyStatus(data: any) {
    this.notifyService.showloading()
    this.apiScService
      .postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.UPDATE_STATUS, {
        id: data.id,
        status: 'DESTROY',
      })
      .then((result: any) => {
        if (result) {
          this.notifyService.hideloading()
          this.notifyService.showSuccess(result.message)
          this.searchData()
        }
      })
  }

  acceptStatus(data: any) {
    this.notifyService.showloading()
    this.apiScService
      .postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.UPDATE_STATUS, {
        id: data.id,
        status: 'ACCEPT',
      })
      .then((result) => {
        if (result) {
          this.notifyService.hideloading()
          this.notifyService.showSuccess(result.message)
          this.searchData()
        }
      })
  }
}
