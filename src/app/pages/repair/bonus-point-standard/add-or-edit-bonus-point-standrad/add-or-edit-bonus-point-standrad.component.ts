import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './add-or-edit-bonus-point-standrad.component.html' })
export class AddOrEditBonusPointStandardComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI THIẾT LẬP ĐIỂM THƯỞNG'
  dataObject: any = {}
  isEditItem = false
  lstMaterialCategory: any[] = []
  dicMaterialCategory: Record<string, any> = {}
  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')
  formatterPoint = (value: number): string => `${value ? value : ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parserPoint = (value: string): string => {
    // Chỉ có 1 dấu . duy nhất ngăn cách giữa phần nguyên và phần thập phân
    // 1,234.56 ✅
    // 1.234.56 ❌
    const result = value.replace(/,/g, '')
    const match = result.match(/^(\d+)?(\.\d*)?$/)
    return match ? result : ''
  }

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<AddOrEditBonusPointStandardComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.dataObject.lstDetail = [
      {
        point: 1,
        money: 0,
      },
    ]
    this.dataObject.lstEffectiveDate = []
    if (this.data && this.data !== null) {
      this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.FIND_DETAIL, {
        id: this.data.id,
      })

      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT ĐỊNH MỨC TIỀN DỊCH VỤ'
    }
  }

  onSave() {
    const isCheck = this.dataObject.lstDetail.some((e: any) => e.point === 0 || e.money === 0)
    if (isCheck) {
      this.notifyService.showError('Vui lòng nhập điểm và tiền dịch vụ lớn hơn 0!')
      return
    }

    const pointValue = this.dataObject.lstDetail.map((e: any) => e.point)

    let isDuplicate = pointValue.some((item: any, index: any) => pointValue.indexOf(item) !== index)
    if (isDuplicate) {
      this.notifyService.showError('Vui lòng nhập điểm không trùng nhau!')
      return
    }

    if (this.dataObject.lstDetail.length > 1) {
      for (let i = 1; i < this.dataObject.lstDetail.length; i++) {
        const currentItem = this.dataObject.lstDetail[i]
        const prevItem = this.dataObject.lstDetail[i - 1]
        if (currentItem.point <= prevItem.point) {
          this.notifyService.showError('Vui lòng nhập điểm lớn hơn điểm trước đó!')
          return
        }
      }
    }

    const data = this.dataObject
    data.effectiveDateFrom = this.dataObject.lstEffectiveDate[0]
    data.effectiveDateTo = this.dataObject.lstEffectiveDate[1]
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_SERVICE_FEE_STANDARD.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  async onAddMaterialCategory() {
    this.dataObject.lstDetail.push({
      id: '',
      point: 0,
      money: 0,
      index: this.dataObject.lstDetail.length,
    })
  }

  async onChangeMaterialCategory(item: any) {
    const mc = this.dicMaterialCategory[item.materialCategoryId]
    item.unitName = mc.unitName
    item.materialCategoryId = mc.id
    item.materialCategoryName = mc.name
  }

  onDelete(index: any) {
    this.dataObject.lstDetail.splice(index, 1)
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
