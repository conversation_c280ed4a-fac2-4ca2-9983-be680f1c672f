import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { RewardDetailComponent } from './reward-detail/reward-detail.component'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  selector: 'app-reward',
  templateUrl: './reward.component.html',
})
export class RewardComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  totalMoney: number = 0
  count: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    // this.loadAllData()/
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_REWARD.PAGINATION, dataSearch).then((res: any) => {
      this.loading = false
      if (res.data) {
        this.total = res.data[1]
        this.listOfData = res.data[0]
        this.count = res.count
        this.totalMoney = res.total
      } else {
        this.total = 0
        this.listOfData = []
        this.count = 0
        this.totalMoney = 0
      }
    })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(RewardDetailComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }
}
