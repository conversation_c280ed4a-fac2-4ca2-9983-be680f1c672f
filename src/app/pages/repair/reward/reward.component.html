<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="T<PERSON><PERSON>" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã nhân viên </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.employeeCode" name="dataSearch.employeeCode" placeholder="Nhập mã nhân viên" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên nhân viên </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.employeeName" name="dataSearch.employeeName" placeholder="Nhập tên nhân viên" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<nz-row>
  <ng-container *ngIf="listOfData.length > 0">
    <div nz-col nzSpan="22"></div>
    <div nz-col nzSpan="4">
      <div nz-row>
        <div nz-col nzSpan="24">
          <nz-form-item nzFlex>
            <div nz-row>
              <nz-form-label [nzSm]="18" [nzXs]="18">Tổng tiền thưởng </nz-form-label>
              <nz-form-control [nzSm]="6" [nzXs]="6" class="text-right">
                <b>{{ totalMoney | number }}</b>
                <i>&nbsp;VNĐ</i>
              </nz-form-control>
              <nz-form-label [nzSm]="18" [nzXs]="18">Tổng công việc </nz-form-label>
              <nz-form-control [nzSm]="6" [nzXs]="6" class="text-right">
                <b>{{ count | number }}</b>
              </nz-form-control>
            </div>
          </nz-form-item>
        </div>
      </div>
    </div>
  </ng-container>
</nz-row>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th class="text-center">Mã nhân viên</th>
        <th class="text-center">Tên nhân viên</th>
        <th class="text-center">Ngày hoàn thành</th>
        <th class="text-center">Mã công việc</th>
        <th class="text-center">Tiền thưởng</th>
        <th class="text-center">Tác Vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">{{ data.employeeCode }}</td>
        <td class="text-center">{{ data.employeeName }}</td>
        <td class="text-center">{{ data.completedDate | date : 'dd/MM/yyyy' }}</td>
        <td class="text-center">{{ data.jobCode }}</td>
        <td class="text-center">{{ data.money | number }}</td>
        <td class="text-center text-nowrap">
          <button class="mr-1" (click)="onShowDetail(data)" nz-tooltip nzTooltipTitle="Chi tiết" nz-button nzType="primary" nzGhost nzShape="circle">
            <i nz-icon nzType="eye"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
