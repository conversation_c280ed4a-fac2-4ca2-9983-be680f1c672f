<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzAlign="middle" nzGutter="8">
    <div nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày áp dụng</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập ngày áp dụng">
          <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataObject.lstEffectiveDate" name="lstEffectiveDate">
          </nz-range-picker>
        </nz-form-control>
      </nz-form-item>
    </div>

    <nz-col nzSpan="24">
      <nz-row class="mt-2">
        <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onAddMaterialCategory()">
          <span nz-icon nzType="plus"></span>Thêm
        </button>
      </nz-row>

      <div nz-row class="mt-3">
        <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstJobTypeMaterialCategories" [nzShowPagination]="false"
          class="mb-3" #ajaxTable [nzFrontPagination]="false" [nzScroll]="{ x: '1200px', y: null }" nzTemplateMode
          nzBordered>
          <thead>
            <tr>
              <th class="text-center" nzRight nzWidth="80px">Tác vụ</th>
              <th class="text-center">Điểm</th>
              <th class="text-center">Số tiền<i> (VND)</i></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstDetail; let i = index">
              <td class="text-center" nzRight nzWidth="80px">
                <button [disabled]="i === 0" nz-button nzType="primary" nzDanger nzShape="circle" nz-tooltip
                  nzTooltipTitle="Xóa" nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?"
                  (nzOnConfirm)="onDelete(i)" class="mr-2">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </td>
              <td class="text-center" nzWidth="80px">
                <nz-input-number [nzDisabled]="i === 0" [nzFormatter]="formatter" [nzParser]="parser"
                  [(ngModel)]="item.point" [ngModelOptions]="{ standalone: true }" [nzMin]="1" name="point" [nzStep]="1"
                  [nzMax]="999999" [nzPlaceHolder]="'Nhập số điểm'" required></nz-input-number>
              </td>
              <td class="text-center" nzWidth="80px">
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.money"
                  [ngModelOptions]="{ standalone: true }" [nzMin]="0" name="money" [nzStep]="1"
                  [nzPlaceHolder]="'Nhập số tiền'" required></nz-input-number>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </nz-col>

  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzShape="round" nzType="primary" class="mr-3"
        (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>