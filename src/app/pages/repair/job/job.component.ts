import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import * as XLSX from 'xlsx'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { JobDetailComponent } from './job-detail/job-detail.component'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({ templateUrl: './job.component.html' })
export class JobComponent implements OnInit {
  currentUser: User | any
  enumData: any
  pageIndex: any
  pageSize: any
  total: any
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  dataEmployee: any = []
  dataResident: any = []
  dataJobCategory: any = []
  dataStatus: any = []
  isShowCompleteModal = false
  dataArea: any = []
  dataApartment: any = []
  dataRoom: any = []
  isVisibleUpdate = false
  dataPaymentType: any = []
  screenWidth: any
  lstJobType: any = []
  isManager: boolean = false
  jobForm: any = [
    { name: 'Tính phí', value: false },
    { name: 'Bảo hành', value: true },
  ]

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    public dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit(): void {
    this.loadAllDataSelect()
    this.screenWidth = window.screen.width
    this.dataPaymentType = this.coreService.convertObjToArray(this.enumData.PaymentType)
    this.dataStatus = this.coreService.convertObjToArray(this.enumData.RepairJobStatus)
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total

    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.PAGINATION, dataSearch).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.loading = false
        this.listOfData = res[0]
        this.total = res[1]
      }
    })
  }

  loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiNtssService.post(this.apiNtssService.EMPLOYEE.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.AREA.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.APARTMENT.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.ROOM.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.LOAD_DATA, {}),
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.dataEmployee = res[0]
      this.dataArea = res[1]
      this.dataApartment = res[2]
      this.dataRoom = res[3]
      this.lstJobType = res[4]
      this.dataJobCategory = res[5]
    })
  }

  onDetail(object: any) {
    // if (object.parentId === this.enumData.JobType2.Multi_Job.code) {
    //   // this.dialog
    //   //   .open(NewMultiJobDetailModelComponent, { disableClose: false, data: object })
    //   //   .afterClosed()
    //   //   .subscribe(() => {})
    // } else {
    this.dialog
      .open(JobDetailComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => {})
    // }
  }

  async onDownloadExcel() {
    this.loading = true

    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      skip: 0,
      take: this.enumData.Page.pageSizeMax,
    }
    this.loading = true
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.notifyService.hideloading()
        if (data && data[0].length > 0) {
          let date = new Date().toISOString()
          const fileName = 'Danh_sach_cong_viec_' + date + '.xlsx'
          let dataExcel: any = []

          data[0].forEach((s: any) => {
            let row: any = {}
            row['Mã Công Việc'] = s.code ? s.code : ''
            row['Công Việc'] = s.hasFee ? 'Tính phí' : 'Bảo hành'
            row['Loại Công Việc'] = s.typeOfJobType + ' - ' + s.jobTypeName
            row['Tên Công Việc'] = s.type ? s.name : s.newJobCategoryName
            row['Khu'] = s.apartmentName ? s.apartmentName : ''
            row['Phòng'] = s.roomName ? s.roomName : ''
            row['Nhân Viên'] = s.employeeName ? s.employeeName : ''
            row['Số lượng vật tư'] = s.quantityMaterial ? s.quantityMaterial : 0
            row['Trạng Thái'] = s.status ? this.coreService.getEnumElementName(this.enumData.JobStatus2, s.status) : ''
            row['Trạng Thái Duyệt'] = s.approvalStatus ? this.coreService.getEnumElementName(this.enumData.JobApprovalStatus, s.approvalStatus) : ''
            row['Thanh Toán'] = s.paymentStatus ? this.coreService.getEnumElementName(this.enumData.PaymentStatus2, s.paymentStatus) : ''
            dataExcel.push(row)
          })
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
          const wb: XLSX.WorkBook = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Danh Sách Công Việc')

          XLSX.writeFile(wb, fileName)
        }
      }
    })
  }

  // areaChange(event: any) {
  //   this.dataSearch.apartmentId = null
  //   this.dataSearch.roomId = null
  //   if (event) {
  //     this.loading = true
  //     let data = {
  //       areaId: event,
  //     }
  //     this.appService.post(this.appService.APARTMENT.LOAD_DATA_BY_AREA, data).then((res: any) => {
  //       if (res) {
  //         this.loading = false
  //         this.dataApartment = res
  //       }
  //     })
  //   } else {
  //   }
  // }

  clickRoom(event: any) {
    this.dataSearch.roomId = null
    if (event) {
      this.LoadDataRoomByApartment(event)
    }
  }

  async LoadDataRoomByApartment(obj: any) {
    let data = {
      apartmentId: obj,
    }

    this.apiNtssService.post(this.apiNtssService.ROOM.LOAD_DATA_BY_APARTMENT, data).then((res: any) => {
      if (res) {
        this.loading = false
        this.dataRoom = res
      }
    })
  }

  handleCancel() {
    this.isVisibleUpdate = false
  }

  // onShowEditOffice(object: any) {
  //   this.dialog
  //     .open(EditJobModelComponent, { disableClose: false, data: object })
  //     .afterClosed()
  //     .subscribe(() => {
  //       this.searchData()
  //     })
  // }
}
