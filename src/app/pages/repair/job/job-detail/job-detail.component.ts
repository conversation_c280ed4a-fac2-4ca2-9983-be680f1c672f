import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { NzImageService } from 'ng-zorro-antd/image'
import { environment } from '../../../../../environments/environment'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './job-detail.component.html' })
export class JobDetailComponent implements OnInit {
  enumData: any
  modelTitle = 'THÔNG TIN CÔNG VIỆC'
  dataObject: any
  UploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private apiScService: ApiScService,
    public dialog: MatDialog,
    private nzImageService: NzImageService,
    private dialogRef: MatDialogRef<JobDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    this.dataObject = new Object()
    this.dataObject.fileList = []
    if (this.data && this.data !== null) {
      this.loadDetail()
    }
  }

  async loadDetail() {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.FIND_DETAIL, { id: this.data.id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }
}
