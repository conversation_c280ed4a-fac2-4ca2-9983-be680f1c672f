<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>
<nz-tabset *ngIf="dataObject" class="mt-3">
  <nz-tab nzTitle="Thông Tin Chung">
    <div nz-row>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã Công Việc</nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.code }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loạ<PERSON>a </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.jobTypeName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại Danh Mục Sửa Chữa </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.jobCategoryName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên Công Việc</nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.name }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.employeeName">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhân Viên </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.employeeName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Vùng
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.areaName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu trọ
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.apartmentName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Phòng
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.roomName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.residentName">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Người Thuê
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.residentName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Lịch Thực Hiện
          </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.scheduleDateFrom | date: 'HH:mm dd/MM/yyyy' }} - {{ dataObject.scheduleDateTo | date:
              'HH:mm dd/MM/yyyy' }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.expiryDate">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hạn Kết Thúc
          </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.expiryDate | date: 'dd/MM/yyyy' }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.processDate">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày Thực Hiện
          </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.processDate | date: 'dd/MM/yyyy' }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.completedDate">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày Hoàn Thành
          </nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24">
            <b>{{ dataObject.completedDate | date: 'dd/MM/yyyy' }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng Thái Công Việc
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b> {{ dataObject.statusName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6" *ngIf="dataObject.approvalStatus">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng Thái Duyệt
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b> {{ dataObject.approvalStatusName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6" *ngIf="dataObject.reason">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" style="white-space: pre-line;">Lý do thay đổi
          </nz-form-label>
          <nz-form-control [nzSm]="23" [nzXs]="24">
            <b>
              {{ dataObject.reason }}
            </b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6" *ngIf="dataObject.description">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" style="white-space: pre-line;">Mô tả
          </nz-form-label>
          <nz-form-control [nzSm]="23" [nzXs]="24">
            <b>
              <div [innerHTML]="dataObject.description"></div>
            </b>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <hr>
      </div>

      <div nz-col nzSpan="6" *ngIf="dataObject.serviceFee > 0">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tiền Dịch Vụ
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.serviceFee | number }}</b>
            <i>&nbsp;VNĐ</i>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.salaryFee > 0">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tiền Công
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.fee | number }}</b>
            <i>&nbsp;VNĐ</i>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.materialFee > 0">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tiền vật tư
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b>{{ dataObject.materialFee | number }}</b>
            <i>&nbsp;VNĐ</i>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6"
        *ngIf="dataObject.money > 0 && dataObject.status === enumData.RepairJobStatus.COMPLETE.code">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tổng tiền thu khách
          </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <b style="font-size: large;">{{ dataObject.money | number }}</b>
            <i>&nbsp;VNĐ</i>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24" *ngIf="dataObject?.lstJobImages && dataObject?.lstJobImages?.length > 5">
        <div nz-row>
          <div nz-col nzSpan="6"></div>
          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh công việc
              </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" style="display: flex;">
                <nz-carousel [nzEffect]="effect" class="carousel" [nzAutoPlay]="true">
                  <div nz-carousel-content *ngFor="let image of dataObject?.lstJobImages; let i = index">
                    <img nz-image width="100%" height="100%" [nzDisablePreview]="true"
                      (click)="openImagePreview(dataObject?.lstJobImages)" style="margin-right: 10px"
                      nzSrc="{{image.url}}" alt="{{ 'Hình ảnh công việc' + i }}" />
                  </div>
                </nz-carousel>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6"></div>
        </div>
      </div>

      <div nz-col nzSpan="24"
        *ngIf="dataObject?.lstJobImages && dataObject?.lstJobImages?.length <= 5 && dataObject?.lstJobImages?.length > 0">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh công việc
          </nz-form-label>
          <nz-form-control [nzSm]="23" [nzXs]="24" style="display: flex;">
            <ng-container *ngFor="let item of dataObject?.lstJobImages; let i = index">
              <img nz-image width="350px" height="250px" style="margin-right: 10px; margin-bottom: 10px"
                [nzDisablePreview]="true" (click)="openImagePreview(dataObject?.lstJobImages)" nzSrc="{{item.url}}"
                alt="{{ 'Hình ảnh công việc' + i }}" />
            </ng-container>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24" *ngIf="dataObject.lstJobDetails && dataObject.lstJobDetails.length > 0">
        <b style="color: blue; font-size: larger;">Danh sách vật tư</b>
        <nz-table #ajaxTable1 [nzData]="['']" [nzShowPagination]="true" nzBordered>
          <thead>
            <tr>
              <th>Tên vật tư</th>
              <th>Số lượng</th>
              <th>Đơn giá</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of dataObject.lstJobDetails">
              <td>{{ data.materialName }}</td>
              <td class="text-right">{{ data.quantity | number}}</td>
              <td class="text-right">{{ data.price | number}}</td>
            </tr>
          </tbody>
        </nz-table>
      </div>

    </div>
    <ng-container *ngIf="dataObject.lstChildJob && dataObject.lstChildJob.length > 0">
      <div class="text-center" style="margin-bottom: 30px;">
        <span class="text-title-sub">Danh sách công việc nhỏ</span>
      </div>
      <ng-container *ngFor="let dataChild of dataObject.lstChildJob;let i = index">
        <div style="margin-bottom: 10px;">
          <b style="color: blue; font-size: larger;">Công việc {{ i + 1 }}</b>
        </div>
        <div nz-row style="margin-bottom: 20px;">
          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã Công Việc</nz-form-label>
              <nz-form-control [nzSm]="12" [nzXs]="24">
                <b>{{ dataChild.code }}</b>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6" *ngIf="!dataChild.type">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại Sửa Chữa </nz-form-label>
              <nz-form-control [nzSm]="12" [nzXs]="24">
                <b>{{ dataChild.jobTypeName }}</b>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại Danh Mục Sửa Chữa </nz-form-label>
              <nz-form-control [nzSm]="12" [nzXs]="24">
                <b>{{ dataChild.jobCategoryName }}</b>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên Công Việc</nz-form-label>
              <nz-form-control [nzSm]="12" [nzXs]="24">
                <b>{{ dataChild.name }}</b>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="24">
            <hr>
          </div>

          <div nz-col nzSpan="6" *ngIf="dataChild.serviceFee > 0">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tiền Dịch Vụ
              </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <b>{{ dataChild.serviceFee | number }}</b>
                <i>&nbsp;VNĐ</i>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6" *ngIf="dataChild.salaryFee > 0">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tiền Công
              </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <b>{{ dataChild.fee | number }}</b>
                <i>&nbsp;VNĐ</i>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6" *ngIf="dataChild.materialFee > 0">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tiền vật tư
              </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <b>{{ dataChild.materialFee | number }}</b>
                <i>&nbsp;VNĐ</i>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6" *ngIf="dataChild.money > 0">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tổng tiền thu khách
              </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <b style="font-size: large;">{{ dataChild.money | number }}</b>
                <i>&nbsp;VNĐ</i>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="24" *ngIf="dataChild?.lstJobImages && dataChild?.lstJobImages?.length > 5">
            <div nz-row>
              <div nz-col nzSpan="6"></div>
              <div nz-col nzSpan="6">
                <nz-form-item nzFlex>
                  <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh công việc
                  </nz-form-label>
                  <nz-form-control [nzSm]="24" [nzXs]="24" style="display: flex;">
                    <nz-carousel [nzEffect]="effect" class="carousel" [nzAutoPlay]="true">
                      <div nz-carousel-content *ngFor="let image of dataChild?.lstJobImages; let i = index">
                        <img nz-image width="100%" height="100%" [nzDisablePreview]="true"
                          (click)="openImagePreview(dataChild?.lstJobImages)" style="margin-right: 10px"
                          nzSrc="{{image.url}}" alt="{{ 'Hình ảnh công việc' + i }}" />
                      </div>
                    </nz-carousel>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col nzSpan="6"></div>
            </div>
          </div>

          <div nz-col nzSpan="24"
            *ngIf="dataChild?.lstJobImages && dataChild?.lstJobImages?.length <= 5 && dataChild?.lstJobImages?.length > 0">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh công việc
              </nz-form-label>
              <nz-form-control [nzSm]="23" [nzXs]="24" style="display: flex;">
                <ng-container *ngFor="let item of dataChild?.lstJobImages; let i = index">
                  <img nz-image width="350px" height="250px" style="margin-right: 10px; margin-bottom: 10px"
                    [nzDisablePreview]="true" (click)="openImagePreview(dataChild?.lstJobImages)" nzSrc="{{item.url}}"
                    alt="{{ 'Hình ảnh công việc' + i }}" />
                </ng-container>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="24" *ngIf="dataChild.lstJobDetails && dataChild.lstJobDetails.length > 0">
            <b style="color: blue; font-size: larger;">Danh sách vật tư</b>
            <nz-table #ajaxTable1 [nzData]="['']" [nzShowPagination]="false" nzBordered>
              <thead>
                <tr>
                  <th>Tên vật tư</th>
                  <th>Số lượng</th>
                  <th>Đơn giá</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of dataChild.lstJobDetails">
                  <td>{{ data.materialName }}</td>
                  <td class="text-right">{{ data.quantity | number}}</td>
                  <td class="text-right">{{ data.price | number}}</td>
                </tr>
              </tbody>
            </nz-table>
          </div>

        </div>
      </ng-container>
    </ng-container>
  </nz-tab>
  <nz-tab nzTitle="Lịch Sử Thao tác">
    <div nz-row nzGutter="24">
      <div nz-col nzSpan="24">
        <nz-table #ajaxTable1 [nzData]="['']" [nzShowPagination]="true" nzBordered>
          <thead>
            <tr>
              <th>Thời gian</th>
              <th>Ghi chú</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of dataObject.lstJobHistories">
              <td><b>{{ data.createdAt | date: 'HH:mm dd/MM/yyyy' }}</b></td>
              <td class="mw-25">
                <div [innerHTML]="data.description"></div>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </div>
  </nz-tab>
</nz-tabset>

<div nz-row class="mt-3">
  <div nz-col nzSpan="24" class="text-center">
    <button nz-button nzShape="round" nzType="default" class="mr-3" (click)="closeDialog()">
      <i nz-icon nzType="lock" nzTheme="outline"></i> Đóng
    </button>
  </div>
</div>