<div nz-row nzGutter="8">
  <button nz-col nzSpan="3" nz-button nzType="primary" (click)="onDownloadExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
  </button>
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã Công Việc </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.code" placeholder="Mã Công Việc" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên Công Việc </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tên Công Việc" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <!-- <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Vùng
            </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select [disabled]="isManager" class="mr-3" [(ngModel)]="dataSearch.areaId"
                (ngModelChange)="areaChange($event)" nzAllowClear nzPlaceHolder="Vùng" nzShowSearch>
                <nz-option *ngFor="let area of dataArea" [nzValue]="area.id" [nzLabel]="area.name">
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div> -->

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu Trọ </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select
                [disabled]="isManager"
                (ngModelChange)="clickRoom($event)"
                class="mr-3"
                [(ngModel)]="dataSearch.apartmentId"
                nzAllowClear
                nzPlaceHolder="Khu Trọ"
                nzShowSearch
              >
                <nz-option *ngFor="let item of dataApartment" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Phòng </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select
                [disabled]="!dataSearch.apartmentId"
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataSearch.roomId"
                name="roomId"
                nzPlaceHolder="Phòng"
              >
                <nz-option *ngFor="let item of dataRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhân Viên </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.employeeId" name="employeeId" nzPlaceHolder="Nhân Viên">
                <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.fullName" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Danh Mục Sửa Chữa </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.jobCategoryId" name="jobCategoryId" nzPlaceHolder="Danh Mục Sửa Chữa">
                <nz-option *ngFor="let item of dataJobCategory" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại Sửa Chữa </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.jobTypeId" name="jobTypeId" nzPlaceHolder="Loại Sửa Chữa">
                <nz-option *ngFor="let item of lstJobType" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình Thức Công Việc </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.isFree" name="isFree" nzPlaceHolder="Hình Thức Công Việc">
                <nz-option *ngFor="let item of jobForm" [nzLabel]="item.name" [nzValue]="item.value"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng Thái </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Trạng Thái">
                <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày tạo </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.createdAt" name="createdAt" style="width: 100%"> </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày Thực Hiện </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.processDate" name="processDate" style="width: 100%"> </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày Kết Thúc </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.expiryDate" name="expiryDate" style="width: 100%"> </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
          <!-- <button class="ml-2" nz-button nzType="default" (click)="onDownloadExcel()"
            *ngIf="coreService.checkPermission(role.Download.code)" nzShape="round">
            <i nz-icon nzType="download" nzTheme="outline"></i>Tải Excel
          </button> -->
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div class="table-scroll-item">
  <div nz-row class="mt-3">
    <nz-table
      class="mb-3"
      nz-col
      nzSpan="24"
      #ajaxTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
      nzTableLayout="fixed"
      [nzScroll]="{ x: '2000px', y: '600px' }"
    >
      <thead>
        <tr>
          <th class="text-center" nzWidth="180px" nzLeft>Mã</th>
          <th class="text-center" nzWidth="120px">Loại công việc</th>
          <th class="text-center" nzWidth="120px">Loại sửa chữa</th>
          <th class="text-center" nzWidth="120px">Tên công việc</th>
          <th class="text-center" nzWidth="120px">Khu</th>
          <th class="text-center" nzWidth="120px">Phòng</th>
          <th class="text-center" nzWidth="120px">Nhân viên</th>
          <th class="text-center" nzWidth="120px">Trạng thái</th>
          <th nzWidth="160px">Tác Vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td nzLeft class="text-center">{{ data.code }}</td>
          <td class="text-center">
            <span *ngIf="data.isFree">Bảo hành</span>
            <span *ngIf="!data.isFree">Tính phí</span>
          </td>
          <td class="text-center">{{ data.typeOfJobType }} <span *ngIf="data.typeOfJobType && data.jobTypeName">-</span> {{ data.jobTypeName }}</td>
          <td>{{ data.name }}</td>
          <td>{{ data.apartmentName }}</td>
          <td>{{ data.roomName }}</td>
          <td>{{ data.employeeName }}</td>
          <td class="text-center">
            <nz-tag class="tag-status" [nzColor]="data.statusColor">
              {{ data.statusName }}
            </nz-tag>
          </td>
          <td class="text-center" nzWidth="160px">
            <button
              nzType="primary"
              nzGhost
              (click)="onDetail(data)"
              nz-tooltip
              nzTooltipTitle="Chi tiết công việc"
              nz-button
              nzShape="circle"
              class="mr-1 mb-1"
            >
              <i nz-icon nzType="eye" nzTheme="outline"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>
<div nz-col nzSpan="24" class="text-right">
  <nz-pagination
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
</div>
