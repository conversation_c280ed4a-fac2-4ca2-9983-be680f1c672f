import { Component, OnInit } from '@angular/core'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { ExcelV2Service, IColum } from '../../../services/excelv2.service'
import { enumData } from '../../../core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({ templateUrl: './satistic-job.component.html' })
export class SatisticJobComponent implements OnInit {
  currentUser: User | any
  enumData: any
  listOfData: any = []
  dataEmployee: any = []
  loading: boolean = true
  master: any
  dataSearch: any = {}

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private authenticationService: AuthenticationService,
    private excelService: ExcelV2Service
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit(): void {
    this.dataSearch = {}
    this.searchData()
    this.loadAllDataSelect()
  }

  loadAllDataSelect() {
    this.notifyService.showloading()
    this.apiNtssService.post(this.apiNtssService.EMPLOYEE.GET_LIST_REPAIR_EMPLOYEE, {}).then(async (res) => {
      this.notifyService.hideloading()
      this.dataEmployee = res
    })
  }

  async searchData() {
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_JOB, this.dataSearch).then((res: any) => {
      if (res) {
        this.loading = false
        this.listOfData = res
      }
    })
  }

  lstHeader: IColum[] = [
    { code: 'employeeName', name: 'Tên nhân viên', type: 'string' },
    { code: 'total', name: 'Tất Cả', type: 'number' },
    { code: 'processing', name: 'Chưa hoàn thành', type: 'number' },
    { code: 'waiting', name: 'Chờ xác nhận', type: 'number' },
    { code: 'refresh', name: 'Làm mới phòng', type: 'number' },
    { code: 'priority', name: 'Gấp', type: 'number' },
  ]

  async onDownloadExcel() {
    this.notifyService.showloading()
    const lstData = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_JOB, this.dataSearch)
    await this.excelService.onDownloadExcelJs({ lstData, lstHeader: this.lstHeader, excelName: 'Thống kê' })
    this.notifyService.hideloading()
  }
}
