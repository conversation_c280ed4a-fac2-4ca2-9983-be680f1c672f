<nz-collapse nz-col nzSpan="24" [nzBordered]="true" class="mt-2">
  <nz-collapse-panel nzHeader="T<PERSON><PERSON>" nzActive="true" class="ant-bg-antiquewhite">
    <div nz-row nzGutter="8">
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhân Viên </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.employeeId" name="employeeId" nzPlaceHolder="Nhân Viên">
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.fullName" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24" class="text-center">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData()">
          <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
        </button>
      </div>
    </div>
  </nz-collapse-panel>
</nz-collapse>

<div nz-row nzGutter="8">
  <div nz-col nzSpan="6" style="padding-top: 30px">
    <button nz-button nzType="primary" class="mr-3 ml-3" (click)="onDownloadExcel()" nzShape="round">
      <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
    </button>
  </div>
</div>

<div nz-row class="mt-3">
  <nz-table class="mb-3" nz-col nzSpan="24" [nzData]="['']" [nzShowPagination]="false" nzBordered nzTableLayout="fixed" [nzScroll]="{ y: '600px' }">
    <thead>
      <tr class="table-thead">
        <th class="text-center" nzWidth="200px">Tên nhân viên</th>
        <th class="text-center" nzWidth="100px">Tất Cả</th>
        <th class="text-center" nzWidth="100px">Chưa hoàn thành</th>
        <th class="text-center" nzWidth="100px">Chờ xác nhận</th>
        <th class="text-center" nzWidth="100px">Làm mới phòng</th>
        <th class="text-center" nzWidth="100px">Gấp</th>
      </tr>
    </thead>
    <tbody>
      <tr class="table-tbody" *ngFor="let data of listOfData">
        <td class="text-center">{{ data.employeeName }}</td>
        <td class="text-center">{{ data.total | number }}</td>
        <td class="text-center">{{ data.processing | number }}</td>
        <td class="text-center">{{ data.waiting | number }}</td>
        <td class="text-center">{{ data.refresh | number }}</td>
        <td class="text-center">{{ data.priority | number }}</td>
      </tr>
    </tbody>
  </nz-table>
</div>
