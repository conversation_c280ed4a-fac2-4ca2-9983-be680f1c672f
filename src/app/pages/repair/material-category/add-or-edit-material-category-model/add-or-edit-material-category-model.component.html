<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzGutter="8">
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên (1-200 kí tự)!">
          <input nz-input placeholder="Nhập tên 1-200 kí tự" [(ngModel)]="dataObject.name" name="name" required
            pattern=".{1,200}" />
        </nz-form-control>
      </nz-form-item>
    </div>
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>G<PERSON><PERSON> b<PERSON></nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="dataObject.sellingPrice" [nzMin]=0
            style="width: 100%;" name="sellingPrice" [nzStep]="1" [nzPlaceHolder]="'Nhập giá bán'" required>
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Giá mua ngoài tối đa</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" style="width: 100%;"
            [(ngModel)]="dataObject.maximumOutsidePurchasePrice" [nzMin]=0 name="maximumOutsidePurchasePrice"
            [nzStep]="1" [nzPlaceHolder]="'Nhập giá mua ngoài tối đa'" required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tồn kho an toàn</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="dataObject.safeInventory"
            style="width: 100%;" [nzMin]=0 name="safeInventory" [nzStep]="1" [nzPlaceHolder]="'Nhập tồn kho an toàn'"
            required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>



    <div nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Loại sửa chữa
        </nz-form-label>
        <button nz-button nzShape="round" nzType="primary" class="ml-2 mr-2" (click)="onClick(true)">
          Chọn tất cả
        </button>
        <button nzDanger nz-button nzShape="round" nzType="primary" class="mr-2" (click)="onClick(false)">
          <i nz-icon nzType="close" nzTheme="outline"></i>Bỏ chọn
        </button>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn loại sửa chữa!">
          <nz-select [nzMaxTagCount]="3" [nzMaxTagPlaceholder]="tagPlaceHolder" name="lstJobTypeId" nzMode="multiple"
            nzPlaceHolder="Chọn loại sửa chữa" [(ngModel)]="dataObject.lstJobTypeId" required>
            <nz-option *ngFor="let item of lstJobType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
          <ng-template #tagPlaceHolder let-selectedList>và {{ selectedList.length }} loại công việc</ng-template>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left">Là vật tư tiêu hao
        </nz-form-label>
        <nz-form-control nzSpan="24">
          <label nz-checkbox [(ngModel)]="dataObject.isConsumableSupply" name="isConsumableSupply"></label>
        </nz-form-control>
      </nz-form-item>
    </div>

    <nz-col nzSpan="24" class="text-right">
      <nz-row class="text-left">
        <div nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left" nzRequired>Đơn vị cơ sở
            </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn đơn vị cơ sở!">
              <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Đơn vị cơ sở" [(ngModel)]="dataObject.baseUnitId"
                name="baseUnitId" required>
                <nz-option *ngFor="let item of lstUnit" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </nz-row>

      <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onAddMaterialCategoryUnit()">
        <span nz-icon nzType="plus"></span>Thêm
      </button>

      <div nz-row class="mt-2">
        <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstMaterialCategoryUnits" [nzShowPagination]="false"
          class="mb-3" #ajaxTable [nzFrontPagination]="false" [nzScroll]="{ x: '1200px', y: null }" nzTemplateMode
          nzBordered>
          <thead>
            <tr>
              <th class="text-center" style="background-color:#ffffff !important; color: #000000 !important;">Đơn vị
                quy đổi</th>
              <th class="text-center" style="background-color:#ffffff !important; color: #000000 !important;">Tỉ lệ quy
                đổi</th>
              <th class="text-center" style="background-color:#ffffff !important; color: #000000 !important;">Tác vụ
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstMaterialCategoryUnits; let i = index">
              <td>
                <nz-select nzShowSearch [(ngModel)]="item.unitId" [ngModelOptions]="{ standalone: true }"
                  nzPlaceHolder="Chọn đơn vị tính">
                  <nz-option *ngFor="let mc of lstUnit" [nzLabel]="mc.name" [nzValue]="mc.id">
                  </nz-option>
                </nz-select>
              </td>
              <td>
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.conversionRate"
                  [ngModelOptions]="{ standalone: true }" style="width: 100%;" [nzMin]=1 [nzStep]="1"
                  [nzPlaceHolder]="'Nhập tỉ lệ quy đổi'" required></nz-input-number>
              </td>
              <td class="text-center">
                <button nz-button nzType="primary" nzDanger nzShape="circle" nz-tooltip nzTooltipTitle="Xóa"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?" (nzOnConfirm)="onDelete(i)" class="mr-2">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </nz-col>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-upload nzMultiple nz-row class="avatar-uploader" [nzAction]="uploadUrl" nzListType="picture-card"
            [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.lstMaterialCategoryImages"
            [nzShowButton]="dataObject.lstMaterialCategoryImages?.length <= 10"
            (nzChange)="handleChangeFileList($event)" [nzPreview]="handlePreview" [nzAccept]="'.png, .jpg, .jpeg'">
            <i nz-icon nzType="plus"></i>
            <div class="ant-upload-text">Upload</div>
          </nz-upload>
          <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
            (nzOnCancel)="previewVisible = false">
            <ng-template #modalContent>
              <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
            </ng-template>
          </nz-modal>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzShape="round" nzType="primary" class="mr-3"
        (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>