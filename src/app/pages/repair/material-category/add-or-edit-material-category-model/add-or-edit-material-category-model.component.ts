import { Component, OnInit, Optional, Inject } from '@angular/core'
import { NotifyService } from '../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { environment } from '../../../../../environments/environment'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './add-or-edit-material-category-model.component.html' })
export class AddOrEditMaterialCategoryModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI LOẠI VẬT TƯ'
  dataObject: any = {}
  isEditItem = false
  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')
  lstUnit: any[] = []
  lstJobType: any[] = []
  uploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<AddOrEditMaterialCategoryModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifyService.showloading()
    this.loadAllData()
    this.dataObject.lstMaterialCategoryImages = []
    this.dataObject.lstMaterialCategoryUnits = []
    this.dataObject.isConsumableSupply = false
    if (this.data && this.data !== null) {
      this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.FIND_DETAIL, {
        id: this.data.id,
      })
      this.dataObject.lstMaterialCategoryImages = this.dataObject.lstMaterialCategoryImages?.map((c: any) => {
        return {
          uid: c.id,
          name: c?.name ?? 'hinh anh',
          status: 'done',
          url: c?.url,
        }
      })
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT LOẠI VẬT TƯ'
    }
    this.notifyService.hideloading()
  }

  async loadAllData() {
    await Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.LOAD_DATA, {}),
    ]).then((rs: any) => {
      this.lstUnit = rs[0]
      this.lstJobType = rs[1]
    })
  }

  onSave() {
    for (let e of this.dataObject.lstMaterialCategoryUnits) {
      if (!e.unitId) {
        this.notifyService.showError(`Vui lòng chọn đơn vị tính!`)
        return
      }
    }

    const arrayLength: number = this.dataObject.lstMaterialCategoryUnits.length
    for (let i = 0; i < arrayLength; i++) {
      const mcI = this.dataObject.lstMaterialCategoryUnits[i]
      for (let j = i + 1; j < arrayLength; j++) {
        const mcJ = this.dataObject.lstMaterialCategoryUnits[j]
        if (mcI.unitId == mcJ.unitId) {
          this.notifyService.showError(`Không thể chọn 2 đơn vị tính giống nhau!`)
          return
        }
      }
    }
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  previewImage: string | undefined = ''
  previewVisible = false

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  handleChangeFileList(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              const img: any = {
                name: item?.name ? item?.name : item?.originFileObj?.name,
                url: item?.url || item?.response?.length > 0 ? item?.response[0] : '',
                uid: item.uid,
              }
              arr.push(img)
            }
          }
          this.dataObject.lstMaterialCategoryImages = arr
        }
        break
      case 'error':
        break
      case 'removed':
        {
          this.dataObject.lstMaterialCategoryImages.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) this.dataObject.lstMaterialCategoryImages.splice(index, 1)
          })
        }
        break
    }
  }

  async onAddMaterialCategoryUnit() {
    this.dataObject.lstMaterialCategoryUnits.push({
      id: '',
      unitId: '',
      conversionRate: 1,
      index: this.dataObject.lstMaterialCategoryUnits.length,
    })
  }

  onDelete(index: any) {
    this.dataObject.lstMaterialCategoryUnits.splice(index, 1)
  }

  onClick(isSelectAll: boolean) {
    if (isSelectAll) this.dataObject.lstJobTypeId = this.lstJobType.map((e: any) => e.id)
    else this.dataObject.lstJobTypeId = []
  }
}
