import { Component, OnInit } from '@angular/core'
import { NotifyService } from '../../../services/notify.service'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditMaterialCategoryModelComponent } from './add-or-edit-material-category-model/add-or-edit-material-category-model.component'
import { CoreService } from '../../../services/core.service'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { MaterialCategoryDetailModelComponent } from './material-category-detail-model/material-category-detail-model.component'
import * as fs from 'file-saver'
import { Workbook } from 'exceljs'
import * as XLSX from 'xlsx'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'
@Component({ templateUrl: './material-category.component.html' })
export class MaterialCategoryComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstUnit: any[] = []
  lstJobType: any = []

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    this.lstUnit = await this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.LOAD_DATA, {})
    this.lstJobType = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.LOAD_DATA, {})
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditMaterialCategoryModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditMaterialCategoryModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(MaterialCategoryDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()

    //#region Sheet 1
    const sheet1 = workbook.addWorksheet('Danh sách loại vật tư')
    const headerRow1 = sheet1.addRow([
      'Mã loại vật tư (code - Điền nếu muốn update Mã loại sửa chữa của vật tư cũ)',
      'Tên loại vật tư * (name)',
      'Giá bán * (sellingPrice)',
      'Giá mua ngoài tối đa * (maximumOutsidePurchasePrice)',
      'Tồn kho an toàn * (safeInventory)',
      'Mã loại sửa chữa (lstJobTypeCode - Các mã cách nhau dấu ,)',
      'Là vật tư tiêu hao (isConsumableSupply) (Yes/No)',
      'Mã đơn vị cơ sở *(baseUnitCode)',
      'Mô tả (description)',
    ])

    // Cell Style : Fill and Border
    headerRow1.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          sheet1.getColumn(colNumber).width = 30
          break
        case 2:
          sheet1.getColumn(colNumber).width = 30
          break
        case 3:
          sheet1.getColumn(colNumber).width = 30
          break
        case 4:
          sheet1.getColumn(colNumber).width = 50
          break
        case 5:
          sheet1.getColumn(colNumber).width = 30
          break
        case 6:
          sheet1.getColumn(colNumber).width = 60
          break
        case 7:
          sheet1.getColumn(colNumber).width = 50
          break
        case 8:
          sheet1.getColumn(colNumber).width = 30
          break
        case 9:
          sheet1.getColumn(colNumber).width = 40
          break
        case 10:
          sheet1.getColumn(colNumber).width = 30
          break

        default:
          sheet1.getColumn(colNumber).width = 12
          break
      }
    })

    //#endregion

    //#region Sheet 2
    const dataSearch = {
      where: { isDeleted: false },
      skip: 0,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.PAGINATION, dataSearch).then((res) => {
      const sheet2 = workbook.addWorksheet('Danh sách loại sửa chữa')
      //#region Body Table
      const header = ['Mã loại sửa chữa', 'Tên loại sửa chữa', 'Loại sửa chữa', 'Mô tả']

      const headerRow = sheet2.addRow(header)

      // Cell Style : Fill and Border
      headerRow.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '203751' },
          bgColor: { argb: '203751' },
        }
        cell.alignment = { horizontal: 'center' }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }

        switch (colNumber) {
          case 4:
            sheet2.getColumn(colNumber).width = 30
            break
          default:
            sheet2.getColumn(colNumber).width = 15
            break
        }
      })

      for (let data of res[0]) {
        const rowData = [data.code || '', data.name || '', data.typeName || '', data.description || '']

        const row = sheet2.addRow(rowData)
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
      }
    })
    //#endregion

    // Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `TEMPLATE_LOAI_VAT_TU${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'code',
          'name',
          'sellingPrice',
          'maximumOutsidePurchasePrice',
          'safeInventory',
          'lstJobTypeCode',
          'isConsumableSupply',
          'baseUnitCode',
          'description',
        ],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()

      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 1

        if (row.code) row.code = row.code.toString()
        if (row.name) row.name = row.name.toString()
        if (!row.name) {
          strErr += `[ Dòng ${idx + 1} - Tên loại vật tư không được để trống ] <br>`
        }
        if (row.name && row.name?.length > 200) {
          strErr += 'Dòng ' + idx + ' - Tên loại vật tư không được lớn hơn 200 ký tự <br>'
        }
        if (row.sellingPrice == null) {
          strErr += `[ Dòng ${idx + 1} - Giá bán không được để trống ] <br>`
        }
        if (row.maximumOutsidePurchasePrice == null) {
          strErr += `[ Dòng ${idx + 1} - Giá mua ngoài tối đa không được để trống ] <br>`
        }
        if (row.safeInventory == null) {
          strErr += `[ Dòng ${idx + 1} - Tồn kho an toàn không được để trống ] <br>`
        }
        if (row.lstJobTypeCode) row.lstJobTypeCode += ''
        row.isConsumableSupply = false
        if (row.isConsumableSupply === 'Yes') {
          row.isConsumableSupply = true
        }

        if (row.baseUnitCode) row.baseUnitCode = row.baseUnitCode.toString()
        if (!row.baseUnitCode) {
          strErr += `[ Dòng ${idx + 1} - Mã đơn vị cơ sở không được để trống ] <br>`
        }

        if (strErr.length > 0) {
          this.notifyService.showError(strErr)
          return
        }
      }

      this.apiScService
        .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.CREATE_DATA_BY_EXCEL, jsonData)
        .then((result) => {
          this.notifyService.hideloading()
          if (result) {
            this.notifyService.showSuccess('Cập nhật file excel thành công')
            this.searchData()
          }
        })
    }
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách loại vật tư')
        //#region Body Table
        const header = [
          'Mã loại vật tư',
          'Tên loại vật tư',
          'Giá bán',
          'Giá vốn',
          'Giá mua ngoài tối đa',
          'Tồn kho an toàn',
          'Đơn vị cơ sở',
          'Đơn vị quy đổi',
          'Tỉ lệ quy đổi',
          'Là vật tư tiêu hao',
          'Mô tả',
          'Trạng thái',
        ]

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.name || '',
            data.sellingPrice || 0,
            data.costPrice || 0,
            data.maximumOutsidePurchasePrice || 0,
            data.safeInventory || 0,
            data.baseUnitName || '',
            data.unitName || '',
            data.conversionRate || 0,
            data.consumableSupplyText || '',
            data.description || '',
            data.isDeleted ? 'Ngưng hoạt động' : 'Đang hoạt động',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_LOAI_VAT_TU_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
