<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>
<nz-tabset *ngIf="dataObject" class="mt-4">
  <nz-tab nzTitle="Thông tin chung">
    <div nz-row class="mt-2">
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mã loại vật tư</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.code }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tên lo<PERSON>i vật tư</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.name }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Giá bán</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.sellingPrice | number }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Giá vốn</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.costPrice | number }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Giá mua ngoài tối đa</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.maximumOutsidePurchasePrice | number }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tồn kho an toàn</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.safeInventory | number }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Đơn vị cơ sở</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.baseUnitName}}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6" *ngIf="dataObject.isConsumableSupply">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Là vật tư tiêu hao
          </nz-form-label>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="6" *ngIf="dataObject.description">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="6" class="text-left">Mô tả
          </nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.description }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24"
        *ngIf="dataObject?.lstMaterialCategoryImages && dataObject?.lstMaterialCategoryImages?.length <= 5 && dataObject?.lstMaterialCategoryImages?.length > 0">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh phiếu vật tư
          </nz-form-label>
          <nz-form-control [nzSm]="23" [nzXs]="24" style="display: flex;">
            <ng-container *ngFor="let item of dataObject?.lstMaterialCategoryImages; let i = index">
              <img nz-image width="350px" height="250px" style="margin-right: 10px; margin-bottom: 10px"
                [nzDisablePreview]="true" (click)="openImagePreview(dataObject?.lstMaterialCategoryImages)"
                nzSrc="{{item.url}}" alt="{{ 'Hình ảnh phiếu vật tư' + i }}" />
            </ng-container>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </nz-tab>

  <nz-tab nzTitle="Danh sách đơn vị tính">
    <div nz-row class="mt-2">
      <div nz-col nzSpan="24">
        <div nz-row class="mt-3">
          <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstMaterialCategoryUnits" [nzShowPagination]="false"
            #ajaxTable [nzFrontPagination]="false" nzTemplateMode nzBordered>
            <thead>
              <tr>
                <th class="text-center" nzWidth="80px">Đơn vị tính</th>
                <th class="text-center" nzWidth="80px">Tỉ lệ quy đổi (So với đơn vị cơ sở)</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of dataObject.lstMaterialCategoryUnits; let i = index">
                <td class="text-center">{{ item.unitName}}</td>
                <td class="text-center">{{ (item.conversionRate | number) + ' ('+dataObject.baseUnitName +')' }}</td>
              </tr>
            </tbody>
          </nz-table>
        </div>
      </div>
    </div>
  </nz-tab>

  <nz-tab nzTitle="Danh sách loại công việc">
    <div nz-row class="mt-2">
      <div nz-col nzSpan="24">
        <div nz-row class="mt-3">
          <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstJobTypeMaterialCategories" [nzShowPagination]="false"
            #ajaxTable [nzFrontPagination]="false" nzTemplateMode nzBordered>
            <thead>
              <tr>
                <th class="text-center" nzWidth="80px">Tên loại công việc</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of dataObject.lstJobTypeMaterialCategories; let i = index">
                <td class="text-center">{{ item.jobTypeName }}</td>
              </tr>
            </tbody>
          </nz-table>
        </div>
      </div>
    </div>
  </nz-tab>
</nz-tabset>