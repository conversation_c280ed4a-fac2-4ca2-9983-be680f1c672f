<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="<PERSON><PERSON><PERSON>" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="24">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Kho <span style="color: red;">*</span>
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select (ngModelChange)="onLoadLstMaterialCategory($event)" nzShowSearch nzAllowClear
                [(ngModel)]="dataSearch.warehouseMaterialId" name="warehouseMaterialId" nzPlaceHolder="Chọn kho">
                <nz-option *ngFor="let item of lstWarehouseCategory" [nzLabel]="item.name"
                  [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Vật tư </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.materialCategoryId" name="type"
                nzPlaceHolder="Chọn vật tư">
                <nz-option *ngFor="let item of lstMaterialCategory" [nzLabel]="item.name"
                  [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <!-- <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">
              <span nz-tooltip nzTooltipTitle="Chỉ hiện những vật tư có số lượng chờ xuất kho lớn hơn 0">Chờ xuất kho
              </span>
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <label nz-checkbox style="margin-left: auto;" class="pt-auto"
                [(ngModel)]="dataSearch.isWaitExport"></label>
            </nz-form-control>
          </nz-form-item>
        </div> -->
        <!-- <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">
              <span nz-tooltip nzTooltipTitle="Chỉ hiện những vật tư có số lượng chờ mua lớn hơn 0">Chờ
                mua</span>
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <label style="margin-left: auto;" nz-checkbox class="pt-auto"
                [(ngModel)]="dataSearch.isWaitBuying"></label>
            </nz-form-control>
          </nz-form-item>
        </div> -->
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>
<button nzShape="round" class="mr-2 mt-4" nz-button nzType="primary" [disabled]="!(setOfCheckedId.size > 0)"
  (click)="createBuyingMaterial()"><span nz-icon nzType="send" nzTheme="outline"></span>Tạo yêu cầu mua vật
  tư</button>
<div nz-row nzGutter="8" class="mt-2">
  <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable nzBordered [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzTableLayout="fixed">
    <thead>
      <tr>
        <th class="text-center" nzWidth="100px">Chọn</th>
        <th class="text-center">Vật tư</th>
        <th class="text-center" style="width: 100px;">ĐVT</th>
        <th class="text-center">Tồn kho</th>
        <th class="text-center">Chờ xuất kho</th>
        <th class="text-center">Chờ mua</th>
        <th class="text-center">Yêu cầu mua</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of listOfData">
        <td nzWidth="100px" [nzDisabled]="!(item.requestBuying > 0)"
          [nzChecked]="setOfCheckedId.has(item.materialCategoryId)"
          (nzCheckedChange)="onItemChecked(item.materialCategoryId, $event)"></td>
        <td class="text-left" style="width: 100px;">{{ item.materialCategoryName}}</td>
        <td class="text-center">{{ item.baseUnitName}}</td>
        <td class="text-right">{{ item.totalInventory }}</td>
        <td class="text-right">{{ item.totalWaitExport}}</td>
        <td class="text-right">{{ item.totalWaitBuying }}</td>
        <td class="text-right">
          <input nz-input placeholder="Nhập số lượng nhập" [(ngModel)]="item.requestBuying" name="requestBuying"
            currencyMask [options]="{ prefix: '', precision: 0, allowNegative: false  }"
            (ngModelChange)="onChangeRequestBuying($event?.target,item)" />
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng
    </ng-template>
  </div>