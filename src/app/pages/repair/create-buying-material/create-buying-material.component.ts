import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './create-buying-material.component.html' })
// danh sách cần mua vật tư theo kho
export class CreateBuyingMaterialComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstWarehouseCategory: any[] = []
  lstMaterialCategory: any[] = []
  isWaitExport = false
  isWaitBuying = false
  /// handle select request buying material
  setOfCheckedId = new Set<string>()
  allChecked = false
  indeterminate = false

  updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id)
    } else {
      this.setOfCheckedId.delete(id)
    }
  }

  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedSet(id, checked)
  }

  onAllChecked(checked: boolean): void {
    this.listOfData.forEach((item: any) => {
      if (item.isApproving) {
        this.updateCheckedSet(item.id, checked)
      }
    })
  }

  refreshCheckedStatus(): void {
    this.allChecked = this.listOfData
      .filter((item: any) => item.isApproving)
      .every((item: any) => this.setOfCheckedId.has(item.id))
    this.indeterminate =
      this.listOfData.some((item: any) => {
        if (item.isApproving && this.setOfCheckedId.has(item.id)) return true
        return false
      }) && !this.allChecked
  }

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    this.dataSearch.isWaitBuying = this.isWaitBuying
    this.dataSearch.isWaitExport = this.isWaitExport
    this.loadAllData()
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.setOfCheckedId = new Set<string>()
    this.allChecked = false
    this.indeterminate = false
  }

  async searchData(reset: boolean = false) {
    this.setOfCheckedId = new Set<string>()
    this.allChecked = false
    this.indeterminate = false
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService
      .postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.LOAD_BUYING_MATERIAL_GROUP_BY_WAREHOUSE_MATERIAL, dataSearch)
      .then((data: any) => {
        if (data) {
          this.loading = false
          this.total = data[1]
          this.listOfData = data[0]
          this.isWaitExport = data[2]
          this.isWaitBuying = data[3]
        }
      })
  }

  async loadAllData() {
    await this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}).then(async (rs: any) => {
      this.lstWarehouseCategory = rs
      if (rs.length > 0) {
        this.dataSearch.warehouseMaterialId = rs[0]?.id
        await this.onLoadLstMaterialCategory(rs[0]?.id)
      }
    })
  }

  async onLoadLstMaterialCategory(id: string) {
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {
        warehouseMaterialId: id,
      })
      .then((rs: any) => {
        this.lstMaterialCategory = rs
        this.searchData()
      })
  }

  createBuyingMaterial() {
    if (this.dataSearch.warehouseMaterialId === null) {
      this.notifyService.showError('Vui lý chọn kho!')
    }
    const lstData = this.listOfData.filter((x: any) => this.setOfCheckedId.has(x.materialCategoryId))

    const body: any = {
      warehouseMaterialId: this.dataSearch.warehouseMaterialId,
      lstBuyingMaterialDetails: [],
    }
    for (let i = 0; i < lstData?.length; i++) {
      body.lstBuyingMaterialDetails.push({
        materialCategoryId: lstData[i].materialCategoryId,
        quantity: lstData[i].requestBuying,
        buyingMaterialQuantity: lstData[i].requestBuying,
        unitId: lstData[i].baseUnitId,
        baseUnitId: lstData[i].baseUnitId,
        conversionRate: 1,
        price: lstData[i].price,
      })
    }
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.CREATE, body).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.searchData(true)
      }
    })
    // #endregion
  }

  onChangeRequestBuying(ev: any, item: any) {
    this.onItemChecked(item?.materialCategoryId, item?.requestBuying > 0)
  }
}
