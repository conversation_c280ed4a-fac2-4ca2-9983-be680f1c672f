<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzAlign="middle" nzGutter="12">
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Loại sửa chữa</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn loại sửa chữa!">
          <nz-select nzShowSearch [(ngModel)]="dataObject.jobTypeId" name="jobTypeId" nzPlaceHolder="Chọn loại sửa chữa"
            required>
            <nz-option *ngFor="let item of lstJobType" [nzLabel]="item.name" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên công việc</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên công việc (1-200 kí tự)!">
          <input nz-input placeholder="Nhập tên công việc 1-200 kí tự" [(ngModel)]="dataObject.name" name="name"
            required pattern=".{1,200}" />
        </nz-form-control>
      </nz-form-item>
    </div>
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Điểm công việc</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số điểm!">
          <nz-input-number [nzFormatter]="formatterPoint" [nzParser]="parserPoint" [(ngModel)]="dataObject.point"
            [nzMin]="1" style="width: 100%;" name="point" [nzStep]="1" nzPlaceHolder="Nhập số điểm >=1"
            required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiền công</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tiền công!">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="dataObject.fee" [nzMin]="0"
            style="width: 100%;" name="fee" [nzStep]="1" [nzPlaceHolder]="'Nhập tiền công'" required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiền vật tư tối đa</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tiền công!">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="dataObject.materialFeeMax"
            [nzMin]="0" style="width: 100%;" name="materialFeeMax" [nzStep]="1" [nzPlaceHolder]="'Nhập tiền công'"
            required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Số điểm phạt khi trễ hạn</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số điểm!">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parserPoint" [(ngModel)]="dataObject.pointDeducted"
            [nzMin]="0" style="width: 100%;" name="pointDeducted" [nzStep]="1" nzPlaceHolder="Nhập số điểm"
            required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Số ngày thực hiện tối đa</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số ngày thực hiện tối đa!">
          <nz-input-number [nzFormatter]="formatter" [nzParser]="parser"
            [(ngModel)]="dataObject.maximumNumberOfDaysOfImplementation" [nzMin]="0" style="width: 100%;"
            name="maximumNumberOfDaysOfImplementation" [nzStep]="1" [nzPlaceHolder]="'Nhập số ngày thực hiện tối đa'"
            required></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-control nzSpan="24">
          <input class="form-check-input checkbox-role" type="checkbox" name="isFree"
            [(ngModel)]="dataObject.isFree" /><label class="loginlabel"><b>Mặc định là bảo hành?</b></label>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-control nzSpan="24">
          <input class="form-check-input checkbox-role" type="checkbox" name="isGroup"
            [(ngModel)]="dataObject.isGroup" /><label class="loginlabel"><b>Là công việc nhóm?</b></label>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-control nzSpan="24">
          <input class="form-check-input checkbox-role" type="checkbox" name="isPriority"
            [(ngModel)]="dataObject.isPriority" /><label class="loginlabel"><b>Gấp?</b></label>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-control nzSpan="24">
          <input class="form-check-input checkbox-role" type="checkbox" name="hasMaterialFee"
            [(ngModel)]="dataObject.hasMaterialFee" /><label class="loginlabel"><b>Tính tiền theo vật tư?</b></label>
        </nz-form-control>
      </nz-form-item>
    </div>
    <!-- Công việc loại này là công việc bảo hành không cho cho loại công việc khác -->
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-control nzSpan="24">
          <input class="form-check-input checkbox-role" type="checkbox" name="isJobFree"
            [(ngModel)]="dataObject.isJobFree" /><label class="loginlabel"><b>Là công việc bảo hành?</b></label>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập Mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Hình ảnh</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-upload nz-row class="avatar-uploader" [nzAction]="uploadUrl" nzListType="picture-card"
            [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.lstJobCategoryImages"
            [nzShowButton]="dataObject.lstJobCategoryImages?.length <= 10" (nzChange)="handleChangeFileList($event)"
            [nzPreview]="handlePreview" [nzAccept]="'.png, .jpg, .jpeg'">
            <i nz-icon nzType="plus"></i>
            <div class="ant-upload-text">Upload</div>
          </nz-upload>
          <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
            (nzOnCancel)="previewVisible = false">
            <ng-template #modalContent>
              <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
            </ng-template>
          </nz-modal>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzShape="round" nzType="primary" class="mr-3"
        (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>