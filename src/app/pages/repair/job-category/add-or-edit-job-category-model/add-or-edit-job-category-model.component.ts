import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { environment } from '../../../../../environments/environment'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './add-or-edit-job-category-model.component.html' })
export class AddOrEditJobCategoryModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI TÊN CÔNG VIỆC'
  dataObject: any = {}
  isEditItem = false
  lstJobType: any[] = []
  lstMaterialCategory: any[] = []
  dicMaterialCategory: Record<string, any> = {}
  uploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  formatterPoint = (value: number): string => `${value ? value : ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parserPoint = (value: string): string => {
    // Chỉ có 1 dấu . duy nhất ngăn cách giữa phần nguyên và phần thập phân
    // 1,234.56 ✅
    // 1.234.56 ❌
    const result = value.replace(/,/g, '')
    const match = result.match(/^(\d+)?(\.\d*)?$/)
    return match ? result : ''
  }
  parser = (value: string): string => value!.replace(/[^\d]/g, '')

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<AddOrEditJobCategoryModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.onLoadSelectBox()
    this.dataObject.lstJobCategoryImages = []
    if (this.data && this.data !== null) {
      this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.FIND_DETAIL, {
        id: this.data.id,
      })
      this.dataObject.lstJobCategoryImages = this.dataObject.lstJobCategoryImages?.map((c: any) => {
        return {
          uid: c.id,
          name: c?.name ?? 'hinh anh',
          status: 'done',
          url: c?.url,
        }
      })

      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT DANH MỤC SỬA CHỮA'
    }
  }

  onLoadSelectBox() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.LOAD_DATA_FOR_WEB, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstJobType = rs[0]
      this.lstMaterialCategory = rs[1]
      for (let mc of this.lstMaterialCategory) this.dicMaterialCategory[mc.id] = mc
    })
  }

  onSave() {
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  async onChangeMaterialCategory(item: any) {
    const mc = this.dicMaterialCategory[item.materialCategoryId]
    item.unitName = mc.baseUnitName
    item.materialCategoryId = mc.id
    item.materialCategoryName = mc.name
    item.price = mc.sellingPrice
  }

  previewImage: string | undefined = ''
  previewVisible = false

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  handleChangeFileList(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              arr.push({
                name: item.name ? item.name : item.originFileObj?.name,
                url: item.url ? item.url : item.response[0],
                uid: item.uid,
              })
            }
          }
          this.dataObject.lstJobCategoryImages = arr
        }
        break
      case 'error':
        break
      case 'removed':
        {
          this.dataObject.lstJobCategoryImages.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) this.dataObject.lstJobCategoryImages.splice(index, 1)
          })
        }
        break
    }
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
