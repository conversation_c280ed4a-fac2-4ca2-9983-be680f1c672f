import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { NzImageService } from 'ng-zorro-antd/image'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './job-category-detail-model.component.html' })
export class JobCategoryDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT DANH MỤC CÔNG VIỆC'
  dataObject: any = {}
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private dialogRef: MatDialogRef<JobCategoryDetailModelComponent>,
    private nzImageService: NzImageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.FIND_DETAIL, {
      id: this.data.id,
    })
    if (this.dataObject.lstJobCategoryImages && this.dataObject.lstJobCategoryImages.length > 0) {
      for (const img of this.dataObject.lstJobCategoryImages) {
        if (!img.src) img.src = img.url
      }
    }
    this.notifySerivce.hideloading()
  }

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
