import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { enumData } from 'src/app/core/enumData'
import * as XLSX from 'xlsx'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditJobCategoryModelComponent } from './add-or-edit-job-category-model/add-or-edit-job-category-model.component'
import { JobCategoryDetailModelComponent } from './job-category-detail-model/job-category-detail-model.component'
import { ApiScService } from 'src/app/services/apiSc.service'
@Component({ templateUrl: './job-category.component.html' })
export class JobCategoryComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  lstJobType: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  onLoadSelectBox() {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.LOAD_DATA, {}).then((result) => {
      if (result) {
        this.lstJobType = result
        this.notifyService.hideloading()
      }
    })
  }

  ngOnInit() {
    this.onLoadSelectBox()
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditJobCategoryModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditJobCategoryModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(JobCategoryDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Template tên công việc')

    //#region Body Table
    const header = [
      'Loại sửa chữa *',
      'Tên *',
      'Số điểm thưởng khi hoàn thành công việc *',
      'Số điểm phạt khi trể hạn *',
      'Số ngày thực hiện tối đa *',
      'Tiền công *',
      'Tiền vật tư tối đa *',
      'Công việc bảo hành * (1: Bảo hành, 0: Không bảo hành)',
      'Mô tả (description)',
    ]
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_TEN_CONG_VIEC_${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'jobTypeCode',
          'name',
          'point',
          'pointDeducted',
          'maximumNumberOfDaysOfImplementation',
          'fee',
          'materialFeeMax',
          'isFree',
          'description',
        ],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.jobTypeCode == null || (typeof row.jobTypeCode === 'string' && row.jobTypeCode.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã loại sửa chữa không được để trống  <br>'
        }
        if (row.name == null || (typeof row.name === 'string' && row.name.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tên công việc không được để trống <br>'
        }
        if (row.point == null || (typeof row.point === 'string' && row.point.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tên công việc không được để trống <br>'
        }
        if (
          row.pointDeducted == null ||
          (typeof row.pointDeducted === 'string' && row.pointDeducted.trim().length == 0)
        ) {
          strErr += 'Dòng ' + idx + ' - Tên công việc không được để trống <br>'
        }
        if (
          row.maximumNumberOfDaysOfImplementation == null ||
          (typeof row.maximumNumberOfDaysOfImplementation === 'string' &&
            row.maximumNumberOfDaysOfImplementation.trim().length == 0)
        ) {
          strErr += 'Dòng ' + idx + ' - Tiền công không được để trống <br>'
        }
        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }

      this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.CREATE_DATA_BY_EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        if (result) {
          this.notifyService.showSuccess('Thêm file excel thành công')
          this.searchData()
        }
      })
    }
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_CATEGORY.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách danh mục sửa chữa')
        //#region Body Table
        const header = [
          'Mã danh mục',
          'Tên danh mục',
          'Loại sửa chữa',
          'Điểm',
          'Số tiền/điểm (VND)',
          'Mô tả',
          'Trạng thái',
        ]

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
              worksheet.getColumn(colNumber).width = 20
              worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
            case 6:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.name || '',
            data.jobTypeName || '',
            data.point || 0,
            data.moneyPerPoint || 0,
            data.description || '',
            data.isDeleted ? 'Ngưng hoạt động' : 'Đang hoạt động',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_DANH_MUC_SUA_CHUA_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
