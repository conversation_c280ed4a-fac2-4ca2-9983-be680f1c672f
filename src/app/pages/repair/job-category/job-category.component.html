<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <button nzShape="round" class="ml-1 mr-2" nz-button nzType="primary" (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  <button nzShape="round" class="mr-2" nz-button (click)="onDownloadTemplateExcel()">
    <span nz-icon nzType="download"></span>Tải Template Excel
  </button>
  <input
    class="hidden"
    type="file"
    id="file"
    (change)="clickImportExcel($event)"
    placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
  />
  <label nz-button for="file" class="ant-btn lable-custom-file-custom mr-2"> <span nz-icon nzType="upload"></span> Nhập Excel </label>
  <button class="mr-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
  </button>
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã tên công việc </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.code" name="dataSearch.code" placeholder="Nhập mã tên công việc" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên công việc </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.name" name="dataSearch.name" placeholder="Nhập tên công việc" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="20" [nzXs]="24" class="text-left">Loại sửa chữa</nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch [(ngModel)]="dataSearch.jobTypeId" name="jobTypeId" nzPlaceHolder="Chọn loại sửa chữa">
                <nz-option *ngFor="let item of lstJobType" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng thái </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.isDeleted" name="isDeleted" nzPlaceHolder="Chọn trạng thái">
                <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th class="text-center">Mã công việc</th>
        <th class="text-center">Loại sửa chữa</th>
        <th class="text-center">Tên công việc</th>
        <th class="text-center">Điểm công việc</th>
        <th class="text-center">Điểm phạt</th>
        <th class="text-center">Số ngày thực hiện tối đa</th>
        <th class="text-center">Trạng thái</th>
        <th class="text-center">Tác Vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">
          {{ data.code }}
        </td>
        <td class="text-center">
          {{ data.jobTypeName }}
        </td>
        <td class="text-center">
          {{ data.name }}
        </td>
        <td class="text-center">
          {{ data.point | number }}
        </td>
        <td class="text-center">
          {{ data.pointDeducted | number }}
        </td>
        <td class="text-center">
          {{ data.maximumNumberOfDaysOfImplementation | number }}
        </td>
        <td class="text-center">
          {{ data.isDeleted ? 'Ngưng hoạt động' : 'Đang hoạt động' }}
        </td>
        <td class="text-center text-nowrap">
          <button class="mr-1" (click)="onShowDetail(data)" nz-tooltip nzTooltipTitle="Chi tiết" nz-button nzType="primary" nzGhost nzShape="circle">
            <i nz-icon nzType="eye"></i>
          </button>
          <button
            class="mr-1"
            *ngIf="data.isDeleted"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)"
            nz-tooltip
            nzTooltipTitle="Kích hoạt"
            nz-button
            nzType="primary"
            nzShape="circle"
          >
            <i nz-icon nzType="play-circle" nzTheme="outline"></i>
          </button>
          <button
            class="mr-1"
            *ngIf="!data.isDeleted"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)"
            nz-tooltip
            nzTooltipTitle="Ngưng hoạt động"
            nz-button
            nzType="primary"
            nzDanger
            nzShape="circle"
          >
            <i nz-icon nzType="stop"></i>
          </button>
          <button
            class="mr-1"
            *ngIf="!data.isDeleted"
            (click)="onShowEdit(data)"
            nz-tooltip
            nzTooltipTitle="Chỉnh Sửa"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <i nz-icon nzType="edit"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
