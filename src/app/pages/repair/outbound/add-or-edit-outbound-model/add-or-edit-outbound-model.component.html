<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzGutter="8">
    <nz-col nzSpan="6" *ngIf="isEditItem || !data?.isAdd">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã</nz-form-label>
        <nz-form-control nzSpan="24"
          nzErrorTip="Vui lòng nhập mã từ 1 đến 36 kí tự và không kèm kí tự đặc biệt khoảng cách">
          <input nz-input [disabled]="isEditItem || !data?.isAdd" placeholder="Nhập mã 1-36 kí tự"
            [(ngModel)]="dataObject.code" name="code" pattern="^([a-zA-Z0-9]){1,36}$" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Kho</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kho">
          <nz-select [disabled]="dataObject.isDisabledEdit || !data?.isAdd" nzShowSearch nzAllowClear
            nzPlaceHolder="Chọn kho" [(ngModel)]="dataObject.warehouseMaterialId" name="warehouseMaterialId"
            (ngModelChange)="onChangeWarehouseMaterialCategory($event)" required>
            <nz-option *ngFor="let item of lstWarehouseMaterial" [nzLabel]="item?.name" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6" *ngIf=" dataObject.type === 'EXPORT_APARTMENT'">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Chọn khu</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.apartmentId" name="apartmentId"
            nzPlaceHolder="Chọn khu">
            <nz-option *ngFor="let apartment of apartments" [nzLabel]="apartment.name" [nzValue]="apartment.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày tạo phiếu</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-date-picker [disabled]="true" nzFormat="dd/MM/yyyy" [(ngModel)]="dataObject.createdAt" name="createdAt">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <!-- <nz-col nzSpan="6" *ngIf=" dataObject.type === 'EXPORT_EMPLOYEE'">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Nhân viên kỹ thuật
        </nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.employeeId" name="employeeId"
            nzPlaceHolder="Chọn nhân viên kỹ thuật">
            <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.fullName" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col> -->




    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <nz-col nzSpan="24" class="text-right">
      <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onAddOutboundDetail()">
        <span nz-icon nzType="plus"></span>Thêm
      </button>

      <div nz-row class="mt-3">
        <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstOutboundDetails" [nzShowPagination]="false" class="mb-3"
          #ajaxTable [nzFrontPagination]="false" [nzScroll]="{ x: '1200px', y: null }" nzTemplateMode nzBordered>
          <thead>
            <tr>
              <th class="text-center" nzWidth="220px">Tên loại vật tư</th>
              <th class="text-center" nzWidth="180px">Đơn vị tính</th>
              <th class="text-center" nzWidth="180px">Tỉ lệ quy đổi</th>
              <th class="text-center" nzWidth="180px" *ngIf="!data?.isAdd">Tồn kho công ty</th>
              <th class="text-center" nzWidth="180px">Số lượng yêu cầu</th>
              <th class="text-center" nzWidth="180px">Số lượng cần xuất (Tính ra theo đơn vị cơ sở)</th>
              <th class="text-center" *ngIf="data?.isSplit" nzWidth="180px">Số lượng tách</th>

              <!-- phiếu mua vật tư tạo từ mobile thì mới hiện một số thong tin này -->
              <th class="text-center" nzWidth="180px" *ngIf="!!dataObject.isFromMobile && !data?.isSplit">Thực xuất</th>
              <th class="text-center" nzWidth="180px" *ngIf="!!dataObject.isFromMobile && !data?.isSplit">Mua ngoài</th>
              <th class="text-center" nzWidth="180px" *ngIf="!!dataObject.isFromMobile && !data?.isSplit">Ngày hoàn
                thành mua</th>
              <!-- phiếu mua vật tư tạo từ mobile thì mới hiện một số thong tin này -->

              <th class="text-center" nzWidth="180px">Đơn vị cơ sở</th>
              <th class="text-center" nzWidth="180px">Số lượng xuất</th>
              <th class="text-center" nzWidth="180px">Đơn giá</th>
              <th class="text-center" nzWidth="180px">Thành tiền</th>
              <th class="text-center" nzWidth="80px" nzRight *ngIf="!dataObject.isDisabledEdit">Tác vụ</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstOutboundDetails; let i = index">
              <td class="text-center">
                <nz-select [disabled]="dataObject.isDisabledEdit" nzShowSearch [(ngModel)]="item.materialCategoryId"
                  [ngModelOptions]="{ standalone: true }" nzPlaceHolder="Chọn tên loại vật tư"
                  (ngModelChange)="onChangeMaterialCategory(item)">
                  <nz-option *ngFor="let mc of lstMaterialCategory" [nzLabel]="mc.name"
                    [nzValue]="mc.materialCategoryId">
                  </nz-option>
                </nz-select>
              </td>

              <td>
                <nz-select [disabled]="dataObject.isDisabledEdit" nzShowSearch [(ngModel)]="item.unitId"
                  [ngModelOptions]="{ standalone: true }" nzPlaceHolder="Chọn đơn vị tính"
                  (ngModelChange)="onChangeUnit(item)">
                  <nz-option *ngFor="let mc of item.lstUnit" [nzLabel]="mc.unitName" [nzValue]="mc.unitId"> </nz-option>
                </nz-select>
              </td>
              <td>
                <span>{{ item.conversionRate }}</span>
              </td>
              <td class="text-center" *ngIf="!data?.isAdd">{{ item.inventory | number }}</td>

              <td class="text-center">
                <nz-input-number [disabled]="dataObject.isDisabledEdit " [nzFormatter]="formatter" [nzParser]="parser"
                  [(ngModel)]="item.quantity" [ngModelOptions]="{ standalone: true }" [nzMin]="1" style="width: 100%"
                  [nzStep]="1" [nzPlaceHolder]="'Nhập số lượng'" required></nz-input-number>
              </td>
              <td class="text-center">{{ item.quantity * item.conversionRate | number}}</td>

              <!-- Số lượng tách -->
              <td class="text-center" *ngIf="data?.isSplit">
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [disabled]="dataObject.isDisabled"
                  [(ngModel)]="item.splitQuantity" [ngModelOptions]="{ standalone: true }" [nzMin]="1"
                  style="width: 100%" [nzStep]="1" [nzPlaceHolder]="'Nhập số lượng tách'" required></nz-input-number>
              </td>
              <!-- số lượng tách -->

              <!-- phiếu mua vật tư tạo từ mobile thì mới hiện một số thong tin này -->
              <td class="text-center" *ngIf="!!dataObject.isFromMobile && !data?.isSplit">
                <!-- Thực xuất = yêu cầu và nhỏ hơn hoặc bằng  item.inventory hoặc mua ngoài lớn > 0-->
                <nz-input-number (ngModelChange)="onChangeWaitingBuyingQuantity(item)" [nzFormatter]="formatter"
                  [nzParser]="parser" [disabled]="dataObject.isDisabled"
                  [(ngModel)]="item.warehouseActualOutboundQuantity" [ngModelOptions]="{ standalone: true }" [nzMin]="0"
                  style="width: 100%" [nzStep]="1" [nzPlaceHolder]="'Nhập số lượng'" required
                  nzDisabled="{{  !!item.isDisabledWarehouseActualOutboundQuantity && item?.warehouseActualOutboundQuantity  ===  item.quantity * item.conversionRate }}">
                </nz-input-number>
              </td>
              <td class="text-center" *ngIf="!!dataObject.isFromMobile && !data?.isSplit">
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.buyOutQuantity"
                  [ngModelOptions]="{ standalone: true }" [nzMin]="0" style="width: 100%" [nzStep]="1"
                  [nzPlaceHolder]="'Nhập số lượng'" required nzDisabled="{{
                    (  +item.warehouseActualOutboundQuantity >= +item.quantity ) || !!item.isWaitBuying
                  }}"></nz-input-number>
              </td>
              <td class="text-center" style="min-width: 200px" *ngIf="!!dataObject.isFromMobile && !data?.isSplit">
                <nz-date-picker placeholder="Chọn ngày" [(ngModel)]="item.waitingBuyingDateExpire" nzFormat="dd/MM/yyyy"
                  [ngModelOptions]="{ standalone: true }"></nz-date-picker>
              </td>

              <!-- phiếu mua vật tư tạo từ mobile thì mới hiện một số thong tin này -->

              <td class="text-center">{{ item.baseUnitName }}</td>
              <td class="text-center">{{ (+item.quantity - +item.buyOutQuantity) * +item.conversionRate | number }}</td>
              <td class="text-center">{{ item.price | number }}</td>
              <td class="text-center">
                {{ (+item.quantity - +item.buyOutQuantity) * +item.conversionRate * +item.price | number }}
              </td>

              <td class="text-center" nzWidth="80px" nzRight *ngIf="!dataObject.isDisabledEdit || !data?.isAdd">
                <button nz-button nzType="primary" nzDanger nzShape="circle" nz-tooltip nzTooltipTitle="Xóa"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?" (nzOnConfirm)="onDelete(i)" class="mr-2">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </nz-col>

    <!-- popover wait buying  -->

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-upload nz-row class="avatar-uploader" [nzAction]="uploadUrl" nzListType="picture-card"
            [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.lstOutboundImages"
            [nzShowButton]="dataObject.lstOutboundImages?.length <= 10" (nzChange)="handleChangeFileList($event)"
            [nzPreview]="handlePreview" [nzAccept]="'.png, .jpg, .jpeg'">
            <i nz-icon nzType="plus"></i>
            <div class="ant-upload-text">Upload</div>
          </nz-upload>
          <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
            (nzOnCancel)="previewVisible = false">
            <ng-template #modalContent>
              <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
            </ng-template>
          </nz-modal>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button *ngIf="!data.isSplit" nz-button [disabled]="!frmAdd.form.valid || dataObject?.lstOutboundDetails == 0"
        nzShape="round" nzType="primary" class="mr-3" (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
      <button *ngIf="data.isSplit" nz-button nzShape="round" nzType="primary" class="mr-3" (click)="onSplit()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Xác nhận tách
      </button>
    </div>
  </div>
</form>