import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { environment } from '../../../../../environments/environment'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'
@Component({ templateUrl: './add-or-edit-outbound-model.component.html' })
export class AddOrEditOutboundModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI PHIẾU XUẤT KHO CHO KHU'
  dataObject: any = {}
  isEditItem = false
  uploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  lstRepairOutboundType: any[] = [
    { code: 'EXPORT_INVENTORY', name: '<PERSON><PERSON><PERSON> tồn kho' },
    { code: 'DAMAGED', name: 'Xuất huỷ' },
  ]
  lstWarehouseMaterial: any[] = []
  lstMaterialCategory: any[] = []
  dicMaterialCategory: Record<string, any> = {}
  lstUnit: any[] = []
  dicUnit: Record<string, any> = {}
  dataEmployee: any[] = []
  outboundType: any[] = []
  apartments: any[] = []

  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditOutboundModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.outboundType = [
      // { code: 'EXPORT_EMPLOYEE', name: 'Xuất nhân viên', color: '' },
      { code: 'EXPORT_APARTMENT', name: 'Xuất khu', color: '' },
    ]

    this.notifyService.showloading()
    await this.loadAllData()
    this.dataObject.lstOutboundDetails = []
    this.dataObject.lstOutboundImages = []
    if (this.data && this.data !== null && this.data.id) {
      await this.onChangeWarehouseMaterialCategory(this.data.warehouseMaterialId)
      await this.apiScService
        .postRepair(this.apiScService.REPAIR_OUTBOUND.FIND_DETAIL, {
          id: this.data.id,
        })
        .then(async (rs) => {
          this.dataObject = rs
          for (let i = 0; i < this.dataObject.lstOutboundDetails.length; i++) {
            const outboundDetail = this.dataObject.lstOutboundDetails[i]
            this.dataObject.lstOutboundDetails[i].isVisibleWaitingBuyingInfo = false
            await this.onChangeMaterialCategory(outboundDetail, true)
          }
        })
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT PHIẾU XUẤT KHO'
    } else {
      this.dataObject.type = this.enumData.RepairOutboundType.EXPORT_APARTMENT.code
      this.dataObject.createdAt = new Date()
    }
    this.notifyService.hideloading()

    if (this.data?.isSplit) {
      this.modelTitle = 'TÁCH PHIẾU XUẤT KHO'
      this.dataObject.isDisabledEdit = true
    }
  }

  async loadAllData() {
    await Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.EMPLOYEE.GET_LIST_REPAIR_EMPLOYEE, {}),
      this.apiNtssService.post(this.apiNtssService.APARTMENT.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstWarehouseMaterial = rs[0]
      this.lstUnit = rs[1]
      this.dataEmployee = rs[2]
      this.apartments = rs[3]
    })
  }

  async onChangeWarehouseMaterialCategory(id: string) {
    //warehouseMaterialId: id
    this.lstMaterialCategory = await this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.LOAD_DATA, {})

    for (let mc of this.lstMaterialCategory) this.dicMaterialCategory[mc.materialCategoryId] = mc

    // Nếu là phiếu yêu cầu xuất kho để nhập vào kho nhân viên thì chọn kho không xoá các vật tư
    // if (this.dataObject?.type == this.enumData.RepairOutboundType.EXPORT_EMPLOYEE.code && this.dataObject.employeeId) {
    //   for (let e of this.dataObject.lstOutboundDetails) {
    //     this.onChangeMaterialCategory(e)
    //   }
    // } else
    if (!this.dataObject.employeeId) this.dataObject.lstOutboundDetails = []
  }

  onSave() {
    // #region Kiểm tra điều kiện trước khi lưu
    for (let e of this.dataObject.lstOutboundDetails) {
      if (!e.materialCategoryId) {
        this.notifyService.showError(`Vui lòng chọn vật tư xuất kho!`)
        return
      }
      if (!e.quantity) {
        this.notifyService.showError(`Vui lòng nhập số lượng xuất kho!`)
        return
      }
      if (!e.conversionRate) {
        this.notifyService.showError(`Vui lòng nhập tỉ lệ quy đổi lớn hơn 0!`)
        return
      }

      if (+e.quantity * +e.conversionRate > +e.inventory && !this.dataObject) {
        this.notifyService.showError(
          `Số lượng xuất kho của vật tư [ ${this.dicMaterialCategory[e.materialCategoryId].name} ] lớn hơn số lượng tồn kho!`
        )
        return
      }
    }

    const arrayLength: number = this.dataObject.lstOutboundDetails.length
    for (let i = 0; i < arrayLength; i++) {
      const mcI = this.dataObject.lstOutboundDetails[i]
      for (let j = i + 1; j < arrayLength; j++) {
        const mcJ = this.dataObject.lstOutboundDetails[j]

        if (mcJ.waitingBuyingDateExpire && this.normalizeDate(mcJ.waitingBuyingDateExpire) < this.normalizeDate(new Date())) {
          this.notifyService.showError(
            `Ngày hoàn thành chờ mua vật tư ${this.dicMaterialCategory[mcI.materialCategoryId].name} không được nhỏ hơn ngày hiện tại!`
          )
          return
        }
        if (mcI.materialCategoryId == mcJ.materialCategoryId) {
          this.notifyService.showError(`Vật tư ${this.dicMaterialCategory[mcI.materialCategoryId].name} đã trùng!`)
          return
        }
      }
    }

    // kiểm tra nếu outboundType = EXPORT_APARTMENT thì bắt buộc phải chọn căn hộ
    if (this.dataObject.type == this.enumData.RepairOutboundType.EXPORT_APARTMENT.code) {
      if (!this.dataObject.apartmentId) {
        this.notifyService.showError(`Vui lòng chọn khu!`)
        return
      }
    }
    // kiểm tra nếu outboundType = EXPORT_EMPLOYEE thì bắt buộc phải chọn nhân viên
    // if (this.dataObject.type == this.enumData.RepairOutboundType.EXPORT_EMPLOYEE.code) {
    //   if (!this.dataObject.employeeId) {
    //     this.notifyService.showError(`Vui lòng chọn nhân viên!`)
    //     return
    //   }
    // }

    // #endregion

    const data = this.dataObject

    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  onSplit() {
    const body = {
      id: this.dataObject.id,
      lstOutboundDetails: this.dataObject.lstOutboundDetails,
    }
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.SPLIT, body).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }
  normalizeDate(date: any): Date {
    if (!(date instanceof Date)) {
      date = new Date(date)
    }
    date.setHours(0, 0, 0, 0)
    return date
  }
  addObject(data: any) {
    const employee = this.dataEmployee.find((e: any) => e.id == data.employeeId)
    if (employee) {
      data.employeeName = employee.fullName
    }
    const apartment = this.apartments.find((e: any) => e.id == data.apartmentId)
    if (apartment) {
      data.apartmentName = apartment.name
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  previewImage: string | undefined = ''
  previewVisible = false

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  handleChangeFileList(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              arr.push({
                name: item?.name ? item?.name : item?.originFileObj?.name,
                url: item?.url || item?.response?.length > 0 ? item?.response[0] : '',
                uid: item.uid,
              })
            }
          }
          this.dataObject.lstOutboundImages = arr
        }
        break
      case 'error':
        break
      case 'removed':
        {
          this.dataObject.lstOutboundImages.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) this.dataObject.lstOutboundImages.splice(index, 1)
          })
        }
        break
    }
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  async onAddOutboundDetail() {
    this.dataObject.lstOutboundDetails.push({
      id: '',
      materialCategoryId: '',
      materialCategoryName: '',
      unitId: '',
      baseUnitId: '',
      conversionRate: 0,
      baseUnitName: '',
      quantity: 0,
      outboundQuantity: 0,
      price: 0,
      inventory: 0,
      index: this.dataObject.lstOutboundDetails.length,
    })
  }

  async onChangeMaterialCategory(item: any, isEdit: boolean = false) {
    if (!isEdit) {
      item.unitId = null
      item.baseUnitId = null
      item.conversionRate = null
      item.baseUnitName = null
      item.quantity = null
      item.outboundQuantity = null
      item.price = null
      item.inventory = null
    }
    const mc = this.dicMaterialCategory[item.materialCategoryId]
    if (mc) {
      item.baseUnitId = mc.baseUnitId
      item.baseUnitName = mc.baseUnitName
      item.materialCategoryId = mc.materialCategoryId
      item.materialCategoryName = mc.name
      // tồn kho của vật tư đã được lấy ở backend từ API trước
      item.inventory = item.inventory
      if (+item.warehouseActualOutboundQuantity > 0) {
        item.price = mc.price
      }
      item.lstUnit = mc.lstMaterialCategoryUnits
    }
  }

  onDelete(index: any) {
    this.dataObject.lstOutboundDetails.splice(index, 1)
  }

  onChangeUnit(item: any) {
    const find = item.lstUnit.find((e: any) => e.unitId == item.unitId)
    item.conversionRate = find.conversionRate
  }

  onChangeType() {
    this.dataObject.employeeId = null
    this.dataObject.employeeName = null
  }

  onChangeWaitingBuyingQuantity(item: any) {
    item.waitingBuyingDateExpire = null
    item.isDisabledWarehouseActualOutboundQuantity = false
  }
}
