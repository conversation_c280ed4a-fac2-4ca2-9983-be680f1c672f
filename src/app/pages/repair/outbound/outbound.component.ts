import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditOutboundModelComponent } from './add-or-edit-outbound-model/add-or-edit-outbound-model.component'
import { OutboundDetailModelComponent } from './outbound-detail-model/outbound-detail-model.component'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'
import { enumData } from 'src/app/core'

@Component({ templateUrl: './outbound.component.html' })
export class OutboundComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  enumDataNew = enumData.RepairOutboundStatus.NEW.code
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  lstOutboundStatus: any[] = []
  listOfData: any = []
  dataSearch: any = {}
  lstSupplier: any = []
  loading = true
  lstWarehouseCategory: any[] = []
  lstRepairOutboundType: any[] = []
  lstMaterialCategory: any[] = []
  lstUnit: any[] = []

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }
  async loadMaterialCategory(id: string) {
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {
        warehouseMaterialId: id,
      })
      .then((res) => {
        this.lstMaterialCategory = res
      })
  }

  ngOnInit() {
    this.lstRepairOutboundType = this.coreService.convertObjToArray(this.enumData.RepairOutboundType)
    this.lstOutboundStatus = this.coreService.convertObjToArray(this.enumData.RepairOutboundStatus)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total

    this.loadAllData()
  }

  async loadAllData() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.SUPPLIER.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstWarehouseCategory = rs[0]
      await this.loadMaterialCategory(this.dataSearch.warehouseMaterialId)
      this.lstUnit = rs[1]
      this.lstSupplier = rs[2]
      this.searchData()
    })
  }

  onShowEdit(object: any, isClearWaitBuying: boolean = false, isSplit: boolean = false) {
    this.dialog
      .open(AddOrEditOutboundModelComponent, { disableClose: false, data: { ...object, isClearWaitBuying, isSplit } })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  onAdd() {
    this.dialog
      .open(AddOrEditOutboundModelComponent, {
        disableClose: false,
        data: {
          isAdd: true,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(OutboundDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  updateCancel(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.UPDATE_CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApprove(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.UPDATE_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  /** Gửi duyệt phiếu xuất kho */
  updateApproving(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_OUTBOUND.UPDATE_APPROVE_FOR_APPROVING, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }
}
