<div nz-row class="mt-2">
  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON> phiếu xuất kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.code }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.employeeName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Nhân viên</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.employeeName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div nz-col nzSpan="6" *ngIf="dataObject.apartmentName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Khu</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.apartmentName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.warehouseMaterialName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Loại phiếu</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.typeName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.employeeName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người nhận</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.employeeName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.reason">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Lý do xuất</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.reason }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdAt | date : 'HH:mm dd/MM/YYYY' }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdByName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedDate">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedDate | date : 'HH:mm dd/MM/YYYY' }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedByName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedByName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.description">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Mô tả </nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.description }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24" *ngIf="dataObject.lstOutboundImages && dataObject.lstOutboundImages.length > 5">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu xuất kho </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex">
        <nz-carousel nz-row nzGutter="4" [nzEffect]="effect" class="carousel" [nzAutoPlay]="true">
          <div nz-col nzSpan="2" nz-carousel-content *ngFor="let item of dataObject.lstOutboundImages; let i = index">
            <img nz-image width="100px" height="100px" nzSrc="{{ item.url }}" alt="" />
          </div>
        </nz-carousel>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24" *ngIf="
      dataObject.lstOutboundImages &&
      dataObject.lstOutboundImages.length <= 5 &&
      dataObject.lstOutboundImages.length > 0
    ">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu xuất kho </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex">
        <nz-row nzGutter="4">
          <ng-container nz-col nzSpan="2" *ngFor="let item of dataObject.lstOutboundImages; let i = index">
            <img nz-image width="100px" height="100px" nzSrc="{{ item.url }}" alt="" />
          </ng-container>
        </nz-row>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24">
    <div nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstOutboundDetails" [nzShowPagination]="false" #ajaxTable
        [nzFrontPagination]="false" nzTemplateMode nzBordered [nzScroll]="{ x: '1200px' }">
        <thead>
          <tr>
            <th class="text-center" nzWidth="180px" nzLeft>Mã vật tư</th>
            <th class="text-center" nzWidth="180px">Tên vật tư</th>
            <th class="text-center" nzWidth="180px">Đơn vị tính</th>
            <th class="text-center" nzWidth="180px">Số lượng yêu cầu</th>
            <th class="text-center" nzWidth="180px">Tỉ lệ quy đổi</th>
            <th class="text-center" nzWidth="180px">Đơn vị cơ sở</th>
            <th class="text-center" nzWidth="180px">Số lượng cần xuất (Tính ra theo đơn vị cơ sở)</th>
            <th class="text-center" nzWidth="180px" *ngIf="!!dataObject.isFromMobile">Thực xuất</th>
            <th class="text-center" nzWidth="180px" *ngIf="!!dataObject.isFromMobile">Mua ngoài</th>
            <th class="text-center" nzWidth="180px" *ngIf="!!dataObject.isFromMobile">Ngày hoàn thành mua</th>

            <th class="text-center" nzWidth="180px">Đơn giá</th>
            <th class="text-center" nzWidth="180px">Thành tiền</th>
            <!-- <th class="text-center" nzWidth="220px" nzRight>Tồn kho công ty khi tạo</th> -->
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataObject.lstOutboundDetails; let i = index">
            <td class="text-center" nzLeft>{{ item.materialCategoryCode }}</td>
            <td class="text-center">{{ item.materialCategoryName }}</td>
            <td class="text-center">{{ item.unitName }}</td>
            <td class="text-center">{{ item.quantity | number }}</td>
            <td class="text-center">{{ item.conversionRate | number }}</td>
            <td class="text-center">{{ item.baseUnitName }}</td>
            <td class="text-center">{{ item.outboundQuantity }}</td>
            <td class="text-center" *ngIf="!!dataObject.isFromMobile">{{ item.warehouseActualOutboundQuantity }}</td>
            <td class="text-center" *ngIf="!!dataObject.isFromMobile">{{ item.buyOutQuantity }}</td>
            <td class="text-center" *ngIf="!!dataObject.isFromMobile">
              {{ item.waitingBuyingDateExpire | date : 'dd/MM/yyyy' }}
            </td>

            <td class="text-center">{{ item.price | number }}</td>
            <td class="text-center">{{ +item.outboundQuantity * +item.price | number }}</td>
            <!-- <td class="text-center" nzRight>{{ item.inventory | number }}</td> -->
          </tr>
        </tbody>
      </nz-table>
    </div>
  </div>
</div>