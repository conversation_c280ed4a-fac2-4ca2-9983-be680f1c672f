<div nz-row nzGutter="8" class="mt-2">
  <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable1 [nzData]="dataObject.lstOutboundHistory"
    [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
    <thead>
      <tr>
        <th class="text-center" nzWidth="160px">Thời gian</th>
        <th class="text-center">Nội dung</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let detail of ajaxTable1.data">
        <td class="text-center">{{ detail.createdAt | date: 'HH:mm dd/MM/YYYY'}}</td>
        <td class="text-center">{{ detail.description}}</td>
      </tr>
    </tbody>
  </nz-table>
</div>