import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  templateUrl: './outbound-detail-model.component.html',
})
export class OutboundDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU XUẤT KHO'
  dataObject: any = {}
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_OUTBOUND.FIND_DETAIL, {
        id: this.data.id,
      })
      .then((rs: any) => {
        this.dataObject = rs
        if (this.dataObject.lstInboundImages && this.dataObject.lstInboundImages.length > 0) {
          for (const img of this.dataObject.lstInboundImages) {
            if (!img.src) img.src = img.url
          }
        }
      })
    this.notifySerivce.hideloading()
  }
}
