<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  <button nzShape="round" class="mr-2" nz-button (click)="onDownloadTemplateExcel()">
    <span nz-icon nzType="download"></span>Tải Template Excel
  </button>
  <input
    class="hidden"
    type="file"
    id="file"
    (change)="clickImportExcel($event)"
    placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
  />
  <label nz-button for="file" class="ant-btn lable-custom-file-custom mr-2"> <span nz-icon nzType="upload"></span> Nhập Excel </label>
  <button class="mr-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
  </button>
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Mã phiếu </nz-form-label>
            <nz-form-control nzSpan="24">
              <input nz-input [(ngModel)]="dataSearch.code" name="dataSearch.code" placeholder="Nhập mã phiếu" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Kho </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.warehouseMaterialId" name="warehouseMaterialId" nzPlaceHolder="Chọn kho">
                <nz-option *ngFor="let item of lstWarehouseCategory" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Trạng thái </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Chọn trạng thái">
                <nz-option *ngFor="let item of lstCheckInventoryStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Ngày tạo </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-date-picker nzFormat="dd/MM/yyyy" [(ngModel)]="dataSearch.createdAt" name="createdAt"> </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Ngày duyệt </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-date-picker nzFormat="dd/MM/yyyy" [(ngModel)]="dataSearch.approvedDate" name="approvedDate"> </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th class="text-center">Mã</th>
        <th class="text-center">Kho</th>
        <th class="text-center">Ngày tạo</th>
        <th class="text-center">Người tạo</th>
        <th class="text-center">Ngày duyệt</th>
        <th class="text-center">Người duyệt</th>
        <th class="text-center">Trạng thái</th>
        <th class="text-center">Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">{{ data.code }}</td>
        <td class="text-center">{{ data.warehouseMaterialName }}</td>
        <td class="text-center">{{ data.createdAt | date : 'HH:mm dd/MM/YYYY' }}</td>
        <td class="text-center">{{ data.createdByName }}</td>
        <td class="text-center">{{ data.approvedDate | date : 'HH:mm dd/MM/YYYY' }}</td>
        <td class="text-center">{{ data.approvedByName }}</td>
        <td class="text-center">{{ data.statusName }}</td>
        <td class="text-center">
          <button
            class="mr-2 mt-2"
            (click)="onShowDetail(data)"
            nz-tooltip
            nzTooltipTitle="Chi tiết"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <i nz-icon nzType="eye"></i>
          </button>
          <button
            class="mr-2 mt-2"
            *ngIf="
              !data.isDeleted &&
              (data.status === enumDataSc.RepairCheckInventoryStatus.NEW.code || data.status === enumDataSc.RepairCheckInventoryStatus.APPROVING.code)
            "
            (click)="onShowEdit(data)"
            nz-tooltip
            nzTooltipTitle="Chỉnh Sửa"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <i nz-icon nzType="edit"></i>
          </button>
          <button
            *ngIf="data.status === enumDataSc.RepairCheckInventoryStatus.NEW.code"
            nzType="primary"
            class="mr-2 mt-2"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn cập nhật đang kiểm phiếu kiểm kho?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="updateApproving(data)"
            nz-tooltip
            nzTooltipTitle="Cập nhật đang kiểm"
            nz-button
            nzType="primary"
            nzShape="circle"
          >
            <i nz-icon nzType="check" nzTheme="outline"></i>
          </button>
          <button
            *ngIf="data.status === enumDataSc.RepairCheckInventoryStatus.NEW.code"
            nzType="primary"
            class="mr-2 mt-2"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn huỷ phiếu kiểm kho"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="updateCancel(data)"
            nz-tooltip
            nzTooltipTitle="Huỷ phiếu kiểm kho"
            nz-button
            nzGhost
            nzDanger="true"
            nzShape="circle"
          >
            <i nz-icon nzType="close" nzTheme="outline"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
