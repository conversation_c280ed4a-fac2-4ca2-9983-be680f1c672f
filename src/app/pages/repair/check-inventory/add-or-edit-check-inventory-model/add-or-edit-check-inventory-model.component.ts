import { Component, OnInit, Optional, Inject } from '@angular/core'
import { NotifyService } from '../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { environment } from '../../../../../environments/environment'
import { CoreService } from '../../../../services/core.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './add-or-edit-check-inventory-model.component.html' })
export class AddOrEditCheckInventoryModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI PHIẾU KIỂM KHO'
  dataObject: any = {}
  isEditItem = false
  uploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  lstWarehouseMaterial: any[] = []
  lstMaterialCategory: any[] = []
  dicMaterialCategory: Record<string, any> = {}

  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private coreService: CoreService,
    private dialogRef: MatDialogRef<AddOrEditCheckInventoryModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    await this.loadAllData()
    this.dataObject.lstCheckInventoryDetails = []
    this.dataObject.lstCheckInventoryImages = []
    if (this.data && this.data !== null) {
      await this.onChangeWarehouseMaterialCategory(this.data.warehouseMaterialId)
      await this.apiScService
        .postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.FIND_DETAIL, {
          id: this.data.id,
        })
        .then(async (rs) => {
          this.dataObject = rs
          for (let e of this.dataObject.lstCheckInventoryDetails) {
            await this.onChangeMaterialCategory(e, true)
          }
        })

      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT PHIẾU KIỂM KHO'
    } else {
      this.dataObject.createdAt = new Date()
    }
  }

  async loadAllData() {
    await Promise.all([this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {})]).then(async (rs) => {
      this.lstWarehouseMaterial = rs[0]
    })
  }

  async onChangeWarehouseMaterialCategory(id: string) {
    this.dataObject.lstCheckInventoryDetails = []
    this.lstMaterialCategory = await this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.LOAD_DATA, {
      warehouseMaterialId: id,
    })
    for (let mc of this.lstMaterialCategory) this.dicMaterialCategory[mc.materialCategoryId] = mc
  }

  onSave() {
    // #region Kiểm tra điều kiện trước khi lưu
    for (let e of this.dataObject.lstCheckInventoryDetails) {
      if (!e.materialCategoryId) {
        this.notifyService.showError(`Vui lòng chọn vật tư kiểm kho!`)
        return
      }
      if (+e.quantity < 0) {
        this.notifyService.showError(`Vui lòng nhập số lượng kiểm kho lớn hơn hoặc bằng 0!`)
        return
      }
    }

    const arrayLength: number = this.dataObject.lstCheckInventoryDetails.length
    for (let i = 0; i < arrayLength; i++) {
      const mcI = this.dataObject.lstCheckInventoryDetails[i]
      for (let j = i + 1; j < arrayLength; j++) {
        const mcJ = this.dataObject.lstCheckInventoryDetails[j]
        if (mcI.materialCategoryId == mcJ.materialCategoryId) {
          this.notifyService.showError(`Vật tư ${this.dicMaterialCategory[mcI.materialCategoryId].name} đã trùng!`)
          return
        }
      }
    }
    // #endregion

    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  previewImage: string | undefined = ''
  previewVisible = false

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  handleChangeFileList(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              arr.push({
                name: item?.name ? item?.name : item?.originFileObj?.name,
                url: item?.url || item?.response?.length > 0 ? item?.response[0] : '',
                uid: item.uid,
              })
            }
          }
          this.dataObject.lstCheckInventoryImages = arr
        }
        break
      case 'error':
        break
      case 'removed':
        {
          this.dataObject.lstCheckInventoryImages.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) this.dataObject.lstCheckInventoryImages.splice(index, 1)
          })
        }
        break
    }
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  async onAddCheckInventoryDetail() {
    this.dataObject.lstCheckInventoryDetails.push({
      id: '',
      materialCategoryId: '',
      materialCategoryName: '',
      unitName: '',
      baseUnitName: '',
      quantity: 0,
      price: 0,
      inventory: 0,
      index: this.dataObject.lstCheckInventoryDetails.length,
    })
  }

  async onChangeMaterialCategory(item: any, isEdit: boolean = false) {
    if (!isEdit) {
      item.unitName = null
      item.baseUnitName = null
      item.quantity = null
      item.price = null
      item.inventory = null
    }

    const mc = this.dicMaterialCategory[item.materialCategoryId]
    item.unitName = mc.unitName
    item.baseUnitName = mc.baseUnitName
    // item.materialCategoryId = mc.id
    item.materialCategoryName = mc.name
    item.inventory = +mc.quantity - +mc.quantityLock
    item.price = mc.price
  }

  onDelete(index: any) {
    this.dataObject.lstCheckInventoryDetails.splice(index, 1)
  }

  async onAddAll() {
    if (!this.dataObject.warehouseMaterialId) {
      this.notifyService.showError(`Vui lòng chọn kho kiểm!`)
      return
    }
    for (let item of this.lstMaterialCategory) {
      const mC = this.dicMaterialCategory[item.materialCategoryId]
      this.dataObject.lstCheckInventoryDetails.push({
        id: '',
        materialCategoryId: item.materialCategoryId,
        materialCategoryName: mC.materialCategoryName,
        unitName: mC.unitName,
        baseUnitName: mC.baseUnitName,
        quantity: 0,
        price: mC.price,
        inventory: mC.quantity,
        index: this.dataObject.lstCheckInventoryDetails.length,
      })
    }
  }

  checkStatus(): boolean {
    if (this.dataObject?.status == this.enumData.RepairCheckInventoryStatus.APPROVING.code) return true
    return false
  }
}
