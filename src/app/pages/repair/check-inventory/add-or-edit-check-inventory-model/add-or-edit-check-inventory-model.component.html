<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzGutter="8">
    <nz-col nzSpan="6" *ngIf="isEditItem">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã</nz-form-label>
        <nz-form-control nzSpan="24"
          nzErrorTip="Vui lòng nhập mã từ 1 đến 36 kí tự và không kèm kí tự đặc biệt khoảng cách">
          <input nz-input [disabled]="isEditItem " placeholder="Nhập mã 1-36 kí tự" [(ngModel)]="dataObject.code"
            name="code" pattern="^([a-zA-Z0-9]){1,36}$" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Kho</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kho">
          <nz-select [disabled]="checkStatus()" nzShowSearch nzAllowClear nzPlaceHolder="Chọn kho"
            [(ngModel)]="dataObject.warehouseMaterialId" name="warehouseMaterialId"
            (ngModelChange)="onChangeWarehouseMaterialCategory($event)" required>
            <nz-option *ngFor="let item of lstWarehouseMaterial" [nzLabel]="item.name" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày tạo phiếu</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-date-picker [disabled]="true" nzFormat="dd/MM/yyyy" [(ngModel)]="dataObject.createdAt" name="createdAt">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="24" class="text-right">
      <button [disabled]="checkStatus()" nzShape="round" class="mr-2" nz-button nzType="primary"
        (click)="onAddCheckInventoryDetail()">
        <span nz-icon nzType="plus"></span>Thêm
      </button>
      <button [disabled]="checkStatus()" nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onAddAll()">
        <span nz-icon nzType="plus"></span>Thêm tất cả
      </button>

      <div nz-row class="mt-2">
        <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstCheckInventoryDetails" [nzShowPagination]="false"
          class="mb-3" #ajaxTable [nzFrontPagination]="false" [nzScroll]="{ x: '1200px', y: null }" nzTemplateMode
          nzBordered>
          <thead>
            <tr>
              <th class="text-center" nzRight nzWidth="80px">Tác vụ</th>
              <th class="text-center">Tên loại vật tư</th>
              <th class="text-center">Đơn vị cơ sở</th>
              <th class="text-center">Số lượng hệ thống</th>
              <th class="text-center">Số lượng thực tế</th>
              <th class="text-center">Chênh lệch</th>
              <th class="text-center">Nguyên nhân</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstCheckInventoryDetails; let i = index">
              <td class="text-center" nzRight nzWidth="80px">
                <button [disabled]="checkStatus()" nz-button nzType="primary" nzDanger nzShape="circle" nz-tooltip
                  nzTooltipTitle="Xóa" nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?"
                  (nzOnConfirm)="onDelete(i)" class="mr-2">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </td>
              <td>
                <nz-select [disabled]="checkStatus()" nzShowSearch [(ngModel)]="item.materialCategoryId"
                  [ngModelOptions]="{ standalone: true }" nzPlaceHolder="Chọn tên loại vật tư"
                  (ngModelChange)="onChangeMaterialCategory(item)">
                  <nz-option *ngFor="let mc of lstMaterialCategory" [nzLabel]="mc.name"
                    [nzValue]="mc.materialCategoryId">
                  </nz-option>
                </nz-select>
              </td>
              <td class="text-center" nzWidth="80px">{{ item.baseUnitName }}</td>
              <td class="text-center" nzWidth="80px">{{ item.inventory | number}}</td>
              <td class="text-center" nzWidth="80px">
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.quantity"
                  [ngModelOptions]="{ standalone: true }" defaultValue="0" [nzMin]="0" style="width: 100%;" [nzStep]="1"
                  [nzPlaceHolder]="'Nhập số lượng'" required></nz-input-number>
              </td>
              <td class="text-center" nzWidth="80px">{{ ( +item.quantity - +item.inventory) | number}} </td>
              <td class="text-center" nzWidth="80px">
                <input nz-input class="mr-2" [(ngModel)]="item.reason" [ngModelOptions]="{ standalone: true }"
                  placeholder="Nhập nguyên nhân" required />
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </nz-col>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-upload nz-row class="avatar-uploader" [nzAction]="uploadUrl" nzListType="picture-card"
            [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.lstCheckInventoryImages"
            [nzShowButton]="dataObject.lstCheckInventoryImages?.length <= 10" (nzChange)="handleChangeFileList($event)"
            [nzPreview]="handlePreview" [nzAccept]="'.png, .jpg, .jpeg'">
            <i nz-icon nzType="plus"></i>
            <div class="ant-upload-text">Upload</div>
          </nz-upload>
          <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
            (nzOnCancel)="previewVisible = false">
            <ng-template #modalContent>
              <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
            </ng-template>
          </nz-modal>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid || dataObject?.lstCheckInventoryDetails == 0" nzShape="round"
        nzType="primary" class="mr-3" (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>