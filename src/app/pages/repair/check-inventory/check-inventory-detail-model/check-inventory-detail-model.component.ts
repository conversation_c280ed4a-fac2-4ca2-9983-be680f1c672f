import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { NzImageService } from 'ng-zorro-antd/image'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './check-inventory-detail-model.component.html' })
export class CheckInventoryDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU KIỂM KHO'
  dataObject: any = {}
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private dialogRef: MatDialogRef<CheckInventoryDetailModelComponent>,
    private nzImageService: NzImageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.FIND_DETAIL, {
        id: this.data.id,
      })
      .then((rs: any) => {
        this.dataObject = rs
        if (this.dataObject.lstCheckInventoryImages && this.dataObject.lstCheckInventoryImages.length > 0) {
          for (const img of this.dataObject.lstCheckInventoryImages) {
            if (!img.src) img.src = img.url
          }
        }
      })
    this.notifySerivce.hideloading()
  }

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
