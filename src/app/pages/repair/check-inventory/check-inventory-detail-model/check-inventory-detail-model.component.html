<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<div nz-row class="mt-4">
  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">M<PERSON> phiếu kiểm kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.code }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left"><PERSON>ho kiểm</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.warehouseMaterialName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON><PERSON> t<PERSON><PERSON></nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdAt | date: 'HH:mm dd/MM/YYYY'}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdByName}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedDate">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedDate | date: 'HH:mm dd/MM/YYYY'}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedByName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedByName}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.description">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Mô tả
      </nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.description }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24"
    *ngIf="dataObject?.lstCheckInventoryImages && dataObject?.lstCheckInventoryImages?.length > 5">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu kiểm kho
      </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex;">
        <nz-carousel nz-row nzGutter="4" [nzEffect]="effect" class="carousel" [nzAutoPlay]="true">
          <div nz-col nzSpan="2" nz-carousel-content
            *ngFor="let item of dataObject?.lstCheckInventoryImages; let i = index">
            <img nz-image width="100px" height="100px" nzSrc="{{item.url}}" alt="" />
          </div>
        </nz-carousel>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24"
    *ngIf="dataObject?.lstCheckInventoryImages && dataObject?.lstCheckInventoryImages?.length <= 5 && dataObject?.lstCheckInventoryImages?.length > 0">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu kiểm kho
      </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex;">
        <nz-row nzGutter="4">
          <ng-container nz-col nzSpan="2" *ngFor="let item of dataObject?.lstCheckInventoryImages; let i = index">
            <img nz-image width="100px" height="100px" class="mr-2" nzSrc="{{item.url}}" alt="" />
          </ng-container>
        </nz-row>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24">
    <div nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstCheckInventoryDetails" [nzShowPagination]="false" #ajaxTable
        [nzFrontPagination]="false" nzTemplateMode nzBordered>
        <thead>
          <tr>
            <th class="text-center">Mã vật tư</th>
            <th class="text-center">Tên vật tư</th>
            <th class="text-center">Đơn vị cơ sở</th>
            <th class="text-center">Số lượng hệ thống</th>
            <th class="text-center">Số lượng thực tế</th>
            <th class="text-center">Chênh lệch</th>
            <th class="text-center">Nguyên nhân</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataObject.lstCheckInventoryDetails; let i = index">
            <td class="text-center" nzWidth="80px">{{ item.materialCategoryCode }}</td>
            <td class="text-center" nzWidth="80px">{{ item.materialCategoryName }}</td>
            <td class="text-center" nzWidth="80px">{{ item.baseUnitName }}</td>
            <td class="text-center" nzWidth="80px">{{ item.inventory | number }}</td>
            <td class="text-center" nzWidth="80px">{{ item.quantity | number }}</td>
            <td class="text-center" nzWidth="80px">{{ ( +item.quantity - +item.inventory) | number}}</td>
            <td class="text-center" nzWidth="80px">{{ item.reason }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </div>
</div>