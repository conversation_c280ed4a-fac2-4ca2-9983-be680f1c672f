import { Component, OnInit } from '@angular/core'
import { NotifyService } from '../../../services/notify.service'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditCheckInventoryModelComponent } from './add-or-edit-check-inventory-model/add-or-edit-check-inventory-model.component'
import { CoreService } from '../../../services/core.service'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CheckInventoryDetailModelComponent } from './check-inventory-detail-model/check-inventory-detail-model.component'
import * as fs from 'file-saver'
import { Workbook } from 'exceljs'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { isInteger } from 'mathjs'
import { ApiScService } from 'src/app/services/apiSc.service'
import { enumData } from 'src/app/core'
@Component({ templateUrl: './check-inventory.component.html' })
export class CheckInventoryComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  enumDataSc= enumData
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  lstCheckInventoryStatus: any[] = []
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstWarehouseCategory: any[] = []
  lstMaterialCategory: any[] = []

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.lstCheckInventoryStatus = this.coreService.convertObjToArray(this.enumData.RepairCheckInventoryStatus)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstWarehouseCategory = rs[0]
      this.lstMaterialCategory = rs[1]
    })
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditCheckInventoryModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditCheckInventoryModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(CheckInventoryDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách phiếu kiểm kho')

    //#region Body Table
    const header = [
      'Mã phiếu * (code)',
      'Mã kho * (warehouseMaterialCode)',
      'Mã vật tư * (materialCategoryCode)',
      'Số lượng * (quantity)',
      'Nguyên nhân (reason)',
      'Mô tả (description)',
    ]
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    // #region Danh sách vật tư
    const worksheet1 = workbook.addWorksheet('Danh sách vật tư')

    //#region Body Table
    const header1 = ['Mã vật tư * (code)', 'Tên vật tư * (name)']
    const headerRow1 = worksheet1.addRow(header1)

    // Add Data and Conditional Formatting
    for (let data of this.lstMaterialCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet1.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow1.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet1.getColumn(colNumber).width = 30
          break
        default:
          worksheet1.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    const worksheet2 = workbook.addWorksheet('Danh sách kho')

    //#region Body Table
    const header2 = ['Mã kho * (code)', 'Tên kho * (name)']
    const headerRow2 = worksheet2.addRow(header2)

    // Add Data and Conditional Formatting
    for (let data of this.lstWarehouseCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet2.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_PHEU_KIEM_KHO${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  lstHeaderRequire: string[] = ['code', 'warehouseMaterialCode', 'materialCategoryCode', 'quantity']

  dicHeader: Record<string, any> = {
    code: 'Mã phiếu',
    warehouseMaterialCode: 'Mã kho',
    materialCategoryCode: 'Mã vật tư',
    quantity: 'Số lượng thực tế',
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['code', 'warehouseMaterialCode', 'materialCategoryCode', 'quantity', 'reason', 'description'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 1
        for (let hd of this.lstHeaderRequire) {
          if (!row[hd]) strErr += 'Dòng ' + idx + ` - ${this.dicHeader[hd]} không được để trống <br>`
        }
        if (row.quantity && (isNaN(row.quantity) || +row.quantity < 0 || !isInteger(row.quantity))) {
          strErr += 'Dòng ' + idx + ` - Số lượng kiểm phải là số nguyên lớn hơn hoặc bằng 0 <br>`
        }

        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }
      this.apiScService
        .postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.CREATE_DATA_BY_EXCEL, jsonData)
        .then((result) => {
          this.notifyService.hideloading()
          if (result) {
            this.notifyService.showSuccess('Thêm file excel thành công')
            this.searchData()
          }
        })
    }
  }

  updateCancel(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.UPDATE_CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApproving(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.UPDATE_APPROVING, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApprove(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.UPDATE_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_CHECK_INVENTORY.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách PKK')
        //#region Body Table
        const header = ['Mã', 'Kho', 'Ngày tạo', 'Ngày duyệt', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.warehouseMaterialName || '',
            data.createdAt ? moment(data.createdAt).format('DD/MM/YYYY') : '',
            data.approvedDate ? moment(data.approvedDate).format('DD/MM/YYYY') : '',
            data.statusName || '',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_PHIEU_KIEM_KHO_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
