import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { isInteger } from 'mathjs'
import * as moment from 'moment'
import * as XLSX from 'xlsx'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditBuyingMaterialModelComponent } from './add-or-edit-buying-material-model/add-or-edit-buying-material-model.component'
import { BuyingMaterialDetailModelComponent } from './buying-material-detail-model/buying-material-detail-model.component'
import { ApiScService } from 'src/app/services/apiSc.service'
@Component({ templateUrl: './buying-material.component.html' })
export class BuyingMaterialComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  lstBuyingMaterialStatus: any[] = []
  lstBuyingMaterialType: any[] = []
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstWarehouseCategory: any[] = []
  lstMaterialCategory: any[] = []

  /// handle select request buying material
  setOfCheckedId = new Set<string>()
  allChecked = false
  indeterminate = false

  updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id)
    } else {
      this.setOfCheckedId.delete(id)
    }
  }

  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedSet(id, checked)
  }

  onAllChecked(checked: boolean): void {
    this.listOfData.forEach((item: any) => {
      if (item.isApproving) {
        this.updateCheckedSet(item.id, checked)
      }
    })
  }

  refreshCheckedStatus(): void {
    this.allChecked = this.listOfData
      .filter((item: any) => item.isApproving)
      .every((item: any) => this.setOfCheckedId.has(item.id))
    this.indeterminate =
      this.listOfData.some((item: any) => {
        if (item.isApproving && this.setOfCheckedId.has(item.id)) return true
        return false
      }) && !this.allChecked
  }

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.lstBuyingMaterialStatus = this.coreService.convertObjToArray(this.enumData.RepairBuyingMaterialStatus)
    this.lstBuyingMaterialType = this.coreService.convertObjToArray(this.enumData.RepairBuyingMaterialType)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total

    this.dataSearch.isDeleted = false
    this.dataSearch.type = this.enumData.RepairBuyingMaterialType.BUYING_MATERIAL_IMPORT_WAREHOUSE.code
    this.searchData()
    this.setOfCheckedId = new Set<string>()
    this.allChecked = false
    this.indeterminate = false
  }

  async loadAllData() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstWarehouseCategory = rs[0]
      this.lstMaterialCategory = rs[1]
    })
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditBuyingMaterialModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditBuyingMaterialModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(BuyingMaterialDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách phiếu mua vật tư')

    //#region Body Table
    const header = [
      'Mã phiếu * (code)',
      'Mã kho * (warehouseMaterialCode)',
      'Nhà cung cấp * (supplierName)',
      'Người nhận * (receivedByName)',
      'Mã loại vật tư * (materialCategoryCode)',
      'Mã đơn vị tính * (unitCode)',
      'Số lượng * (quantity)',
      'Mô tả (description)',
    ]
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
        case 8:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    // #region Danh sách vật tư
    const worksheet1 = workbook.addWorksheet('Danh sách vật tư')

    //#region Body Table
    const header1 = ['Mã vật tư * (code)', 'Tên vật tư * (name)']
    const headerRow1 = worksheet1.addRow(header1)

    // Add Data and Conditional Formatting
    for (let data of this.lstMaterialCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet1.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow1.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet1.getColumn(colNumber).width = 30
          break
        default:
          worksheet1.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    const worksheet2 = workbook.addWorksheet('Danh sách kho')

    //#region Body Table
    const header2 = ['Mã kho * (code)', 'Tên kho * (name)']
    const headerRow2 = worksheet2.addRow(header2)

    // Add Data and Conditional Formatting
    for (let data of this.lstWarehouseCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet2.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_PHIEU_NHAP_KHO${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  lstHeaderRequire: string[] = ['code', 'warehouseMaterialCode', 'materialCategoryCode', 'unitCode', 'quantity']

  dicHeader: Record<string, any> = {
    code: 'Số thứ tự',
    warehouseMaterialCode: 'Mã kho',
    materialCategoryCode: 'Mã vật tư',
    unitCode: 'Mã đơn vị tính',
    quantity: 'Số lượng',
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['code', 'warehouseMaterialCode', 'materialCategoryCode', 'unitCode', 'quantity', 'description'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 1
        for (let hd of this.lstHeaderRequire) {
          if (!row[hd]) strErr += 'Dòng ' + idx + ` - ${this.dicHeader[hd]} không được để trống <br>`
        }
        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
        if (row.quantity && (isNaN(row.quantity) || +row.quantity < 0 || !isInteger(row.quantity))) {
          strErr += 'Dòng ' + idx + ` - Số lượng xuất phải là số nguyên lớn hơn 0 <br>`
        }
      }
      this.apiScService
        .postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.CREATE_DATA_BY_EXCEL, jsonData)
        .then((result) => {
          this.notifyService.hideloading()
          if (result) {
            this.notifyService.showSuccess('Thêm file excel thành công')
            this.searchData()
          }
        })
    }
  }

  updateCancel(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.UPDATE_CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApprove(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.UPDATE_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApproving(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.UPDATE_APPROVING, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách PNK')
        //#region Body Table
        const header = ['Mã', 'Kho', 'Ngày tạo', 'Ngày duyệt', 'Loại phiếu', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.warehouseMaterialName || '',
            data.createdAt ? moment(data.createdAt).format('DD/MM/YYYY') : '',
            data.approvedDate ? moment(data.approvedDate).format('DD/MM/YYYY') : '',
            data.typeName || '',
            data.statusName || '',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_PHIEU_NHAP_KHO_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }

  sendToFinancialAccountant() {
    this.notifyService.showloading()

    const lstBuyingMaterialIds = Array.from(this.setOfCheckedId)

    this.apiScService
      .postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.SEND_BUYING_MATERIAL_FINANCIAL_ACCOUNTANT, {
        lstBuyingMaterialIds,
      })
      .then((res) => {
        this.notifyService.showSuccess(res.message)
        this.notifyService.hideloading()
        this.ngOnInit()
      })
  }
}
