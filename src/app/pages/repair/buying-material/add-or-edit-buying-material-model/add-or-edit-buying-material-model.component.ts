import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import * as XLSX from 'xlsx'
import { environment } from '../../../../../environments/environment'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'
@Component({ templateUrl: './add-or-edit-buying-material-model.component.html' })
export class AddOrEditBuyingMaterialModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI PHIẾU YÊU CẦU MUA VẬT TƯ'
  dataObject: any = {}
  isEditItem = false
  uploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  lstWarehouseMaterial: any[] = []
  lstMaterialCategory: any[] = []
  lstSupplier: any[] = []
  lstEmployee: any[] = []
  lstUnit: any[] = []
  dicMaterialCategory: Record<string, any> = {}
  dicMaterialCategoryCode: Record<string, any> = {}
  dicUnit: Record<string, any> = {}

  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditBuyingMaterialModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifyService.showloading()
    await this.loadAllData()
    this.dataObject.lstBuyingMaterialDetails = []
    this.dataObject.lstBuyingMaterialImages = []
    if (this.data && this.data !== null) {
      await this.apiScService
        .postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.FIND_DETAIL, {
          id: this.data.id,
        })
        .then(async (rs: any) => {
          this.dataObject = rs
          for (let e of this.dataObject.lstBuyingMaterialDetails) {
            await this.onChangeMaterialCategory(e, true)
          }
        })

      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT PHIẾU YÊU CẦU MUA VẬT TƯ'
    } else {
      this.dataObject.createdAt = new Date()
    }

    this.notifyService.hideloading()
  }

  async loadAllData() {
    await Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.SUPPLIER.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.EMPLOYEE.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstWarehouseMaterial = rs[0]
      this.lstMaterialCategory = rs[1]
      this.lstSupplier = rs[2]
      this.lstEmployee = rs[3]
      this.lstUnit = rs[4]

      for (let mc of this.lstMaterialCategory) {
        this.dicMaterialCategory[mc.id] = mc
        this.dicMaterialCategoryCode[mc.code] = mc
      }
      for (let un of this.lstUnit) {
        this.dicUnit[un.code] = un
      }
    })
  }

  onSave() {
    // #region Kiểm tra điều kiện trước khi lưu
    for (let e of this.dataObject.lstBuyingMaterialDetails) {
      if (!e.materialCategoryId) {
        this.notifyService.showError(`Vui lòng chọn vật tư nhập kho!`)
        return
      }
      if (!e.quantity) {
        this.notifyService.showError(`Vui lòng nhập số lượng nhập kho!`)
        return
      }
      if (!e.conversionRate) {
        this.notifyService.showError(`Vui lòng nhập tỉ lệ quy đổi lớn hơn 0!`)
        return
      }
    }

    const arrayLength: number = this.dataObject.lstBuyingMaterialDetails.length
    for (let i = 0; i < arrayLength; i++) {
      const mcI = this.dataObject.lstBuyingMaterialDetails[i]
      for (let j = i + 1; j < arrayLength; j++) {
        const mcJ = this.dataObject.lstBuyingMaterialDetails[j]
        if (mcI.materialCategoryId == mcJ.materialCategoryId) {
          this.notifyService.showError(`Vật tư ${this.dicMaterialCategory[mcI.materialCategoryId].name} đã trùng!`)
          return
        }
      }
    }
    // #endregion

    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  previewImage: string | undefined = ''
  previewVisible = false

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  handleChangeFileList(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              arr.push({
                name: item?.name ? item?.name : item?.originFileObj?.name,
                url: item?.url || item?.response?.length > 0 ? item?.response[0] : '',
                uid: item.uid,
              })
            }
          }
          this.dataObject.lstBuyingMaterialImages = arr
        }
        break
      case 'error':
        break
      case 'removed':
        {
          this.dataObject.lstBuyingMaterialImages.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) this.dataObject.lstBuyingMaterialImages.splice(index, 1)
          })
        }
        break
    }
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  async onAddBuyingMaterialDetail() {
    this.dataObject.lstBuyingMaterialDetails.push({
      id: '',
      materialCategoryId: '',
      materialCategoryName: '',
      unitId: '',
      baseUnitId: '',
      conversionRate: 1,
      baseUnitName: '',
      quantity: 0,
      inboundQuantity: 0,
      price: 0,
      lstUnit: [],
      index: this.dataObject.lstBuyingMaterialDetails.length,
    })
  }

  async onChangeMaterialCategory(item: any, isEdit: boolean = false) {
    if (!isEdit) {
      item.unitId = null
      item.baseUnitId = null
      item.conversionRate = null
      item.baseUnitName = null
      item.quantity = null
      item.inboundQuantity = null
      item.price = null
      item.lstUnit = []
    }
    const mc = this.dicMaterialCategory[item.materialCategoryId]
    item.baseUnitId = mc.baseUnitId
    item.baseUnitName = mc.baseUnitName
    item.materialCategoryId = mc.id
    item.materialCategoryName = mc.name
    item.lstUnit = mc.lstMaterialCategoryUnits
  }

  onDelete(index: any) {
    this.dataObject.lstBuyingMaterialDetails.splice(index, 1)
  }

  onChangeSupplier(id: string) {
    const findSup = this.lstSupplier.find((e: any) => e.id == id)
    if (findSup) this.dataObject.supplierName = findSup.name
  }

  onChangeEmployee(id: string) {
    const findEmp = this.lstEmployee.find((e: any) => e.id == id)
    if (findEmp) this.dataObject.receivedByName = findEmp.fullName
  }

  onChangeUnit(item: any) {
    const find = item.lstUnit.find((e: any) => e.unitId == item.unitId)
    item.conversionRate = find.conversionRate
  }

  onDownloadTemplate() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách loại vật tư nhập kho')

    //#region Body Table
    const header = [
      'Mã loại vật tư * (materialCategoryCode)',
      'Đơn vị tính * (unitCode)',
      'Tỉ lệ quy đổi (conversionRate)',
      'Số lượng * (quantity)',
      'Đơn giá (price)',
    ]
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 40
          break
        case 2:
        case 3:
        case 4:
        case 5:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    // #region Danh sách loại vật tư
    const worksheet2 = workbook.addWorksheet('Danh sách loại vật tư')
    const header2 = ['Mã loại vật tư', 'Tên loại vật tư', 'Đơn vị cơ sở', 'Đơn vị tính', 'Tỉ lệ quy đổi']
    const headerRow2 = worksheet2.addRow(header2)
    for (let e of this.lstMaterialCategory) {
      const rowData = [e.code || '', e.name || '', e.baseUnitName || '', e.unitName || '', e.conversionRate || '']
      worksheet2.addRow(rowData)
    }
    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })
    //#endregion

    // #region Danh sách đơn vị tính
    const worksheet3 = workbook.addWorksheet('Danh sách đơn vị tính')
    const header3 = ['Mã đơn vị tính', 'Tên đơn vị tính']
    const headerRow3 = worksheet3.addRow(header3)
    for (let e of this.lstUnit) {
      const rowData = [e.code || '', e.name || '']
      worksheet3.addRow(rowData)
    }
    // Cell Style : Fill and Border
    headerRow3.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet3.getColumn(colNumber).width = 30
          break
        default:
          worksheet3.getColumn(colNumber).width = 12
          break
      }
    })
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_LOAI_VAT_TU${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  clickImportExcel(event: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['materialCategoryCode', 'unitCode', 'conversionRate', 'quantity', 'price'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file-1')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.materialCategoryCode) {
          const mc = this.dicMaterialCategoryCode[row.materialCategoryCode]
          if (!mc) strErr += 'Dòng ' + idx + ' - Mã loại vật tư không tồn tại <br>'
          else {
            row.unitId = mc.unitId
            row.baseUnitId = mc.baseUnitId
            row.baseUnitName = mc.baseUnitName
            row.materialCategoryId = mc.id
            row.materialCategoryName = mc.name
          }
        }
        if (row.unitCode) {
          const un = this.dicUnit[row.unitCode]
          if (!un) strErr += 'Dòng ' + idx + ' - Đơn vị tính không tồn tại <br>'
          else {
            row.unitId = un.id
          }
        }

        if (row.conversionRate) {
          if (isNaN(row.conversionRate)) {
            strErr += 'Dòng ' + idx + ' - Tỉ lệ quy đổi phải là số <br>'
          } else if (+row.conversionRate < 0) {
            strErr += 'Dòng ' + idx + ' - Tỉ lệ quy đổi phải là số dương <br>'
          }
        }

        if (row.quantity) {
          if (isNaN(row.quantity)) {
            strErr += 'Dòng ' + idx + ' - Số lượng phải là số <br>'
          } else if (+row.quantity < 0) {
            strErr += 'Dòng ' + idx + ' - Số lượng phải là số dương <br>'
          }
        }

        if (row.price) {
          if (isNaN(row.price)) {
            strErr += 'Dòng ' + idx + ' - Đơn giá phải là số <br>'
          } else if (+row.price < 0) {
            strErr += 'Dòng ' + idx + ' - Đơn giá phải là số dương <br>'
          }
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (let row of jsonData) {
        this.dataObject.lstBuyingMaterialDetails.push({
          id: '',
          materialCategoryId: row.materialCategoryId,
          materialCategoryName: row.materialCategoryName,
          unitId: row.unitId,
          baseUnitId: row.baseUnitId,
          conversionRate: row?.conversionRate || 0,
          baseUnitName: row.baseUnitName,
          quantity: row?.quantity || 0,
          inboundQuantity: (row?.quantity || 0) * row?.conversionRate || 0,
          price: row?.price || 0,
          index: this.dataObject.lstBuyingMaterialDetails.length,
        })
      }
    }
  }
}
