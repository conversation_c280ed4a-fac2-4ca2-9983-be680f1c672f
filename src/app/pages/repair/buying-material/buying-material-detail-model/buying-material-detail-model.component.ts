import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { NzImageService } from 'ng-zorro-antd/image'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './buying-material-detail-model.component.html' })
export class BuyingMaterialDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU MUA VẬT TƯ'
  dataObject: any = {}
  effect = 'scrollx'
  editBuyingMaterialSelected: any = null

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private dialogRef: MatDialogRef<BuyingMaterialDetailModelComponent>,
    private nzImageService: NzImageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    this.dataObject.lstBuyingMaterialImages = []
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.FIND_DETAIL, {
        id: this.data.id,
      })
      .then((rs: any) => {
        this.dataObject = rs
        this.dataObject.lstBuyingMaterialImages = this.dataObject.lstBuyingMaterialImages?.map((c: any) => {
          return {
            uid: c.id,
            name: c?.name ?? 'hinh anh',
            status: 'done',
            url: c?.url,
            src: c?.url,
          }
        })
        this.notifySerivce.hideloading()
      })
  }

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  onEdit(value: boolean, item: any): void {
    if (value) this.editBuyingMaterialSelected = { ...item }
  }
  async onSaveEditWaitBuyingMaterial() {
    this.notifySerivce.showloading()
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_BUYING_MATERIAL.UPDATE_WAITING_BUYING_DATE_EXPIRE, {
        id: this.editBuyingMaterialSelected.id,
        waitingBuyingDateExpire: this.editBuyingMaterialSelected.waitingBuyingDateExpire,
      })
      .then((res) => {
        this.notifySerivce.hideloading()
        this.editBuyingMaterialSelected = null
        this.ngOnInit()
        this.notifySerivce.showSuccess(res.message)
      })
      .catch((err) => {
        this.notifySerivce.hideloading()
      })
  }
  onChangeWaitingBuyingDateExpire(data: Date) {
    this.editBuyingMaterialSelected.waitingBuyingDateExpire = data
  }
  onCloseModalEditWaitBuyingMaterial() {
    this.editBuyingMaterialSelected = null
  }
}
