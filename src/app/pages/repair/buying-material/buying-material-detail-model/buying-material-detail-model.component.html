<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<div nz-row class="mt-4">
  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">M<PERSON> phiếu mua vật tư</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.code }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.warehouseMaterialName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Nh<PERSON> cung cấp</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.supplierName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người nhận</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.receivedByName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdAt | date : 'HH:mm dd/MM/YYYY' }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdByName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedDate">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedDate | date : 'HH:mm dd/MM/YYYY' }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedByName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedByName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24" *ngIf="dataObject.description">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Mô tả </nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.description }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24">
    <label class="text-left">Hình ảnh phiếu mua vật tư</label>
    <nz-row nzGutter="4">
      <ng-container nz-col nzSpan="2" *ngFor="let item of dataObject?.lstBuyingMaterialImages; let i = index">
        <img nz-image width="100px" height="100px" nzSrc="{{ item.url }}" alt="" loading="lazy" />
      </ng-container>
    </nz-row>
  </div>

  <div nz-col nzSpan="24">
    <div nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstBuyingMaterialDetails" [nzShowPagination]="false" #ajaxTable
        [nzFrontPagination]="false" nzTemplateMode nzBordered [nzScroll]="{ x: '1200px' }">
        <thead>
          <tr>
            <th class="text-center" nzWidth="180px" nzLeft>Mã vật tư</th>
            <th class="text-center" nzWidth="180px">Tên vật tư</th>
            <th class="text-center" nzWidth="180px">Đơn vị tính</th>
            <th class="text-center" nzWidth="180px">Số lượng</th>
            <th class="text-center" nzWidth="180px">Tỉ lệ quy đổi</th>
            <th class="text-center" nzWidth="180px">Đơn vị cơ sở</th>
            <th class="text-center" nzWidth="180px">Số lượng mua</th>
            <th class="text-center" nzWidth="180px">Đơn giá</th>
            <th class="text-center" nzWidth="200px">Ngày hoàn thành chờ mua</th>
            <th class="text-center" nzWidth="180px" nzRight>Thành tiền</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataObject.lstBuyingMaterialDetails; let i = index">
            <td class="text-center" nzLeft>{{ item.materialCategoryCode }}</td>
            <td class="text-center">{{ item.materialCategoryName }}</td>
            <td class="text-center">{{ item.unitName }}</td>
            <td class="text-center">{{ item.quantity | number }}</td>
            <td class="text-center">{{ item.conversionRate | number }}</td>
            <td class="text-center">{{ item.baseUnitName }}</td>
            <td class="text-center">{{ item.buyingMaterialQuantity | number }}</td>
            <td class="text-center">{{ item.price | number }}</td>
            <td class="text-center" style="min-width: 200px">
              <!-- người dùng chọn chỉnh sửa ngày hoàn thành chờ mua của vật tư nào -->
              <button [(nzPopoverVisible)]="!!editBuyingMaterialSelected"
                (nzPopoverVisibleChange)="onEdit($event, item)" nz-button nzType="text" nz-popover
                [nzPopoverContent]="contentTemplate" nzPopoverTrigger="click">
                <span *ngIf="item.waitingBuyingDateExpire">{{
                  item.waitingBuyingDateExpire
                  | date
                  : 'dd/MM/yyyy' ||
                  'Chọn
                  ngày'
                  }}</span>
                <span *ngIf="!item.waitingBuyingDateExpire">Chọn ngày</span>
                <span nz-icon nzType="edit" nzTheme="outline"></span>
              </button>
            </td>

            <td class="text-center" nzRight>{{ +item.buyingMaterialQuantity * +item.price | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </div>
</div>

<ng-template #contentTemplate>
  <div style="width: 250px">
    <h5>Chỉnh sửa ngày hoàn thành chờ mua</h5>
    <nz-date-picker (ngModelChange)="onChangeWaitingBuyingDateExpire($event)" [nzAllowClear]="false"
      placeholder="Chọn ngày" [(ngModel)]="editBuyingMaterialSelected.waitingBuyingDateExpire" nzFormat="dd/MM/yyyy"
      [ngModelOptions]="{ standalone: true }"></nz-date-picker>

    <div
      style="width: 100%; display: flex; justify-content: flex-end; align-items: center; margin-top: 10px; gap: 10px">
      <button nz-button nzType="default" nzDanger (click)="onCloseModalEditWaitBuyingMaterial()">Huỷ</button>
      <button nz-button nzType="primary" (click)="onSaveEditWaitBuyingMaterial()">Lưu</button>
    </div>
  </div>
</ng-template>