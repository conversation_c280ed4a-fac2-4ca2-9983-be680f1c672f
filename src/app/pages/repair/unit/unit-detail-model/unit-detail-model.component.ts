import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './unit-detail-model.component.html' })
export class UnitDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT ĐƠN VỊ TÍNH'
  dataObject: any = {}

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private dialogRef: MatDialogRef<UnitDetailModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    this.dataObject = await this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.FIND_DETAIL, {
      id: this.data.id,
    })
    this.notifySerivce.hideloading()
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
