import { Component, OnInit } from '@angular/core'
import { NotifyService } from '../../../services/notify.service'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditTransferWarehouseModelComponent } from './add-or-edit-transfer-warehouse-model/add-or-edit-transfer-warehouse-model.component'
import { CoreService } from '../../../services/core.service'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { TransferWarehouseDetailModelComponent } from './transfer-warehouse-detail-model/transfer-warehouse-detail-model.component'
import * as fs from 'file-saver'
import { Workbook } from 'exceljs'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { isInteger } from 'mathjs'
import { ApiScService } from 'src/app/services/apiSc.service'
import { enumData } from 'src/app/core'

@Component({ templateUrl: './transfer-warehouse.component.html' })
export class TransferWarehouseComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  enumDataNew = enumData.RepairTransferWarehouseStatus.NEW.code
  enumDataSent = enumData.RepairTransferWarehouseStatus.SENT.code
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  lstTransferWarehouseStatus: any[] = []
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstWarehouseFromCategory: any[] = []
  lstWarehouseToCategory: any[] = []
  lstMaterialCategory: any[] = []

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.lstTransferWarehouseStatus = this.coreService.convertObjToArray(this.enumData.RepairTransferWarehouseStatus)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, { noFilterUser: true }),
      this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {}),
    ]).then(async (rs) => {
      this.lstWarehouseFromCategory = rs[0]
      this.lstWarehouseToCategory = rs[1]
      this.lstMaterialCategory = rs[2]
    })
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditTransferWarehouseModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditTransferWarehouseModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(TransferWarehouseDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách phiếu chuyển kho')

    //#region Body Table
    const header = [
      'Mã phiếu * (code)',
      'Mã kho đi * (fromWarehouseMaterialCode)',
      'Mã kho đến * (toWarehouseMaterialCode)',
      'Mã vật tư * (materialCategoryCode)',
      'Số lượng * (quantity)',
      'Mô tả (description)',
    ]
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 4:
        case 5:
        case 6:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
        case 3:
          worksheet.getColumn(colNumber).width = 35
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    // #region Danh sách vật tư
    const worksheet1 = workbook.addWorksheet('Danh sách vật tư')

    //#region Body Table
    const header1 = ['Mã vật tư * (code)', 'Tên vật tư * (name)']
    const headerRow1 = worksheet1.addRow(header1)

    // Add Data and Conditional Formatting
    for (let data of this.lstMaterialCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet1.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow1.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet1.getColumn(colNumber).width = 30
          break
        default:
          worksheet1.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    const worksheet2 = workbook.addWorksheet('Danh sách kho')

    //#region Body Table
    const header2 = ['Mã kho * (code)', 'Tên kho * (name)']
    const headerRow2 = worksheet2.addRow(header2)

    // Add Data and Conditional Formatting
    for (let data of this.lstWarehouseFromCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet2.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_PHEU_CHUYEN_KHO${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  lstHeaderRequire: string[] = [
    'code',
    'fromWarehouseMaterialCode',
    'toWarehouseMaterialCode',
    'materialCategoryCode',
    'quantity',
  ]

  dicHeader: Record<string, any> = {
    code: 'Mã phiếu',
    fromWarehouseMaterialCode: 'Mã kho đi',
    toWarehouseMaterialCode: 'Mã kho đến',
    materialCategoryCode: 'Mã vật tư',
    quantity: 'Số lượng',
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'code',
          'fromWarehouseMaterialCode',
          'toWarehouseMaterialCode',
          'materialCategoryCode',
          'quantity',
          'description',
        ],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 1
        for (let hd of this.lstHeaderRequire) {
          if (!row[hd]) strErr += 'Dòng ' + idx + ` - ${this.dicHeader[hd]} không được để trống <br>`
        }

        if (row.quantity && (isNaN(row.quantity) || +row.quantity < 0 || !isInteger(row.quantity))) {
          strErr += 'Dòng ' + idx + ` - Số lượng chuyển phải là số nguyên lớn hơn 0 <br>`
        }

        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }
      this.apiScService
        .postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.CREATE_DATA_BY_EXCEL, jsonData)
        .then((result) => {
          this.notifyService.hideloading()
          if (result) {
            this.notifyService.showSuccess('Thêm file excel thành công')
            this.searchData()
          }
        })
    }
  }

  updateCancel(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.UPDATE_CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApprove(data: any) {
    this.notifyService.showloading()
    this.apiScService
      .postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.UPDATE_APPROVE, { id: data.id })
      .then((res) => {
        this.notifyService.showSuccess(res.message)
        this.notifyService.hideloading()
        this.searchData()
      })
  }

  updateSent(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.UPDATE_SENT, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách PXK')
        //#region Body Table
        const header = ['Mã', 'Kho đi', 'Kho đến', 'Ngày tạo', 'Ngày duyệt', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.fromWarehouseMaterialName || '',
            data.toWarehouseMaterialName || '',
            data.createdAt ? moment(data.createdAt).format('DD/MM/YYYY') : '',
            data.approvedDate ? moment(data.approvedDate).format('DD/MM/YYYY') : '',
            data.statusName || '',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_PHIEU_CHUYEN_KHO_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
