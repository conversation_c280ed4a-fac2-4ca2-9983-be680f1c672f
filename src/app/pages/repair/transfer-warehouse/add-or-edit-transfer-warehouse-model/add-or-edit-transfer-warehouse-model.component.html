<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzGutter="8">
    <nz-col nzSpan="6" *ngIf="isEditItem">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã</nz-form-label>
        <nz-form-control nzSpan="24"
          nzErrorTip="Vui lòng nhập mã từ 1 đến 36 kí tự và không kèm kí tự đặc biệt khoảng cách">
          <input nz-input [disabled]="isEditItem" placeholder="Nhập mã 1-36 kí tự" [(ngModel)]="dataObject.code"
            name="code" pattern="^([a-zA-Z0-9]){1,36}$" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Kho đi</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kho đi">
          <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn kho đi"
            [(ngModel)]="dataObject.fromMaterialWarehouseId" name="fromMaterialWarehouseId"
            (ngModelChange)="onChangeWarehouseMaterialCategory($event)" required>
            <nz-option *ngFor="let item of lstWarehouseFromMaterial" [nzLabel]="item.name" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Kho đến</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kho đến">
          <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn kho đến"
            [(ngModel)]="dataObject.toMaterialWarehouseId" name="toMaterialWarehouseId" required>
            <nz-option *ngFor="let item of lstWarehouseToMaterial" [nzLabel]="item.name" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày tạo phiếu</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-date-picker [disabled]="true" nzFormat="dd/MM/yyyy" [(ngModel)]="dataObject.createdAt" name="createdAt">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="24" class="text-right">
      <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onAddTransferWarehouseDetail()">
        <span nz-icon nzType="plus"></span>Thêm
      </button>

      <div nz-row class="mt-2">
        <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstTransferWarehouseDetails" [nzShowPagination]="false"
          class="mb-3" #ajaxTable [nzFrontPagination]="false" [nzScroll]="{ x: '1200px', y: null }" nzTemplateMode
          nzBordered>
          <thead>
            <tr>
              <th class="text-center" nzWidth="180px">Tên loại vật tư</th>
              <th class="text-center">Đơn vị tính</th>
              <th class="text-center">Số lượng chuyển (Theo đơn vị quy đổi)</th>
              <th class="text-center">Tỉ lệ quy đổi</th>
              <th class="text-center">Đơn vị cơ sở</th>
              <th class="text-center">Số lượng</th>
              <th class="text-center">Đơn giá</th>
              <th class="text-center">Thành tiền</th>
              <th class="text-center">Tồn kho</th>
              <th class="text-center" nzRight nzWidth="80px">Tác vụ</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstTransferWarehouseDetails; let i = index">
              <td>
                <nz-select nzShowSearch [(ngModel)]="item.materialCategoryId" [ngModelOptions]="{ standalone: true }"
                  nzPlaceHolder="Chọn tên loại vật tư" (ngModelChange)="onChangeMaterialCategory(item)">
                  <nz-option *ngFor="let mc of lstMaterialCategory" [nzLabel]="mc.name"
                    [nzValue]="mc.materialCategoryId">
                  </nz-option>
                </nz-select>
              </td>
              <td>
                <nz-select nzShowSearch [(ngModel)]="item.unitId" [ngModelOptions]="{ standalone: true }"
                  nzPlaceHolder="Chọn đơn vị tính" (ngModelChange)="onChangeUnit(item)">
                  <nz-option *ngFor="let mc of item.lstUnit" [nzLabel]="mc.unitName" [nzValue]="mc.unitId">
                  </nz-option>
                </nz-select>
              </td>
              <td class="text-center">
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.quantity"
                  [ngModelOptions]="{ standalone: true }" [nzMin]="1" style="width: 100%;" [nzStep]="1"
                  [nzPlaceHolder]="'Nhập số lượng'" required></nz-input-number>
              </td>
              <td>
                <span>{{item.conversionRate}}</span>
              </td>
              <td class="text-center">{{ item.baseUnitName }}</td>
              <td class="text-center">{{ (+item.quantity * +item.conversionRate) | number }}</td>
              <td class="text-center">{{ item.price | number}}</td>
              <td class="text-center">{{ (+item.quantity * +item.conversionRate * +item.price) | number}} </td>
              <td class="text-center">{{ item.inventory | number}}</td>
              <td class="text-center" nzRight>
                <button nz-button nzType="primary" nzDanger nzShape="circle" nz-tooltip nzTooltipTitle="Xóa"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?" (nzOnConfirm)="onDelete(i)" class="mr-2">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </nz-col>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-upload nz-row class="avatar-uploader" [nzAction]="uploadUrl" nzListType="picture-card"
            [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.lstTransferWarehouseImages"
            [nzShowButton]="dataObject.lstTransferWarehouseImages?.length <= 10"
            (nzChange)="handleChangeFileList($event)" [nzPreview]="handlePreview" [nzAccept]="'.png, .jpg, .jpeg'">
            <i nz-icon nzType="plus"></i>
            <div class="ant-upload-text">Upload</div>
          </nz-upload>
          <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
            (nzOnCancel)="previewVisible = false">
            <ng-template #modalContent>
              <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
            </ng-template>
          </nz-modal>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid  || dataObject?.lstTransferWarehouseDetails == 0" nzShape="round"
        nzType="primary" class="mr-3" (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>