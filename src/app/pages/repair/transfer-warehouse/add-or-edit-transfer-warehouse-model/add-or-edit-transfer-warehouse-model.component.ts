import { Component, OnInit, Optional, Inject } from '@angular/core'
import { NotifyService } from '../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { environment } from '../../../../../environments/environment'
import { CoreService } from '../../../../services/core.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './add-or-edit-transfer-warehouse-model.component.html' })
export class AddOrEditTransferWarehouseModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI PHIẾU CHUYỂN KHO'
  dataObject: any = {}
  isEditItem = false
  uploadUrl = `${environment.backEnd}/uploadFiles/upload_single`
  lstWarehouseFromMaterial: any[] = []
  lstWarehouseToMaterial: any[] = []
  lstMaterialCategory: any[] = []
  dicMaterialCategory: Record<string, any> = {}

  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private coreService: CoreService,
    private dialogRef: MatDialogRef<AddOrEditTransferWarehouseModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    await this.loadAllData()
    this.dataObject.lstTransferWarehouseDetails = []
    this.dataObject.lstTransferWarehouseImages = []
    if (this.data && this.data !== null) {
      await this.onChangeWarehouseMaterialCategory(this.data.warehouseMaterialId)
      await this.apiScService
        .postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.FIND_DETAIL, {
          id: this.data.id,
        })
        .then(async (rs) => {
          this.dataObject = rs
          for (let e of this.dataObject.lstTransferWarehouseDetails) {
            await this.onChangeMaterialCategory(e, true)
          }
        })

      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT PHIẾU CHUYỂN KHO'
    } else {
      this.dataObject.createdAt = new Date()
    }
  }

  async loadAllData() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, { noFilterUser: true }),
    ]).then(async (rs) => {
      this.lstWarehouseFromMaterial = rs[0]
      this.lstWarehouseToMaterial = rs[1]
    })
  }

  async onChangeWarehouseMaterialCategory(id: string) {
    this.lstMaterialCategory = []
    this.dataObject.lstTransferWarehouseDetails = []
    this.lstMaterialCategory = await this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.LOAD_DATA, {
      warehouseMaterialId: id,
    })
    for (let mc of this.lstMaterialCategory) this.dicMaterialCategory[mc.materialCategoryId] = mc
  }

  onSave() {
    // #region Kiểm tra điều kiện trước khi lưu
    if (this.dataObject.fromMaterialWarehouseId == this.dataObject.toMaterialWarehouseId) {
      this.notifyService.showError(`Vui lòng chọn kho đi khác kho đến!`)
      return
    }
    for (let e of this.dataObject.lstTransferWarehouseDetails) {
      if (!e.materialCategoryId) {
        this.notifyService.showError(`Vui lòng chọn vật tư xuất kho!`)
        return
      }
      if (!e.quantity) {
        this.notifyService.showError(`Vui lòng nhập số lượng xuất kho!`)
        return
      }

      if (+e.quantity > +e.inventory) {
        this.notifyService.showError(
          `Số lượng xuất kho của vật tư [ ${this.dicMaterialCategory[e.materialCategoryId].name} ] lớn hơn số lượng tồn kho!`
        )
        return
      }
    }

    const arrayLength: number = this.dataObject.lstTransferWarehouseDetails.length
    for (let i = 0; i < arrayLength; i++) {
      const mcI = this.dataObject.lstTransferWarehouseDetails[i]
      for (let j = i + 1; j < arrayLength; j++) {
        const mcJ = this.dataObject.lstTransferWarehouseDetails[j]
        if (mcI.materialCategoryId == mcJ.materialCategoryId) {
          this.notifyService.showError(`Vật tư ${this.dicMaterialCategory[mcI.materialCategoryId].name} đã trùng!`)
          return
        }
      }
    }
    // #endregion

    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  previewImage: string | undefined = ''
  previewVisible = false

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  handleChangeFileList(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              arr.push({
                name: item?.name ? item?.name : item?.originFileObj?.name,
                url: item?.url || item?.response?.length > 0 ? item?.response[0] : '',
                uid: item.uid,
              })
            }
          }
          this.dataObject.lstTransferWarehouseImages = arr
        }
        break
      case 'error':
        break
      case 'removed':
        {
          this.dataObject.lstTransferWarehouseImages.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) this.dataObject.lstTransferWarehouseImages.splice(index, 1)
          })
        }
        break
    }
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  async onAddTransferWarehouseDetail() {
    this.dataObject.lstTransferWarehouseDetails.push({
      id: '',
      materialCategoryId: '',
      materialCategoryName: '',
      unitId: '',
      baseUnitId: '',
      conversionRate: 1,
      baseUnitName: '',
      quantity: 0,
      transferQuantity: 0,
      price: 0,
      inventory: 0,
      lstUnit: [],
      index: this.dataObject.lstTransferWarehouseDetails.length,
    })
  }

  async onChangeMaterialCategory(item: any, isEdit: boolean = false) {
    if (!isEdit) {
      item.unitId = null
      item.baseUnitId = null
      item.conversionRate = null
      item.baseUnitName = null
      item.quantity = null
      item.transferQuantity = null
      item.price = null
      item.inventory = null
      item.lstUnit = []
    }

    const mc = this.dicMaterialCategory[item.materialCategoryId]
    item.baseUnitName = mc.baseUnitName
    item.materialCategoryName = mc.name
    item.inventory = +mc.quantity - +mc.quantityLock
    item.price = mc.price
    item.lstUnit = mc.lstMaterialCategoryUnits
  }

  onChangeUnit(item: any) {
    const find = item.lstUnit.find((e: any) => e.unitId == item.unitId)
    item.conversionRate = find.conversionRate
  }

  onDelete(index: any) {
    this.dataObject.lstTransferWarehouseDetails.splice(index, 1)
  }
}
