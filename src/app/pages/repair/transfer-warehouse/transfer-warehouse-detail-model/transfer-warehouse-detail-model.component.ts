import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { NzImageService } from 'ng-zorro-antd/image'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './transfer-warehouse-detail-model.component.html' })
export class TransferWarehouseDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU CHUYỂN KHO'
  dataObject: any = {}
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private nzImageService: NzImageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    this.dataObject.lstTransferWarehouseImages = []
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_TRANSFER_WAREHOUSE.FIND_DETAIL, {
        id: this.data.id,
      })
      .then(async (rs: any) => {
        this.dataObject = rs
      })
    this.notifySerivce.hideloading()
  }

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }
}
