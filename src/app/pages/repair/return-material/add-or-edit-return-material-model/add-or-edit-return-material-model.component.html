<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzGutter="8">
    <nz-col nzSpan="6" *ngIf="isEditItem">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã</nz-form-label>
        <nz-form-control nzSpan="24"
          nzErrorTip="Vui lòng nhập mã từ 1 đến 36 kí tự và không kèm kí tự đặc biệt khoảng cách">
          <input nz-input [disabled]="isEditItem" placeholder="Nhập mã 1-36 kí tự" [(ngModel)]="dataObject.code"
            name="code" pattern="^([a-zA-Z0-9]){1,36}$" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Kho</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn kho">
          <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn kho" [(ngModel)]="dataObject.warehouseMaterialId"
            name="warehouseMaterialId" required>
            <nz-option *ngFor="let item of lstWarehouseMaterial" [nzLabel]="item.name" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Người trả</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người trả">
          <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn người trả" [(ngModel)]="dataObject.employeeId"
            name="employeeId" required (ngModelChange)="onChangeEmployee($event)">
            <nz-option *ngFor="let item of lstEmployee" [nzLabel]=" item.fullName" [nzValue]="item.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày tạo phiếu</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-date-picker [disabled]="true" nzFormat="dd/MM/yyyy" [(ngModel)]="dataObject.createdAt" name="createdAt">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>


    <nz-col nzSpan="24">
      <nz-row class="mt-2">
        <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onAddOutboundDetail()">
          <span nz-icon nzType="plus"></span>Thêm
        </button>
        <button class="mr-2" nz-button nzType="primary" nzGhost (click)="onDownloadTemplate()" nzShape="round">
          <i nz-icon nzType="download" nzTheme="outline"></i>Tải Template Mẫu
        </button>

        <input style="display: none;" class="hidden" type="file" id="file-1" (change)="clickImportExcel($event)"
          placeholder="Upload file"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        <label nz-button for="file-1" class="ant-btn lable-custom-file-custom">
          <span nz-icon nzType="upload"></span> Nhập Excel
        </label>
      </nz-row>

      <div nz-row class="mt-3">
        <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstReturnMaterialDetail" [nzShowPagination]="false"
          class="mb-3" #ajaxTable [nzFrontPagination]="false" [nzScroll]="{ x: '1200px', y: null }" nzTemplateMode
          nzBordered>
          <thead>
            <tr>
              <th class="text-center" nzRight nzWidth="80px" nzLeft>Tác vụ</th>
              <th class="text-center" nzWidth="180px">Tên loại vật tư</th>
              <th class="text-center" nzWidth="180px">Đơn vị tính</th>
              <th class="text-center" nzWidth="180px">Số lượng</th>
              <th class="text-center" nzWidth="180px">Tỉ lệ quy đổi</th>
              <th class="text-center" nzWidth="180px">Đơn vị cơ sở</th>
              <th class="text-center" nzWidth="180px">Số lượng trả</th>
              <th class="text-center" nzWidth="180px">Đơn giá</th>
              <th class="text-center" nzWidth="180px">Thành tiền</th>
              <th class="text-center" nzWidth="180px" nzRight>Tồn kho</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstReturnMaterialDetail; let i = index">
              <td class="text-center" nzLeft>
                <button nz-button nzType="primary" nzDanger nzShape="circle" nz-tooltip nzTooltipTitle="Xóa"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?" (nzOnConfirm)="onDelete(i)" class="mr-2">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </td>
              <td>
                <nz-select nzShowSearch [(ngModel)]="item.materialCategoryId" [ngModelOptions]="{ standalone: true }"
                  nzPlaceHolder="Chọn tên vật tư" (ngModelChange)="onChangeMaterialCategory(item)">
                  <nz-option *ngFor="let mc of lstMaterialCategory" [nzLabel]="mc.name"
                    [nzValue]="mc.materialCategoryId">
                  </nz-option>
                </nz-select>
              </td>
              <td>
                <nz-select nzShowSearch [(ngModel)]="item.unitId" [ngModelOptions]="{ standalone: true }"
                  nzPlaceHolder="Chọn đơn vị tính" (ngModelChange)="onChangeUnit(item)">
                  <nz-option *ngFor="let mc of lstUnit" [nzLabel]="mc.name" [nzValue]="mc.id">
                  </nz-option>
                </nz-select>
              </td>
              <td class="text-center">
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.quantity"
                  [ngModelOptions]="{ standalone: true }" [nzMin]="1" style="width: 100%;" [nzStep]="1"
                  [nzPlaceHolder]="'Nhập số lượng'" required></nz-input-number>
              </td>
              <td>
                <nz-input-number [nzFormatter]="formatter" [nzParser]="parser" [(ngModel)]="item.conversionRate"
                  [ngModelOptions]="{ standalone: true }" style="width: 100%;" [nzMin]=0 [nzStep]="1"
                  [nzPlaceHolder]="'Nhập tỉ lệ quy đổi'" required></nz-input-number>
              </td>
              <td class="text-center">{{ item.baseUnitName }}</td>
              <td class="text-center">{{ (+item.quantity * +item.conversionRate) | number }}</td>
              <td class="text-center">{{ item.price | number}}</td>
              <td class="text-center">{{( +item.quantity * +item.conversionRate * +item.price) | number
                }}</td>
              <td class="text-center" nzRight>{{ item.inventory | number}}</td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </nz-col>

    <div nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-upload nz-row class="avatar-uploader" [nzAction]="uploadUrl" nzListType="picture-card"
            [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.lstReturnMaterialImages"
            [nzShowButton]="dataObject.lstReturnMaterialImages?.length <= 10" (nzChange)="handleChangeFileList($event)"
            [nzPreview]="handlePreview" [nzAccept]="'.png, .jpg, .jpeg'">
            <i nz-icon nzType="plus"></i>
            <div class="ant-upload-text">Upload</div>
          </nz-upload>
          <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
            (nzOnCancel)="previewVisible = false">
            <ng-template #modalContent>
              <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
            </ng-template>
          </nz-modal>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid  || dataObject?.lstReturnMaterialDetail == 0" nzShape="round"
        nzType="primary" class="mr-3" (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>