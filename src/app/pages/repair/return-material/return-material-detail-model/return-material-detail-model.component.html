<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<div nz-row class="mt-4">
  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON> phi<PERSON>u tr<PERSON> vật tư</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.code }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.warehouseMaterialName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON><PERSON><PERSON> tr<PERSON></nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.employeeName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdAt | date: 'HH:mm dd/MM/YYYY'}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdByName}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedDate">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedDate | date: 'HH:mm dd/MM/YYYY'}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedByName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedByName}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedByName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedByName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.description">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Mô tả
      </nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.description }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24"
    *ngIf="dataObject?.lstReturnMaterialImages && dataObject?.lstReturnMaterialImages?.length > 5">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu trả vật tư
      </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex;">
        <nz-carousel nz-row nzGutter="4" [nzEffect]="effect" class="carousel" [nzAutoPlay]="true">
          <div nz-col nzSpan="2" nz-carousel-content
            *ngFor="let item of dataObject?.lstReturnMaterialImages; let i = index">
            <img nz-image width="100px" height="100px" nzSrc="{{item.url}}" alt="" />
          </div>
        </nz-carousel>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24"
    *ngIf="dataObject?.lstReturnMaterialImages && dataObject?.lstReturnMaterialImages?.length <= 5 && dataObject?.lstReturnMaterialImages?.length > 0">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu trả vật tư
      </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex;">
        <nz-row nzGutter="4">
          <ng-container nz-col nzSpan="2" *ngFor="let item of dataObject?.lstReturnMaterialImages; let i = index">
            <img nz-image width="100px" height="100px" nzSrc="{{item.url}}" alt="" />
          </ng-container>
        </nz-row>
      </nz-form-control>
    </nz-form-item>
  </div>



  <div nz-col nzSpan="24">
    <div nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstReturnMaterialDetail" [nzShowPagination]="false" #ajaxTable
        [nzFrontPagination]="false" nzTemplateMode nzBordered>
        <thead>
          <tr>
            <th class="text-center" nzWidth="180px" nzLeft>Mã vật tư</th>
            <th class="text-center" nzWidth="180px">Tên vật tư</th>
            <th class="text-center" nzWidth="180px">Đơn vị tính</th>
            <th class="text-center" nzWidth="180px">Số lượng trả (Theo đơn vị quy đổi)</th>
            <th class="text-center" nzWidth="180px">Tỉ lệ quy đổi</th>
            <th class="text-center" nzWidth="180px">Đơn vị cơ sở</th>
            <th class="text-center" nzWidth="180px">Số lượng</th>
            <th class="text-center" nzWidth="180px">Đơn giá</th>
            <th class="text-center" nzWidth="180px">Thành tiền</th>
            <th class="text-center" nzWidth="180px" nzRight>Tồn kho</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataObject.lstReturnMaterialDetail; let i = index">
            <td class="text-center" nzLeft>{{ item.materialCategoryCode }}</td>
            <td class="text-center">{{ item.materialCategoryName }}</td>
            <td class="text-center">{{ item.unitName }}</td>
            <td class="text-center">{{ item.quantity | number }}</td>
            <td class="text-center">{{ item.conversionRate | number }}</td>
            <td class="text-center">{{ item.baseUnitName }}</td>
            <td class="text-center">{{ item.returnQuantity | number }}</td>
            <td class="text-center">{{ item.price | number }}</td>
            <td class="text-center">{{ (+item.returnQuantity * +item.price) | number }}</td>
            <td class="text-center" nzRight>{{ item.inventory | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </div>
</div>