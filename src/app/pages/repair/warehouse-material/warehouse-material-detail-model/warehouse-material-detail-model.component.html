<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<nz-tabset *ngIf="dataObject" class="mt-4">
  <nz-tab nzTitle="Thông tin chung">
    <div nz-row class="mt-4">
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mã kho</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.code }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tên kho</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.name }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Đ<PERSON>a chỉ</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.address }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8" *ngIf="dataObject.description">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mô tả
          </nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.description }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8" *ngIf="dataObject.employeeName">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Thủ kho</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.employeeName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8" *ngIf="dataObject.lstApartmentName?.length > 0">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Danh sách khu</nz-form-label>
          <nz-form-control>
            <b>{{ dataObject.lstApartmentName }}</b>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24">
        <div nz-row class="mt-3">
          <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstMaterialCategoryDetails" [nzShowPagination]="false"
            #ajaxTable [nzFrontPagination]="false" nzTemplateMode nzBordered>
            <thead>
              <tr>
                <th class="text-center">Mã vật tư</th>
                <th class="text-center">Tên vật tư</th>
                <th class="text-center">Đơn vị tính cơ sở</th>
                <th class="text-center">Tồn kho</th>
                <th class="text-center">Đơn giá</th>
                <th class="text-center">Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of dataObject.lstMaterialCategoryDetails; let i = index">

                <td class="text-center" nzWidth="80px">{{ item.materialCategoryName }}</td>
                <td class="text-center" nzWidth="80px">{{ item.materialCategoryCode }}</td>
                <td class="text-center" nzWidth="80px">{{ item.baseUnitName }}</td>
                <td class="text-center" nzWidth="80px">{{ item.quantity | number }}</td>
                <td class="text-center" nzWidth="80px">{{ item.price | number }}</td>
                <td class="text-center" nzWidth="80px">{{ (+item.quantity * +item.price) | number }}</td>
              </tr>
            </tbody>
          </nz-table>
        </div>
      </div>
    </div>
  </nz-tab>
  <nz-tab nzTitle="Lịch sử">
    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable1 [nzData]="listOfData" [(nzPageSize)]="pageSize"
        [nzLoading]="loading" [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th class="text-center" nzWidth="160px">Thời gian</th>
            <th class="text-center">Nội dung</th>
            <th class="text-center" nzWidth="160px">Người cập nhật</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let detail of ajaxTable1.data">
            <td class="text-center">{{ detail.createdAt | date: 'HH:mm dd/MM/YYYY'}}</td>
            <td class="text-center">{{ detail.description}}</td>
            <td class="text-center">{{ detail.createdByName}}</td>
          </tr>
        </tbody>
      </nz-table>
      <div nz-col nzSpan="24" class="text-right">
        <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
          (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
          nzShowSizeChanger>
        </nz-pagination>
        <ng-template #rangeTemplate let-range="range" let-total>
          Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng
        </ng-template>
      </div>
    </div>
  </nz-tab>
</nz-tabset>