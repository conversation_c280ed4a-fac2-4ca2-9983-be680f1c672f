import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './warehouse-material-detail-model.component.html' })
export class WarehouseMaterialDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT KHO VẬT TƯ'
  dataObject: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  listOfData: any = []
  total: number = 0
  loading: boolean = false
  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public notifySerivce: NotifyService,
    public apiScService: ApiScService,
    private dialogRef: MatDialogRef<WarehouseMaterialDetailModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.notifySerivce.showloading()
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.FIND_DETAIL, {
        id: this.data.id,
      })
      .then((rs: any) => {
        this.dataObject = rs
        this.searchData()
      })
    this.notifySerivce.hideloading()
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  async searchData(reset: boolean = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = { warehouseMaterialId: this.data.id }
    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService
      .postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.HISTORY_PAGINATION, dataSearch)
      .then((rs: any) => {
        if (rs) {
          this.loading = false
          this.total = rs[1]
          this.listOfData = rs[0]
        }
      })
  }
}
