import { Component, OnInit } from '@angular/core'
import { NotifyService } from '../../../services/notify.service'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditWarehouseMaterialModelComponent } from './add-or-edit-warehouse-material-model/add-or-edit-warehouse-material-model.component'
import { CoreService } from '../../../services/core.service'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { WarehouseMaterialDetailModelComponent } from './warehouse-material-detail-model/warehouse-material-detail-model.component'
import * as fs from 'file-saver'
import { Workbook } from 'exceljs'
import * as XLSX from 'xlsx'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './warehouse-material.component.html' })
export class WarehouseMaterialComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditWarehouseMaterialModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditWarehouseMaterialModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(WarehouseMaterialDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  isVisibleDelete = false
  dataDelete: any = {}
  onAddMaterialIntoWarehouse(data: any) {
    this.notifyService.showloading()
    this.dataDelete = { title: `Kho ${data.name}`, warehouseId: data.id }
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA_NOT_IN_WH, { id: data.id }).then((res) => {
      this.notifyService.hideloading()
      this.dataDelete.lstMaterial = res || []
      this.dataDelete.lstMaterialId = []
      this.isVisibleDelete = true
    })
  }

  handleOkDelete() {
    this.notifyService.showloading()
    if (!this.dataDelete.lstMaterialId?.length) {
      this.notifyService.showError('Vui lòng chọn vật tư cần thêm vào kho!')
      return
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.ADD_MATERIAL, this.dataDelete).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.handleDelete()
    })
  }

  handleDelete() {
    this.isVisibleDelete = false
    this.dataDelete = {}
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách kho')

    //#region Body Table
    const header = [
      'Tên kho (name) ',
      'Địa chỉ (address) ',
      'Nhân viên thủ kho * (employeeCode) ',
      'Danh sách mã khu * (apartmentCode) ',
      'Mô tả (description) ',
    ]

    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet.getColumn(colNumber).width = 30
          break
        case 4:
          worksheet.getColumn(colNumber).width = 40
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_KHO${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['name', 'address', 'employeeCode', 'apartmentCode', 'description'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.apartmentCode == null || (typeof row.apartmentCode === 'string' && row.apartmentCode.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã khu không được để trống  <br>'
        }
        if (row.name == null || (typeof row.name === 'string' && row.name.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tên kho không được để trống <br>'
        }
        if (row.employeeCode == null || (typeof row.employeeCode === 'string' && row.employeeCode.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã nhân viên không được để trống <br>'
        }
        if (row.address == null || (typeof row.address === 'string' && row.address.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Địa chỉ không được để trống <br>'
        }
        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.CREATE_DATA_BY_EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        if (result) {
          this.notifyService.showSuccess('Thêm file excel thành công')
          this.searchData()
        }
      })
    }
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách kho')
        //#region Body Table
        const header = ['Mã kho', 'Tên kho', 'Mô tả', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [data.code || '', data.name || '', data.description || '', data.isDeleted ? 'Ngưng hoạt động' : 'Đang hoạt động']

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_KHO_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
