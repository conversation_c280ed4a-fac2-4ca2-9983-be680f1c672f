<div class="text-center">
  <span class="text-title">{{ modelTitle }}</span>
</div>

<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
  <div nz-row class="mt-2" nzGutter="8">
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên (1-200 kí tự)!">
          <input nz-input placeholder="Nhập tên 1-200 kí tự" [(ngModel)]="dataObject.name" name="name" required
            pattern=".{1,200}" />
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Đ<PERSON><PERSON> chỉ</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập địa chỉ (1-200 kí tự)!">
          <input nz-input placeholder="Nhập địa chỉ 1-200 kí tự" [(ngModel)]="dataObject.address" name="address"
            required pattern=".{1,200}" />
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea rows="2" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
            name="description"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Nhân viên thủ kho</nz-form-label>
        <nz-form-control nzSpan="24">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.employeeId" name="employeeId"
            nzPlaceHolder="Chọn khu" (ngModelChange)="onChangeEmployee($event)" required>
            <nz-option *ngFor="let ap of lstEmployee" [nzLabel]="ap.fullName" [nzValue]="ap.id">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Khu</nz-form-label>
        <button nz-button nzShape="round" nzType="primary" class="ml-2 mr-2" (click)="onClick(true)">
          Chọn tất cả
        </button>
        <button nz-button nzShape="round" nzType="primary" class="mr-2" (click)="onClick(false)">
          Bỏ chọn
        </button>
        <nz-form-control nzSpan="24">
          <nz-select [nzMaxTagCount]="3" [nzMaxTagPlaceholder]="tagPlaceHolder" nzMode="multiple"
            nzPlaceHolder="Chọn kho" (ngModelChange)="onChangeListApartment($event)"
            [(ngModel)]="dataObject.lstApartmentId" name="lstApartmentId" required>
            <nz-option *ngFor="let ap of lstApartment" [nzLabel]="ap.name" [nzValue]="ap.id"></nz-option>
          </nz-select>
          <ng-template #tagPlaceHolder let-selectedList>và {{ selectedList.length }} khu</ng-template>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div nz-row>
    <div nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzShape="round" nzType="primary" class="mr-3"
        (click)="onSave()">
        <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
      </button>
    </div>
  </div>
</form>