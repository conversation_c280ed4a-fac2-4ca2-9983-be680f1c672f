import { Component, OnInit, Input, Optional, Inject } from '@angular/core'
import { NotifyService } from '../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({ templateUrl: './add-or-edit-warehouse-material-model.component.html' })
export class AddOrEditWarehouseMaterialModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'THÊM MỚI KHO VẬT TƯ'
  dataObject: any = {}
  isEditItem = false
  lstApartment: any[] = []
  lstEmployee: any[] = []
  dicApartment: any = {}
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditWarehouseMaterialModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    if (this.data && this.data !== null) {
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT KHO VẬT TƯ'
      await this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.FIND_DETAIL, { id: this.data.id }).then(async (rs) => {
        this.dataObject = rs
        if (this.dataObject.lstApartmentId?.length > 0) {
          this.onChangeListApartment(this.dataObject.lstApartmentId)
        }
      })
    }
    this.loadAllData()
  }

  onSave() {
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  async loadAllData() {
    await Promise.all([
      await this.apiNtssService.post(this.apiNtssService.APARTMENT.LOAD_DATA, {}),
      await this.apiNtssService.post(this.apiNtssService.EMPLOYEE.LOAD_DATA, {}),
    ]).then((rs: any) => {
      this.lstApartment = rs[0]
      this.lstEmployee = rs[1]
      for (let e of this.lstApartment) {
        this.dicApartment[e.id] = e
      }
    })
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  async onChangeListApartment(lstApartmentId: string[]) {
    this.dataObject.lstApartment = []
    for (let id of lstApartmentId) {
      const find = this.dicApartment[id]
      if (find) {
        this.dataObject.lstApartment.push({
          apartmentId: find.id,
          apartmentCode: find.code,
          apartmentName: find.name,
        })
      }
    }
  }

  onChangeEmployee(id: string) {
    this.dataObject.employeeCode = null
    this.dataObject.employeeName = null
    const ap = this.lstEmployee.find((e) => e.id == id)
    if (ap) {
      this.dataObject.employeeCode = ap.code
      this.dataObject.employeeName = ap.fullName
    }
  }

  async onClick(isSelectAll: boolean) {
    if (isSelectAll) this.dataObject.lstApartmentId = this.lstApartment.map((e: any) => e.id)
    else this.dataObject.lstApartmentId = []
    await this.onChangeListApartment(this.dataObject.lstApartmentId)
  }
}
