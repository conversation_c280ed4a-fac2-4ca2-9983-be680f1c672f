import { CommonModule } from '@angular/common'
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCarouselModule } from 'ng-zorro-antd/carousel'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzImageModule } from 'ng-zorro-antd/image'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { CURRENCY_MASK_CONFIG, CurrencyMaskConfig, CurrencyMaskModule } from 'ng2-currency-mask'
import { MaterialModule } from '../../app.module'
import { DirectivesModule } from '../../directive/directives.module'
import { AddOrEditBonusPointStandardComponent } from './bonus-point-standard/add-or-edit-bonus-point-standrad/add-or-edit-bonus-point-standrad.component'
import { BonusPointStandardDetailComponent } from './bonus-point-standard/bonus-point-standard-detail/bonus-point-standard-detail.component'
import { BonusPointStandardComponent } from './bonus-point-standard/bonus-point-standard.component'
import { AddOrEditBuyingMaterialModelComponent } from './buying-material/add-or-edit-buying-material-model/add-or-edit-buying-material-model.component'
import { BuyingMaterialDetailModelComponent } from './buying-material/buying-material-detail-model/buying-material-detail-model.component'
import { BuyingMaterialComponent } from './buying-material/buying-material.component'
import { AddOrEditCheckInventoryModelComponent } from './check-inventory/add-or-edit-check-inventory-model/add-or-edit-check-inventory-model.component'
import { CheckInventoryDetailModelComponent } from './check-inventory/check-inventory-detail-model/check-inventory-detail-model.component'
import { CheckInventoryComponent } from './check-inventory/check-inventory.component'
import { CreateBuyingMaterialComponent } from './create-buying-material/create-buying-material.component'
import { AddOrEditInboundModelComponent } from './inbound/add-or-edit-inbound-model/add-or-edit-inbound-model.component'
import { InboundDetailModelComponent } from './inbound/inbound-detail-model/inbound-detail-model.component'
import { InboundDetailComponent } from './inbound/inbound-detail-model/tabs/detail/inbound-detail.component'
import { InboundHistoryComponent } from './inbound/inbound-detail-model/tabs/history/inbound-history.component'
import { InboundComponent } from './inbound/inbound.component'
import { AddOrEditJobCategoryModelComponent } from './job-category/add-or-edit-job-category-model/add-or-edit-job-category-model.component'
import { JobCategoryDetailModelComponent } from './job-category/job-category-detail-model/job-category-detail-model.component'
import { JobCategoryComponent } from './job-category/job-category.component'
import { AddOrEditJobTypeModelComponent } from './job-type/add-or-edit-job-type-model/add-or-edit-job-type-model.component'
import { JobTypeDetailModelComponent } from './job-type/job-type-detail-model/job-type-detail-model.component'
import { JobTypeComponent } from './job-type/job-type.component'
import { JobDetailComponent } from './job/job-detail/job-detail.component'
import { JobComponent } from './job/job.component'
import { AddOrEditMaterialCategoryModelComponent } from './material-category/add-or-edit-material-category-model/add-or-edit-material-category-model.component'
import { MaterialCategoryDetailModelComponent } from './material-category/material-category-detail-model/material-category-detail-model.component'
import { MaterialCategoryComponent } from './material-category/material-category.component'
import { AddOrEditOutboundModelComponent } from './outbound/add-or-edit-outbound-model/add-or-edit-outbound-model.component'
import { OutboundDetailModelComponent } from './outbound/outbound-detail-model/outbound-detail-model.component'
import { OutboundDetailComponent } from './outbound/outbound-detail-model/tabs/detail/outbound-detail.component'
import { OutboundHistoryComponent } from './outbound/outbound-detail-model/tabs/history/outbound-history.component'
import { OutboundComponent } from './outbound/outbound.component'
import { RepairRoutingModule } from './repair-routing.module'
import { ReportEmployeeMaterialComponent } from './report/report-employee-material/report-employee-material.component'
import { ReportMaterialDetailForWarehouseComponent } from './report/report-material-detail-for-warehouse/report-material-detail-for-warehouse.component'
import { ReportMaterialDetailHistoryComponent } from './report/report-material-detail-history/report-material-detail-history.component'
import { ReportMaterialDetailComponent } from './report/report-material-detail/report-material-detail.component'
import { ReportMaterialDetailForMaterialCategoryComponent } from './report/report-material/report-material-detail-for-material-category/report-material-detail-for-material-category.component'
import { ReportMaterialComponent } from './report/report-material/report-material.component'
import { ReportUsingMaterialByApartmentComponent } from './report/report-using-material/report-using-material-by-apartment/report-using-material-by-apartment.component'
import { ReportUsingMaterialByEmployeeComponent } from './report/report-using-material/report-using-material-by-employee/report-using-material-by-employee.component'
import { ReportUsingMaterialByRoomComponent } from './report/report-using-material/report-using-material-by-room/report-using-material-by-room.component'
import { ReportUsingMaterialDetailComponent } from './report/report-using-material/report-using-material-detail/report-using-material-detail.component'
import { ReportWarehouseMaterialComponent } from './report/report-warehouse-material/report-warehouse-material.component'
import { AddOrEditReturnMaterialModelComponent } from './return-material/add-or-edit-return-material-model/add-or-edit-return-material-model.component'
import { ReturnMaterialDetailModelComponent } from './return-material/return-material-detail-model/return-material-detail-model.component'
import { ReturnMaterialComponent } from './return-material/return-material.component'
import { RewardStatisticsComponent } from './reward-tab/reward-statistics/reward-statistics.component'
import { RewardTabComponent } from './reward-tab/reward-tab.component'
import { RewardDetailComponent } from './reward/reward-detail/reward-detail.component'
import { RewardComponent } from './reward/reward.component'
import { SatisticJobComponent } from './satistic-job/satistic-job.component'
import { AddOrEditTransferWarehouseModelComponent } from './transfer-warehouse/add-or-edit-transfer-warehouse-model/add-or-edit-transfer-warehouse-model.component'
import { TransferWarehouseDetailModelComponent } from './transfer-warehouse/transfer-warehouse-detail-model/transfer-warehouse-detail-model.component'
import { TransferWarehouseComponent } from './transfer-warehouse/transfer-warehouse.component'
import { AddOrEditUnitModelComponent } from './unit/add-or-edit-unit-model/add-or-edit-unit-model.component'
import { UnitDetailModelComponent } from './unit/unit-detail-model/unit-detail-model.component'
import { UnitComponent } from './unit/unit.component'
import { AddOrEditWarehouseMaterialModelComponent } from './warehouse-material/add-or-edit-warehouse-material-model/add-or-edit-warehouse-material-model.component'
import { WarehouseMaterialDetailModelComponent } from './warehouse-material/warehouse-material-detail-model/warehouse-material-detail-model.component'
import { WarehouseMaterialComponent } from './warehouse-material/warehouse-material.component'
import { AddOrEditServiceFeeStandardComponent } from './service-fee-standard/add-or-edit-service-fee-standard/add-or-edit-service-fee-standard.component'
import { ServiceFeeStandardDetailComponent } from './service-fee-standard/service-fee-standard-detail/service-fee-standard-detail.component'
import { ServiceFeeStandardComponent } from './service-fee-standard/service-fee-standard.component'
import { ReportWarehouseInOutComponent } from './report/report-warehouse-in-out/report-warehouse-in-out.component'
import { ReportWarehouseInventoryComponent } from './report/report-warehouse-inventory/report-warehouse-inventory.component'
import { ReportMaterialInOutComponent } from './report/report-material-in-out/report-material-in-out.component'
import { ReportMaterialInOutDetailComponent } from './report/report-material-in-out/report-material-in-out-detail/report-material-in-out-detail.component'
import { ReportMaterialInventoryComponent } from './report/report-material-inventory/report-material-inventory.component'
import { ReportEmployeeJobRewardComponent } from './report/report-employee-job-reward/report-employee-job-reward.component'
import { ReportEmployeeJobRewardDetailComponent } from './report/report-employee-job-reward/report-employee-job-reward-detail/report-employee-job-reward-detail.component'
import { ReportApartmentJobComponent } from './report/report-apartment-job/report-apartment-job.component'
import { ReportEmployeeAdvanceComponent } from './report/report-employee-advance/report-employee-advance.component'
import { ReportEmployeeAdvanceDetailComponent } from './report/report-employee-advance/report-employee-advance-detail/report-employee-advance-detail.component'
import { ReportWarehouseInOutDetailComponent } from './report/report-warehouse-in-out/report-warehouse-in-out-detail/report-warehouse-in-out-detail.component'
import { ReportEmployeeInventoryComponent } from './report/report-employee-inventory/report-employee-inventory.component'
import { ReportPerfomentEmployeeComponent } from './report/report-perfoment-employee/report-perfoment-employee.component'
import { ReportPerfomentEmployeeDetailComponent } from './report/report-perfoment-employee/report-perfoment-employee-detail/report-perfoment-employee-detail.component'
export const CustomCurrencyMaskConfig: CurrencyMaskConfig = {
  align: 'right',
  allowNegative: false,
  decimal: '.',
  precision: 0,
  prefix: '',
  suffix: '',
  thousands: ',',
}
@NgModule({
  declarations: [
    WarehouseMaterialComponent,
    WarehouseMaterialDetailModelComponent,
    AddOrEditWarehouseMaterialModelComponent,
    UnitComponent,
    UnitDetailModelComponent,
    AddOrEditUnitModelComponent,
    MaterialCategoryComponent,
    MaterialCategoryDetailModelComponent,
    AddOrEditMaterialCategoryModelComponent,
    AddOrEditJobCategoryModelComponent,
    JobCategoryDetailModelComponent,
    JobCategoryComponent,
    AddOrEditJobTypeModelComponent,
    JobTypeDetailModelComponent,
    JobTypeComponent,
    AddOrEditInboundModelComponent,
    InboundDetailModelComponent,
    InboundDetailComponent,
    InboundHistoryComponent,
    InboundComponent,
    AddOrEditOutboundModelComponent,
    OutboundDetailModelComponent,
    OutboundDetailComponent,
    OutboundHistoryComponent,
    OutboundComponent,
    ServiceFeeStandardComponent,
    AddOrEditTransferWarehouseModelComponent,
    TransferWarehouseDetailModelComponent,
    TransferWarehouseComponent,
    AddOrEditCheckInventoryModelComponent,
    CheckInventoryDetailModelComponent,
    CheckInventoryComponent,
    AddOrEditReturnMaterialModelComponent,
    ReturnMaterialDetailModelComponent,
    ReturnMaterialComponent,
    ReportWarehouseMaterialComponent,
    ReportMaterialComponent,
    BuyingMaterialComponent,
    AddOrEditBuyingMaterialModelComponent,
    BuyingMaterialDetailModelComponent,
    ServiceFeeStandardDetailComponent,
    AddOrEditServiceFeeStandardComponent,
    ReportMaterialDetailComponent,
    ReportEmployeeMaterialComponent,
    ReportMaterialDetailHistoryComponent,
    RewardComponent,
    RewardDetailComponent,
    RewardTabComponent,
    RewardStatisticsComponent,
    ReportUsingMaterialByApartmentComponent,
    ReportUsingMaterialDetailComponent,
    JobComponent,
    ReportUsingMaterialByRoomComponent,
    ReportUsingMaterialByEmployeeComponent,
    SatisticJobComponent,
    ReportMaterialDetailForWarehouseComponent,
    JobDetailComponent,
    ReportMaterialDetailForMaterialCategoryComponent,
    CreateBuyingMaterialComponent,
    BonusPointStandardComponent,
    BonusPointStandardDetailComponent,
    AddOrEditBonusPointStandardComponent,
    ReportWarehouseInOutComponent,
    ReportWarehouseInventoryComponent,
    ReportMaterialInOutComponent,
    ReportMaterialInOutDetailComponent,
    ReportMaterialInventoryComponent,
    ReportEmployeeJobRewardComponent,
    ReportEmployeeJobRewardDetailComponent,
    ReportApartmentJobComponent,
    ReportEmployeeAdvanceComponent,
    ReportEmployeeAdvanceDetailComponent,
    ReportWarehouseInOutDetailComponent,
    ReportEmployeeInventoryComponent,
    ReportPerfomentEmployeeComponent,
    ReportPerfomentEmployeeDetailComponent,
  ],
  imports: [
    CommonModule,
    RepairRoutingModule,
    FormsModule,
    NzButtonModule,
    NzTableModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDatePickerModule,
    NzTimePickerModule,
    NzPopconfirmModule,
    NzPaginationModule,
    MaterialModule,
    ReactiveFormsModule,
    CurrencyMaskModule,
    NzCollapseModule,
    NzInputNumberModule,
    DirectivesModule,
    NzUploadModule,
    NzRadioModule,
    NzCascaderModule,
    NzTagModule,
    NzImageModule,
    NzCarouselModule,
    NzPopoverModule,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class RepairModule {}
