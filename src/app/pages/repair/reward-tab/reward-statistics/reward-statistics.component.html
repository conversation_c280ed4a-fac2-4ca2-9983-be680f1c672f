<nz-row>
  <nz-col nzSpan="12">
    <nz-radio-group [(ngModel)]="radioValue" nzButtonStyle="solid" (ngModelChange)="onChange()">
      <label nz-radio-button nzValue="EMPLOYEE">Theo nhân viên</label>
      <label nz-radio-button nzValue="JOBTYPE"><PERSON>ửa chữa</label>
    </nz-radio-group>
  </nz-col>
</nz-row>

<ng-container *ngIf="radioValue === 'JOBTYPE'">
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left"><PERSON><PERSON><PERSON> sử<PERSON> chữa </nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.jobTypeId" name="jobTypeId" nzPlaceHolder="Loại sửa chữa">
                <nz-option *ngFor="let item of dataJobType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button class="mt-3" nz-button nzType="default" (click)="searchDataJobType()" nzShape="round">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>

  <div nz-row class="mt-3">
    <div class="table-scroll-item">
      <nz-table
        style="width: 50%"
        class="mb-3"
        nz-col
        nzSpan="24"
        [nzData]="['']"
        [nzLoading]="loading"
        [nzShowPagination]="false"
        nzBordered
        nzTableLayout="fixed"
        [nzScroll]="{ y: '600px' }"
      >
        <thead>
          <tr>
            <th class="text-center" nzWidth="50px"></th>
            <th class="text-center">Loại sửa chữa</th>
            <th class="text-center">Tiền thưởng<i>&nbsp;(VNĐ)</i></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data of listOfData">
            <tr>
              <td
                [nzExpand]="expandSet.has(data.jobTypeCode)"
                (nzExpandChange)="onExpandChange(data.jobTypeCode, $event)"
                style="text-align: center !important"
                nzLeft
              ></td>
              <td nzWidth="300px">
                <b>{{ data.jobTypeName }}</b>
              </td>
              <td class="text-right" nzWidth="300px">
                <b>{{ data.money | number }}</b>
              </td>
            </tr>
            <ng-container *ngIf="expandSet.has(data.jobTypeCode)">
              <tr *ngFor="let child of data.lstChild">
                <td nzWidth="50px" nzLeft></td>
                <td class="text-right" nzWidth="300px">
                  <i>{{ child.jobName }}</i>
                </td>
                <td class="text-right" nzWidth="300px">
                  <i>{{ child.money | number }}</i>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </nz-table>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="radioValue === 'EMPLOYEE'">
  <div nz-row nzGutter="8">
    <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
      <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
        <div nz-row nzGutter="8">
          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã nhân viên </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.employeeCode" name="employeeCode" nzPlaceHolder="Mã nhân viên">
                  <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên nhân viên </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.employeeId" name="employeeId" nzPlaceHolder="Tên nhân viên">
                  <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.fullName" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="6">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tháng </nz-form-label>
              <nz-form-control [nzSm]="20" [nzXs]="24">
                <nz-date-picker nzFormat="MM/yyyy" nzMode="month" [(ngModel)]="dataSearch.month" name="month" nzPlaceHolder="Tháng"> </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="24" class="text-center">
            <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchDataEmployee()">
              <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
            </button>
          </div>
        </div>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <div nz-row class="mt-3">
    <div class="table-scroll-item">
      <nz-table
        class="mb-3"
        nz-col
        nzSpan="24"
        [nzData]="['']"
        [nzLoading]="loading"
        [nzShowPagination]="false"
        nzBordered
        nzTableLayout="fixed"
        [nzScroll]="{ y: '600px' }"
      >
        <thead>
          <tr>
            <th class="text-center" nzWidth="50px"></th>
            <th class="text-center">Mã Nhân Viên</th>
            <th class="text-center">Tên nhân viên</th>
            <th class="text-center">Tiền thưởng</th>
            <th class="text-center">Điểm</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data of listOfData">
            <tr>
              <td
                [nzExpand]="expandSet.has(data.employeeCode)"
                (nzExpandChange)="onExpandChange(data.employeeCode, $event)"
                style="text-align: center !important"
                nzLeft
              ></td>
              <td nzWidth="300px" nzLeft>
                <b>{{ data.employeeCode }}</b>
              </td>
              <td nzWidth="300px">
                <b>{{ data.employeeName }}</b>
              </td>
              <td class="text-right" nzWidth="300px">
                <b>{{ data.money | number }}</b>
              </td>
              <td nzWidth="300px">{{ data.point | number }}</td>
            </tr>
            <ng-container *ngIf="expandSet.has(data.employeeCode)">
              <tr *ngFor="let child of data.lstChild">
                <td nzWidth="50px" nzLeft></td>
                <td class="text-left" nzWidth="300px" nzLeft>
                  <i>{{ child.jobName }}</i>
                </td>
                <td class="text-left" nzWidth="300px">
                  <i>{{ child.completedDate | date : 'dd/MM/yyyy' }}</i>
                </td>
                <td class="text-right" nzWidth="300px">
                  <i>{{ child.point | number }}</i>
                </td>
                <td class="text-right" nzWidth="300px">
                  <i>{{ child.money | number }}</i>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </nz-table>
    </div>
  </div>
</ng-container>
