import { Component, OnInit } from '@angular/core'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({
  selector: 'app-reward-statistics',
  templateUrl: './reward-statistics.component.html',
})
export class RewardStatisticsComponent implements OnInit {
  currentUser: User | any
  enumData: any
  modalTitle: string = ''
  dataJobType: any
  dataEmployee: any
  listOfData: any = []
  dataSearch: any = {}
  dataUploadExcel: any
  loading: boolean = true
  radioValue = ''
  expandSet = new Set<string>()

  constructor(
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit(): void {
    this.dataSearch.isDeleted = false
    this.radioValue = ''
  }

  onChange() {
    if (this.radioValue === 'JOBTYPE') {
      this.searchDataJobType()
      this.loadSelectBox()
    } else if (this.radioValue === 'EMPLOYEE') {
      this.searchDataEmployee()
      this.loadSelectBoxForEmployee()
    }
  }

  onExpandChange(id: string, checked: boolean) {
    if (checked) this.expandSet.add(id)
    else this.expandSet.delete(id)
  }

  loadSelectBox() {
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB_TYPE.LOAD_DATA, {}).then((data: any) => {
      if (data) {
        this.dataJobType = data
      }
    })
  }

  loadSelectBoxForEmployee() {
    this.apiNtssService.post(this.apiNtssService.EMPLOYEE.GET_LIST_REPAIR_EMPLOYEE, {}).then((data: any) => {
      if (data) {
        this.dataEmployee = data
      }
    })
  }

  async searchDataJobType() {
    this.loading = true

    this.listOfData = []
    this.apiScService.postRepair(this.apiScService.REPAIR_REWARD.STATISTIC_BY_JOB_TYPE, this.dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.listOfData = data
      }
    })
  }

  async searchDataEmployee() {
    this.loading = true
    let dataSearch = await this.filterDataSearchEmployee()

    this.listOfData = []
    this.apiScService.postRepair(this.apiScService.REPAIR_REWARD.STATISTIC_BY_EMPLOYEE, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.listOfData = data
      }
    })
  }

  async filterDataSearchEmployee() {
    const where: any = {}
    if (this.dataSearch?.employeeId) {
      where.employeeId = this.dataSearch.employeeId
    }
    if (this.dataSearch?.employeeCode) {
      where.employeeId = this.dataSearch.employeeCode
    }
    if (this.dataSearch?.employeeName) {
      where.employeeName = this.dataSearch.employeeName
    }
    if (this.dataSearch?.employeeCode) {
      where.employeeCode = this.dataSearch.employeeCode
    }
    if (this.dataSearch?.month) {
      where.month = this.dataSearch.month
    }

    return where
  }

  onShowModel(data: any) {
    // this.dialog
    //   .open(AddApartmentForEmployeeComponent, { disableClose: false, data })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     this.searchDataEmployee()
    //   })
  }

  onShowDetail(data: any) {
    // this.dialog
    //   .open(ApartmentManagerDetailComponent, { disableClose: false, data })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     this.searchDataEmployee()
    //   })
  }

  onDetail(object: any) {
    // this.dialog
    //   .open(ApartmetDetailComponent, { disableClose: false, data: object })
    //   .afterClosed()
    //   .subscribe((data) => {
    //     this.searchDataApartment()
    //   })
  }
}
