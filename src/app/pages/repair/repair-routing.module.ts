import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { BonusPointStandardComponent } from './bonus-point-standard/bonus-point-standard.component'
import { CheckInventoryComponent } from './check-inventory/check-inventory.component'
import { CreateBuyingMaterialComponent } from './create-buying-material/create-buying-material.component'
import { InboundComponent } from './inbound/inbound.component'
import { JobCategoryComponent } from './job-category/job-category.component'
import { JobComponent } from './job/job.component'
import { MaterialCategoryComponent } from './material-category/material-category.component'
import { OutboundComponent } from './outbound/outbound.component'
import { ReportMaterialComponent } from './report/report-material/report-material.component'
import { ReportUsingMaterialByApartmentComponent } from './report/report-using-material/report-using-material-by-apartment/report-using-material-by-apartment.component'
import { ReportUsingMaterialByEmployeeComponent } from './report/report-using-material/report-using-material-by-employee/report-using-material-by-employee.component'
import { ReportUsingMaterialByRoomComponent } from './report/report-using-material/report-using-material-by-room/report-using-material-by-room.component'
import { ReportWarehouseMaterialComponent } from './report/report-warehouse-material/report-warehouse-material.component'
import { ReturnMaterialComponent } from './return-material/return-material.component'
import { RewardTabComponent } from './reward-tab/reward-tab.component'
import { SatisticJobComponent } from './satistic-job/satistic-job.component'
import { TransferWarehouseComponent } from './transfer-warehouse/transfer-warehouse.component'
import { UnitComponent } from './unit/unit.component'
import { WarehouseMaterialComponent } from './warehouse-material/warehouse-material.component'
import { ServiceFeeStandardComponent } from './service-fee-standard/service-fee-standard.component'
import { ReportWarehouseInOutComponent } from './report/report-warehouse-in-out/report-warehouse-in-out.component'
import { ReportWarehouseInventoryComponent } from './report/report-warehouse-inventory/report-warehouse-inventory.component'
import { ReportMaterialInOutComponent } from './report/report-material-in-out/report-material-in-out.component'
import { ReportMaterialInventoryComponent } from './report/report-material-inventory/report-material-inventory.component'
import { ReportEmployeeJobRewardComponent } from './report/report-employee-job-reward/report-employee-job-reward.component'
import { ReportApartmentJobComponent } from './report/report-apartment-job/report-apartment-job.component'
import { ReportEmployeeAdvanceComponent } from './report/report-employee-advance/report-employee-advance.component'
import { ReportEmployeeInventoryComponent } from './report/report-employee-inventory/report-employee-inventory.component'
import { ReportPerfomentEmployeeComponent } from './report/report-perfoment-employee/report-perfoment-employee.component'
import { JobTypeComponent } from './job-type/job-type.component'

const routes: Routes = [
  { path: 'repair-warehouse-material', component: WarehouseMaterialComponent },
  { path: 'repair-unit', component: UnitComponent },
  { path: 'repair-material-category', component: MaterialCategoryComponent },
  { path: 'repair-job-category', component: JobCategoryComponent },
  { path: 'bonus-point-standard', component: BonusPointStandardComponent },
  { path: 'repair-job-type', component: JobTypeComponent },
  { path: 'repair-inbound', component: InboundComponent },
  { path: 'repair-outbound', component: OutboundComponent },
  { path: 'repair-transfer-warehouse', component: TransferWarehouseComponent },
  { path: 'repair-check-inventory', component: CheckInventoryComponent },
  { path: 'repair-return-material', component: ReturnMaterialComponent },
  { path: 'repair-report-warehouse-material', component: ReportWarehouseMaterialComponent },
  { path: 'repair-inventory', component: ReportMaterialComponent },
  { path: 'repair-buying-material', component: CreateBuyingMaterialComponent },
  { path: 'repair-reward', component: RewardTabComponent },
  { path: 'repair-using-material-by-apartment', component: ReportUsingMaterialByApartmentComponent },
  { path: 'repair-job', component: JobComponent },
  { path: 'repair-using-material-by-employee', component: ReportUsingMaterialByEmployeeComponent },
  { path: 'repair-using-material-by-room', component: ReportUsingMaterialByRoomComponent },
  { path: 'statistic-job', component: SatisticJobComponent },
  { path: 'service-fee', component: ServiceFeeStandardComponent },
  { path: 'report-warehouse-in-out', component: ReportWarehouseInOutComponent },
  { path: 'report-warehouse-inventory', component: ReportWarehouseInventoryComponent },
  { path: 'report-material-in-out', component: ReportMaterialInOutComponent },
  { path: 'report-material-inventory', component: ReportMaterialInventoryComponent },
  { path: 'report-employee-reward', component: ReportEmployeeJobRewardComponent },
  { path: 'report-apartment-job', component: ReportApartmentJobComponent },
  { path: 'report-employee-advance', component: ReportEmployeeAdvanceComponent },
  { path: 'report-employee-material', component: ReportEmployeeInventoryComponent },
  { path: 'report-perfoment-employee', component: ReportPerfomentEmployeeComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RepairRoutingModule {}
