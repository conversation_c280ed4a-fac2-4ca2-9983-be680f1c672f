<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
    <thead>
      <tr>
        <th class="text-center" nzWidth="160px">Thời gian</th>
        <th class="text-center">Nội dung</th>
        <th class="text-center" nzWidth="160px">Người cập nhật</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let detail of ajaxTable.data">
        <td class="text-center">{{ detail.createdAt | date: 'HH:mm dd/MM/YYYY'}}</td>
        <td class="text-center">{{ detail.description}}</td>
        <td class="text-center">{{ detail.createdByName}}</td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng
    </ng-template>
  </div>
</div>