import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ReportEmployeeAdvanceDetailComponent } from './report-employee-advance-detail/report-employee-advance-detail.component'
import { enumData } from 'src/app/core/enumData'
import { ExcelV2Service, IColum } from 'src/app/services/excelv2.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './report-employee-advance.component.html' })
export class ReportEmployeeAdvanceComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private excelService: ExcelV2Service
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_EMPLOYEE_ADVANCE, dataSearch).then((rs: any) => {
      if (rs) {
        this.loading = false
        this.total = rs[1]
        this.listOfData = rs[0]
        // this.totalQuantity = rs.totalQuantity
        // this.totalMoney = rs.totalMoney
      }
    })
  }

  ngOnInit() {
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataSearch.month = new Date()
    this.searchData()
  }

  onShowDetail(object: any) {
    const data: any = { id: object.id, month: this.dataSearch.month }
    this.dialog.open(ReportEmployeeAdvanceDetailComponent, { disableClose: false, data })
  }

  lstHeader: IColum[] = [
    { code: 'fullName', name: 'Nhân viên', type: 'string' },
    { code: 'totalMoney', name: 'Tạm ứng', type: 'number' },
    { code: 'payslipMoney', name: 'Đối ứng', type: 'number' },
    { code: 'refundMoney', name: 'Hoàn ứng', type: 'number' },
    { code: 'leftMoney', name: 'Còn lại', type: 'number' },
  ]

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 100000000,
    }
    const [lstData, _num] = await this.apiScService.postRepair(
      this.apiScService.REPAIR_JOB.REPORT_EMPLOYEE_ADVANCE,
      dataSearch
    )
    await this.excelService.onDownloadExcelJs({ lstData, lstHeader: this.lstHeader, excelName: 'BC' })
    this.notifyService.hideloading()
  }
}
