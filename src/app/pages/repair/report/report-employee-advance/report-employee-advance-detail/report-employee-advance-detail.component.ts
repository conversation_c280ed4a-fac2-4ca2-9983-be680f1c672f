import { Component, Inject, OnInit, Optional } from '@angular/core'
import { NotifyService } from '../../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './report-employee-advance-detail.component.html' })
export class ReportEmployeeAdvanceDetailComponent implements OnInit {
  lstDetail: any = []
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<ReportEmployeeAdvanceDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.DETAIL_BUY_MATERIAL, this.data).then((rs: any) => {
      this.lstDetail = rs
      this.notifyService.hideloading()
    })
  }
}
