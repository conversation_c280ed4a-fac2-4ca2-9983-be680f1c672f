import { Component, Inject, OnInit, Optional } from '@angular/core'
import { NotifyService } from '../../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './report-employee-job-reward-detail.component.html' })
export class ReportEmployeeJobRewardDetailComponent implements OnInit {
  dataObject: any = {}
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<ReportEmployeeJobRewardDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.DETAIL_EMPLOYEE_JOB_REWARD, this.data).then((rs: any) => {
      this.dataObject = rs
      this.notifyService.hideloading()
    })
  }
}
