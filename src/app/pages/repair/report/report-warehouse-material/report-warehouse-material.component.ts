import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import * as XLSX from 'xlsx'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ReportMaterialDetailForWarehouseComponent } from '../report-material-detail-for-warehouse/report-material-detail-for-warehouse.component'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  selector: 'app-report-warehouse-material',
  templateUrl: './report-warehouse-material.component.html',
})
export class ReportWarehouseMaterialComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstWarehouse: any[] = []
  totalQuantity: number = 0
  totalMoney: number = 0
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService
      .postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.REPORT_MATERIAL, dataSearch)
      .then((rs: any) => {
        if (rs) {
          this.loading = false
          this.total = rs.data[1]
          this.listOfData = rs.data[0]
          this.totalQuantity = rs.totalQuantity
          this.totalMoney = rs.totalMoney
        }
      })
  }

  ngOnInit() {
    this.loadAllData()
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}).then(async (rs) => {
      this.lstWarehouse = rs
    })
  }

  onShowDetail(object: any) {
    const data: any = { warehouseMaterialId: object.id }
    this.dialog
      .open(ReportMaterialDetailForWarehouseComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  //#region Excel

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.REPORT_MATERIAL, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res.data) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách vật tư')
        //#region Body Table
        const header = ['Kho', 'Tổng số lượng', 'Tổng trị giá (VNĐ)']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res.data[0]) {
          const rowData = [data.name || '', data.totalQuantity || 0, data.totalMoney || 0]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `BAO_CAO_TON_KHO_THEO_KHO_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet()

    //#region Body Table
    const header = ['Mã kho', 'Tên vật tư', 'Số lượng tồn kho (theo Đơn vị cơ sở)', 'Giá vốn tồn kho']
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet.getColumn(colNumber).width = 50
          break
        case 4:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `Template_Nhap_Ton_Kho_Theo_Kho${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })

      const worksheet = workBook.Sheets[workBook.SheetNames[0]]
      const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0] as string[] // Get the first row as headers

      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['warehouseCode', 'materialCategoryName', 'quantity', 'price'],
      })

      // Check for the number of columns
      if (headers && headers.length !== 4) {
        // Ensure there are exactly 4 columns
        this.notifyService.hideloading()
        this.notifyService.showError('Template không hợp lệ.')
        return
      }

      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('fileWh')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 2

        if (row.warehouseCode) row.warehouseCode += ''
        if (!row.warehouseCode) {
          strErr += 'Dòng ' + idx + ' - Mã kho không được để trống  <br>'
        }

        if (row.materialCategoryName) row.materialCategoryName += ''
        if (!row.materialCategoryName) {
          strErr += 'Dòng ' + idx + ' - Tên vật tư không được để trống  <br>'
        }
        if (!(+row.quantity > 0)) {
          strErr += 'Dòng ' + idx + ' - Số lượng tồn kho không hợp lệ  <br>'
        }
        if (!(+row.price > 0)) {
          strErr += 'Dòng ' + idx + ' - Giá vốn tồn kho không hợp lệ  <br>'
        }
        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }
      this.apiScService
        .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.CREATE_DATA_BY_EXCEL, jsonData)
        .then((result) => {
          this.notifyService.hideloading()
          if (result) {
            this.notifyService.showSuccess('Thêm file excel thành công')
            this.searchData()
          }
        })
    }
  }

  //#endregion
}
