import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as moment from 'moment'
import * as fs from 'file-saver'
import { User } from '../../../../../models/user.model'
import { AuthenticationService } from '../../../../../services/authentication.service'
import { CoreService } from '../../../../../services/core.service'
import { NotifyService } from '../../../../../services/notify.service'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({ templateUrl: './report-using-material-detail.component.html' })
export class ReportUsingMaterialDetailComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any[] = []
  dataSearch: any = {}
  loading = true
  lstApartment: any[] = []
  lstRoom: any[] = []
  totalQuantity: number = 0
  totalMoney: number = 0
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_USING_MATERIAL_DETAIL, dataSearch).then((rs: any) => {
      if (rs) {
        this.loading = false
        this.total = rs.data[1]
        this.listOfData = rs.data[0]
        this.totalQuantity = rs.totalQuantity
        this.totalMoney = rs.totalMoney
      }
    })
  }

  ngOnInit() {
    if (this.data.apartmentId) this.dataSearch.apartmentId = this.data.apartmentId
    if (this.data.roomId) this.dataSearch.roomId = this.data.roomId
    if (this.data.materialCategoryId) this.dataSearch.materialCategoryId = this.data.materialCategoryId
    if (this.data.employeeId) this.dataSearch.employeeId = this.data.employeeId
    this.loadAllData()
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    Promise.all([this.apiNtssService.post(this.apiNtssService.APARTMENT.LOAD_DATA, {})]).then(async (rs) => {
      this.lstApartment = rs[0]
    })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: 0,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_USING_MATERIAL_DETAIL, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res.data) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách vật tư')
        //#region Body Table
        const header = ['Ngày hoàn thành công việc', 'Tên loại vật tư', 'Đơn vị tính', 'Đơn giá', 'Tổng số lượng', 'Tổng trị giá']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res.data[0]) {
          const rowData = [
            data.completedDate ? moment(data.completedDate).format('DD/MM/YYYY') : '',
            data.materialName || '',
            data.baseUnitName || '',
            data.sellPrice || 0,
            data.totalQuantity || 0,
            data.totalMoney || 0,
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `BAO_CAO_SU_DUNG_VAT_TU_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
