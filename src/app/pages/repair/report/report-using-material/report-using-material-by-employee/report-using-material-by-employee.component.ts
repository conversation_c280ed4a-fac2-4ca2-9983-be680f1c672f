import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { User } from '../../../../../models/user.model'
import { AuthenticationService } from '../../../../../services/authentication.service'
import { CoreService } from '../../../../../services/core.service'
import { NotifyService } from '../../../../../services/notify.service'
import { ReportUsingMaterialDetailComponent } from '../report-using-material-detail/report-using-material-detail.component'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

/** BC Sử dụng vật tư theo NV */
@Component({ templateUrl: './report-using-material-by-employee.component.html' })
export class ReportUsingMaterialByEmployeeComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  enumDataSC = enumData
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstEmployee: any[] = []
  totalQuantity: number = 0
  totalMoney: number = 0
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_MATERIAL_DETAIL_BY_EMPLOYEE, dataSearch).then((rs: any) => {
      if (rs) {
        this.loading = false
        this.total = rs.data[1]
        this.listOfData = rs.data[0]
        this.totalQuantity = rs.totalQuantity
        this.totalMoney = rs.totalMoney
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataSearch.month = new Date()
    this.searchData()
  }

  async loadAllData() {
    Promise.all([this.apiNtssService.post(this.apiNtssService.EMPLOYEE.LOAD_DATA, { type: this.enumDataSC.UserType.Repair_Staff.code })]).then(
      async (rs) => {
        this.lstEmployee = rs[0]
      }
    )
  }

  onShowDetail(object: any) {
    const data: any = { employeeId: object.id }
    this.dialog
      .open(ReportUsingMaterialDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_MATERIAL_DETAIL_BY_EMPLOYEE, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res.data) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách nhân viên')
        //#region Body Table
        const header = ['Nhân viên', 'Tổng số lượng', 'Tổng trị giá (VNĐ)']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res.data[0]) {
          const rowData = [data.fullName || '', data.totalQuantity || 0, data.totalMoney || 0]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `BAO_CAO_SU_DUNG_VAT_TU_THEO_NV_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
