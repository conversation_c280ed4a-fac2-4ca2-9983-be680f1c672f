<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <button class="mr-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
  </button>
  <button class="mr-2" nz-button nzType="default" (click)="onDownloadDetailExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Chi tiết Excel
  </button>
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="24">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Chọn tháng </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-date-picker
                nzMode="month"
                [nzShowTime]="false"
                [(ngModel)]="dataSearch.month"
                name="month"
                style="width: 100%"
                nzPlaceHolder="Chọn tháng"
                nzFormat="MM / YYYY"
                (ngModelChange)="onChangeMonth()"
              >
              </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Chọn ngày </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.date" name="date" style="width: 100%" (ngModelChange)="onChangeDate()">
              </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Khu </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select
                (ngModelChange)="loadRoomByApartmentId($event)"
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataSearch.apartmentId"
                name="apartmentId"
                nzPlaceHolder="Chọn khu"
              >
                <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Phòng </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.roomId" name="roomId" nzPlaceHolder="Chọn phòng">
                <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-col nzSpan="12" class="text-left">
    <span style="font-size: medium"
      >Tổng số lượng:
      <b
        ><i>{{ totalQuantity | number }}</i></b
      >
    </span>
  </nz-col>
  <nz-col nzSpan="12" class="text-right">
    <span style="font-size: medium"
      >Tổng trị giá:
      <b style="color: blue"
        ><i>{{ totalMoney | number }} VNĐ</i></b
      >
    </span>
  </nz-col>
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th class="text-center">Phòng</th>
        <th class="text-center">Tổng số lượng vật tư sử dụng</th>
        <th class="text-center">Tổng trị giá vật tư (VNĐ)</th>
        <th class="text-center">Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">{{ data.name }}</td>
        <td class="text-center">{{ data.totalQuantity | number }}</td>
        <td class="text-center">{{ data.totalMoney | number }}</td>
        <td class="text-center">
          <button class="mr-1" (click)="onShowDetail(data)" nz-tooltip nzTooltipTitle="Chi tiết" nz-button nzType="primary" nzGhost nzShape="circle">
            <i nz-icon nzType="eye"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
