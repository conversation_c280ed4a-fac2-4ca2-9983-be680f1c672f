import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { User } from '../../../../../models/user.model'
import { AuthenticationService } from '../../../../../services/authentication.service'
import { CoreService } from '../../../../../services/core.service'
import { NotifyService } from '../../../../../services/notify.service'
import { ReportUsingMaterialDetailComponent } from '../report-using-material-detail/report-using-material-detail.component'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

/** BC <PERSON>ử dụng vật tư theo phòng */
@Component({ templateUrl: './report-using-material-by-room.component.html' })
export class ReportUsingMaterialByRoomComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstRoom: any[] = []
  lstApartment: any[] = []
  totalQuantity: number = 0
  totalMoney: number = 0
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_MATERIAL_DETAIL_BY_ROOM, dataSearch).then((rs: any) => {
      if (rs) {
        this.loading = false
        this.total = rs.data[1]
        this.listOfData = rs.data[0]
        this.totalQuantity = rs.totalQuantity
        this.totalMoney = rs.totalMoney
      }
    })
  }

  ngOnInit() {
    if (this?.data?.apartmentId) this.dataSearch.apartmentId = this.data.apartmentId
    this.loadAllData()
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataSearch.month = new Date()
    this.searchData()
  }

  onChangeMonth() {
    this.dataSearch.date = []
  }
  onChangeDate() {
    this.dataSearch.month = null
  }

  async loadAllData() {
    const whereCon: any = {}
    if (this?.dataSearch?.apartmentId) whereCon.apartmentId = this.dataSearch.apartmentId
    const [lstRoom, lstApartment] = await Promise.all([
      this.apiNtssService.post(this.apiNtssService.ROOM.FIND, whereCon),
      this.apiNtssService.post(this.apiNtssService.APARTMENT.LOAD_DATA, {}),
    ])
    this.lstApartment = lstApartment
    this.lstRoom = lstRoom
  }

  async loadRoomByApartmentId(event: any) {
    const whereCon: any = {}
    if (event) whereCon.apartmentId = event
    const lstRoom = await this.apiNtssService.post(this.apiNtssService.ROOM.FIND, whereCon)
    this.lstRoom = lstRoom
  }

  onShowDetail(object: any) {
    const data: any = { roomId: object.id }
    this.dialog
      .open(ReportUsingMaterialDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_MATERIAL_DETAIL_BY_ROOM, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res.data) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách phòng')
        //#region Body Table
        const header = ['Phòng', 'Tổng số lượng', 'Tổng trị giá (VNĐ)']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res.data[0]) {
          const rowData = [data.name || '', data.totalQuantity || 0, data.totalMoney || 0]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `BAO_CAO_SU_DUNG_VAT_TU_THEO_PHONG_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }

  async onDownloadDetailExcel() {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPORT_MATERIAL_DETAIL.REPORT_MATERIAL_DETAIL_BY_ROOM_DETAIL, this.dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res.data) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách phòng')
        //#region Body Table
        const header = ['Phòng', 'Tên loại vật tư', 'Đơn vị tính', 'Đơn giá', 'Tổng số lượng', 'Tổng trị giá']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
          switch (colNumber) {
            case 4:
            case 3:
            case 5:
            case 6:
            case 1:
              worksheet.getColumn(colNumber).width = 20
              break
            case 2:
              worksheet.getColumn(colNumber).width = 50
              break
            default:
              worksheet.getColumn(colNumber).width = 20
              break
          }
        })

        for (let data of res.data) {
          const rowData = [
            data.roomName || '',
            data.materialCategoryName || '',
            data.unitName || '',
            new Intl.NumberFormat('vi-VN', {
              style: 'decimal',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(data.price || 0),
            new Intl.NumberFormat('vi-VN', {
              style: 'decimal',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(data.quantityUsed || 0),
            new Intl.NumberFormat('vi-VN', {
              style: 'decimal',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(data.totalPrice || 0),
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
            cell.alignment = { horizontal: colNumber > 3 ? 'right' : 'left' }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `BAO_CAO_SU_DUNG_VAT_TU_CHI_TIET_THEO_PHONG_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
