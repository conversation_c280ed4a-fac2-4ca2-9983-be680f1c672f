<nz-tabset>
  <nz-tab nzTitle="Nhập kho">
    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable [nzData]="dataObject.lstInbound" [nzShowPagination]="false"
        nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th><PERSON><PERSON><PERSON></th>
            <th>M<PERSON> phiếu nhập</th>
            <th>Số lượng</th>
            <th>Giá trị</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of ajaxTable.data">
            <td class="text-center">{{ data.approvedDate | date: ' dd/MM/yyyy' }}</td>
            <td class="text-center">{{ data.code }}</td>
            <td class="text-center">{{ data.totalQuantity | number }}</td>
            <td class="text-center">{{ data.totalMoney | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-tab>
  <nz-tab nzTitle="Xu<PERSON>t kho">
    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable2 [nzData]="dataObject.lstOutbound" [nzShowPagination]="false"
        nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th>Ngày duyệt</th>
            <th>Mã phiếu xuất</th>
            <th>Số lượng</th>
            <th>Giá trị</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of ajaxTable2.data">
            <td class="text-center">{{ data.approvedDate | date: ' dd/MM/yyyy' }}</td>
            <td class="text-center">{{ data.code }}</td>
            <td class="text-center">{{ data.totalQuantity | number }}</td>
            <td class="text-center">{{ data.totalMoney | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-tab>
</nz-tabset>