<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="<PERSON><PERSON><PERSON>" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="24">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Chọn tháng </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-date-picker
                nzMode="month"
                [nzShowTime]="false"
                [(ngModel)]="dataSearch.month"
                name="month"
                style="width: 100%"
                nzPlaceHolder="Chọn tháng"
                nzFormat="MM / YYYY"
              >
              </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Tên kho </nz-form-label>
            <nz-form-control nzSpan="24">
              <input nz-input [(ngModel)]="dataSearch.name" name="dataSearch.name" placeholder="Nhập tên kho" />
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
          <button nz-button nzType="primary" class="mr-3 ml-3" (click)="onDownloadExcel()" nzShape="round">
            <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th [colSpan]="1" [rowSpan]="2">Kho</th>
        <th [colSpan]="2">Nhập</th>
        <th [colSpan]="2">Xuất</th>
        <th [colSpan]="1" [rowSpan]="2">Tác vụ</th>
      </tr>
      <tr>
        <th [rowSpan]="2">Số lượng</th>
        <th [rowSpan]="2">Giá trị</th>
        <th [rowSpan]="2">Số lượng</th>
        <th [rowSpan]="2">Giá trị</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">{{ data.name }}</td>
        <td class="text-center">{{ data.totalQuantityIn | number }}</td>
        <td class="text-center">{{ data.totalMoneyIn | number }}</td>
        <td class="text-center">{{ data.totalQuantityOut | number }}</td>
        <td class="text-center">{{ data.totalMoneyOut | number }}</td>
        <td class="text-center">
          <button class="mr-2" (click)="onShowDetail(data)" nz-tooltip nzTooltipTitle="Chi tiết" nz-button nzType="primary" nzGhost nzShape="circle">
            <i nz-icon nzType="eye"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
