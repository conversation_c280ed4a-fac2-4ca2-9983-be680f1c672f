import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ReportMaterialDetailHistoryComponent } from '../report-material-detail-history/report-material-detail-history.component'
import { ApiScService } from 'src/app/services/apiSc.service'
@Component({ templateUrl: './report-material-detail.component.html' })
export class ReportMaterialDetailComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  totalQuantity: number = 0
  totalMoney: number = 0
  modelTitle: string = 'CHI TIẾT VẬT TƯ'

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialogRef: MatDialogRef<ReportMaterialDetailComponent>,
    private authenticationService: AuthenticationService,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    if (this.data.materialCategoryId) this.dataSearch.materialCategoryId = this.data.materialCategoryId
    if (this.data.warehouseMaterialId) this.dataSearch.warehouseMaterialId = this.data.warehouseMaterialId
    if (this.data.employeeId) this.dataSearch.employeeId = this.data.employeeId
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService
      .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.REPORT_MATERIAL_DETAIL, dataSearch)
      .then((rs: any) => {
        if (rs) {
          this.loading = false
          this.total = rs.data[1]
          this.listOfData = rs.data[0]
          this.totalQuantity = rs.totalQuantity
          this.totalMoney = rs.totalMoney
        }
      })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService
      .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.REPORT_MATERIAL_DETAIL, dataSearch)
      .then((res) => {
        this.notifyService.hideloading()
        if (res.data) {
          const workbook = new Workbook()
          const worksheet = workbook.addWorksheet('Danh sách vật tư')
          //#region Body Table
          const header = [
            'Mã loại vật tư',
            'Tên loại vật tư',
            'Số lượng tồn kho công ty',
            'Giá vốn tồn kho công ty',
            'Đơn vị tính',
            'Quy đổi',
            'Đơn vị cơ sở',
            'Giá trị tồn kho (VNĐ)',
          ]

          const headerRow = worksheet.addRow(header)

          // Cell Style : Fill and Border
          headerRow.eachCell((cell, colNumber) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: '203751' },
              bgColor: { argb: '203751' },
            }
            cell.alignment = { horizontal: 'center' }
            cell.font = { bold: true, color: { argb: 'FFFFFF' } }
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }

            switch (colNumber) {
              case 1:
              case 2:
              case 3:
              case 4:
              case 5:
              case 6:
                worksheet.getColumn(colNumber).width = 30
                break
              default:
                worksheet.getColumn(colNumber).width = 15
                break
            }
          })

          for (let data of res.data[0]) {
            const rowData = [
              data.materialCategoryCode || '',
              data.materialCategoryName || '',
              data.quantity || 0,
              data.price || 0,
              data.unitName || '',
              data.conversionRate || 0,
              data.baseUnitName || '',
              +data.price * +data.quantity || 0,
            ]

            const row = worksheet.addRow(rowData)
            row.eachCell((cell, colNumber) => {
              cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
              }
            })
          }

          //#region Save File
          workbook.xlsx.writeBuffer().then((data) => {
            let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
            let date = new Date().toISOString()
            const fileName = `BAO_CAO_TON_KHO_THEO_LVT_${date}.xlsx`
            fs.saveAs(blob, fileName)
            this.notifyService.hideloading()
          })
          //#endregion
        }
      })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  onShowDetail(data: any) {
    this.dialog.open(ReportMaterialDetailHistoryComponent, { data })
  }
}
