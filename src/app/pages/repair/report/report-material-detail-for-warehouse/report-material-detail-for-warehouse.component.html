<nz-tabset>
  <nz-tab nzTitle="Thông tin chung">
    <div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
      <button class="mr-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
        <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
      </button>
    </div>

    <div nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="12" class="text-left">
        <span style="font-size: medium;">Tổng số lượng:
          <b><i>{{totalQuantity | number}}</i></b>
        </span>
      </nz-col>
      <nz-col nzSpan="12" class="text-right">
        <span style="font-size: medium;">Tổng trị giá:
          <b style="color: blue;"><i>{{totalMoney | number}} VNĐ</i></b>
        </span>
      </nz-col>
    </div>

    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
        [nzLoading]="loading" [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th class="text-center">Mã loại vật tư</th>
            <th class="text-center">Tên loại vật tư</th>
            <th class="text-center">Số lượng tồn kho </th>
            <th class="text-center">Giá vốn tồn kho</th>
            <th class="text-center">Đơn vị cơ sở</th>
            <th class="text-center">Giá trị tồn kho (VNĐ)</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let detail of ajaxTable.data">
            <td class="text-center">{{ detail.materialCategoryCode}}</td>
            <td class="text-center">{{ detail.materialCategoryName}}</td>
            <td class="text-center">{{ detail.quantity || '' | number}}</td>
            <td class="text-center">{{ detail.price | number}}</td>
            <td class="text-center">{{ detail.baseUnitName}}</td>
            <td class="text-center">{{ (+detail.price * +detail.quantity)| number }}</td>
          </tr>
        </tbody>
      </nz-table>
      <div nz-col nzSpan="24" class="text-right">
        <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
          (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
          nzShowSizeChanger>
        </nz-pagination>
        <ng-template #rangeTemplate let-range="range" let-total>
          Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng
        </ng-template>
      </div>
    </div>
  </nz-tab>

  <nz-tab nzTitle="Lịch sử">
    <app-report-material-detail-history></app-report-material-detail-history>
  </nz-tab>
</nz-tabset>