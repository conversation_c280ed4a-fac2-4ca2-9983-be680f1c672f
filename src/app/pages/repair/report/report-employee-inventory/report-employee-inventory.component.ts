import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ReportMaterialDetailComponent } from '../report-material-detail/report-material-detail.component'
import { ExcelV2Service, IColum } from '../../../../services/excelv2.service'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({ templateUrl: './report-employee-inventory.component.html' })
export class ReportEmployeeInventoryComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  listOfData: any[] = []
  dataSearch: any = {}
  loading = true
  lstEmployee: any[] = []
  totalQuantity: number = 0
  totalMoney: number = 0
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private excelService: ExcelV2Service
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.REPORT_MATERIAL_BY_EMPLOYEE, dataSearch).then((rs: any) => {
      if (rs) {
        this.loading = false
        this.total = rs.data[1]
        this.listOfData = rs.data[0]
        this.totalQuantity = rs.totalQuantity
        this.totalMoney = rs.totalMoney
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    Promise.all([this.apiNtssService.post(this.apiNtssService.EMPLOYEE.LOAD_DATA, {})]).then(async (rs) => {
      this.lstEmployee = rs[0]
    })
  }

  onShowDetail(object: any) {
    const data: any = { employeeId: object.id }
    this.dialog
      .open(ReportMaterialDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  lstHeader: IColum[] = [
    { code: 'fullName', name: 'Nhân viên', type: 'string' },
    { code: 'totalQuantity', name: 'SL đang giữ', type: 'number' },
    { code: 'totalMoney', name: 'Giá trị', type: 'number' },
  ]

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 100000000,
    }
    const res = await this.apiScService.postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY_DETAIL.REPORT_MATERIAL_BY_EMPLOYEE, dataSearch)
    res.data[0].push({ fullName: 'Tổng', totalQuantity: res.totalQuantity, totalMoney: res.totalMoney })
    await this.excelService.onDownloadExcelJs({ lstData: res.data[0], lstHeader: this.lstHeader, excelName: 'BC' })
    this.notifyService.hideloading()
  }
}
