<nz-tabset>
  <nz-tab nzTitle="Nhập kho">
    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable [nzData]="dataObject.lstInboundDetail"
        [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th><PERSON><PERSON><PERSON></th>
            <th>Mã phiếu nhập</th>
            <th><PERSON><PERSON> lượng</th>
            <th>Giá trị</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of ajaxTable.data">
            <td class="text-center">{{ data.approvedDate | date: ' dd/MM/yyyy' }}</td>
            <td class="text-center">{{ data.inboundCode }}</td>
            <td class="text-center">{{ data.inboundQuantity | number }}</td>
            <td class="text-center">{{ data.totalMoney | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-tab>
  <nz-tab nzTitle="Xuất kho">
    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable2 [nzData]="dataObject.lstOutboundDetail"
        [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th>Ngày duyệt</th>
            <th>Mã phiếu xuất</th>
            <th>Số lượng</th>
            <th>Giá trị</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of ajaxTable2.data">
            <td class="text-center">{{ data.approvedDate | date: ' dd/MM/yyyy' }}</td>
            <td class="text-center">{{ data.outboundCode }}</td>
            <td class="text-center">{{ data.outboundQuantity | number }}</td>
            <td class="text-center">{{ data.totalMoney | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-tab>
</nz-tabset>