import { Component, Inject, OnInit, Optional } from '@angular/core'
import { NotifyService } from '../../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  selector: 'app-report-material-in-out-detail',
  templateUrl: './report-material-in-out-detail.component.html',
})
export class ReportMaterialInOutDetailComponent implements OnInit {
  dataObject: any = {}
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<ReportMaterialInOutDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.notifyService.showloading()
    this.apiScService
      .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DETAIL_IN_OUT, this.data)
      .then((rs: any) => {
        this.dataObject = rs
        this.notifyService.hideloading()
      })
  }
}
