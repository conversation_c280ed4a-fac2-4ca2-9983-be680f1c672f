import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { ExcelV2Service, IColum } from '../../../../services/excelv2.service'
import { NotifyService } from '../../../../services/notify.service'
import { ReportWarehouseInOutDetailComponent } from '../report-warehouse-in-out/report-warehouse-in-out-detail/report-warehouse-in-out-detail.component'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  selector: 'app-report-warehouse-inventory',
  templateUrl: './report-warehouse-inventory.component.html',
})
export class ReportWarehouseInventoryComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstApartment: any[] = []
  totalQuantity: number = 0
  totalMoney: number = 0
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private excelService: ExcelV2Service
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService
      .postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.PAGINATION_REPORT_INVENTORY, dataSearch)
      .then((rs: any) => {
        if (rs) {
          this.loading = false
          this.total = rs[1]
          this.listOfData = rs[0]
          this.totalQuantity = rs?.[2]
          this.totalMoney = rs?.[3]
        }
      })
  }

  ngOnInit() {
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    // this.dataSearch.month = new Date()
    this.searchData()
  }

  onShowDetail(object: any) {
    const data: any = { warehouseMaterialId: object.id }
    this.dialog
      .open(ReportWarehouseInOutDetailComponent, {
        disableClose: false,
        data: { id: object.id, month: this.dataSearch.month },
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  lstHeader: IColum[] = [
    { code: 'name', name: 'Kho', type: 'string' },
    { code: 'totalQuantity', name: 'Số lượng tồn', type: 'number' },
    { code: 'totalMoney', name: 'Giá trị', type: 'number' },
  ]

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 100000000,
    }
    const [lstData, _num, totalQuantity, totalMoney] = await this.apiScService.postRepair(
      this.apiScService.REPAIR_WAREHOUSE_MATERIAL.PAGINATION_REPORT_INVENTORY,
      dataSearch
    )
    lstData.push({ name: 'Tổng', totalQuantity, totalMoney })
    await this.excelService.onDownloadExcelJs({ lstData, lstHeader: this.lstHeader, excelName: 'BC' })
    this.notifyService.hideloading()
  }
}
