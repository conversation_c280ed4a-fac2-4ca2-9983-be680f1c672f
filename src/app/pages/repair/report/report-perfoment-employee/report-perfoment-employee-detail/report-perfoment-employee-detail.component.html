<nz-tabset>
  <nz-tab [nzTitle]="title">
    <div nz-row nzGutter="8" class="mt-2">
      <nz-table class="mb-3" nz-col nzSpan="24" [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
        <thead>
          <tr>
            <th>Mã công việc</th>
            <th>Loại công việc</th>
            <th>Loại sửa chữa</th>
            <th>Tên công việc</th>
            <th>Tạo</th>
            <th>Duyệt công việc</th>
            <th>Nhận</th>
            <th>hoàn thành</th>
            <th>Du<PERSON><PERSON>t hoàn thành</th>
            <th>Tác vụ</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of lstDetail">
            <td class="text-center">{{ item.code }}</td>
            <td class="text-center">{{ item.typeName }}</td>
            <td class="text-center">{{ item.typeOfJobName }}</td>
            <td class="text-center">{{ item.name }}</td>
            <td class="text-center">{{ item.createdAt | date: 'dd/MM/YYYY HH:mm' }}</td>
            <td class="text-center">{{ item.approvedDate | date: 'dd/MM/YYYY HH:mm' }}</td>
            <td class="text-center">{{ item.receivingDate | date: 'dd/MM/YYYY HH:mm' }}</td>
            <td class="text-center">{{ item.completedDate | date: 'dd/MM/YYYY HH:mm' }}</td>
            <td class="text-center">{{ item.managerApprovedDate | date: 'dd/MM/YYYY HH:mm' }}</td>
            <td class="text-center">
              <button nzType="primary" nzGhost (click)="onDetail(item)" nz-tooltip nzTooltipTitle="Chi tiết công việc"
                nz-button nzShape="circle" class="mr-1 mb-1">
                <i nz-icon nzType="eye" nzTheme="outline"></i>
              </button>
            </td>

          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-tab>
</nz-tabset>

<div nz-row class="mt-3">
  <div nz-col nzSpan="24" class="text-center">
    <button nz-button nzShape="round" nzType="default" class="mr-3" (click)="closeDialog()">
      <i nz-icon nzType="lock" nzTheme="outline"></i> Đóng
    </button>
  </div>
</div>