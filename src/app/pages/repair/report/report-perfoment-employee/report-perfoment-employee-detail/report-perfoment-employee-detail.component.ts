import { Component, Inject, OnInit, Optional } from '@angular/core'
import { NotifyService } from '../../../../../services/notify.service'
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { JobDetailComponent } from '../../../job/job-detail/job-detail.component'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './report-perfoment-employee-detail.component.html' })
export class ReportPerfomentEmployeeDetailComponent implements OnInit {
  lstDetail: any = []
  dataObject: any = {}
  title = ''
  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialog: MatDialog,
    private dialogRef: MatDialogRef<ReportPerfomentEmployeeDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.dataObject = this.data
    this.title = this.dataObject.code + ' - ' + this.dataObject.fullName
    this.notifyService.showloading()
    this.apiScService
      .postRepair(this.apiScService.REPAIR_JOB.REPORT_PERFOMENT_EMPLOYEE_DETAIL, this.data)
      .then((rs: any) => {
        this.lstDetail = rs
        this.notifyService.hideloading()
      })
  }

  onDetail(object: any) {
    this.dialog.open(JobDetailComponent, { disableClose: false, data: object })
  }

  closeDialog() {
    this.dialogRef.close(0)
  }
}
