import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { ExcelV2Service, IColum } from '../../../../services/excelv2.service'
import { NotifyService } from '../../../../services/notify.service'
import { ReportPerfomentEmployeeDetailComponent } from './report-perfoment-employee-detail/report-perfoment-employee-detail.component'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'

@Component({ templateUrl: './report-perfoment-employee.component.html' })
export class ReportPerfomentEmployeeComponent implements OnInit {
  currentUser: User | any
  enumData: any = {}
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataEmployee: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private excelService: ExcelV2Service
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataSearch.month = new Date()
    this.searchData()
    this.loadAllDataSelect()
  }

  loadAllDataSelect() {
    this.apiNtssService.post(this.apiNtssService.EMPLOYEE.PAGINATION_FOR_REPAIR_STAFF, { where: {}, skip: 0, take: 1000000 }).then((res) => {
      this.dataEmployee = res[0]
    })
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_PERFOMENT_EMPLOYEE, dataSearch).then((rs: any) => {
      if (rs) {
        this.loading = false
        this.total = rs[1]
        this.listOfData = rs[0]
        // this.totalQuantity = rs.totalQuantity
        // this.totalMoney = rs.totalMoney
      }
    })
  }

  onShowDetail(object: any) {
    const data: any = { lstId: object.lstJobId, code: object.code, fullName: object.fullName }
    this.dialog.open(ReportPerfomentEmployeeDetailComponent, { disableClose: false, data })
  }

  lstHeader: IColum[] = [
    { code: 'fullName', name: 'Nhân viên', type: 'string' },
    { code: 'jobCount', name: 'Số lượng công việc', type: 'number' },
    { code: 'dayCompleteCount', name: 'Hoàn thành', type: 'number' },
    { code: 'dayApprovedFirstCount', name: 'Duyệt công việc', type: 'number' },
    { code: 'dayReceiveCount', name: 'Nhận', type: 'number' },
    { code: 'dayWorkCount', name: 'Làm', type: 'number' },
    { code: 'dayApprovedCount', name: 'Duyệt', type: 'number' },
  ]

  async onDownloadExcel() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 100000000,
    }
    const [lstData, _num] = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_PERFOMENT_EMPLOYEE, dataSearch)
    await this.excelService.onDownloadExcelJs({ lstData, lstHeader: this.lstHeader, excelName: 'BC' })
    this.notifyService.hideloading()
  }

  lstHeaderDetail: IColum[] = [
    { code: 'employeeName', name: 'Nhân viên', type: 'string' },
    { code: 'code', name: 'Mã công việc', type: 'string' },
    { code: 'typeName', name: 'Loại công việc', type: 'string' },
    { code: 'typeOfJobName', name: 'Loại sửa chữa', type: 'string' },
    { code: 'name', name: 'Tên công việc', type: 'string' },
    { code: 'createdAt', name: 'Tạo', type: 'datetime' },
    { code: 'approvedDate', name: 'Duyệt công việc', type: 'datetime' },
    { code: 'receivingDate', name: 'Nhận', type: 'datetime' },
    { code: 'completedDate', name: 'Hoàn thành', type: 'datetime' },
    { code: 'managerApprovedDate', name: 'Duyệt hoàn thành', type: 'datetime' },
  ]

  async onDownloadExcelDetail() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 100000000,
    }
    const [lstDataEmp, _num] = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_PERFOMENT_EMPLOYEE, dataSearch)
    const lstId = []
    for (let emp of lstDataEmp) {
      lstId.push(...emp.lstJobId)
    }
    const lstData = await this.apiScService.postRepair(this.apiScService.REPAIR_JOB.REPORT_PERFOMENT_EMPLOYEE_DETAIL, {
      lstId,
    })
    await this.excelService.onDownloadExcelJs({
      lstData,
      lstHeader: this.lstHeaderDetail,
      excelName: 'BC chi tiết',
      fileName: 'BC_chi_tiet',
    })
    this.notifyService.hideloading()
  }
}
