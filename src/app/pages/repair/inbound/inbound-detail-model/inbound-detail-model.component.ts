import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  templateUrl: './inbound-detail-model.component.html',
})
export class InboundDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU NHẬP KHO'
  dataObject: any
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.notifySerivce.showloading()
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_INBOUND.FIND_DETAIL, {
        id: this.data.id,
      })
      .then((rs: any) => {
        if (rs.lstInboundImages && rs.lstInboundImages.length > 0) {
          for (const img of rs.lstInboundImages) {
            if (!img.src) img.src = img.url
          }
        }
        this.dataObject = rs
      })
    this.notifySerivce.hideloading()
  }
}
