import { Component, Inject, Input, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { CoreService } from '../../../../../../services/core.service'
import { NotifyService } from '../../../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  selector: 'app-inbound-history',
  templateUrl: './inbound-history.component.html',
})
export class InboundHistoryComponent implements OnInit {
  @Input() dataObject: any
  constructor(
    public coreService: CoreService,
    public notifySerivce: NotifyService,
    public apiScService: ApiScService,
    private dialogRef: MatDialogRef<InboundHistoryComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {}

  closeDialog() {
    this.dialogRef.close(1)
  }
}
