<div nz-row nzGutter="8" class="mt-2">
  <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable1 [nzData]="dataObject.__inboundHistories__"
    [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
    <thead>
      <tr>
        <th class="text-center" nzWidth="160px">Thời gian</th>
        <th class="text-center">Nội dung</th>
        <th class="text-center" nzWidth="160px"><PERSON><PERSON><PERSON><PERSON> cập nhật</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let detail of ajaxTable1.data">
        <td class="text-center">{{ detail.createdAt | date: 'HH:mm dd/MM/YYYY'}}</td>
        <td class="text-center">{{ detail.description}}</td>
        <td class="text-center">{{ detail.createdByName}}</td>
      </tr>
    </tbody>
  </nz-table>
</div>