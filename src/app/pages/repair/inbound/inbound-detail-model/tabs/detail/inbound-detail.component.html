<div nz-row class="mt-4">
  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">M<PERSON> phiếu nhập kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.code }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Kho</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.warehouseMaterialName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.supplierName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Loại phiếu</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.typeName }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdAt | date: ' HH:mm dd/MM/YYYY'}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người tạo</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.createdByName}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedDate">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Ngày duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedDate | date: ' HH:mm dd/MM/YYYY'}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.approvedByName">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Người duyệt</nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.approvedByName}}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="6" *ngIf="dataObject.description">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Mô tả
      </nz-form-label>
      <nz-form-control>
        <b>{{ dataObject.description }}</b>
      </nz-form-control>
    </nz-form-item>
  </div>

  <div nz-col nzSpan="24" *ngIf="!dataObject?.lstInboundImages || dataObject?.lstInboundImages?.length === 0">
    <label>Không có hình ảnh chứng
      từ</label>
  </div>
  <div nz-col nzSpan="24"
    *ngIf="dataObject && dataObject?.lstInboundImages && dataObject?.lstInboundImages?.length > 0">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hình ảnh phiếu nhập kho
      </nz-form-label>
      <nz-form-control nzSpan="24" style="display: flex;">
        <nz-row nzGutter="4">
          <ng-container nz-col nzSpan="2" *ngFor="let item of dataObject?.lstInboundImages || []; let i = index">
            <img nz-image width="100px" height="100px" nzSrc="{{item.url}}" alt="" />
          </ng-container>
        </nz-row>
      </nz-form-control>
    </nz-form-item>
  </div>


  <div nz-col nzSpan="24" *ngIf="dataObject?.lstInboundDetails">
    <div nz-row class="mt-3">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstInboundDetails" [nzShowPagination]="false" #ajaxTable
        [nzFrontPagination]="false" nzTemplateMode nzBordered [nzScroll]="{ x:'1200px' }">
        <thead>
          <tr>
            <th class="text-center" nzWidth="180px" nzLeft>Mã vật tư</th>
            <th class="text-center" nzWidth="180px">Tên vật tư</th>
            <th class="text-center" nzWidth="180px">Đơn vị tính</th>
            <th class="text-center" nzWidth="180px">Số lượng</th>
            <th class="text-center" nzWidth="180px">Tỉ lệ quy đổi</th>
            <th class="text-center" nzWidth="180px">Đơn vị cơ sở</th>
            <th class="text-center" nzWidth="180px">Số lượng nhập</th>
            <th class="text-center" nzWidth="180px">Đơn giá</th>
            <th class="text-center" nzWidth="180px" nzRight>Thành tiền</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataObject?.lstInboundDetails || []; let i = index">

            <td class="text-center" nzLeft>{{ item.materialCategoryCode }}</td>
            <td class="text-center">{{ item.materialCategoryName }}</td>
            <td class="text-center">{{ item.unitName }}</td>
            <td class="text-center">{{ item.quantity | number }}</td>
            <td class="text-center">{{ item.conversionRate | number }}</td>
            <td class="text-center">{{ item.baseUnitName }}</td>
            <td class="text-center">{{ item.inboundQuantity | number }}</td>
            <td class="text-center">{{ item.price | number }}</td>
            <td class="text-center" nzRight>{{ (+item.inboundQuantity * +item.price) | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </div>
</div>