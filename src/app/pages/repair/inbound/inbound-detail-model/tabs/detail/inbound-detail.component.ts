import { Component, Inject, Input, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzImageService } from 'ng-zorro-antd/image'
import { AuthenticationService } from '../../../../../../services/authentication.service'
import { CoreService } from '../../../../../../services/core.service'
import { NotifyService } from '../../../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({
  selector: 'app-inbound-detail',
  templateUrl: './inbound-detail.component.html',
})
export class InboundDetailComponent implements OnInit {
  @Input() dataObject: any
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU NHẬP KHO'
  effect = 'scrollx'

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    public apiScService: ApiScService,
    public notifySerivce: NotifyService,
    private nzImageService: NzImageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {}

  openImagePreview(images: any[]) {
    this.nzImageService.preview(images, { nzZoom: 1 })
  }
}
