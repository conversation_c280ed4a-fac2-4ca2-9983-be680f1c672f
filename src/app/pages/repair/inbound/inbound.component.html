<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <!-- <button nzShape="round" class="mr-2" nz-button nzType="primary" *ngIf="coreService.checkPermission(role.Add.code)"
    (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm
    mới</button>
  <button nzShape="round" *ngIf="coreService.checkPermission(role.Download.code)" class="mr-2" nz-button
    (click)="onDownloadTemplateExcel()"><span nz-icon nzType="download"></span>Tải
    Template Excel</button>
  <input class="hidden" type="file" id="file" (change)="clickImportExcel($event)" placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
  <label *ngIf="coreService.checkPermission(role.Upload.code)" nz-button for="file"
    class="ant-btn lable-custom-file-custom mr-2">
    <span nz-icon nzType="upload"></span> Nhập Excel
  </label>
  <button class="mr-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
  </button> -->
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Mã phiếu </nz-form-label>
            <nz-form-control nzSpan="24">
              <input nz-input [(ngModel)]="dataSearch.code" name="dataSearch.code" placeholder="Nhập mã phiếu" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Mã phiếu tổng </nz-form-label>
            <nz-form-control nzSpan="24">
              <input
                nz-input
                [(ngModel)]="dataSearch.buyingMaterialSummaryCode"
                name="dataSearch.buyingMaterialSummaryCode"
                placeholder="Nhập mã phiếu tổng"
              />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Loại </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.type" name="type" nzPlaceHolder="Chọn loại">
                <nz-option *ngFor="let item of lstRepairInboundType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Kho </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select
                (ngModelChange)="loadMaterialCategory($event)"
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataSearch.warehouseMaterialId"
                name="warehouseMaterialId"
                nzPlaceHolder="Chọn kho"
              >
                <nz-option *ngFor="let item of lstWarehouseCategory" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.supplierId" name="warehouseMaterialId" nzPlaceHolder="Chọn nhà cung cấp">
                <nz-option *ngFor="let item of lstSupplier" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Vật tư </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.materialCategoryId" name="type" nzPlaceHolder="Chọn vật tư">
                <nz-option *ngFor="let item of lstMaterialCategory" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Trạng thái </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Chọn trạng thái">
                <nz-option *ngFor="let item of lstInboundStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Ngày tạo </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-date-picker nzFormat="dd/MM/yyyy" [(ngModel)]="dataSearch.createdAt" name="createdAt"> </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Ngày duyệt </nz-form-label>
            <nz-form-control nzSpan="24">
              <!-- <nz-date-picker nzFormat="dd/MM/yyyy" [(ngModel)]="dataSearch.approvedDate" name="approvedDate">
              </nz-date-picker> -->
              <nz-range-picker style="width: 100%" [(ngModel)]="dataSearch.approvedDateRangeDate"></nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div nz-row nzGutter="8" class="mt-2 table-scroll-item">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
    [nzScroll]="{ x: '1200px' }"
  >
    <thead>
      <tr>
        <th class="text-center">Mã</th>
        <th class="text-center">Kho</th>
        <th class="text-center">Nhà cung cấp</th>
        <th class="text-center">Ngày tạo</th>
        <th class="text-center">Người tạo</th>
        <th class="text-center">Ngày duyệt</th>
        <th class="text-center">Người duyệt</th>
        <th class="text-center">Loại phiếu</th>
        <th class="text-center" nzWidth="140px">Trạng thái</th>
        <th class="text-center" nzWidth="140px">Trạng thái thánh toán</th>
        <th class="text-center" nzRight>Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData">
        <td class="text-center">{{ data.code }}</td>
        <td class="text-center">{{ data.warehouseMaterialName }}</td>
        <td class="text-center">{{ data.supplierName }}</td>
        <td class="text-center">{{ data.createdAt | date : 'HH:mm dd/MM/YYYY' }}</td>
        <td class="text-center">{{ data.createdByName }}</td>
        <td class="text-center">{{ data.approvedDate | date : 'HH:mm dd/MM/YYYY' }}</td>
        <td class="text-center">{{ data.approvedByName }}</td>
        <td class="text-center">{{ data.typeName }}</td>
        <td class="text-center">
          <nz-tag style="width: 100%" [nzColor]="data?.statusColor" [nzBordered]="true">{{ data.statusName }}</nz-tag>
        </td>
        <td class="text-center">
          <nz-tag style="width: 100%" [nzColor]="data?.paymentStatusColor" [nzBordered]="true">{{ data.paymentStatusName }}</nz-tag>
        </td>
        <td class="text-center" nzRight>
          <button
            class="mr-2 mt-2"
            (click)="onShowDetail(data)"
            nz-tooltip
            nzTooltipTitle="Chi tiết"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <i nz-icon nzType="eye"></i>
          </button>
          <button
            class="mr-2 mt-2"
            *ngIf="!data.isDeleted && data.status === enumDataNew"
            (click)="onShowEdit(data)"
            nz-tooltip
            nzTooltipTitle="Thêm hình ảnh chứng từ"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <span nz-icon nzType="upload" nzTheme="outline"></span>
          </button>
          <button
            *ngIf="data.status === enumDataNew"
            nzType="primary"
            class="mr-2 mt-2"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn duyệt phiếu nhập kho?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="updateApprove(data)"
            nz-tooltip
            nzTooltipTitle="Duyệt phiếu nhập kho"
            nz-button
            nzType="primary"
            nzShape="circle"
          >
            <i nz-icon nzType="check" nzTheme="outline"></i>
          </button>
          <button
            style="background: green"
            *ngIf="data.status === enumDataNew"
            nzType="primary"
            class="mr-2 mt-2"
            nz-tooltip
            nzTooltipTitle="Tách phiếu nhập kho"
            nz-button
            nzPrimary
            nzShape="circle"
            (click)="onShowEdit(data, true)"
          >
            <span nz-icon nzType="split-cells" nzTheme="outline"></span>
          </button>
          <button
            *ngIf="data.status === enumDataNew"
            nzType="primary"
            class="mr-2 mt-2"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn huỷ phiếu nhập kho"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="updateCancel(data)"
            nz-tooltip
            nzTooltipTitle="Huỷ phiếu nhập kho"
            nz-button
            nzGhost
            nzDanger="true"
            nzShape="circle"
          >
            <i nz-icon nzType="close" nzTheme="outline"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
