import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { isInteger } from 'mathjs'
import * as moment from 'moment'
import * as XLSX from 'xlsx'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditInboundModelComponent } from './add-or-edit-inbound-model/add-or-edit-inbound-model.component'
import { InboundDetailModelComponent } from './inbound-detail-model/inbound-detail-model.component'
import { ApiScService } from 'src/app/services/apiSc.service'
import { ApiNtssService } from 'src/app/services'
import { enumData } from 'src/app/core'
@Component({ templateUrl: './inbound.component.html' })
export class InboundComponent implements OnInit {
  currentUser: User | any
  enumData = enumData
  enumDataNew = enumData.RepairInboundStatus.NEW.code
  pageIndex: number = 0
  pageSize: number = 0
  pageSizeMax: number = 0
  total: number = 0
  lstInboundStatus: any[] = []
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstWarehouseCategory: any[] = []
  lstRepairInboundType: any[] = [
    { code: 'IMPORT_INVENTORY', name: 'Nhập tồn' },
    { code: 'RETURN_MATERIAL', name: 'Trả vật tư' },
    { code: 'TRANSFER_WAREHOUSE', name: 'Chuyển kho' },
    { code: 'CHECK_INVENTORY', name: 'Kiểm kho' },
    { code: 'BUYING_IMPORT_INVENTORY', name: 'Mua vật tư nhập kho' },
  ]

  lstMaterialCategory: any[] = []
  lstRecieveBy: any[] = []
  lstSupplier: any[] = []
  lstUnit: any[] = []

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private apiNtssService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.loadAllData()
    this.lstInboundStatus = this.coreService.convertObjToArray(this.enumData.RepairInboundStatus)
    this.dataSearch.isDeleted = false
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async loadAllData() {
    Promise.all([
      this.apiScService.postRepair(this.apiScService.REPAIR_WAREHOUSE_MATERIAL.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.EMPLOYEE.LOAD_DATA, {}),
      this.apiNtssService.post(this.apiNtssService.SUPPLIER.LOAD_DATA, {}),
      this.apiScService.postRepair(this.apiScService.REPAIR_UNIT.LOAD_DATA, {}),
      this.loadMaterialCategory(null),
    ]).then(async (rs) => {
      this.lstWarehouseCategory = rs[0]
      this.lstRecieveBy = rs[1]
      this.lstSupplier = rs[2]
      this.lstUnit = rs[3]
    })
  }

  async loadMaterialCategory(id: string | null) {
    await this.apiScService
      .postRepair(this.apiScService.REPAIR_MATERIAL_CATEGORY.LOAD_DATA, {
        warehouseMaterialId: id,
      })
      .then((res) => {
        this.lstMaterialCategory = res
      })
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditInboundModelComponent, {
        disableClose: false,
      })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any, isSplit: boolean = false) {
    this.dialog
      .open(AddOrEditInboundModelComponent, { disableClose: false, data: { ...object, isSplit: isSplit } })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(InboundDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách phiếu nhập kho')

    //#region Body Table
    const header = [
      'STT * (code)',
      'Mã kho * (warehouseMaterialCode)',
      'Nhà cung cấp (supplierTaxCode)',
      'Người nhận * (receivedByCode)',
      'Mã loại vật tư * (materialCategoryCode)',
      'Mã đơn vị tính * (unitCode)',
      'Số lượng * (quantity)',
      'Đơn giá * (price)',
      'Mô tả (description)',
    ]
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
        case 8:
        case 9:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    // #region Danh sách vật tư
    const worksheet1 = workbook.addWorksheet('Danh sách vật tư')

    const header1 = ['Mã vật tư * (code)', 'Tên vật tư * (name)']
    const headerRow1 = worksheet1.addRow(header1)

    // Add Data and Conditional Formatting
    for (let data of this.lstMaterialCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet1.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow1.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet1.getColumn(colNumber).width = 30
          break
        default:
          worksheet1.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    // #region Danh sách kho
    const worksheet2 = workbook.addWorksheet('Danh sách kho')

    const header2 = ['Mã kho * (code)', 'Tên kho * (name)']
    const headerRow2 = worksheet2.addRow(header2)

    // Add Data and Conditional Formatting
    for (let data of this.lstWarehouseCategory) {
      const rowData = [data.code || '', data.name || '']
      worksheet2.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    // #region Danh sách người nhận
    const worksheet3 = workbook.addWorksheet('Danh sách người nhận')

    const header3 = ['Mã * (code)', 'Tên * (name)']
    const headerRow3 = worksheet3.addRow(header3)

    // Add Data and Conditional Formatting
    for (let data of this.lstRecieveBy) {
      const rowData = [data.code || '', data.fullName || '']
      worksheet3.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow3.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet3.getColumn(colNumber).width = 30
          break
        default:
          worksheet3.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    // #region Danh sách nhà cung cấp
    const worksheet4 = workbook.addWorksheet('Danh sách nhà cung cấp')

    const header4 = ['Mã số thuế * (taxCode)', 'Tên * (name)']
    const headerRow4 = worksheet4.addRow(header4)

    // Add Data and Conditional Formatting
    for (let data of this.lstSupplier) {
      const rowData = [data.taxCode || '', data.name || '']
      worksheet4.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow4.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet4.getColumn(colNumber).width = 30
          break
        default:
          worksheet4.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    // #region Danh sách nhà cung cấp
    const worksheet5 = workbook.addWorksheet('Danh sách đơn vị tính')

    const header5 = ['Mã * (code)', 'Tên * (name)']
    const headerRow5 = worksheet5.addRow(header5)

    // Add Data and Conditional Formatting
    for (let data of this.lstUnit) {
      const rowData = [data.code || '', data.name || '']
      worksheet5.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow5.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
        case 2:
          worksheet5.getColumn(colNumber).width = 30
          break
        default:
          worksheet5.getColumn(colNumber).width = 12
          break
      }
    })
    // #endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_PHIEU_NHAP_KHO${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  lstHeaderRequire: string[] = ['code', 'warehouseMaterialCode', 'receivedByCode', 'materialCategoryCode', 'unitCode', 'quantity', 'price']

  dicHeader: Record<string, any> = {
    code: 'Số thứ tự',
    warehouseMaterialCode: 'Mã kho',
    receivedByCode: 'Mã người nhận',
    materialCategoryCode: 'Mã loại vật tư',
    unitCode: 'Mã đơn vị tính',
    quantity: 'Số lượng',
    price: 'Đơn giá',
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'code',
          'warehouseMaterialCode',
          'supplierTaxCode',
          'receivedByCode',
          'materialCategoryCode',
          'unitCode',
          'quantity',
          'price',
          'description',
        ],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 1
        for (let hd of this.lstHeaderRequire) {
          if (!row[hd]) strErr += 'Dòng ' + idx + ` - ${this.dicHeader[hd]} không được để trống <br>`
        }
        if (row.price && (isNaN(row.price) || +row.price < 0 || !isInteger(row.price))) {
          strErr += 'Dòng ' + idx + ` - Đơn giá phải là số nguyên lớn hơn 0 <br>`
        }

        if (row.quantity && (isNaN(row.quantity) || +row.quantity < 0 || !isInteger(row.quantity))) {
          strErr += 'Dòng ' + idx + ` - Số lượng xuất phải là số nguyên lớn hơn 0 <br>`
        }
      }
      if (strErr.length > 0) {
        this.notifyService.hideloading()
        this.notifyService.showError(strErr)
        return
      }
      this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.CREATE_DATA_BY_EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        if (result) {
          this.notifyService.showSuccess('Thêm file excel thành công')
          this.searchData()
        }
      })
    }
  }

  updateCancel(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.UPDATE_CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  updateApprove(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.UPDATE_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  cancelPayment(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.CANCEL_PAYMENT, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }
  confirmPayment(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.CONFIRM_PAYMENT, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_INBOUND.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách PNK')
        //#region Body Table
        const header = ['Mã', 'Kho', 'Ngày tạo', 'Ngày duyệt', 'Loại phiếu', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.warehouseMaterialName || '',
            data.createdAt ? moment(data.createdAt).format('DD/MM/YYYY') : '',
            data.approvedDate ? moment(data.approvedDate).format('DD/MM/YYYY') : '',
            data.typeName || '',
            data.statusName || '',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_PHIEU_NHAP_KHO_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
