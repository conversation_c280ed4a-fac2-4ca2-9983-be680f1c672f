<nz-row>
  <nz-col nzSpan="24" class="text-center fs-24 fw-600">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<nz-row class="mt-4 mb-4">
  <nz-col nzSpan="4">
    <nz-form-label nzSpan="24" class="text-left">Loại thanh toán</nz-form-label>
  </nz-col>

  <nz-col nzSpan="8">
    <nz-radio-group required nzDisabled [(ngModel)]="dataObject.paymentType" name="paymentType">
      <label nz-radio nzValue="N">Thông thường</label>
      <!-- Normal -->
      <label nz-radio nzValue="A">Tạm ứng</label>
      <!-- Advance -->
    </nz-radio-group>
  </nz-col>
</nz-row>

<nz-row nzGutter="32">
  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON> <PERSON>ồ sơ thanh toán</nz-form-label>
      <nz-form-control class="input-color" nzSpan="24">
        <span>{{ dataObject.code }}</span>
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Tiêu đề hồ sơ thanh toán</nz-form-label>
      <nz-form-control class="input-color" nzSpan="24">
        {{ dataObject.name }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp</nz-form-label>
      <nz-form-control class="input-color" nzSpan="24">
        {{ dataObject.supplierName }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>
</nz-row>
<nz-row class="mt-3" nzGutter="8">
  <!-- Danh sách hóa đơn -->
  <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hóa đơn</nz-form-label>
      <nz-form-control class="input-color" nzSpan="24" nzErrorTip="Vui lòng chọn danh sách hóa đơn !">
        {{ dataObject.paymentBillCodes }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <!-- Po -->
  <nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">PO</nz-form-label>
      <nz-form-control class="input-color" nzSpan="24" nzErrorTip="Vui lòng chọn PO!">
        {{ dataObject.paymentPoCodes }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <!-- Hợp đồng -->
  <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" class="text-left">Hợp đồng</nz-form-label>
      <nz-form-control class="input-color" nzSpan="24" nzErrorTip="Vui lòng chọn hợp đồng !">
        {{ dataObject.paymentContractCodes }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <!-- Đơn vị tiền tệ -->
  <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.A.code">
    <nz-form-item nzFlex>
      <nz-form-label nzSpan="24" nzRequired class="text-left">Đơn vị tiền tệ</nz-form-label>
      <nz-form-control nzSpan="24" class="input-color">
        {{ dataObject.currencyName }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <!-- số tiền tạm ứng -->
  <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.A.code">
    <nz-form-item>
      <nz-form-label nzSpan="24" class="text-left" nzRequired> Số tiền tạm ứng </nz-form-label>
      <nz-form-control nzSpan="24" class="input-color">
        {{ dataObject.moneyAdvance | number }}
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8">
    <nz-form-item>
      <nz-form-label nzSpan="24" nzFor="fileAttach" class="text-left"> File đính kèm </nz-form-label>
      <nz-form-control nzSpan="24">
        <div class="tooltip" *ngIf="dataObject.fileAttach && dataObject.fileAttach.length > 0">
          <a href="{{ dataObject.fileAttach }}" target="_blank">{{ dataObject.fileAttach.split('/').pop() }}</a>
        </div>
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
    <nz-form-item>
      <nz-form-label nzSpan="24" nzFor="filePaymentRequest" class="text-left"> File đề nghị thanh toán </nz-form-label>
      <nz-form-control nzSpan="24">
        <div class="tooltip" *ngIf="dataObject.filePaymentRequest && dataObject.filePaymentRequest.length > 0">
          <a href="{{ dataObject.filePaymentRequest }}" target="_blank">{{ dataObject.filePaymentRequest.split('/').pop() }}</a>
        </div>
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
    <nz-form-item>
      <nz-form-label nzSpan="24" nzFor="fileAcceptanceReport" class="text-left"> File biên bản nghiệm thu </nz-form-label>
      <nz-form-control nzSpan="24">
        <div class="tooltip" *ngIf="dataObject.fileAcceptanceReport && dataObject.fileAcceptanceReport.length > 0">
          <a href="{{ dataObject.fileAcceptanceReport }}" target="_blank">{{ dataObject.fileAcceptanceReport.split('/').pop() }}</a>
        </div>
      </nz-form-control>
    </nz-form-item>
  </nz-col>

  <nz-col nzSpan="24">
    <nz-form-item>
      <nz-form-label nzSpan="24" class="text-left">Ghi chú</nz-form-label>
      <nz-form-control nzSpan="24">
        <textarea disabled nz-input placeholder="Mô tả" [(ngModel)]="dataObject.note" name="note" rows="7" auto></textarea>
      </nz-form-control>
    </nz-form-item>
  </nz-col>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center mt-4">
    <button (click)="onBack()" nz-button class="mr-4 button-exit"><span nz-icon nzType="close"></span>Thoát</button>

    <button *ngIf="dataObject.status === enumDataStatus.CHECKING.code" class="mr-4 button-save" nz-button nzType="primary" (click)="onConfirmation()">
      <span nz-icon nzType="check"></span>Xác nhận hợp lệ
    </button>
    <button
      *ngIf="dataObject.status === enumDataStatus.CHECKING.code"
      nz-button
      nzType="primary"
      nzGhost
      (click)="onRecheck(dataObject)"
      class="mr-2"
    >
      <span nz-icon nzType="redo"></span>
      Kiểm tra lại
    </button>
  </nz-col>
</nz-row>
