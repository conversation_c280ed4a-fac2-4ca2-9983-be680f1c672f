import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Router, ActivatedRoute } from '@angular/router'
import { NotifyService, CoreService, StorageService, ApiService } from 'src/app/services'
import { enumData } from '../../../core'

@Component({
  templateUrl: './payment-detail.component.html',
  styleUrls: ['./payment-detail.component.scss'],
})
export class PaymentDetailComponent {
  dataObject: any = {}
  isEditItem = false
  modalTitle = 'Chi tiết thanh toán'
  language_key: any
  lstCompany: any = []
  id: any
  lstPO: any = []
  lstContract: any = []
  lstBill: any = []
  enumDataStatus = enumData.PaymentStatus
  paymentType = enumData.paymentType
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.loadAllDataSelect()
    this.id = this.route.snapshot.paramMap.get('id')
    this.onLoadDetail(this.id)
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.BILL.FIND, {
        paymentStatus: enumData.BillPaymentStatus.NEW.code,
        status: enumData.BillStatus.CONFIRMED.code,
      }),
      this.apiService.post(this.apiService.PO.FIND, {}),
      this.apiService.post(this.apiService.CONTRACT.FIND, {}),
    ]).then(async (res) => {
      this.lstBill = res[0]
      this.lstPO = res[1]
      this.lstContract = res[2]
    })
    this.notifyService.hideloading()
  }

  onLoadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.FIND_DETAIL, { id: id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res
      }
    })
  }

  onBack() {
    this.router.navigate(['/payment'])
  }

  downloadFileFromS3(url: string) {
    window.open(url, '_blank')
  }
  onConfirmation() {}

  onRecheck(data: any) {
    this.apiService.post(this.apiService.PAYMENT.RECHECK, { id: data.id }).then((result) => {
      if (result) {
        this.notifyService.showSuccess(result.message)
      }
    })
  }
}
