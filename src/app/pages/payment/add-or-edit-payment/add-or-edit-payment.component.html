<nz-row>
  <nz-col nzSpan="32" class="text-center fs-24 fw-600">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<form nz-form #frmAdd="ngForm">
  <nz-row class="mt-4 mb-4">
    <nz-col nzSpan="4">
      <nz-form-label nzSpan="24" nzRequired class="text-left" nzRequired>Loại thanh toán</nz-form-label>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-radio-group required [(ngModel)]="dataObject.paymentType" name="paymentType">
        <label nz-radio nzValue="N">Thông thường</label>
        <!-- Normal -->
        <label nz-radio nzValue="A">Tạm ứng</label>
        <!-- Advance -->
      </nz-radio-group>
    </nz-col>
  </nz-row>

  <nz-row nzGutter="32  ">
    <!-- <PERSON><PERSON> hồ sơ thanh toán -->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã hồ sơ thanh toán</nz-form-label>
        <nz-form-control nzSpan="24">
          <input disabled nz-input [(ngModel)]="dataObject.code" name="code" required pattern=".{1,100}" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Tiêu đề-->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiêu đề hồ sơ thanh toán</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tiêu đề hồ sơ thanh toán!">
          <input nz-input placeholder="Nhập 1-250 kí tự" [(ngModel)]="dataObject.name" name="name" required pattern=".{1,50}" autocomplete="off" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Nhà cung cấp  -->
    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Nhà cung cấp</nz-form-label>

        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn nhà cung cấp">
          <nz-select
            nzShowSearch
            nzAllowClear
            nzPlaceHolder="Chọn Nhà cung cấp"
            [(ngModel)]="dataObject.supplierId"
            name="supplierId"
            required
            [nzDropdownStyle]="{ color: 'green' }"
          >
            <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>
  <nz-row nzGutter="32">
    <!-- Danh sách hóa đơn -->
    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Hóa đơn</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn danh sách hóa đơn !">
          <nz-select
            nzMode="multiple"
            required
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.billIds"
            name="billIds"
            nzPlaceHolder="Chọn danh sách hóa đơn "
            (ngModelChange)="onChangeBill($event)"
          >
            <nz-option *ngFor="let item of lstBill" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Po -->
    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">PO</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn PO!">
          <nz-select disabled nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataObject.poIds" name="poIds" nzPlaceHolder="PO">
            <nz-option *ngFor="let item of lstPO" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Hợp đồng -->
    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Hợp đồng</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn hợp đồng !">
          <nz-select
            disabled
            nzMode="multiple"
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.contractIds"
            name="contractIds"
            nzPlaceHolder="Chọn hợp đồng "
          >
            <nz-option *ngFor="let item of lstContract" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Po -->
    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.A.code">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>PO</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn PO!">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.poIds" name="poIds" nzPlaceHolder="PO" required>
            <nz-option *ngFor="let item of lstPO" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Đơn vị tiền tệ -->
    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.A.code">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" nzRequired class="text-left">Đơn vị tiền tệ</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn đơn vị tiền tệ">
          <nz-select
            nzShowSearch
            nzAllowClear
            nzPlaceHolder="Chọn đơn vị tiền tệ"
            [(ngModel)]="dataObject.settingStringId"
            name="settingStringId"
            required
          >
            <nz-option *ngFor="let item of lstCurrency" [nzLabel]="item.code + '-' + item.name" [nzValue]="item.id"> </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- số tiền tạm ứng -->
    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.A.code">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired> Số tiền tạm ứng </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số tiền tạm ứng!">
          <input nz-input currencyMask name="moneyAdvance" [(ngModel)]="dataObject.moneyAdvance" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileAttach" class="text-left" nzRequired>File đính kèm</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file file đính kèm">
          <label for="fileAttach" class="custom-file-upload">
            <span nz-icon nzType="upload"></span> {{ language_key?.SupplierRegistration_UploadFile || 'Upload File' }}
          </label>
          <input
            class="hidden"
            type="file"
            name="fileAttach"
            id="fileAttach"
            [(ngModel)]="dataObject.fileAttach"
            (change)="handleFileInput($event, 'fileAttach')"
          />
          <div class="tooltip" *ngIf="dataObject.fileAttach && dataObject.fileAttach.length > 0">
            <a href="{{ dataObject.fileAttach }}" target="_blank"> {{ language_key?.SupplierRegistration_ViewFile || 'Xem file' }} </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="filePaymentRequest" nzRequired class="text-left"> File đề nghị thanh toán </nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="'Vui lòng upload file đề nghị thanh toán'">
          <label for="filePaymentRequest" class="custom-file-upload">
            <span nz-icon nzType="upload"></span> {{ language_key?.SupplierRegistration_UploadFile || 'Upload File' }}
          </label>
          <input
            class="hidden"
            type="file"
            id="filePaymentRequest"
            [(ngModel)]="dataObject.filePaymentRequest"
            name="filePaymentRequest"
            (change)="handleFileInput($event, 'filePaymentRequest')"
          />
          <div class="tooltip" *ngIf="dataObject.filePaymentRequest && dataObject.filePaymentRequest.length > 0">
            <a href="{{ dataObject.filePaymentRequest }}" target="_blank"> {{ language_key?.SupplierRegistration_ViewFile || 'Xem file' }} </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8" *ngIf="dataObject.paymentType === paymentType.N.code">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileAcceptanceReport" nzRequired class="text-left"> File biên bản nghiệm thu </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload Giấy phép kinh doanh có điều kiện">
          <label for="fileAcceptanceReport" class="custom-file-upload">
            <span nz-icon nzType="upload"></span> {{ language_key?.SupplierRegistration_UploadFile || 'Upload File' }}
          </label>
          <input
            class="hidden"
            type="file"
            id="fileAcceptanceReport"
            [(ngModel)]="dataObject.fileAcceptanceReport"
            name="fileAcceptanceReport"
            (change)="handleFileInput($event, 'fileAcceptanceReport')"
          />
          <div class="tooltip" *ngIf="dataObject.fileAcceptanceReport && dataObject.fileAcceptanceReport.length > 0">
            <a href="{{ dataObject.fileAcceptanceReport }}" target="_blank">
              {{ language_key?.SupplierRegistration_ViewFile || 'Xem file' }}
            </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Ghi chú -->
    <nz-col [nzSpan]="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">Ghi chú</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea nz-input placeholder="Nhập ghi chú" [(ngModel)]="dataObject.note" name="note" rows="5"> </textarea>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-row class="mt-2" *ngIf="dataObject.paymentType === paymentType.N.code">
    <nz-col nzSpan="24" class="text-center">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit"><span nz-icon nzType="close"></span>Thoát</button>

      <button
        class="mr-4 button-save"
        nz-button
        [disabled]="!frmAdd.form.valid || !dataObject.fileAttach || !dataObject.filePaymentRequest || !dataObject.fileAcceptanceReport"
        nzType="primary"
        (click)="onSave()"
      >
        <span nz-icon nzType="save"></span>Lưu
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-2" *ngIf="dataObject.paymentType === paymentType.A.code">
    <nz-col nzSpan="24" class="text-center">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit"><span nz-icon nzType="close"></span>Thoát</button>

      <button class="mr-4 button-save" nz-button [disabled]="!frmAdd.form.valid || !dataObject.fileAttach" nzType="primary" (click)="onSave()">
        <span nz-icon nzType="save"></span>Lưu
      </button>
    </nz-col>
  </nz-row>
</form>
