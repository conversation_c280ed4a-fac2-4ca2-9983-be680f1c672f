<nz-row class="mb-3">
  <nz-breadcrumb>
    <nz-breadcrumb-item>
      <span nz-icon nzType="home"></span>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      <a>
        <span nz-icon nzType="home" nzTheme="outline"></span>
        <span>Quản trị mua hàng</span>
      </a>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>Thanh toán</nz-breadcrumb-item>
    <nz-breadcrumb-item>Danh sách thanh toán</nz-breadcrumb-item>
  </nz-breadcrumb>
</nz-row>

<nz-collapse>
  <nz-collapse-panel [nzHeader]="language_key?.SEARCH_ADVANCED || 'Tìm kiếm'">
    <nz-row class="mt-3" nzGutter="32">
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON> hồ sơ thanh toán</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.code" name="code" [placeholder]="'Tìm theo mã hồ sơ thanh toán'" class="input-enter" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tiêu đề hồ sơ thanh toán</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.name" name="name" [placeholder]="'Tìm theo mã hồ sơ thanh toán'" class="input-enter" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Trạng thái</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" [nzPlaceHolder]="'Chọn trạng thái thanh toán'">
              <nz-option *ngFor="let item of paymentStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12"> </nz-col>

      <nz-col nzSpan="24" class="text-center mt-4">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true, true)" class="mr-2">
          <span nz-icon nzType="redo"></span>Xóa bộ lọc
        </button>
        <button nzShape="round" nz-button (click)="searchData(true)" nzType="primary" nzGhost><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3" nzJustify="space-between" nzAlign="middle">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" nzGhost (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ language_key?.CREATE_SUPPLIER || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th nzWidth="50px">STT</th>
        <th>Mã HSTT</th>
        <th>Tiêu đề hồ sơ thanh toán</th>
        <th>Nhà cung cấp</th>
        <th>Trạng thái</th>
        <th nzWidth="150px" nzRight>{{ language_key?.PurchaseOrder_Action || 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData; let i = index">
        <td class="text-center">{{ i + 1 }}</td>

        <td>{{ data.code }}</td>
        <td>{{ data.name }}</td>
        <td>{{ data.supplierName }}</td>

        <td nzAlign="center">
          <nz-tag
            [ngStyle]="{
              color: data.statusColor,
              border: '1px solid ' + data.statusBorderColor
            }"
            [nzColor]="data.statusBgColor"
            style="width: 200px; font-weight: 600; border-radius: 30px"
          >
            <div style="display: flex; align-items: center; justify-content: center">
              <div
                [ngStyle]="{
                background: data.statusColor,
              }"
                class="dot"
              ></div>
              <span class="ml-1"> {{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>

        <!-- Nhà cung cấp tạo -->
        <td nzWidth="150px" nzRight class="text-center" *ngIf="data.isSupplierCreate">
          <button nz-tooltip (click)="showDetail(data)" [nzTooltipTitle]="'Xem chi tiết'" class="btn-primary mb-2 mt-2 mr-2" nz-button>
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.CHECKING.code"
            class="mr-2"
            (nzOnConfirm)="onValidConfirm(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="'Xác nhận hợp lệ'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn xác nhận hợp lệ cho hồ sơ thanh toán này?"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="send" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.CHECKING.code"
            class="mr-2"
            (nzOnConfirm)="onRequestConfirm(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="language_key?.Payment_CheckProfile || 'Yêu cầu xác nhận lại'"
            nz-button
            nz-popconfirm
            [nzPopconfirmTitle]="language_key?.Payment_SureCheckProfile || 'Bạn có chắc yêu cầu xác nhận lại ?'"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="check" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.REQUEST_RECHECK.code || data.status === paymentStatusObj.CONFIRMED.code"
            nz-button
            (click)="approvedRequest(data)"
            nzShape="circle"
            class="mr-2"
            nzPopconfirmTitle="Bạn có chắc muốn gửi duyệt ? "
            nzTooltipTitle="Gửi duyệt thanh toán"
            nzTooltipPlacement="top"
            nz-tooltip
          >
            <i nz-icon class="text-icon" nzType="send"></i>
          </button>

          <!-- Duyệt -->

          <button
            *ngIf="data.status === paymentStatusObj.WAIT_APPROVE.code"
            nz-button
            (click)="onApproved(data)"
            nzShape="circle"
            class="mr-2"
            nzPopconfirmTitle="Bạn có chắc muốn [DUYỆT] hồ sơ thanh toán này ?"
            nzTooltipTitle="[DUYỆT] hồ sơ thanh toán"
            nzTooltipPlacement="top"
            nz-tooltip
          >
            <i nz-icon class="text-icon" nzType="check"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.WAIT_APPROVE.code"
            class="mr-2"
            (nzOnConfirm)="onReCheck(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="'Yêu cầu kiểm tra lại'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn Yêu cầu kiểm tra lại cho hồ sơ thanh toán này?"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="redo" class="text-icon"></i>
          </button>
        </td>

        <!-- admin tạo -->
        <td nzWidth="150px" nzRight class="text-center" *ngIf="!data.isSupplierCreate">
          <button nz-tooltip (click)="showDetail(data)" [nzTooltipTitle]="'Xem chi tiết'" class="btn-primary mb-2 mt-2 mr-2" nz-button>
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            class="mr-1 text-btn"
            (click)="clickEdit(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="'Chỉnh sửa'"
            nz-button
            *ngIf="
              data.status === paymentStatusObj.NEW.code ||
              data.status === paymentStatusObj.WAIT_APPROVE.code ||
              data.status === paymentStatusObj.REQUEST_CONFIRM.code
            "
          >
            <i nz-icon nzType="edit" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.CHECKING.code"
            class="mr-2"
            (nzOnConfirm)="onValidConfirm(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="'Xác nhận hợp lệ'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn xác nhận hợp lệ cho hồ sơ thanh toán này?"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="send" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.CHECKING.code"
            class="mr-2"
            (nzOnConfirm)="onRequestConfirm(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="language_key?.Payment_CheckProfile || 'Yêu cầu xác nhận lại'"
            nz-button
            nz-popconfirm
            [nzPopconfirmTitle]="language_key?.Payment_SureCheckProfile || 'Bạn có chắc yêu cầu xác nhận lại ?'"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="check" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.NEW.code || data.status === paymentStatusObj.REQUEST_CONFIRM.code"
            class="mr-2"
            (nzOnConfirm)="onChecking(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="language_key?.Payment_CheckProfile || 'Kiểm tra hồ sơ'"
            nz-button
            nz-popconfirm
            [nzPopconfirmTitle]="language_key?.Payment_SureCheckProfile || 'Bạn có chắc kiểm tra hồ sơ ?'"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="check" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.WAIT_APPROVE.code"
            class="mr-2"
            (nzOnConfirm)="onReCheck(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="'Yêu cầu kiểm tra lại'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn Yêu cầu kiểm tra lại cho hồ sơ thanh toán này?"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="redo" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === paymentStatusObj.REQUEST_RECHECK.code || data.status === paymentStatusObj.CONFIRMED.code"
            nz-button
            (click)="approvedRequest(data)"
            nzShape="circle"
            class="mr-2"
            nzPopconfirmTitle="Bạn có chắc muốn gửi duyệt ? "
            nzTooltipTitle="Gửi duyệt thanh toán"
            nzTooltipPlacement="top"
            nz-tooltip
          >
            <i nz-icon class="text-icon" nzType="send"></i>
          </button>
          <!-- Duyệt -->

          <button
            *ngIf="data.status === paymentStatusObj.WAIT_APPROVE.code"
            nz-button
            (click)="onApproved(data)"
            nzShape="circle"
            class="mr-2"
            nzPopconfirmTitle="Bạn có chắc muốn [DUYỆT] hồ sơ thanh toán này ?"
            nzTooltipTitle="[DUYỆT] hồ sơ thanh toán"
            nzTooltipPlacement="top"
            nz-tooltip
          >
            <i nz-icon class="text-icon" nzType="check"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
