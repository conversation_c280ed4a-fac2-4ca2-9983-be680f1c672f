import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { PaymentComponent } from './payment.component'
import { AddOrEditPaymentComponent } from './add-or-edit-payment/add-or-edit-payment.component'
import { PaymentDetailComponent } from './payment-detail/payment-detail.component'

const routes: Routes = [
  { path: '', component: PaymentComponent },
  { path: 'add', component: AddOrEditPaymentComponent },
  { path: 'edit/:id', component: AddOrEditPaymentComponent },
  { path: 'detail/:id', component: PaymentDetailComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PaymentRoutingModule {}
