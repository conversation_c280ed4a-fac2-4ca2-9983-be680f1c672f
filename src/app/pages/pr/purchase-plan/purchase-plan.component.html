<nz-row nzGutter="8">
  <nz-col nzSpan="24">
    <button
      *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
      nz-button
      class="mr-2"
      nzType="primary"
      (click)="clickAdd()"
    >
      <span nz-icon nzType="plus"></span> {{ language_key?.ADD || 'Thêm mới' }}
    </button>
    <button
      nz-button
      nzType="primary"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
      class="mr-2"
      (click)="clickImportExcel()"
    >
      <span nz-icon nzType="import"></span> Import Excel
    </button>
    <button nz-button (click)="onDownloadExcel()" *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)">
      <span nz-icon nzType="download"></span> {{ language_key?.DOWNLOAD_EXCEL || 'Tải Excel' }}
    </button>
  </nz-col>
</nz-row>

<nz-collapse nzBordered="false" class="mt-3">
  <nz-collapse-panel [nzHeader]="language_key?.SEARCH_ADVANCED || 'Tìm kiếm nâng cao'" class="ant-bg-antiquewhite">
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Phòng ban </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.departmentId" name="departmentId" nzPlaceHolder="Chọn phòng ban">
              <nz-option *ngFor="let item of dataDepartments" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.MATERIAL || 'Vật tư' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-cascader
              [nzPlaceHolder]="language_key?.MATERIAL_ENTER || 'Chọn Vật tư'"
              [(ngModel)]="dataSearch.serviceChoose"
              name="serviceChoose"
              id="serviceChoose"
              [nzLoadData]="loadDataService"
            >
            </nz-cascader>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.statusId"
              name="statusId"
              [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'"
            >
              <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="8">
    <input nz-input [(ngModel)]="dataSearch.code" placeholder="Lọc theo Mã kế hoạch" />
  </nz-col>
  <nz-col nzSpan="8">
    <input nz-input [(ngModel)]="dataSearch.name" placeholder="Lọc theo Tên kế hoạch" />
  </nz-col>
  <nz-col nzSpan="8">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    class="mb-3"
    nzBordered
  >
    <thead>
      <tr>
        <th>{{ language_key?.PURCHASE_PLAN_CODE || 'Mã kế hoạch' }}</th>
        <th>Tiêu đề</th>
        <th>Phòng ban</th>
        <th>{{ language_key?.MATERIAL || 'Vật tư' }}</th>
        <th>Tổng SL kế hoạch</th>
        <th>Ngân sách</th>
        <th>SL Đã nhập kho</th>
        <th>SL còn lại</th>
        <th>Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td (click)="clickDetail(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="clickDetail(data)">{{ data.name }}</td>
        <td class="mw-25" (click)="clickDetail(data)">{{ data.departmentName }}</td>
        <td class="mw-25" (click)="clickDetail(data)">{{ data.itemName }}</td>
        <td class="text-right" (click)="clickDetail(data)">{{ data.quantity | number }}</td>
        <td class="text-right" (click)="clickDetail(data)">{{ data.budget | number }}</td>
        <td class="text-right" (click)="clickDetail(data)">{{ data.quantityInbound | number }}</td>
        <td class="text-right" (click)="clickDetail(data)">{{ data.quantity - data.quantityInbound | number }}</td>
        <td class="text-nowrap">
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
            (click)="clickEdit(data)"
            class="mr-2 mb-2"
            nz-button
            nz-popconfirm
            nzTooltipTitle="Cập nhật"
            nzTooltipTitle="Cập nhật"
            nzPopconfirmPlacement="bottom"
            nz-tooltip
            nzConfirm
          >
            <span nz-icon nzType="edit"></span>
          </button>

          <button
            *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)"
            nz-tooltip
            [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'"
            nz-button
            nzDanger
          >
            <span nz-icon nzType="stop"></span>
          </button>
          <button
            *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)"
            nz-tooltip
            [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
            nz-button
            nzType="primary"
          >
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination
    class="mt-3"
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Nhập Kế Hoạch Bằng Excel" (nzOnCancel)="handleCancel()">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center">
      <button
        style="height: 50px; width: 200px"
        nz-button
        nzType="primary"
        *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
        (click)="onDownloadTemplate()"
      >
        <span nz-icon nzType="cloud-download" style="font-size: x-large"></span>Export Template
      </button>
    </nz-row>
    <nz-row style="text-align: center; justify-content: center" class="mt-3">
      <nz-upload
        style="width: 100%"
        [nzFileList]="fileList"
        [nzAction]="urlUpload"
        [nzBeforeUpload]="beforeUpload"
        [nzHeaders]="{ authorization: 'authorization-text' }"
        [nzRemove]="clearFileList"
        [nzDownload]="handleDownload"
        *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
        (nzChange)="onImport($event)"
      >
        <button nz-button style="height: 50px; width: 200px" [disabled]="fileList.length > 0">
          <span nz-icon nzType="cloud-upload" style="font-size: x-large"></span>Upload Template
        </button>
      </nz-upload>
    </nz-row>
  </ng-container>

  <ng-container *nzModalFooter>
    <button
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
      (click)="handleUpload()"
      nz-button
      nzType="primary"
      [disabled]="!checkTemplete"
    >
      <span nz-icon nzType="plus"></span> Tạo Kế Hoạch
    </button>
    <button (click)="handleCancel()" nz-button nzDanger><span nz-icon nzType="close"></span> {{ language_key?.CLOSE || 'Đóng' }}</button>
  </ng-container>
</nz-modal>
