import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './purchase-plan-detail.component.html' })
export class PurchasePlanDetailComponent implements OnInit {
  modalTitle = 'Chi Tiết Kế Hoạch Mua Hàng'
  dataObject: any = {}
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.loadDetail(this.data.id)
    }
  }

  async loadDetail(idStr: string) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_PLAN.FIND_DETAIL, { id: idStr }).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res || {}
    })
  }
}
