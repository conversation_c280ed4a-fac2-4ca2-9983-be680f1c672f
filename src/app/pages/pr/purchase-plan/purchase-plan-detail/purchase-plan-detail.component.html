<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<nz-tabset matDialogContent>

  <nz-tab [nzTitle]="language_key?.GENERAL_INFO || 'Thông Tin Chung'">

    <nz-row nzGutter="8">

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.PURCHASE_PLAN_CODE || 'Mã kế hoạch' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.code }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left"> Tên kế hoạch</nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.name }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Phòng ban</nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.departmentName }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">{{ language_key?.MATERIAL || 'Item' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.itemName }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">{{ language_key?.QUANTITY || 'Số lượng' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.quantity | number }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Ngân sách</nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.budget | number }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Ngân sách còn lại</nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.budgetRemaining | number }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.NOTE || 'Ghi chú' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <b>{{ dataObject.description }} </b>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

  </nz-tab>

  <nz-tab nzTitle="Tiến Độ Mua Hàng">
    <nz-row nzGutter="8" class="mt-3" *ngIf="dataObject.__progresss__">
      <nz-table nz-col nzSpan="24" [nzShowPagination]="false" [nzData]="dataObject.__progresss__.length > 0 ? [''] : []"
        nzBordered>
        <thead>
          <tr>
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Ngày thực hiện</th>
            <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
            <th>{{ language_key?.NOTE || 'Ghi chú' }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of dataObject.__progresss__; let i = index">
            <td>{{ data.sort }}</td>
            <td>{{ data.progressDate | date: 'dd/MM/yyyy' }}</td>
            <td>{{ data.quantity | number }}</td>
            <td>{{ data.description }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>


  </nz-tab>

  <nz-tab class="ant-bg-antiquewhite" nzTitle="Lịch Sử">
    <nz-row nzGutter="8">
      <nz-table nz-col nzSpan="24" [nzData]="['']" nzBordered>
        <thead>
          <tr class="text-nowrap">
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Ngày tạo</th>
            <th>Người tạo </th>
            <th>Mô tả</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dataC of dataObject.__histories__; let i = index">
            <td class="text-nowrap"> {{ i + 1 }} </td>
            <td class="text-nowrap"> {{ dataC.createdAt | date: 'HH:mm dd/MM/yyyy'}} </td>
            <td class="text-nowrap">{{ dataC.createdByName }}</td>
            <td>{{ dataC.description }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>

  </nz-tab>

</nz-tabset>