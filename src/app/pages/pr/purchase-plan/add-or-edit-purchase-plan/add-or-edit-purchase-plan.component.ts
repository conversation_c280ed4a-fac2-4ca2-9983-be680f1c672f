import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { forkJoin, Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-purchase-plan.component.html' })
export class AddOrEditPurchasePlanComponent implements OnInit {
  modalTitle = 'Thêm M<PERSON>ch <PERSON>a Hàng'
  enumData = enumData
  dataObject: any = { __progresss__: [] }
  dataChild: any = {}
  dataDepartment: any[] = []
  dataServices: any[] = []
  language_key: any
  editCache: { [key: string]: { edit: boolean; data: any } } = {}
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditPurchasePlanComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.loadAllSelect()
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    if (this.data && this.data.id) {
      this.modalTitle = 'Thông Tin Kế Hoạch Mua Hàng'
    }
  }

  searchData() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_PLAN.FIND_DETAIL_EDIT, { id: this.data.id }).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
      let indexTemp = 0
      this.dataObject.__progresss__.forEach((element: any) => {
        element.index = indexTemp + ''
        this.editCache[element.index ? element.index : indexTemp] = {
          edit: false,
          data: {
            index: element.index ? element.index : indexTemp,
            ...element,
          },
        }
        indexTemp++
      })

      if (res.__service__.parentId) {
        if (res.__service__.__parent__.parentId) {
          this.dataObject.serviceChoose = [res.__service__.__parent__.parentId, res.__service__.parentId, res.serviceId]
        } else {
          this.dataObject.serviceChoose = [res.__service__.parentId, res.serviceId]
        }
      } else {
        this.dataObject.serviceChoose = [res.serviceId]
      }
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.serviceChoose && this.dataObject.serviceChoose.length > 0) {
      this.dataObject.serviceId = this.dataObject.serviceChoose[this.dataObject.serviceChoose.length - 1]
    } else {
      this.notifyService.showError('Vui lòng chọn vật tư.')
      return
    }

    if (this.dataObject.__progresss__.length === 0) {
      this.notifyService.showError('Vui lòng thêm tiến độ!')
      return
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.PURCHASE_PLAN.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.PURCHASE_PLAN.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  onAdd() {
    if (this.dataChild.quantity == null) {
      this.notifyService.showError('Vui lòng nhập số lượng !')
      return
    }
    let totalQ = 0
    if (this.dataObject.__progresss__.length > 0) {
      totalQ = this.dataObject.__progresss__.map((s: any) => s.quantity).reduce((a: any, b: any) => a + b, 0)
    }
    // console.log(this.dataChild.quantity, this.dataObject.quantity, totalQ)
    // if (this.dataObject.quantity < totalQ + this.dataChild.quantity) {
    //   this.notifyService.showError('Số lượng thêm đã vượt quá số lượng tổng !')
    //   return
    // }
    if (this.dataChild.sort == null) {
      this.notifyService.showError('Vui lòng nhập số thứ tụ!')
      return
    }
    if (this.dataChild.progressDate == null) {
      this.notifyService.showError('Vui lòng nhập thời gian thực hiện!')
      return
    }
    const checkExist = this.dataObject.__progresss__.filter((s: any[]) => s.sort === this.dataChild.sort)
    if (checkExist && checkExist.length > 0) {
      this.notifyService.showError('STT đã tồn tại!')
      return
    }

    this.dataObject.__progresss__.push(this.dataChild)
    this.editCache[this.dataChild.index] = {
      edit: false,
      data: { ...this.dataChild },
    }
    this.dataChild = {}
  }

  onDelete(index: any) {
    this.dataObject.__progresss__.splice(index, 1)
    this.dataObject.__progresss__ = this.dataObject.__progresss__.filter((x: any) => x.sort !== null)
  }

  loadAllSelect() {
    this.notifyService.showloading()
    forkJoin({
      requestOne: this.apiService.post(this.apiService.DEPARTMENT.FIND, {}),
      requestTwo: this.apiService.post(this.apiService.SERVICE.FIND, {}),
    }).subscribe(({ requestOne, requestTwo }) => {
      this.notifyService.hideloading()
      this.dataDepartment = requestOne
      this.dataServices = requestTwo
      if (this.data && this.data.id) {
        this.searchData()
      }
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>((resolve) => {
      if (index < 0) {
        // if index less than 0 it is root node
        node.children = this.dataServices
          .filter((c: any) => c.level === 1)
          .map((c: any) => {
            return { value: c.id, label: c.name, isLeaf: c.isLast }
          })
      } else {
        node.children = this.dataServices
          .filter((c: any) => c.parentId === node.value)
          .map((c: any) => {
            return { value: c.id, label: c.name, isLeaf: c.isLast }
          })
      }
      resolve()
    })
  }

  startEdit(index: string) {
    this.editCache[index].edit = true
  }

  saveEdit(id: string) {
    const index = this.dataObject.__progresss__.findIndex((item: any) => item.index === id)
    this.editCache[id].edit = false
    Object.assign(this.dataObject.__progresss__[index], this.editCache[id].data)
  }

  cancelEdit(id: string) {
    const index = this.dataObject.__progresss__.findIndex((item: any) => item.index === id)
    if (this.editCache[id].data.isNew !== true) {
      this.editCache[id] = {
        data: { ...this.dataObject.__progresss__[index] },
        edit: false,
      }
    } else {
      this.dataObject.__progresss__ = this.dataObject.__progresss__.filter((d: any) => d.index !== id)
      delete this.editCache[id]
    }
  }

  onDesName(event: string, index: number, item: any) {
    if (event) {
      item.isEditDes = true
      this.dataObject.__progresss__[index].descriptionText = event
    }
  }
}
