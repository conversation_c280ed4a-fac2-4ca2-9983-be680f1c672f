<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.PURCHASE_PLAN_CODE || 'Mã kế hoạch' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Mã kế hoạch (1-250 kí tự)!">
            <input nz-input placeholder="Nhập 1-250 kí tự" [(ngModel)]="dataObject.code" name="code" required pattern=".{1,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left"> Tên kế hoạch</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tên kế hoạch (1-250 kí tự)!">
            <input nz-input placeholder="Nhập 1-250 kí tự" [(ngModel)]="dataObject.name" name="name" required pattern=".{1,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Phòng ban</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Phòng ban">
            <nz-select nzShowSearch nzAllowClear name="departmentId" [(ngModel)]="dataObject.departmentId" nzPlaceHolder="Chọn Phòng ban" required>
              <nz-option *ngFor="let item of dataDepartment" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8" *ngIf="loadDataService">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">{{ language_key?.MATERIAL || 'Vật tư' }} </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Vật tư">
            <nz-cascader
              [nzPlaceHolder]="language_key?.MATERIAL_ENTER || 'Chọn Vật tư'"
              [(ngModel)]="dataObject.serviceChoose"
              name="serviceChoose"
              id="serviceChoose"
              [nzLoadData]="loadDataService"
            >
            </nz-cascader>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.QUANTITY || 'Số lượng' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số lượng">
            <input nz-input [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'" numbersOnly
              [(ngModel)]="dataObject.quantity" name="quantity" required currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col> -->

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngân sách</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập ngân sách">
            <input
              nz-input
              placeholder="Nhập ngân sách"
              numbersOnly
              [(ngModel)]="dataObject.budget"
              name="budget"
              required
              currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
            />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.NOTE || 'Ghi chú' }}
          </nz-form-label>
          <nz-form-control [nzSm]="23" [nzXs]="24">
            <textarea
              rows="2"
              nz-input
              [(ngModel)]="dataObject.description"
              name="description"
              placeholder="Nhập 1-1000 kí tự"
              pattern=".{1,1000}"
            ></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-collapse class="mt-1">
      <nz-collapse-panel nzHeader="Thêm Mới Tiến Độ" class="antx-bg-antiquewhite" [nzActive]="!dataObject.id">
        <form nz-form #frmAdd2="ngForm">
          <nz-row nzGutter="12">
            <nz-col nzSpan="4">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>
                  {{ language_key?.NO || 'STT' }}
                </nz-form-label>
                <nz-form-control nzSpan="24">
                  <input nz-input placeholder="Nhập STT" [(ngModel)]="dataChild.sort" name="sort" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="4">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời gian</nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-date-picker nzFormat="dd/MM/yyyy" name="progressDate" nzPlaceHolder="Thời gian" [(ngModel)]="dataChild.progressDate" required>
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="4">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.QUANTITY || 'Số lượng' }}</nz-form-label>
                <nz-form-control nzSpan="24">
                  <input
                    nz-input
                    currencyMask
                    [options]="{ prefix: '', precision: 0, allowNegative: false }"
                    [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'"
                    [(ngModel)]="dataChild.quantity"
                    name="quantity"
                    required
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">{{ language_key?.NOTE || 'Ghi chú' }} </nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixButton">
                    <input
                      type="text"
                      nz-input
                      [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
                      [(ngModel)]="dataChild.description"
                      name="description"
                    />
                  </nz-input-group>
                  <ng-template #suffixButton>
                    <button nz-button nzType="primary" (click)="onAdd()" [disabled]="!frmAdd2.form.valid" nzSearch>Thêm</button>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
        </form>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-row class="mt-3" *ngIf="dataObject.__progresss__">
      <nz-table nz-col nzSpan="24" [nzShowPagination]="false" [nzData]="dataObject.__progresss__.length > 0 ? [''] : []" nzBordered>
        <thead>
          <tr>
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Ngày thực hiện</th>
            <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
            <th>{{ language_key?.NOTE || 'Ghi chú' }}</th>
            <th class="text-center">Tác vụ</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of dataObject.__progresss__; let i = index">
            <td>{{ data.sort }}</td>

            <ng-container *ngIf="!editCache[data.index].edit; else progressDateInput">
              <td>{{ data.progressDate | date : 'dd/MM/yyyy HH:mm' }}</td>
            </ng-container>

            <ng-template #progressDateInput>
              <td class="text-center">
                <nz-date-picker
                  nzFormat="dd/MM/yyyy"
                  name="progressDate"
                  nzPlaceHolder="Thời gian"
                  [(ngModel)]="editCache[data.index].data.progressDate"
                  required
                >
                </nz-date-picker>
              </td>
            </ng-template>

            <ng-container *ngIf="!editCache[data.index].edit; else quantityInput">
              <td class="text-right">{{ data.quantity | number }}</td>
            </ng-container>

            <ng-template #quantityInput>
              <td class="text-center">
                <input
                  nz-input
                  placeholder="Nhập số lượng"
                  numbersOnly
                  [(ngModel)]="editCache[data.index].data.quantity"
                  name="quantity"
                  required
                  pattern=".{1,100}"
                  currencyMask
                  [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
                />
              </td>
            </ng-template>

            <ng-container *ngIf="!editCache[data.index].edit; else desInput">
              <ng-container *ngIf="!data.isEditDes">
                <td>{{ data.description }}</td>
              </ng-container>
              <ng-container *ngIf="data.isEditDes">
                <td>{{ data.descriptionText }}</td>
              </ng-container>
            </ng-container>

            <ng-template #desInput>
              <td class="text-center">
                <input
                  (ngModelChange)="onDesName($event, i, editCache[data.index].data)"
                  style="width: 300px"
                  nz-input
                  placeholder="Nhập mô tả tiến độ"
                  [(ngModel)]="editCache[data.index].data.description"
                  [name]="'description' + i"
                  pattern=".{1,250}"
                />
              </td>
            </ng-template>

            <td class="text-center">
              <button nz-tooltip [nzTooltipTitle]="language_key?.DELETE || 'Xóa'" nz-button nzDanger (click)="onDelete(i)" class="mr-2">
                <span nz-icon nzType="delete"></span>
              </button>

              <ng-container *ngIf="!editCache[data.index].edit; else saveTpl">
                <button nz-tooltip nzTooltipTitle="Sửa" nz-button (click)="startEdit(data.index)" class="mr-2">
                  <span nz-icon nzType="edit"></span>
                </button>
              </ng-container>
              <ng-template #saveTpl>
                <button nz-tooltip nzTooltipTitle="Lưu" class="mr-2" nz-button nzType="primary" (click)="saveEdit(data.index)">
                  <span nz-icon nzType="save"></span>
                </button>
                <button nz-tooltip nzTooltipTitle="Huỷ" nz-button nzDanger (click)="cancelEdit(data.index)">
                  <span nz-icon nzType="close"></span>
                </button>
              </ng-template>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        <span *ngIf="!dataObject.id">{{ language_key?.ADD || 'Tạo mới' }}</span>
        <span *ngIf="dataObject.id">Cập Nhật</span>
      </button>
    </nz-col>
  </nz-row>
</form>
