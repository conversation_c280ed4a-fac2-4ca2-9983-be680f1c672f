import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { NzMessageService } from 'ng-zorro-antd/message'
import { Observable, Observer, Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { enumData } from '../../../core'
import { User } from '../../../models'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { AddOrEditPurchasePlanComponent } from './add-or-edit-purchase-plan/add-or-edit-purchase-plan.component'
import { PurchasePlanDetailComponent } from './purchase-plan-detail/purchase-plan-detail.component'
import { environment } from 'src/environments/environment'
import { NzUploadFile } from 'ng-zorro-antd/upload'
@Component({ templateUrl: './purchase-plan.component.html' })
export class PurchasePlanComponent implements OnInit {
  currentUser!: User
  pageIndex: any
  pageSize: any
  total: any
  dataFilterStatus: any
  listOfData: any[] = []
  dataSearch: any = {}
  loading = true
  isVisible = false
  dataUploadExcel: any[] = []
  dataServices: any[] = []
  dataDepartments: any[] = []
  checkTemplete = false
  fileList: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  urlUpload = `${environment.backEnd}/${this.apiService.UPLOAD_FILE.UPLOAD_SINGLE}`
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    private msg: NzMessageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.pageIndex = enumData.Page.pageIndex
    this.pageSize = enumData.Page.pageSize
    this.total = enumData.Page.total
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.enumRole = this.enumProject.Features.PURCHASE_PLAN_001.code
    this.action = this.enumProject.Action
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.loadDepartment()
    this.loadService()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.departmentId && this.dataSearch.departmentId !== '') {
      where.departmentId = this.dataSearch.departmentId
    }
    if (this.dataSearch.serviceChoose && this.dataSearch.serviceChoose.length > 0) {
      where.serviceId = this.dataSearch.serviceChoose[this.dataSearch.serviceChoose.length - 1]
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.PURCHASE_PLAN.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickDetail(data: any) {
    this.dialog
      .open(PurchasePlanDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditPurchasePlanComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(data: any) {
    this.dialog
      .open(AddOrEditPurchasePlanComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_PLAN.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  onDownloadExcel() {
    this.notifyService.showloading()
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.departmentId && this.dataSearch.departmentId !== '') {
      where.departmentId = this.dataSearch.departmentId
    }
    if (this.dataSearch.serviceId) {
      where.serviceId = this.dataSearch.serviceId
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.loading = true
    this.apiService.post(this.apiService.PURCHASE_PLAN.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.notifyService.hideloading()
        if (data && data[0].length > 0) {
          let date = new Date().toISOString()
          const fileName = `Ke_Hoach_Mua_Hang_${date}.xlsx`
          let dataExcel: any[] = []
          data[0].forEach((s: any) => {
            dataExcel.push({
              'Mã kế hoạch': s.code,
              'Tên kế hoạch': s.name,
              'Phòng ban': s.departmentName,
              'Loại vật tư': s.serviceName,
              'Số lượng': s.quantity,
              'Ghi chú kế hoạch': s.description,
            })
          })
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
          const wb: XLSX.WorkBook = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Kế Hoạch Mua Hàng')

          XLSX.writeFile(wb, fileName)
        }
      }
    })
  }

  clickImportExcel() {
    this.isVisible = true
    this.dataUploadExcel = []
    this.fileList = []
    this.checkTemplete = false
  }

  handleCancel() {
    this.isVisible = false
    this.dataUploadExcel = []
    this.fileList = []
  }

  beforeUpload = (file: any, _fileList: any[]) => {
    return new Observable((observer: Observer<boolean>) => {
      const isJpgOrPng =
        file.type === 'application/vnd.ms-excel,application' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isJpgOrPng) {
        this.msg.error('Tệp tin tải lên không đúng định dạng file Excel!')
        observer.complete()
        this.fileList = []
        return
      }
      const isLt2M = file.size! / 1024 / 1024 < 5
      if (!isLt2M) {
        this.msg.error('Tệp tin tải lên không được vượt quá 5MB!')
        observer.complete()
        this.fileList = []
        return
      }
      observer.next(isJpgOrPng && isLt2M)
      observer.complete()
    })
  }

  onDownloadTemplate() {
    let dataExcel: any[] = []
    dataExcel.push({
      'Mã KH': '',
      'Tên KH': '',
      'Mã phòng ban': '',
      'Mã loại vật tư': '',
      'Số lượng': '',
      'Ngân sách': '',
      'Ghi chú KH': '',

      'TĐMH STT': '',
      'TĐMH Thời gian': '',
      'TĐMH Số lượng': '',
      'TĐMH Ghi chú': '',
    })
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
    const wb: XLSX.WorkBook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Mẫu Kế Hoạch Mua Hàng')
    XLSX.writeFile(wb, 'Template_Ke_Hoach_Mua_Hang.xlsx')
  }

  onImport(ev: any) {
    this.dataUploadExcel = []
    const lstStatus = ['done', 'error']
    if (!lstStatus.includes(ev.file.status)) {
      // console.log(ev.file, ev.fileList)
      return
    }
    if (lstStatus.includes(ev.file.status)) {
      this.notifyService.showSuccess(`[${ev.file.name}] file đã tải lên thành công!`)
      let workBook = null
      let jsonData: any = null
      const lstHeader = [
        'code',
        'name',
        'departmentCode',
        'serviceCode',
        'quantity',
        'budget',
        'description',
        'progressSort',
        'progressDate',
        'progressQuantity',
        'progressDescription',
      ]
      const reader = new FileReader()
      const file = ev.file.originFileObj
      reader.readAsBinaryString(file)
      reader.onload = async () => {
        workBook = XLSX.read(reader.result, { type: 'binary' })
        jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
          raw: true,
          defval: null,
          header: lstHeader,
        })

        // bỏ dòng đầu tiên
        let isErr = false
        const header: any = jsonData.shift()

        if (
          header.code !== 'Mã KH' ||
          header.name !== 'Tên KH' ||
          header.departmentCode !== 'Mã phòng ban' ||
          header.serviceCode !== 'Mã loại vật tư' ||
          header.quantity !== 'Số lượng' ||
          header.budget !== 'Ngân sách' ||
          header.description !== 'Ghi chú KH' ||
          header.progressSort !== 'TĐMH STT' ||
          header.progressDate !== 'TĐMH Thời gian' ||
          header.progressQuantity !== 'TĐMH Số lượng' ||
          header.progressDescription !== 'TĐMH Ghi chú'
        ) {
          isErr = true
        }

        if (isErr) {
          this.notifyService.showError('File không đúng template mẫu')
          this.checkTemplete = false
          return
        }
        let pushInList: any[] = []

        let strErr = ''
        for (const row of jsonData) {
          let idx = jsonData.indexOf(row) + 1
          let department: any
          let service: any
          let num1, num2, num3: any

          if (row.code !== null && row.code.trim().length > 0 && row.name !== null && row.name.trim().length > 0) {
            if (row.quantity == null || (typeof row.quantity === 'string' && row.quantity.trim().length == 0)) {
              strErr += `Dòng ${idx} - Số lượng không được để trống và nhỏ hơn 0 <br>`
            } else {
              num1 = parseInt(row.quantity)
              if (!num1) {
                strErr += `Dòng ${idx} - Số lượng [${row.quantity}] không đúng định dạng số<br>`
              }
            }
            if (row.code !== null && row.code.trim().length > 0) {
              const existData = this.listOfData.find((c) => c.code.toUpperCase() === row.code.trim().toUpperCase())
              if (existData) {
                strErr += `Dòng ${idx} - Mã kế hoạch [${row.code}] đã tồn tại trong hệ thống<br>`
              }
            }
            if (typeof row.serviceCode !== 'string') {
              row.serviceCode = row.serviceCode.toString()
            }
            if (row.serviceCode !== null && row.serviceCode.trim().length > 0) {
              service = this.dataServices.find((c) => c.code.toUpperCase() === row.serviceCode.trim().toUpperCase())
              if (!service) {
                strErr += `Dòng ${idx} - Mã loại vật tư [ ${row.serviceCode} ] không tồn tại trong hệ thống<br>`
              }
            }
            if (row.departmentCode !== null && row.departmentCode.trim().length > 0) {
              department = this.dataDepartments.find((c) => c.code.toUpperCase() === row.departmentCode.trim().toUpperCase())
              if (!department) {
                strErr += `Dòng ${idx} - Mã phòng ban [${row.departmentCode}] không tồn tại trong hệ thống<br>`
              }
            }
          }

          if (row.progressSort == null || (typeof row.progressSort === 'string' && row.progressSort.trim().length == 0)) {
            strErr += `Dòng ${idx} - STT không được để trống và nhỏ hơn 0 <br>`
          } else {
            num2 = parseInt(row.progressSort)
            if (!num2) {
              strErr += `Dòng ${idx} - STT [${row.progressSort}] không đúng định dạng số<br>`
            }
          }
          if (row.progressQuantity == null || (typeof row.progressQuantity === 'string' && row.progressQuantity.trim().length == 0)) {
            strErr += `Dòng ${idx} - Số lượng không được để trống và nhỏ hơn 0 <br>`
          } else {
            num3 = parseInt(row.progressQuantity)
            if (!num3) {
              strErr += `Dòng ${idx} - Số lượng [${row.progressQuantity}] không đúng định dạng số<br>`
            }
          }

          let pDate = this.coreService.excelDateToJSDate(row.progressDate)
          if (!pDate) {
            strErr += `Dòng ${idx} - TĐMH Thời gian không được để trống và đúng định dạng ngày tháng <br>`
          }

          if (strErr.length > 0) {
            break
          }
          if (row.code !== null && row.code.trim().length > 0 && row.name !== null && row.name.trim().length > 0) {
            let item = {
              code: row.code,
              name: row.name,
              serviceId: null,
              departmentId: null,
              quantity: row.quantity,
              budget: row.budget,
              description: row.description,
              __progresss__: [
                {
                  sort: row.progressSort,
                  progressDate: pDate,
                  quantity: row.progressQuantity,
                  description: row.progressDescription,
                },
              ],
            }
            if (service !== undefined && service !== null) {
              item.serviceId = service.id
            }
            if (department !== undefined && department !== null) {
              item.departmentId = department.id
            }
            pushInList.push(item)
          } else {
            let item = pushInList[pushInList.length - 1]
            item.__progresss__.push({
              sort: row.progressSort,
              progressDate: pDate,
              quantity: row.progressQuantity,
              description: row.progressDescription,
            })
          }
        }
        if (strErr.length > 0) {
          this.notifyService.showError(strErr)
          this.checkTemplete = false
          return
        } else if (pushInList.length > 0) {
          pushInList.forEach((s) => {
            this.dataUploadExcel = [...this.dataUploadExcel, s]
          })
          this.checkTemplete = true
        }
      }
    }
    // } else if (ev.file.status === 'error') {
    //   this.notifyService.showError(`[${ev.file.name}] file tải lên không đúng định dạng.`)
    //   this.checkTemplete = false
    // }
    this.fileList = ev.fileList
  }

  handleUpload() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_PLAN.CREATELIST, this.dataUploadExcel).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.fileList = []
        this.handleCancel()
        this.searchData()
      }
    })
  }

  loadService() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataServices = result
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise(async (resolve) => {
      if (index < 0) {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.code,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.code,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
      }
      resolve(1)
    })
  }

  loadDepartment() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.DEPARTMENT.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataDepartments = result
    })
  }

  clearFileList = (): boolean => {
    this.fileList = []
    this.checkTemplete = false
    return true
  }

  async handleDownload(file: NzUploadFile): Promise<void> {
    window.open(file.response[0])
  }
}
