import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { AriseComponent } from './arise/arise.component'
import { PlanComponent } from './plan/plan.component'
import { PurchasePlanComponent } from './purchase-plan/purchase-plan.component'

const routes: Routes = [
  { path: 'purchase-plan', component: PurchasePlanComponent },
  { path: 'plan', component: PlanComponent },
  { path: 'arise', component: AriseComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PrRoutingModule {}
