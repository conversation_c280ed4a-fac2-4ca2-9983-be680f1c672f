import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-plan.component.html' })
export class AddOrEditPlanComponent implements OnInit {
  enumData = enumData
  modalTitle = 'THÊM MỚI PR THEO KẾ HOẠCH MUA HÀNG'
  dataObject: any = { lstItem: [] }
  dataCategory: any[] = []
  lstUnit: any[] = []
  dataEmployee: any[] = []
  dataPurchasePlans: any[] = []
  dataBranch: any[] = []
  dataMaterial: any[] = []
  dataObjectMaterial: any[] = []
  dataServices: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditPlanComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadEmployee()
    this.loadAllDataSelect()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.modalTitle = 'CẬP NHẬT PR THEO KẾ HOẠCH MUA HÀNG'
      this.dataObject.lstItem = []
      this.searchData(this.data)
      this.setServiceLevel1()
    }
  }

  searchData(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.FINDDETAIL, data).then((result) => {
      result.quantity = 0
      this.dataObject = result
      result.lstItem.forEach((item: any) => {
        item.planName = item.__purchasePlan__.name
      })
      let employee = result.lstPermission.filter((c: any) => c.level === 1).map((c: any) => c.employeeId)
      this.dataObject.employeeId = employee[0]
      let arr = result.lstPermission.filter((c: any) => c.level === 2).map((c: any) => c.employeeId)
      this.dataObject.lstEmployee = arr
      if (this.dataObject && this.dataObject.id) {
        this.onChangeInCharge(this.dataObject.empInChargeId)
        this.onChangeProposer(this.dataObject.empProposerId)
      }
      this.notifyService.hideloading()
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.lstItem.length === 0) {
      this.notifyService.showError('Vui lòng thêm ít nhất 1 Item')
      return
    }
    if (!this.dataObject.isChangePrItem) {
      this.dataObject.isChangePrItem = this.dataObject.lstItem.some((c: any) => c.isNew)
    }

    if (!this.dataObject.lstEmployee || this.dataObject.lstEmployee.length === 0) {
      this.notifyService.showError('Vui lòng thêm ít nhất 1 người duyệt cấp 2')
      return
    }

    this.dataObject.prType = enumData.PRType.Plan.code
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  loadItem() {
    for (const item of this.dataMaterial) {
      if (item.id === this.dataObject.itemId) {
        this.dataObject.productName = item.name
        break
      }
    }
  }
  addObject() {
    this.apiService.post(this.apiService.PR.PLANCREATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(true)
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.PR.PLANUPDATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  loadEmployee() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.EMPLOYEE.FIND, { isGet: true }).then((result) => {
      this.notifyService.hideloading()
      this.dataEmployee = result
    })
  }

  onChangeProposer(id: any) {
    const emp = this.dataEmployee.find((s) => s.id === id)
    this.dataObject.pBranchName = emp.branchName
    this.dataObject.pDepartmentName = emp.departmentName
  }

  onChangeInCharge(id: any) {
    const emp = this.dataEmployee.find((s) => s.id === id)
    this.dataObject.iBranchName = emp.branchName
    this.dataObject.iDepartmentName = emp.departmentName
    this.dataObject.iPhone = emp.phone
    this.dataObject.iEmail = emp.email
  }

  onAdd() {
    if (!this.dataObject.purchasePlanId) {
      this.notifyService.showError('Vui lòng chọn ít nhất một kế hoạch mua hàng')
      return
    }

    let serviceLevel1 = this.getServiceLevel1(this.dataObject.purchasePlanId)
    if (this.dataObject.serviceLevel1) {
      if (this.dataObject.serviceLevel1 !== serviceLevel1) {
        this.notifyService.showError('Vui lòng chọn cùng nhóm vật tư !')
        return
      }
    } else {
      this.dataObject.serviceLevel1 = serviceLevel1
    }

    let planName = this.dataPurchasePlans.filter((x) => x.id === this.dataObject.purchasePlanId)
    const data = {
      purchasePlanId: this.dataObject.purchasePlanId,
      quantity: this.dataObject.quantity,
      itemId: this.dataObject.itemId,
      productName: this.dataObject.productName,
      suggestReason: this.dataObject.suggestReason,
      description: this.dataObject.description,
      planName: planName[0].name,
      unit: this.dataObject.unit,
      isNew: true,
    }
    this.dataObject.lstItem.push(data)
  }

  onDelete(index: any) {
    if (this.dataObject.lstItem[index]?.id) this.dataObject.isChangePrItem = true
    this.dataObject.lstItem.splice(index, 1)
    this.dataObject.lstItem = this.dataObject.lstItem.filter((x: any) => x.purchasePlanId !== null)

    if (this.dataObject.lstItem.length === 0) {
      this.dataObject.serviceLevel1 = null
    }
  }

  onchangeQuantity(event: any) {
    const plant = this.dataPurchasePlans.find((s) => s.id === event)
    this.dataObject.quantity = plant.quantity - plant.quantityInbound
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.BRANCH.FIND, {}),
      this.apiService.post(this.apiService.OBJECT.FIND, {}),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.PURCHASE_PLAN.FIND, {}),
      this.apiService.post(this.apiService.SERVICE.FIND, {}),
      this.apiService.post(this.apiService.MATERIAL.FIND, {}), // 5
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.dataBranch = res[0]
      this.dataObjectMaterial = res[1]
      this.lstUnit = res[2]
      this.dataPurchasePlans = res[3]
      this.dataServices = res[4]
      this.dataMaterial = res[5]
      this.setServiceLevel1()
    })
  }

  setServiceLevel1() {
    if (this.dataServices.length > 0 && this.dataObject.lstItem.length > 0) {
      let purchasePlanId = this.dataObject.lstItem[0].purchasePlanId
      let purchasePlan = this.dataPurchasePlans.find((s) => s.id == purchasePlanId)
      let childId = purchasePlan.serviceId
      for (let index = 0; index < 4; index++) {
        const element = this.dataServices.find((s) => s.id === childId)
        if (!element) break
        if (element.level === 1) {
          this.dataObject.serviceLevel1 = element.id
          break
        }
        childId = element.parentId
      }
    }
  }

  getServiceLevel1(purchasePlanId: any) {
    if (this.dataServices.length > 0 && purchasePlanId) {
      let purchasePlan = this.dataPurchasePlans.find((s) => s.id == purchasePlanId)
      let childId = purchasePlan.serviceId
      for (let index = 0; index < 4; index++) {
        const element = this.dataServices.find((s) => s.id === childId)
        if (element) {
          if (element.level === 1) {
            return element.id
          } else {
            childId = element.parentId
          }
        }
      }
    }
  }
}
