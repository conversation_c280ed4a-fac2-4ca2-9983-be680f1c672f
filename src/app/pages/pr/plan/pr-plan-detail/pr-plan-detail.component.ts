import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { User } from 'src/app/models'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { ItemTechComponent } from '../../item-tech/item-tech.component'
import { AddOrEditPlanComponent } from '../add-or-edit-plan/add-or-edit-plan.component'

@Component({ templateUrl: './pr-plan-detail.component.html' })
export class PrPlanDetailComponent implements OnInit {
  modalTitle = 'CHI TIẾT PR THEO KẾ HOẠCH MUA HÀNG'
  enumData = enumData
  dataObject: any = {}
  isVisibleCancel = false
  dataReject: any = {}
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  action: any
  enumRole: any
  currentUser!: User
  isVisibleRecheck = false
  dataObjectReject: any = {}
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    private dialogRef: MatDialogRef<PrPlanDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.PR_002.code
    this.action = this.enumProject.Action
    this.searchData(this.data)
  }

  searchData(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.FINDDETAIL, data).then((result) => {
      this.dataObject = result

      this.notifyService.hideloading()
    })
  }
  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  onForwardApprove() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.APPROVE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  onForwardCancel() {
    this.isVisibleCancel = true
  }

  confirmCancel() {
    this.notifyService.showloading()
    if (this.dataReject.reason == null || this.dataReject.reason.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do')
      return
    }
    this.dataObject.reason = this.dataReject.reason
    this.apiService.post(this.apiService.PR.CANCEL, this.dataObject).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisibleCancel = false
      this.closeDialog(true)
    })
  }

  onForwardProcessing() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.PROCESS, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  onForwardClose() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.CLOSE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  itemTech(object: any) {
    this.dialog
      .open(ItemTechComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData(this.data)
      })
  }

  clickEdit(data: { quantity: null }) {
    this.closeDialog(true)
    data.quantity = null
    this.dialog
      .open(AddOrEditPlanComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.apiService.eventReloadPrPlan.next(true)
      })
  }

  onForwardRecheck() {
    this.dataObject.noteReCheck = ''
    this.isVisibleRecheck = true
  }

  confirmRecheck() {
    this.notifyService.showloading()
    if (this.dataObjectReject.noteReCheck == null || this.dataObjectReject.noteReCheck.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập yêu cầu kiểm tra lại')
      return
    }
    this.dataObject.noteReCheck = this.dataObjectReject.noteReCheck
    this.apiService.post(this.apiService.PR.RECHECK, this.dataObject).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisibleRecheck = false
      this.closeDialog(true)
    })
  }
}
