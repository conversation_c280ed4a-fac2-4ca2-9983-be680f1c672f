import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { PrRoutingModule } from './pr-routing.module'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { CurrencyMaskModule, CURRENCY_MASK_CONFIG } from 'ng2-currency-mask'
import { MaterialModule } from '../../app.module'
import { DirectivesModule } from '../../directive/directives.module'
import { AriseComponent } from './arise/arise.component'
import { AddOrEditAriseComponent } from './arise/add-or-edit-arise/add-or-edit-arise.component'
import { PlanComponent } from './plan/plan.component'
import { AddOrEditPlanComponent } from './plan/add-or-edit-plan/add-or-edit-plan.component'
import { PrPlanDetailComponent } from './plan/pr-plan-detail/pr-plan-detail.component'
import { PrAriseDetailComponent } from './arise/pr-arise-detail/pr-arise-detail.component'
import { PurchasePlanComponent } from './purchase-plan/purchase-plan.component'
import { AddOrEditPurchasePlanComponent } from './purchase-plan/add-or-edit-purchase-plan/add-or-edit-purchase-plan.component'
import { PurchasePlanDetailComponent } from './purchase-plan/purchase-plan-detail/purchase-plan-detail.component'
import { ItemTechComponent } from './item-tech/item-tech.component'
import { AddOrEditItemTechComponent } from './item-tech/add-or-edit-item-tech/add-or-edit-item-tech.component'
import { ItemTechListDetailComponent } from './item-tech/item-tech-list-detail/item-tech-list-detail.component'
import { NzAvatarModule } from 'ng-zorro-antd/avatar'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCardModule } from 'ng-zorro-antd/card'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions'
import { NzDividerModule } from 'ng-zorro-antd/divider'
import { NzDrawerModule } from 'ng-zorro-antd/drawer'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzMessageModule } from 'ng-zorro-antd/message'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzProgressModule } from 'ng-zorro-antd/progress'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzTypographyModule } from 'ng-zorro-antd/typography'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { CustomCurrencyMaskConfig } from '../setting/setting.module'

@NgModule({
  declarations: [
    AriseComponent,
    AddOrEditAriseComponent,
    PlanComponent,
    AddOrEditPlanComponent,
    PrPlanDetailComponent,
    PrAriseDetailComponent,
    PurchasePlanComponent,
    AddOrEditPurchasePlanComponent,
    PurchasePlanDetailComponent,
    ItemTechComponent,
    AddOrEditItemTechComponent,
    ItemTechListDetailComponent,
  ],
  imports: [
    CommonModule,
    PrRoutingModule,
    CurrencyMaskModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzTableModule,
    NzDividerModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDescriptionsModule,
    NzListModule,
    NzDatePickerModule,
    NzStatisticModule,
    NzPaginationModule,
    NzCollapseModule,
    NzCascaderModule,
    NzPopoverModule,
    NzTypographyModule,
    MaterialModule,
    NzPopconfirmModule,
    NzDrawerModule,
    NzCardModule,
    NzUploadModule,
    NzProgressModule,
    NzAvatarModule,
    NzMessageModule,
    DirectivesModule,
    NzTagModule,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PrModule {}
