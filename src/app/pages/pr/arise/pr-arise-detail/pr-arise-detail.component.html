<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row>

    <nz-col nzSpan="24" class="text-center">

      <button *ngIf="dataObject.isApprover && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-button nzType="primary" class="mr-4 ant-btn-success" (click)="onForwardApprove()">
        <span nz-icon nzType="check"></span> &nbsp; <b>Phê Duyệt</b>
      </button>

      <button *ngIf="dataObject.isCanceler && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-button nzDanger class="mr-4" (click)="onForwardCancel()">
        <span nz-icon nzType="stop"></span> &nbsp; <b>Hủy</b>
      </button>

      <button *ngIf="dataObject.isRecheck && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-button class="mr-4 ant-btn-warning" (click)="onForwardRecheck()">
        <span nz-icon nzType="redo"></span> &nbsp; <b>Yêu Cầu Kiểm Tra Lại</b>
      </button>

      <button *ngIf="dataObject.isProcess && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-button class="mr-4" (click)="onForwardProcessing()">
        <span nz-icon nzType="small-dash"></span> &nbsp; <b>Đang thực hiện</b>
      </button>

      <button *ngIf="dataObject.isEdit && authenticationService.checkPermission([enumRole], action.Update.code)"
        nzType="primary" nz-button class="mr-4" (click)="clickEdit(dataObject)">
        <span nz-icon nzType="edit"></span> &nbsp; <b>Chỉnh sửa</b>
      </button>

      <button *ngIf="dataObject.isCloser && authenticationService.checkPermission([enumRole], action.Update.code)"
        nz-button nzDanger class="mr-4" (click)="onForwardClose()">
        <span nz-icon nzType="lock"></span> &nbsp; <b>
          {{ language_key?.CLOSE || 'Đóng' }}
        </b>
      </button>

    </nz-col>

    <nz-col nzSpan="24" class="text-center mt-3" *ngIf="dataObject.message">
      <p style="color: red; font-style: italic;"> {{ dataObject.message }} </p>
    </nz-col>

  </nz-row>

  <nz-tabset>

    <nz-tab [nzTitle]="language_key?.GENERAL_INFO || 'Thông Tin Chung'">

      <nz-row nzGutter="8" class="mt-3">
        <nz-col nzSpan="24">
          <span class="fs-24 fw-500 text-primary">Thông tin bộ phận đề xuất</span>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">NV đề xuất
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__empProposer__ ? dataObject.__empProposer__.name : '' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">
              {{ language_key?.BRANCH || 'Chi nhánh' }}
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__empProposer__
                && dataObject.__empProposer__.__department__
                && dataObject.__empProposer__.__department__.__branch__
                ? dataObject.__empProposer__.__department__.__branch__.name : '' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">Phòng ban
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__empProposer__
                && dataObject.__empProposer__.__department__
                ? dataObject.__empProposer__.__department__.name : '' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24">
          <hr />
          <span class="fs-24 fw-500 text-primary">Thông tin người phụ trách</span>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">NV phụ trách
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__empInCharge__ ? dataObject.__empInCharge__.name : '' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">Phòng ban
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__empInCharge__
                && dataObject.__empInCharge__.__department__
                ? dataObject.__empInCharge__.__department__ .name : '' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24">Email
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__empInCharge__ ? dataObject.__empInCharge__.email : '' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>


        <nz-col nzSpan="24">
          <hr />
          <span class="fs-24 fw-500 text-primary">Thông tin đề xuất mua hàng</span>
        </nz-col>

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Mã PR</nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.code }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Tên PR</nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.name }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">
              {{ language_key?.STATUS || 'Trạng thái' }}
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ coreService.getEnumElementName(enumPRStatus, dataObject.status) }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Ngày tạo </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.createdAt | date: 'dd/MM/yyyy' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6" *ngIf="dataObject.__branch__">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">
              {{ language_key?.BRANCH || 'Chi nhánh' }}
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__branch__.name }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6" *ngIf="dataObject.__object__">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">
              {{ language_key?.OBJECT || 'Đối tượng' }}
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.__object__.name }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Ngày cần giao</nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.deliveryDate | date: 'dd/MM/yyyy' }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24" *ngIf="dataObject.reason">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Lý do hủy </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.reason }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Địa điểm giao hàng </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.deliveryAddress }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Ghi chú </nz-form-label>
            <nz-form-control nzSpan="24">
              <b>{{ dataObject.description }}</b>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Ảnh </nz-form-label>
            <nz-form-control nzSpan="24">
              <span class="tooltipelement"><img src="{{ dataObject.imgUrl }}" height="auto" width="240px" /></span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">File đính kèm </nz-form-label>
            <nz-form-control *ngIf="dataObject.fileUrl" nzSpan="24">
              <a href="{{dataObject.fileUrl}}" target="_blank">File đính kèm</a>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

      </nz-row>

    </nz-tab>

    <nz-tab nzTitle="Thông Tin Item Và Quyền">

      <nz-collapse class="mt-3">
        <nz-collapse-panel nzHeader="Danh sách item PR" class="ant-bg-antiquewhite" nzActive="true">
          <nz-col nzSpan="24">
            <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="dataObject.lstItem" [nzShowPagination]="true"
              nzBordered>
              <thead>
                <tr>
                  <th>{{ language_key?.NO || 'STT' }}</th>
                  <th>{{ language_key?.MATERIAL_CODE || 'Mã LVKD' }}</th>
                  <th>Tên hàng hóa</th>
                  <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                  <th>Lý do</th>
                  <th>{{ language_key?.NOTE || 'Ghi chú' }}</th>
                  <th>Tùy chọn</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of dataObject.lstItem; let i = index">
                  <td>{{ i + 1 }} </td>
                  <td>{{ data.__service__ ? data.__service__.code : '' }} </td>
                  <td>{{ data.productName }} </td>
                  <td>{{ data.quantity | number }} </td>
                  <td>{{ data.suggestReason }} </td>
                  <td>{{ data.description }} </td>
                  <td>
                    <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-tooltip
                      nzTooltipTitle="Thiết lập yêu cầu kỹ thuật" (click)="itemTech(data)" nz-button nzType="primary">
                      <span nz-icon nzType="robot"></span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </nz-col>
        </nz-collapse-panel>
      </nz-collapse>

      <nz-collapse class="mt-3">
        <nz-collapse-panel nzHeader="Danh sách quyền của PR" class="ant-bg-antiquewhite" nzActive="true">
          <nz-col nzSpan="24">
            <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="dataObject.lstPermission" [nzShowPagination]="true"
              nzBordered>
              <thead>
                <tr>
                  <th>{{ language_key?.NO || 'STT' }}</th>
                  <th>Tên nhân viên</th>
                  <th>Cấp độ quyền</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of dataObject.lstPermission; let i = index">
                  <td>{{ i + 1 }} </td>
                  <td>{{ data.__employee__ ? data.__employee__.name : '' }} </td>
                  <td>{{ data.level}} </td>
                </tr>
              </tbody>
            </nz-table>
          </nz-col>
        </nz-collapse-panel>
      </nz-collapse>

    </nz-tab>

    <nz-tab class="ant-bg-antiquewhite" nzTitle="Lịch Sử">

      <nz-col nzSpan="24">
        <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="['']" nzBordered>
          <thead>
            <tr class="text-nowrap">
              <th>{{ language_key?.NO || 'STT' }}</th>
              <th>Ngày tạo</th>
              <th>Người tạo </th>
              <th>Trạng thái cũ </th>
              <th>{{ language_key?.CURRENT_STATUS || 'Trạng thái hiện tại' }}</th>
              <th>Mô tả</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataC of dataObject.__histories__; let i = index">
              <td class="text-nowrap">{{ i + 1 }} </td>
              <td class="text-nowrap"><b>{{ dataC.createdAt | date: 'HH:mm:ss dd/MM/yyyy'}}</b> </td>
              <td class="text-nowrap">{{ dataC.createdByName }}</td>
              <td class="text-nowrap">
                {{ coreService.getEnumElementName(enumPRStatus, dataC.statusCurrent) }}
              </td>
              <td class="text-nowrap">
                <b>{{ coreService.getEnumElementName(enumPRStatus, dataC.statusConvert) }}</b>
              </td>
              <td>{{ dataC.description }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>

    </nz-tab>

  </nz-tabset>
</div>

<nz-modal [(nzVisible)]="isVisibleCancel" nzTitle="Nhập lý do từ chối" (nzOnCancel)="isVisibleCancel = false"
  [nzWidth]="'60vw'" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Lý do:</h4>
      <nz-col nzSpan="24" class="text-center">
        <textarea nz-input rows="5" auto placeholder="Nhập nội dung gửi" [(ngModel)]="dataReject.reason"></textarea>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [TỪ CHỐI] PR?" nzPopconfirmPlacement="bottom"
          *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" (nzOnConfirm)="confirmCancel()"
          nz-button class="ant-btn-blue">
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleRecheck" nzTitle="Nhập Nội dung điều chỉnh" (nzOnCancel)="isVisibleRecheck = false"
  [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">Nội dung điều chỉnh</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập nội dung điều chỉnh!">
            <textarea nz-input rows="5" auto placeholder="Nhập nội dung điều chỉnh"
              [(ngModel)]="dataObjectReject.noteReCheck"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

  </ng-container>
  <div *nzModalFooter class="text-center">
    <button [disabled]="!dataObjectReject.noteReCheck || dataObjectReject.noteReCheck === ''" nzType="primary"
      (click)="confirmRecheck()" nz-button>
      <span nz-icon nzType="save"></span>Xác nhận
    </button>
    <button (click)="isVisibleRecheck = false" nz-button>
      <span nz-icon nzType="close"></span> Đóng
    </button>
  </div>
</nz-modal>