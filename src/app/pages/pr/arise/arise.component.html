<nz-collapse nzBordered="false">
  <nz-collapse-panel [nzHeader]="language_key?.SEARCH_ADVANCED || 'Tìm kiếm'" class="ant-bg-antiquewhite">
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Tên PR </nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input placeholder="Nhập Tên PR" [(ngModel)]="dataSearch.name" name="name" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"><PERSON><PERSON><PERSON> v<PERSON>t tư </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.serviceId" name="serviceId" nzPlaceHolder="Chọn Loại vật tư">
              <nz-option *ngFor="let item of dataServices" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.status"
              name="status"
              [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'"
            >
              <nz-option *ngFor="let item of dataPRStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">NV đề xuất </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.empProposerId" name="empProposerId" nzPlaceHolder="Chọn NV đề xuất">
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">NV phụ trách </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.empInChargeId" name="empInChargeId" nzPlaceHolder="Chọn NV phụ trách">
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày cần giao </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-range-picker nzFormat="dd/MM/yyyy" [(ngModel)]="dataSearch.deliveryDate"> </nz-range-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
    <nz-row>
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)" class="mr-2">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
        <button *ngIf="authenticationService.checkPermission(enumRole, action.Export.code)" nz-button (click)="onDownloadExcel()">
          <span nz-icon nzType="download"></span> {{ language_key?.DOWNLOAD_EXCEL || 'Tải Excel' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" (click)="clickAdd()" *ngIf="authenticationService.checkPermission(enumRole, action.Create.code)">
      <span nz-icon nzType="plus"></span> {{ language_key?.ADD || 'Thêm mới' }}
    </button>
    <!-- <button nz-button nzType="primary" class="mr-2" (click)="clickImportExcel()">
      <span nz-icon nzType="import"></span> Import Excel
    </button> -->
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
  >
    <thead>
      <tr>
        <th>Mã PR</th>
        <th>Tên PR</th>
        <th>NV đề xuất</th>
        <th>NV phụ trách</th>
        <th>Trạng thái</th>
        <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data" [style.color]="data.noteReCheck && data.status === 'NEW' ? 'orange' : ''">
        <td class="mw-25" (click)="showDetail(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="showDetail(data)">{{ data.name }}</td>
        <td (click)="showDetail(data)">{{ data.empProposerName }}</td>
        <td (click)="showDetail(data)">{{ data.empInChargeName }}</td>
        <td class="mw-25" (click)="showDetail(data)">
          <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
        </td>
        <td>
          <button
            *ngIf="data.isCanceler && authenticationService.checkPermission(enumRole, action.Update.code)"
            class="mr-2"
            (click)="onCancel(data)"
            nz-tooltip
            nzTooltipTitle="HỦY PR"
            nz-button
            nzDanger
          >
            <span nz-icon nzType="stop"></span>
          </button>

          <button
            *ngIf="data.isRecheck && authenticationService.checkPermission(enumRole, action.Update.code)"
            class="mr-2 ant-btn-warning"
            (click)="onRecheck(data)"
            nz-tooltip
            nzTooltipTitle="YÊU CẦU KIỂM TRA LẠI PR"
            nz-button
          >
            <span nz-icon nzType="redo"></span>
          </button>

          <button
            *ngIf="data.isCloser && authenticationService.checkPermission(enumRole, action.Update.code)"
            class="mr-2"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn [ĐÓNG]  ?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="onClose(data)"
            nz-tooltip
            nzTooltipTitle="ĐÓNG"
            nz-button
            nzType="primary"
          >
            <span nz-icon nzType="lock"></span>
          </button>

          <button
            *ngIf="data.isProcess && authenticationService.checkPermission(enumRole, action.Update.code)"
            class="mr-2"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn [THỰC HIỆN] PR ?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="onProcess(data)"
            nz-tooltip
            nzTooltipTitle="THỰC HIỆN"
            nz-button
            nzType="primary"
          >
            <span nz-icon nzType="swap"></span>
          </button>

          <button
            *ngIf="data.isApprover && authenticationService.checkPermission(enumRole, action.Update.code)"
            class="mr-2 ant-btn-success"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn [PHÊ DUYỆT] PR ?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="onApprove(data)"
            nz-tooltip
            nzTooltipTitle="PHÊ DUYỆT"
            nz-button
            nzType="dashed"
          >
            <span nz-icon nzType="check"></span>
          </button>

          <button
            *ngIf="data.isEdit && authenticationService.checkPermission(enumRole, action.Update.code)"
            (click)="clickEdit(data)"
            nz-tooltip
            nzTooltipTitle="Chỉnh sửa"
            nz-button
            nzType="primary"
          >
            <span nz-icon nzType="edit"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Nhập Yêu Cầu Mua Hàng Phát Sinh Bằng Excel" (nzOnCancel)="handleCancel()">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center; display: grid">
      <button
        style="height: 50px; width: 200px"
        nz-button
        nzType="primary"
        *ngIf="authenticationService.checkPermission(enumRole, action.Import.code)"
        (click)="onDownloadTemplate()"
      >
        <span nz-icon nzType="cloud-download" style="font-size: x-large"></span>Export Template
      </button>
      <nz-col nzSpan="24"><br /></nz-col>
      <nz-upload
        nzAction="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        [nzBeforeUpload]="beforeUpload"
        *ngIf="authenticationService.checkPermission(enumRole, action.Import.code)"
        [nzHeaders]="{ authorization: 'authorization-text' }"
        (nzChange)="onImport($event)"
      >
        <button nz-button style="height: 50px; width: 200px">
          <span nz-icon nzType="cloud-upload" style="font-size: x-large"></span>Upload Template
        </button>
      </nz-upload>
    </nz-row>
  </ng-container>
  <ng-container *nzModalFooter>
    <button
      *ngIf="authenticationService.checkPermission(enumRole, action.Import.code)"
      (click)="handleUpload()"
      nz-button
      nzType="primary"
      [disabled]="!checkTemplete"
    >
      <span nz-icon nzType="plus"></span> Tạo Yêu Cầu
    </button>
    <button (click)="handleCancel()" nz-button nzDanger><span nz-icon nzType="close"></span> {{ language_key?.CLOSE || 'Đóng' }}</button>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleCancel" nzTitle="Nhập lý do từ chối" (nzOnCancel)="closeCancel()" [nzWidth]="'60vw'" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Lý do:</h4>
      <nz-col nzSpan="24" class="text-center">
        <textarea nz-input rows="5" auto placeholder="Nhập nội dung gửi" [(ngModel)]="dataReject.reason"></textarea>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button
          *ngIf="authenticationService.checkPermission(enumRole, action.Update.code)"
          nz-popconfirm
          nzPopconfirmTitle="Bạn có chắc muốn [TỪ CHỐI] PR?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="confirmCancel()"
          nz-button
          class="ant-btn-blue"
        >
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleRecheck" nzTitle="Nhập Nội dung điều chỉnh" (nzOnCancel)="onCloseRecheck()" [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">Nội dung điều chỉnh</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập nội dung điều chỉnh!">
            <textarea nz-input rows="5" auto placeholder="Nhập nội dung điều chỉnh" [(ngModel)]="dataRecheck.noteReCheck"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter class="text-center">
    <button [disabled]="!dataRecheck.noteReCheck || dataRecheck.noteReCheck === ''" nzType="primary" (click)="confirmRecheck()" nz-button>
      <span nz-icon nzType="save"></span>Xác nhận
    </button>
    <button (click)="isVisibleRecheck = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>
