import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { NzMessageService } from 'ng-zorro-antd/message'
import { Observable, Observer, Subscription } from 'rxjs'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { AddOrEditAriseComponent } from './add-or-edit-arise/add-or-edit-arise.component'
import { PrAriseDetailComponent } from './pr-arise-detail/pr-arise-detail.component'
import * as moment from 'moment'
import * as XLSX from 'xlsx'
@Component({ templateUrl: './arise.component.html' })
export class AriseComponent implements OnInit {
  pageIndex: any
  pageSize: any
  total: any
  dataPRStatus: any

  listOfData: any[] = []
  dataSearch: any = {}
  loading = true
  enumRole: any
  isVisible = false
  dataUploadExcel: any[] = []
  dataServices: any[] = []
  dataEmployee: any[] = []
  checkTemplete = false
  fileList: any[] = []
  data: any
  isVisibleCancel = false
  dataReject: any = {}
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  action: any
  currentUser: any
  enumData: any
  isVisibleRecheck = false
  dataRecheck: any = {}
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    private msg: NzMessageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.pageIndex = enumData.Page.pageIndex
    this.pageSize = enumData.Page.pageSize
    this.total = enumData.Page.total
    this.dataPRStatus = this.coreService.convertObjToArray(this.enumData.PRStatus)
    this.enumRole = this.enumProject.Features.PR_001.code
    this.action = this.enumProject.Action
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.loadEmployee()
    this.loadService()
    this.apiService.eventCloseModal.subscribe((res) => {
      if (res === true) {
        this.searchData()
      }
    })
  }

  ngOnDestroy() {
    this.apiService.eventReloadPrPlan.next(false)
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }
    if (this.dataSearch.serviceId) {
      where.serviceId = this.dataSearch.serviceId
    }
    if (this.dataSearch.empProposerId) {
      where.empProposerId = this.dataSearch.empProposerId
    }
    if (this.dataSearch.empInChargeId) {
      where.empInChargeId = this.dataSearch.empInChargeId
    }
    if (this.dataSearch.deliveryDate && this.dataSearch.deliveryDate !== '') {
      where.deliveryDate = this.dataSearch.deliveryDate
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    where.prType = enumData.PRType.Arise.code
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.PR.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  showDetail(data: any) {
    this.dialog
      .open(PrAriseDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditAriseComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(data: any) {
    data.quantity = null
    this.dialog
      .open(AddOrEditAriseComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  onDownloadExcel() {
    this.notifyService.showloading()
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }
    if (this.dataSearch.serviceId) {
      where.serviceId = this.dataSearch.serviceId
    }
    if (this.dataSearch.empProposerId) {
      where.empProposerId = this.dataSearch.empProposerId
    }
    if (this.dataSearch.empInChargeId) {
      where.empInChargeId = this.dataSearch.empInChargeId
    }
    if (this.dataSearch.deliveryDate && this.dataSearch.deliveryDate !== '') {
      where.deliveryDate = this.dataSearch.deliveryDate
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    where.prType = enumData.PRType.Arise.code
    const dataSearch: any = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.loading = true
    this.apiService.post(this.apiService.PR.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.notifyService.hideloading()
        if (data && data[0].length > 0) {
          const date = new Date().toISOString()
          const fileName = `YC_Mua_Hang_Phat_Sinh_${date}.xlsx`
          let dataExcel: any[] = []
          data[0].forEach((s: any) => {
            dataExcel.push({
              'Mã PR': s.code,
              'Tên PR': s.name,
              'NV đề xuât': s.empProposerName || '',
              'NV phụ trách': s.empInChargeName || '',
              'Ngày cần giao': moment(s.deliveryDate).format('DD-MM-YYYY'),
              'Số lượng': s.quantity,
              'Địa điểm giao hàng': s.deliveryAddress,
            })
          })
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
          const wb: XLSX.WorkBook = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'YC Mua Hàng Phát Sinh')

          XLSX.writeFile(wb, fileName)
        }
      }
    })
  }

  clickImportExcel() {
    this.isVisible = true
    this.dataUploadExcel = []
    this.fileList = []
  }

  handleCancel() {
    this.isVisible = false
    this.dataUploadExcel = []
    this.fileList = []
  }

  beforeUpload = (file: any, _fileList: any[]) => {
    return new Observable((observer: Observer<boolean>) => {
      const isJpgOrPng =
        file.type === 'application/vnd.ms-excel,application' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isJpgOrPng) {
        this.msg.error('Tệp tin tải lên không đúng định dạng file Excel!')
        observer.complete()
        return
      }
      const isLt2M = file.size! / 1024 / 1024 < 5
      if (!isLt2M) {
        this.msg.error('Tệp tin tải lên không được vượt quá 5MB!')
        observer.complete()
        return
      }
      observer.next(isJpgOrPng && isLt2M)
      observer.complete()
    })
  }

  onDownloadTemplate() {
    let dataExcel: any[] = []
    dataExcel.push({
      'Mã PR': '',
      'Tên PR': '',
      'Mã NV đề xuât': '',
      'Mã NV phụ trách': '',
      'Mã loại vật tư': '',
      'Tên sản phẩm': '',
      'Ngày cần giao': '',
      'Số lượng': '',
      'Địa điểm giao hàng': '',
      'Lý do đề xuất': '',
    })
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
    const wb: XLSX.WorkBook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Mẫu YC Mua Hàng Phát Sinh')
    XLSX.writeFile(wb, 'Template_YC_Mua_Hang_Phat_Sinh.xlsx')
  }

  onImport(ev: any) {
    if (ev.file.status !== 'uploading') {
      // console.log(ev.file, ev.fileList)
    }
    if (ev.file.status === 'done') {
      this.notifyService.showSuccess(`[ ${ev.file.name} ] file đã tải lên thành công!`)

      let workBook = null
      let jsonData: any = null
      const lstHeader = [
        'code',
        'name',
        'empProposerCode',
        'empInChargeCode',
        'serviceCode',
        'productName',
        'deliveryDate',
        'quantity',
        'deliveryAddress',
        'suggestReason',
      ]
      const reader = new FileReader()
      const file = ev.file.originFileObj
      reader.readAsBinaryString(file)
      reader.onload = async () => {
        workBook = XLSX.read(reader.result, { type: 'binary' })
        jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
          raw: true,
          defval: null,
          header: lstHeader,
        })

        // bỏ dòng đầu tiên
        let isErr = false
        const header: any = jsonData.shift()
        if (
          header.code !== 'Mã PR' ||
          header.name !== 'Tên PR' ||
          header.empProposerCode !== 'Mã NV đề xuât' ||
          header.empInChargeCode !== 'Mã NV phụ trách' ||
          header.serviceCode !== 'Mã loại vật tư' ||
          header.productName !== 'Tên sản phẩm' ||
          header.deliveryDate !== 'Ngày cần giao' ||
          header.quantity !== 'Số lượng' ||
          header.deliveryAddress !== 'Địa điểm giao hàng' ||
          header.suggestReason !== 'Lý do đề xuất'
        ) {
          isErr = true
        }

        if (isErr) {
          this.notifyService.showError('File không đúng template mẫu')
          this.checkTemplete = false
          return
        }
        let pushInList: any[] = []

        let strErr = ''
        for (const row of jsonData) {
          let idx = jsonData.indexOf(row) + 1
          let service: any
          let empProposer: any
          let empInCharge: any
          let p: any

          if (row.quantity == null || (typeof row.quantity === 'string' && row.quantity.trim().length == 0)) {
            strErr += 'Dòng ' + idx + ' - Số lượng không được để trống và nhỏ hơn 0 <br>'
          } else {
            p = parseInt(row.quantity)
            if (!p) {
              strErr += 'Dòng ' + idx + ' - Số lượng [ ' + row.quantity + ' ] không đúng định dạng số<br>'
            }
          }

          if (row.code !== null && row.code.trim().length > 0) {
            const existData = this.listOfData.find((c) => c.code.toUpperCase() === row.code.trim().toUpperCase())
            if (existData) {
              strErr += 'Dòng ' + idx + ' - Mã PR [ ' + row.code + ' ] đã tồn tại trong hệ thống<br>'
            }
          }

          if (row.serviceCode !== null && row.serviceCode.trim().length > 0) {
            service = this.dataServices.find((c) => c.code.toUpperCase() === row.serviceCode.trim().toUpperCase())
            if (!service) {
              strErr += 'Dòng ' + idx + ' - Mã loại vật tư [ ' + row.serviceCode + ' ] không tồn tại trong hệ thống<br>'
            }
          }

          if (row.empProposerCode !== null && row.empProposerCode.trim().length > 0) {
            empProposer = this.dataEmployee.find((c) => c.code.toUpperCase() === row.empProposerCode.trim().toUpperCase())
            if (!empProposer) {
              strErr += 'Dòng ' + idx + ' - Mã NV đề xuât [ ' + row.empProposerCode + ' ] không tồn tại trong hệ thống<br>'
            }
          }

          if (row.empInChargeCode !== null && row.empInChargeCode.trim().length > 0) {
            empInCharge = this.dataEmployee.find((c) => c.code.toUpperCase() === row.empInChargeCode.trim().toUpperCase())
            if (!empInCharge) {
              strErr += 'Dòng ' + idx + ' - Mã NV phụ trách [ ' + row.empInChargeCode + ' ] không tồn tại trong hệ thống<br>'
            }
          }

          if (strErr.length > 0) {
            break
          }
          const item = {
            code: row.code,
            name: row.name,
            productName: row.productName,
            deliveryDate: row.deliveryDate,
            quantity: row.quantity,
            serviceId: null,
            empProposerId: null,
            empInChargeId: null,
            suggestReason: row.suggestReason,
            deliveryAddress: row.deliveryAddress,
          }
          if (service !== undefined && service !== null) {
            item.serviceId = service.id
          }
          if (empProposer !== undefined && empProposer !== null) {
            item.empProposerId = empProposer.id
          }
          if (empInCharge !== undefined && empInCharge !== null) {
            item.empInChargeId = empInCharge.id
          }
          pushInList.push(item)
        }
        if (strErr.length > 0) {
          this.notifyService.showError(strErr)
          this.checkTemplete = false
          return
        } else if (pushInList.length > 0) {
          pushInList.forEach((s) => {
            this.dataUploadExcel = [...this.dataUploadExcel, s]
          })
          this.checkTemplete = true
        }
      }
    } else if (ev.file.status === 'error') {
      this.notifyService.showError(`[ ${ev.file.name} ] file tải lên không đúng định dạng.`)
      this.checkTemplete = false
    }
  }

  handleUpload() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.CREATELIST, this.dataUploadExcel).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.fileList = []
        this.handleCancel()
        this.searchData()
      }
    })
  }

  loadService() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataServices = result
    })
  }

  loadEmployee() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataEmployee = result
    })
  }

  onApprove(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.APPROVE, data).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onCancel(data: any) {
    this.data = data
    this.isVisibleCancel = true
  }

  closeCancel() {
    this.isVisibleCancel = false
    this.dataReject.reason = ''
  }

  confirmCancel() {
    this.notifyService.showloading()
    const data = this.data
    if (this.dataReject.reason == null || this.dataReject.reason.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do')
      return
    }
    data.reason = this.dataReject.reason
    this.apiService.post(this.apiService.PR.CANCEL, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisibleCancel = false
      this.searchData()
    })
  }

  onProcess(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.PROCESS, data).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onClose(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.CLOSE, data).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onRecheck(data: any) {
    data.noteReCheck = ''
    this.dataRecheck = data
    this.isVisibleRecheck = true
  }

  onCloseRecheck() {
    this.dataRecheck.noteRecheck = null
    this.isVisibleRecheck = false
  }

  confirmRecheck() {
    this.notifyService.showloading()
    const data = this.dataRecheck
    if (this.dataRecheck.noteReCheck == null || this.dataRecheck.noteReCheck.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập nội dung cần điều chĩnh')
      return
    }
    data.noteReCheck = this.dataRecheck.noteReCheck
    data.id = this.dataRecheck.id
    this.apiService.post(this.apiService.PR.RECHECK, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisibleRecheck = false
      this.searchData()
    })
  }
}
