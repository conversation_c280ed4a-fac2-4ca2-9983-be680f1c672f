import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Observable, Observer, Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { User } from '../../../../models'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-arise.component.html' })
export class AddOrEditAriseComponent implements OnInit {
  currentUser!: User
  modalTitle = 'THÊM MỚI PR PHÁT SINH'
  dateFormat = 'dd/MM/yyyy'
  dataObject: any = {}
  dataCategory: any[] = []
  lstUnit: any[] = []
  otherOptions: any[] = []
  options: any[] = []
  uploadUrl: string = this.apiService.UploadUrl
  dataEmployee: any[] = []
  dataServices: any[] = []
  dataMaterial: any[] = []
  dataBranch: any[] = []
  nzOptions: NzCascaderOption[] | null = null
  isUploading = false
  previewImage: string | undefined = ''
  previewVisible = false
  dataObjectMaterial: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditAriseComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  changeNzOptions(): void {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadEmployee()
    this.loadAllDataSelect()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.dataObject.lstItem = []
      this.modalTitle = 'CẬP NHẬT PR PHÁT SINH'
      this.searchData(this.data)
      this.setServiceLevel1()
    } else {
      this.dataObject.lstItem = []
      this.dataObject.serviceLevel1 = null
    }
  }

  searchData(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.FINDDETAIL, data).then((result) => {
      this.dataObject = result
      result.lstItem.forEach((item: any) => {
        item.serviceName = item.__service__.name
      })
      let employee = result.lstPermission.filter((c: any) => c.level === 1).map((c: any) => c.employeeId)
      this.dataObject.employeeId = employee[0]
      let arr = result.lstPermission.filter((c: any) => c.level === 2).map((c: any) => c.employeeId)
      this.dataObject.lstEmployee = arr
      this.dataObject.lstFile = []
      this.dataObject.lstImage = []
      if (this.dataObject.fileUrl != null) this.dataObject.lstFile = [{ name: 'file đính kèm', status: 'done', url: this.dataObject.fileUrl }]

      if (this.dataObject.imgUrl != null) this.dataObject.lstImage = [{ name: 'c.fileName2', status: 'done', url: this.dataObject.imgUrl }]
      this.dataObject.lstItem = result.lstItem
      this.dataObject.quantity = null
      this.notifyService.hideloading()
      if (this.dataObject && this.dataObject.id) {
        this.onChangeInCharge(this.dataObject.empInChargeId)
        this.onChangeProposer(this.dataObject.empProposerId)
      }
    })
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.prType = enumData.PRType.Arise.code
    if (this.dataObject.lstItem.length === 0) {
      this.notifyService.showError('Vui lòng thêm ít nhất 1 item')
      return
    }
    if (!this.dataObject.isChangePrItem) {
      this.dataObject.isChangePrItem = this.dataObject.lstItem.some((c: any) => c.isNew)
    }
    if (!this.dataObject.lstEmployee || this.dataObject.lstEmployee.length === 0) {
      this.notifyService.showError('Vui lòng thêm ít nhất 1 người duyệt cấp 2')
      return
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.PR.ARISECREATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(true)
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.PR.ARISEUPDATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  loadEmployee() {
    this.notifyService.showloading()
    // isGet: lấy thêm thông tin cần để xử lý
    this.apiService.post(this.apiService.EMPLOYEE.FIND, { isGet: true }).then((result) => {
      this.notifyService.hideloading()
      this.dataEmployee = result
    })
  }

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>((resolve) => {
      if (index < 0) {
        // if index less than 0 it is root node
        node.children = this.dataServices
          .filter((c) => c.level === 1)
          .map((c) => {
            return { value: c.id, label: c.name, isLeaf: c.isLast }
          })
      } else {
        node.children = this.dataServices
          .filter((c) => c.parentId === node.value)
          .map((c) => {
            return { value: c.id, label: c.name, isLeaf: c.isLast }
          })
      }
      resolve()
    })
  }

  onChangeProposer(id: any) {
    const emp = this.dataEmployee.find((s) => s.id === id)
    this.dataObject.pBranchName = emp?.branchName || ''
    this.dataObject.pDepartmentName = emp?.departmentName || ''
  }

  onChangeInCharge(id: any) {
    const emp = this.dataEmployee.find((s) => s.id === id)
    this.dataObject.iBranchName = emp?.branchName || ''
    this.dataObject.iDepartmentName = emp?.departmentName || ''
    this.dataObject.iEmail = emp?.email || ''
  }

  onAdd() {
    if (this.dataObject.serviceChoose && this.dataObject.serviceChoose.length > 0) {
      if (this.dataObject.serviceLevel1) {
        if (this.dataObject.serviceLevel1 !== this.dataObject.serviceChoose[0]) {
          this.notifyService.showError('Vui lòng chọn cùng nhóm vật tư !')
          return
        }
      } else {
        this.dataObject.serviceLevel1 = this.dataObject.serviceChoose[0]
      }
      this.dataObject.serviceId = this.dataObject.serviceChoose[this.dataObject.serviceChoose.length - 1]
    } else {
      this.notifyService.showError('Vui lòng chọn vật tư.')
      return
    }

    let serviceName = this.dataServices.filter((x) => x.id === this.dataObject.serviceId)
    const data = {
      serviceId: this.dataObject.serviceId,
      quantity: this.dataObject.quantity,
      productName: this.dataObject.productName,
      itemId: this.dataObject.itemId,
      suggestReason: this.dataObject.suggestReason,
      serviceName: serviceName[0].name,
      description: this.dataObject.description,
      unit: this.dataObject.unit,
      isNew: true,
    }
    this.dataObject.lstItem.push(data)
    this.reserLstIteṃ̣()
  }

  reserLstIteṃ̣() {
    this.dataObject.serviceId = ''
    this.dataObject.itemId = ''
    this.dataObject.quantity = ''
    this.dataObject.productName = ''
    this.dataObject.suggestReason = ''
    this.dataObject.serviceChoose = ''
    this.dataObject.description = ''
    this.dataObject.unit = ''
  }

  onDelete(index: any) {
    if (this.dataObject.lstItem[index]?.id) this.dataObject.isChangePrItem = true
    this.dataObject.lstItem.splice(index, 1)
    this.dataObject.lstItem = this.dataObject.lstItem.filter((x: any) => x.serviceId !== null)

    if (this.dataObject.lstItem.length === 0) {
      this.dataObject.serviceLevel1 = null
    }
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.BRANCH.FIND, {}),
      this.apiService.post(this.apiService.OBJECT.FIND, {}),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SERVICE.FIND, {}),
      this.apiService.post(this.apiService.MATERIAL.FIND, {}), // 5
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.dataBranch = res[0]
      this.dataObjectMaterial = res[1]
      this.lstUnit = res[2]
      this.dataServices = res[3]
      this.dataMaterial = res[4]
      this.setServiceLevel1()
    })
  }

  loadItem() {
    for (const item of this.dataMaterial) {
      if (item.id === this.dataObject.itemId) {
        this.dataObject.productName = item.name
        break
      }
    }
  }

  // FILE
  handlePreview = async (file: NzUploadFile) => {
    if (!file.url && !file['preview']) {
      file['preview'] = await this.coreService.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file['preview']
    this.previewVisible = true
  }
  handleChange(info: { file: NzUploadFile; fileList: NzUploadFile[] }, lstImage: any[]) {
    let arr = []
    switch (info.file.status) {
      case 'uploading':
        this.isUploading = true
        break
      case 'done':
        this.isUploading = false
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              const url = item?.url || item?.response[0] || item?.response?.fileUrl || ''
              arr.push({
                url: url,
                name: item?.response?.fileName || item?.name,
                uid: item.uid,
              })
            }
            lstImage = arr
          }
        }
        break
      case 'error':
        this.isUploading = false
        break
      case 'removed':
        {
          lstImage.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) lstImage.splice(index, 1)
          })
        }
        break
    }
  }

  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      if (file.size! > enumData.maxSizeUpload * 1024 * 1024) {
        this.notifyService.showWarning('Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.')
        observer.complete()
        return
      }
      observer.next(true)
      observer.complete()
    })

  setServiceLevel1() {
    if (this.dataServices.length > 0 && this.dataObject.lstItem.length > 0) {
      let childId = this.dataObject.lstItem[0].serviceId
      for (let index = 0; index < 4; index++) {
        const element = this.dataServices.find((s) => s.id === childId)
        if (element) {
          if (element.level === 1) {
            this.dataObject.serviceLevel1 = element.id
            break
          } else {
            childId = element.parentId
          }
        } else {
          break
        }
      }
    }
  }
}
