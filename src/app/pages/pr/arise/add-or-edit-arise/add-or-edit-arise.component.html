<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent *ngIf="dataObject">
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <span class="fs-24 fw-500 text-primary">Thông tin bộ phận đề xuất</span>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24" nzRequired>NV đề xuất </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.empProposerId"
              name="empProposerId"
              nzPlaceHolder="Chọn NV đề xuất"
              (ngModelChange)="onChangeProposer($event)"
              required
            >
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8" *ngIf="dataObject.pBranchName">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.BRANCH || 'Chi nhánh' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <input style="font-weight: bold" nz-input [(ngModel)]="dataObject.pBranchName" name="pBranchName" readonly />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8" *ngIf="dataObject.pDepartmentName">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Phòng ban </nz-form-label>
          <nz-form-control nzSpan="24">
            <input style="font-weight: bold" nz-input [(ngModel)]="dataObject.pDepartmentName" name="pDepartmentName" readonly />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <hr />
        <span class="fs-24 fw-500 text-primary">Thông tin người phụ trách</span>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24" nzRequired>NV phụ trách </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.empInChargeId"
              name="empInChargeId"
              nzPlaceHolder="Chọn NV phụ trách"
              (ngModelChange)="onChangeInCharge($event)"
              required
            >
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8" *ngIf="dataObject.iEmail">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Email </nz-form-label>
          <nz-form-control nzSpan="24">
            <input style="font-weight: bold" nz-input [(ngModel)]="dataObject.iEmail" name="iEmail" readonly />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <hr />
        <span class="fs-24 fw-500 text-primary">Thông tin đề xuất mua hàng</span>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ghi chú </nz-form-label>
          <nz-form-control nzSpan="24">
            <textarea nz-input rows="3" auto placeholder="Nhập ghi chú " name="note" [(ngModel)]="dataObject.description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">
            {{ language_key?.BRANCH || 'Chi nhánh' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn chi nhánh'"
              [(ngModel)]="dataObject.branchId"
              name="branchId"
            >
              <nz-option *ngFor="let item of dataBranch" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên PR</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tên PR ">
            <input nz-input placeholder="Nhập Tên PR" [(ngModel)]="dataObject.name" name="name" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>
            {{ language_key?.OBJECT || 'Đối tượng' }}
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [nzPlaceHolder]="language_key?.OBJECT_ENTER || 'Chọn đối tượng'"
              [(ngModel)]="dataObject.objectId"
              name="objectId"
              required
            >
              <nz-option *ngFor="let item of dataObjectMaterial" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày cần giao</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Ngày cần giao">
            <nz-date-picker [nzFormat]="dateFormat" name="deliveryDate" nzPlaceHolder="Ngày cần giao" [(ngModel)]="dataObject.deliveryDate" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Địa điểm giao hàng </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Địa điểm giao hàng !">
            <textarea
              nz-input
              rows="1"
              auto
              placeholder="Nhập Địa điểm giao hàng "
              [(ngModel)]="dataObject.deliveryAddress"
              name="deliveryAddress"
              required
            ></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired> Người duyệt Cấp 1</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn người duyệt "
              [(ngModel)]="dataObject.employeeId"
              (ngModelChange)="dataObject.isChangePrApprover = true"
              name="employeeId"
              required
            >
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired> Người duyệt Cấp 2</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Chọn người duyệt PR Cấp 2 !">
            <nz-select
              [(ngModel)]="dataObject.lstEmployee"
              (ngModelChange)="dataObject.isChangePrApprover = true"
              [ngModelOptions]="{ standalone: true }"
              nzMode="multiple"
              nzPlaceHolder=" Người duyệt cấp 2"
              required
            >
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <div nz-col [nzSpan]="12">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">File đính kèm</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-upload
              nzListType="picture-card"
              [(nzFileList)]="dataObject.lstFile"
              [nzShowButton]="dataObject?.lstFile?.length < 1"
              [nzAction]="uploadUrl"
              [nzPreview]="handlePreview"
              [nzMultiple]="false"
              (nzChange)="handleChange($event, dataObject.lstFile)"
              nzAccept=".xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              nzShowButton
            >
              <div>
                <i nz-icon nzType="plus"></i>
                <div style="margin-top: 8px">Upload</div>
              </div>
            </nz-upload>
            <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null" (nzOnCancel)="previewVisible = false">
              <ng-template #modalContent>
                <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
              </ng-template>
            </nz-modal>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col [nzSpan]="12">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Hình ảnh</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-upload
              nzListType="picture-card"
              [(nzFileList)]="dataObject.lstImage"
              [nzShowButton]="dataObject?.lstImage?.length < 1"
              [nzAction]="uploadUrl"
              [nzPreview]="handlePreview"
              [nzMultiple]="false"
              (nzChange)="handleChange($event, dataObject.lstImage)"
              nzAccept=".png, .jpg, .jpeg, .svg, .webp"
              nzShowButton
            >
              <div>
                <i nz-icon nzType="plus"></i>
                <div style="margin-top: 8px">Upload</div>
              </div>
            </nz-upload>
            <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null" (nzOnCancel)="previewVisible = false">
              <ng-template #modalContent>
                <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
              </ng-template>
            </nz-modal>
          </nz-form-control>
        </nz-form-item>
      </div>
    </nz-row>

    <nz-collapse>
      <nz-collapse-panel nzHeader="Danh sách item PR" class="ant-bg-antiquewhite" nzActive="true">
        <form nz-form #frmAdd2="ngForm">
          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>
                  {{ language_key?.MATERIAL_GROUP || 'Nhóm vật tư' }}
                </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Nhóm vật tư">
                  <nz-cascader
                    [nzOptions]="nzOptions"
                    [nzPlaceHolder]="language_key?.MATERIAL_ENTER || 'Chọn Vật tư'"
                    [(ngModel)]="dataObject.serviceChoose"
                    name="serviceChoose"
                    id="serviceChoose"
                    [nzLoadData]="loadDataService"
                    [nzShowSearch]="true"
                  >
                  </nz-cascader>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên hàng hóa </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tên hàng hóa ">
                  <!-- <input nz-input placeholder="Nhập Tên hàng hóa " [(ngModel)]="dataObject.productName"
                    name="productName" required /> -->
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn hàng hóa'"
                    [(ngModel)]="dataObject.itemId"
                    name="itemId"
                    id="itemId"
                    (ngModelChange)="loadItem()"
                    [ngModelOptions]="{ standalone: true }"
                  >
                    <nz-option *ngFor="let item of this.dataMaterial" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Đơn vị tính</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Đơn vị tính">
                  <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.unit" name="unit" nzPlaceHolder="Chọn đơn vị tính" required>
                    <nz-option *ngFor="let item of lstUnit" [nzLabel]="item.code" [nzValue]="item.code"> </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.QUANTITY || 'Số lượng' }} </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Số lượng (Tối đa 7 số)">
                  <input
                    nz-input
                    currencyMask
                    [options]="{ prefix: '', precision: 0, allowNegative: false }"
                    [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'"
                    [(ngModel)]="dataObject.quantity"
                    name="quantity"
                    pattern=".{1,7}"
                    required
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Lý do đề xuất </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Lý do đề xuất !">
                  <textarea
                    nz-input
                    rows="1"
                    auto
                    placeholder="Nhập Lý do đề xuất "
                    [(ngModel)]="dataObject.suggestReason"
                    name="suggestReason"
                    required
                  ></textarea>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="24">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Mô tả </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Mô tả !">
                  <textarea
                    nz-input
                    rows="1"
                    auto
                    placeholder="Nhập Mô tả "
                    [(ngModel)]="dataObject.description"
                    name="description"
                    required
                  ></textarea>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
          <nz-col nzSpan="24" class="text-center mb-3">
            <button nz-button (click)="onAdd()" [disabled]="!frmAdd2.form.valid" nzType="primary">
              <span nz-icon nzSearch nzType="plus"></span>Thêm
            </button>
          </nz-col>
        </form>

        <nz-col nzSpan="24">
          <nz-table
            nz-col
            nzSpan="24"
            [nzData]="dataObject.lstItem && dataObject.lstItem.length > 0 ? [''] : []"
            [nzShowPagination]="false"
            nzBordered
          >
            <thead>
              <tr>
                <th>{{ language_key?.NO || 'STT' }}</th>
                <th>{{ language_key?.MATERIAL_CODE || 'Tên LVKD' }}</th>
                <th>Đơn vị tính</th>
                <th>Tên hàng hóa</th>
                <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th>Lý do</th>
                <th>Mô tả</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstItem; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.productName }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.suggestReason }}</td>
                <td>{{ data.description }}</td>
                <td>
                  <button nz-tooltip [nzTooltipTitle]="language_key?.DELETE || 'Xóa'" nz-button nzDanger (click)="onDelete(i)">
                    <span nz-icon nzType="delete"></span>
                  </button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid || !dataObject.lstItem || dataObject.lstItem.length === 0" nzType="primary" (click)="onSave()">
        <span nz-icon nzType="save"></span> {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>
