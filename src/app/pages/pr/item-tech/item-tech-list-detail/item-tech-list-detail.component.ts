import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import * as uuid from 'uuid'

@Component({ templateUrl: './item-tech-list-detail.component.html' })
export class ItemTechListDetailComponent implements OnInit, OnDestroy {
  modalTitle = 'C<PERSON>u hình danh sách'
  loading = true
  editCache: any = {}
  listOfData: any[] = []
  pageSize = enumData.Page.pageSizeMax
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  action: any
  currentUser: any
  enumRole: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.SETTING_001.code
    this.action = this.enumProject.Action
    this.searchData()
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }
  startEdit(id: string) {
    this.editCache[id].edit = true
  }

  cancelEdit(id: string) {
    const index = this.listOfData.findIndex((c) => c.id === id)
    if (this.editCache[id].data.isNew !== true) {
      this.editCache[id] = {
        data: { ...this.listOfData[index] },
        edit: false,
      }
    } else {
      this.listOfData = this.listOfData.filter((d) => d.id !== id)
      delete this.editCache[id]
    }
  }

  saveEdit(id: string) {
    this.notifyService.showloading()
    const index = this.listOfData.findIndex((c) => c.id === id)
    if (!this.editCache[id].data.name) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }
    if (this.editCache[id].data.name.length > 250) {
      this.notifyService.showError(enumData.Warnings.TooLong)
      return
    }
    if (this.editCache[id].data.value === null) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }
    if (this.editCache[id].data.value < 0 || this.editCache[id].data.value > 100) {
      this.notifyService.showError('Vui lòng nhập số (0-100)')
      return
    }

    let url = this.apiService.PR.ITEM.ITEMTECHLISTDETAIL_UPDATE
    if (this.editCache[id].data.isNew === true) {
      delete this.editCache[id].data.id
      url = this.apiService.PR.ITEM.ITEMTECHLISTDETAIL_CREATE
    }

    this.apiService.post(url, this.editCache[id].data).then((result) => {
      if (result) {
        this.editCache[id].edit = false
        if (!this.editCache[id].data.isNew) {
          Object.assign(this.listOfData[index], this.editCache[id].data)
        } else if (result.id) {
          const item = this.editCache[id].data
          item.id = result.id
          delete item.isNew
          Object.assign(this.listOfData[index], item)

          delete this.editCache[id]
          this.editCache[item.id] = {
            edit: false,
            data: { ...item },
          }
        }
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      }
    })
  }

  updateEditCache() {
    this.listOfData.forEach((item) => {
      this.editCache[item.id] = {
        edit: false,
        data: { ...item },
      }
    })
  }

  addRow() {
    const item = {
      id: uuid.v4(),
      name: '',
      value: 0,
      itemTechId: this.data.id,
      isNew: true,
    }
    this.listOfData = [...this.listOfData, item]
    this.editCache[item.id] = {
      edit: true,
      data: { ...item },
    }
  }

  async searchData() {
    this.loading = true
    this.apiService.get(this.apiService.PR.ITEM.ITEMTECHLISTDETAIL_LIST(this.data.id), {}).then((res) => {
      this.loading = false
      this.listOfData = res
      this.updateEditCache()
    })
  }

  startDelete(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PR.ITEM.ITEMTECHLISTDETAIL_DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.listOfData = this.listOfData.filter((d) => d.id !== data.id)
      delete this.editCache[data.id]
    })
  }
}
