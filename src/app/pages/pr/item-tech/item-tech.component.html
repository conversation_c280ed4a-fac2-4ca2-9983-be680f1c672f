<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8">
    <nz-col nzSpan="18">
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" class="mr-2" nz-button
        (click)="clickAdd()" nzType="primary">
        <span nz-icon nzType="plus"></span>
        {{ language_key?.ADD || 'Thêm mới' }}
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)" class="mr-2" nz-button
        (click)="clickDeleteAll()" nzDanger>Xoá tất cả
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="mr-2" nz-button
        (click)="clickExportExcel()">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
      <input *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="hidden" type="file"
        id="file" (change)="clickImportExcel($event)" onclick="this.value=null"
        accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
      <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file"
        class="lable-custom-file">
        <span nz-icon nzType="upload"></span>
        {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
      </label>
    </nz-col>
    <nz-col nzSpan="6">
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
        (click)="loadTech()" class="ant-btn-blue">
        Tải yêu cầu kỹ thuật từ template
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <span *ngIf="sumPercent < 100" class="text-orange"> Tổng tỉ trọng đạt {{ sumPercent }}%, chưa đủ 100% </span>
    <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <!-- header gid level 1 -->
        <tr>
          <th>{{ language_key?.NO || 'STT' }}</th>
          <th>Tên tiêu chí</th>
          <th>Tỉ trọng(%)</th>
          <th>Giá trị đạt</th>
          <th>Kiểu dữ liệu</th>
          <th>Bắt buộc?</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let rowData of listOfData">
          <tr>
            <td (click)="clickEdit(rowData)">
              {{ rowData.sort > 0 ? rowData.sort : '' }}</td>
            <td (click)="clickEdit(rowData)" class="mw-25">
              {{ rowData.name }}
            </td>
            <td (click)="clickEdit(rowData)" class="text-right">{{ rowData.percent }}</td>
            <td (click)="clickEdit(rowData)" class="text-right">{{ rowData.percentRule | number }}</td>
            <td (click)="clickEdit(rowData)">
              {{ rowData.type }}</td>
            <td (click)="clickEdit(rowData)">
              {{ rowData.isRequired ? 'Bắt buộc' : 'Không' }}</td>
            <td>
              <button
                *ngIf="rowData.type === dataType.List.code && authenticationService.checkPermission([enumRole], action.Update.code)"
                nz-tooltip nzTooltipTitle="Thiết lập danh sách" class="mr-3" nz-button
                [nzType]="rowData.__itemTechListDetails__?.length > 0 ? 'default' : 'dashed'"
                (click)="settingList(rowData)">
                <span nz-icon nzType="unordered-list"></span>
              </button>
              <button *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)" nz-popconfirm
                nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?" nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="clickDelete(rowData)" nz-tooltip nzTooltipTitle="Xoá tiêu chí" nz-button nzDanger>
                <span nz-icon nzType="delete"></span>
              </button>
            </td>
          </tr>
          <ng-container>
            <td [nzIndentSize]="10" colspan="8" scope="colgroup"
              *ngIf="rowData.sumPercent < 100 && rowData.sumPercent >= 0" class="text-orange">
              Tổng tỉ trọng trong mục này đạt {{ rowData.sumPercent }}%, chưa đủ 100%
            </td>
            <tr *ngFor="let item of rowData.__childs__">
              <td (click)="clickEdit(item)" [nzIndentSize]="10">{{ item.sort > 0 ? item.sort : '' }}</td>
              <td (click)="clickEdit(item)" class="mw-25">
                {{ item.name }}
              </td>
              <td (click)="clickEdit(item)" class="text-right">{{ item.percent }}</td>
              <td (click)="clickEdit(item)" class="text-right">{{ item.percentRule | number }}</td>
              <td (click)="clickEdit(item)">
                {{ item.type }}</td>
              <td (click)="clickEdit(item)">
                {{ item.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td>
                <button
                  *ngIf="item.type === dataType.List.code && authenticationService.checkPermission([enumRole], action.Update.code)"
                  nz-tooltip nzTooltipTitle="Thiết lập danh sách" class="mr-3" nz-button
                  [nzType]="item.__itemTechListDetails__?.length > 0 ? 'default' : 'dashed'"
                  (click)="settingList(item)">
                  <span nz-icon nzType="unordered-list"></span>
                </button>
                <button *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)" nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?" nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="clickDelete(item)" nz-tooltip nzTooltipTitle="Xoá tiêu chí" nz-button nzDanger>
                  <span nz-icon nzType="delete"></span>
                </button>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>
</div>