import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { InboundComponent } from './inbound/inbound.component'
import { AddOrEditInboundComponent } from './inbound/add-or-edit-inbound/add-or-edit-inbound.component'
import { InboundDetailComponent } from './inbound/inbound-detail/inbound-detail.component'
import { ShipmentScheduleComponent } from './shipment-schedule/shipment-schedule.component'

const routes: Routes = [
  { path: 'inbound', component: InboundComponent },
  { path: 'inbound/add', component: AddOrEditInboundComponent },
  { path: 'inbound/edit/:id', component: AddOrEditInboundComponent },
  { path: 'inbound/detail', component: InboundDetailComponent },
  { path: 'shipment-schedule', component: ShipmentScheduleComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class InboundRoutingModule {}
