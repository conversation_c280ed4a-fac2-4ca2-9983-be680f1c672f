import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { NzCalendarMode } from 'ng-zorro-antd/calendar'
import { ApiService, AuthenticationService, NotifyService } from 'src/app/services'
import { PopupListInboundComponent } from './popup-list-inbound/popup-list-inbound.component'

@Component({
  templateUrl: './shipment-schedule.component.html',
  styleUrls: ['./shipment-schedule.component.scss'],
})
export class ShipmentScheduleComponent {
  currentUser: any
  date = new Date()
  mode: NzCalendarMode = 'month'
  dataCalendar: any = {}
  dataSearch: any = {}

  lstCompany: any[] = []
  objPermission: any = {}
  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
  }

  ngOnInit() {
    this.loadDataSelect()

    this.date = new Date()
    this.loadDataByMonth()
  }

  async loadDataByMonth(date?: any) {
    await this.apiService
      .post(this.apiService.INBOUND.LOAD_DATA_BY_MONTH, {
        date: this.date,
      })
      .then((res) => {
        this.dataCalendar = res
      })
  }

  async loadDataSelect() {}

  async companyChange(companyId: string) {
    this.dataSearch.companyId = companyId
  }

  onViewDetail(data: any) {
    this.dialog
      .open(PopupListInboundComponent, { data: { date: data.dateTime } })
      .afterClosed()
      .subscribe(() => {
        this.loadDataByMonth()
      })
  }

  onPrev() {
    this.date = new Date(this.date.getFullYear(), this.date.getMonth() - 1, this.date.getDate())
    this.loadDataByMonth()
  }

  onNext() {
    this.date = new Date(this.date.getFullYear(), this.date.getMonth() + 1, this.date.getDate())
    this.loadDataByMonth()
  }

  onChangeDate() {
    this.loadDataByMonth()
  }
  getDateFormat() {
    return "'Tháng' MM/YYYY"
  }
}
