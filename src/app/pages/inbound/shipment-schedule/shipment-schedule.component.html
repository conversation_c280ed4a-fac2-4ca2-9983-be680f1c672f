<nz-row class="mb-3">
  <nz-breadcrumb>
    <nz-breadcrumb-item>
      <span nz-icon nzType="home" nzTheme="outline"></span>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      <span nz-icon nzType="home" nzTheme="outline"></span>
      <span>Quản Trị <PERSON></span>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>Lịch giao hàng</nz-breadcrumb-item>
  </nz-breadcrumb>
</nz-row>

<nz-row class="mt-2" [nzGutter]="8">
  <nz-col [nzSpan]="16">
    <button nz-button nzType="default" class="mr-2" (click)="onPrev()"><span nz-icon nzType="arrow-left"></span>Tháng trước</button>
    <nz-date-picker [nzMode]="'month'" [(ngModel)]="date" (ngModelChange)="onChangeDate()" style="width: 200px" class="mr-2"></nz-date-picker>
    <button nz-button nzType="default" class="mr-2" (click)="onNext()">Tháng sau<span nz-icon nzType="arrow-right"></span></button>
  </nz-col>
</nz-row>
<nz-calendar [(ngModel)]="date" [(nzMode)]="mode" [nzDateCell]="dateCellTpl" (nzSelectChange)="loadDataByMonth()"> </nz-calendar>
<ng-template #dateCellTpl let-date>
  <ng-container *ngIf="dataCalendar[date.toLocaleDateString()]">
    <div class="text-center" (click)="onViewDetail(dataCalendar[date.toLocaleDateString()])">
      <i nz-icon nzType="calendar" style="color: #0085ff; font-size: 36px"></i>
      <div class="date-item-style">
        {{ 'SL IB: ' + dataCalendar[date.toLocaleDateString()].total }}
      </div>
    </div>
  </ng-container>
</ng-template>

<style>
  .date-item-style {
    color: #0085ff;
    background: #0085ff1a;
  }

  :host() ::ng-deep.ant-picker-calendar-header {
    display: none;
  }

  :host() ::ng-deep.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: transparent;
    border: none;
    border-radius: 4px;
    background: #f5f6ff;
    color: #0470c3;
  }
</style>
