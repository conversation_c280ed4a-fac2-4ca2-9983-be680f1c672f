<!-- title -->
<div class="p-3">
  <nz-row>
    <nz-col nzSpan="24" class="text-center fs-24 fw-600">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-table
      nz-col
      nzSpan="24"
      class="mb-3"
      #basicTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr class="text-nowrap">
          <th nzWidth="100px">STT</th>
          <th nzWidth="100px">{{ 'Số Inbound' }}</th>
          <th nzWidth="100px">{{ 'Số HĐ ' }}</th>
          <th nzWidth="100px">{{ 'Số PO' }}</th>
          <th nzWidth="100px">{{ 'NCC' }}</th>
          <th nzWidth="100px">{{ '<PERSON><PERSON> <PERSON><PERSON>ận dự kiến' }}</th>
          <th nzWidth="100px">{{ 'Tổng SL sản phẩm' }}</th>
          <th nzWidth="100px">{{ 'Tổng SL giao' }}</th>
          <th nzWidth="100px">{{ 'Tổng SL chưa giao' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index">
          <td>{{ i + 1 }}</td>
          <td>{{ data.inboundNumber }}</td>
          <td>{{ data.contractName }}</td>
          <td>{{ data.poNumber }}</td>
          <td>{{ data.supplierName }}</td>
          <td>{{ data.expectWarehouseName }}</td>
          <td>{{ data.totalProduct }}</td>
          <td>{{ data.totalProductNotShip }}</td>
          <td>{{ data.totalProductHasShip }}</td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>

  <nz-row>
    <nz-col nzSpan="24" class="text-center">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="this.searchData()"
        (nzPageSizeChange)="this.searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
    </nz-col>
  </nz-row>
</div>
