import { Component, Inject } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  templateUrl: './popup-list-inbound.component.html',
  styleUrls: ['./popup-list-inbound.component.scss'],
})
export class PopupListInboundComponent {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataSearch: any = {}
  dataStatus = this.coreService.convertObjToArray(enumData.SupplierStatus)
  listOfData: any[] = []
  errorString!: string
  isExporting = false
  lstErrorImport: any[] = []
  isVisibleError = false
  isVisibleActive = false
  placeholder = ['Từ ngày', 'Đến ngày']
  isVisibleChangePw = false
  supplierChoose: any

  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumData: any
  dataActive: any = {}
  currentSupplierData: any = {}
  modalTitle = 'Danh sách inbound'
  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private router: Router,
    public authenticationService: AuthenticationService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))

    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.searchData()
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    if (clearFilter) this.dataSearch = {}
    if (reset) this.pageIndex = 1

    this.dataSearch.date = this.data.date

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.INBOUND.LOAD_PAGINATION_BY_DATE, dataSearch).then((res) => {
      this.loading = false
      this.listOfData = res[0]
      this.total = res[1]
    })
  }

  closeModelError() {
    this.isVisibleError = false
  }

  viewHistory(data: any) {
    this.router.navigate(['/inbound/inbound/inbound-history'])
  }

  viewDetailInfo(data: any) {
    this.router.navigate(['/inbound/inbound/detail'], {
      state: {
        data,
      },
    })
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }
}
