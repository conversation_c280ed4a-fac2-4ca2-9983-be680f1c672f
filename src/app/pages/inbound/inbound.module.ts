import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { CurrencyMaskModule, CURRENCY_MASK_CONFIG } from 'ng2-currency-mask'
import { MaterialModule } from '../../app.module'
import { DirectivesModule } from '../../directive/directives.module'
import { NzAvatarModule } from 'ng-zorro-antd/avatar'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCardModule } from 'ng-zorro-antd/card'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions'
import { NzDividerModule } from 'ng-zorro-antd/divider'
import { NzDrawerModule } from 'ng-zorro-antd/drawer'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzMessageModule } from 'ng-zorro-antd/message'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzProgressModule } from 'ng-zorro-antd/progress'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzTypographyModule } from 'ng-zorro-antd/typography'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { CustomCurrencyMaskConfig } from '../setting/setting.module'
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzStepsModule } from 'ng-zorro-antd/steps'
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker'
import { InboundRoutingModule } from './inbound-routing.module'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { InboundComponent } from './inbound/inbound.component'
import { AddOrEditInboundComponent } from './inbound/add-or-edit-inbound/add-or-edit-inbound.component'
import { InboundDetailComponent } from './inbound/inbound-detail/inbound-detail.component'
import { ShipmentScheduleComponent } from './shipment-schedule/shipment-schedule.component'
import { NzCalendarModule } from 'ng-zorro-antd/calendar'
import { PopupListInboundComponent } from './shipment-schedule/popup-list-inbound/popup-list-inbound.component'

@NgModule({
  declarations: [InboundComponent, AddOrEditInboundComponent, InboundDetailComponent, ShipmentScheduleComponent, PopupListInboundComponent],
  imports: [
    CommonModule,
    InboundRoutingModule,
    CurrencyMaskModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzTableModule,
    NzDividerModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDescriptionsModule,
    NzListModule,
    NzDatePickerModule,
    NzStatisticModule,
    NzPaginationModule,
    NzCollapseModule,
    NzCascaderModule,
    NzPopoverModule,
    NzTypographyModule,
    MaterialModule,
    NzPopconfirmModule,
    NzDrawerModule,
    NzCardModule,
    NzUploadModule,
    NzProgressModule,
    NzAvatarModule,
    NzMessageModule,
    DirectivesModule,
    NzTagModule,
    NzBreadCrumbModule,
    NzDropDownModule,
    NzStepsModule,
    NzTimePickerModule,
    NzInputNumberModule,
    NzCalendarModule,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class InboundModule {}
