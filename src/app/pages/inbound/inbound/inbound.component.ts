import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { Workbook } from 'exceljs'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import * as fs from 'file-saver'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { InboundDetailComponent } from './inbound-detail/inbound-detail.component'

@Component({
  templateUrl: './inbound.component.html',
  styleUrls: ['./inbound.component.scss'],
})
export class InboundComponent {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataSearch: any = {}
  dataStatus = this.coreService.convertObjToArray(enumData.InboundStatus)
  listOfData: any[] = []
  errorString!: string
  isExporting = false
  lstErrorImport: any[] = []
  isVisibleError = false
  isVisibleActive = false
  placeholder = ['Từ ngày', 'Đến ngày']
  isVisibleChangePw = false
  supplierChoose: any

  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumData: any
  dataActive: any = {}
  currentSupplierData: any = {}
  objPermission: any = {}
  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private router: Router,
    public authenticationService: AuthenticationService,
    private route: ActivatedRoute
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))

    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    if (clearFilter) this.dataSearch = {}
    if (reset) this.pageIndex = 1

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    if (this.dataSearch.createdAt) {
      if (this.dataSearch.createdAt[0]) this.dataSearch.dateStart = this.dataSearch.createdAt[0]
      if (this.dataSearch.createdAt[1]) this.dataSearch.dateEnd = this.dataSearch.createdAt[1]
    }

    this.apiService.post(this.apiService.INBOUND.PAGINATION, dataSearch).then((res) => {
      this.loading = false
      this.listOfData = res[0]
      this.total = res[1]
    })
  }

  clickExportExcel() {
    this.loading = true
    this.notifyService.showloading()
    const where = this.dataSearch
    const dataSearch = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
      order: { createdAt: 'DESC' },
    }

    this.apiService.post(this.apiService.INBOUND.PAGINATION, dataSearch).then((res: any) => {
      if (res) {
        this.loading = false
        this.notifyService.hideloading()

        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách inbound')
        //#region Body Table
        const header = [
          'Số IB PMS',
          'Số IB SAP ',
          'Số PO',
          'Số Shipment',
          'Shipping Cost',
          'NCC',
          'PR',
          'Ngày giao hàng',
          'Ngày về cảng dự kiến',
          'Kho nhận dự kiến',
          'Ngày tạo',
          'Người phụ trách',
          'Trạng thái',
        ]
        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3085FE' },
            bgColor: { argb: '3085FE' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' }, size: 13 }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet.getColumn(colNumber).width = 30
              break
            case 2:
              worksheet.getColumn(colNumber).width = 50
              break
            default:
              worksheet.getColumn(colNumber).width = 30
              worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
          }
        })

        // Add Data and Conditional Formatting
        for (let data of res[0]) {
          const rowData = [
            data.inboundNumber || '',
            data.sapInboundNumber || '',
            data.poNumber || '',
            data.shipmentNumber || '',
            data.shipmentCostNumber || '',
            data.supplierName || '',
            data.prCode || '',
            data.deliveryDate ? moment(data.deliveryDate).format('DD/MM/YYYY') : '',
            data.dateArrivalWarehouse ? moment(data.dateArrivalWarehouse).format('DD/MM/YYYY') : '',
            data.expectWarehouseName || '',
            moment(data.createdAt).format('DD/MM/YYYY') || '',
            data.employeeInchargeName || '',
            data.statusName,
          ]
          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }
        //#endregion

        //#region Save File
        workbook.xlsx.writeBuffer().then((data: any) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `INBOUND_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }

  clickAdd() {
    this.router.navigate(['/inbound/inbound/add'])
  }

  clickEdit(data: any) {
    this.router.navigate(['/inbound/inbound/edit', data.id])
  }

  destroy(data: any) {
    this.loading = true
    this.apiService
      .post(this.apiService.INBOUND.UPDATE_CANCEL, {
        id: data.id,
        status: this.enumData.InboundStatus.CANCEL.code,
      })
      .then((res: any) => {
        this.notifyService.showSuccess(res.message)
        this.loading = false
        this.searchData()
      })
  }

  confirmInfo(data: any) {
    this.loading = true
    this.apiService
      .post(this.apiService.INBOUND.UPDATE_STATUS, {
        id: data.id,
        status: this.enumData.InboundStatus.WAITING.code,
      })
      .then((res: any) => {
        this.notifyService.showSuccess(`Xác nhận thông tin thành công!`)
        this.loading = false
        this.searchData()
      })
  }

  clickExportExcelTemplate() {
    this.notifyService.showloading()
    const workbook = new Workbook()
    const worksheet1 = workbook.addWorksheet('Danh sách inbound')
    //#region Body Table
    const header1 = [
      'Tiêu đề inbound *',
      'Mã PO *',
      'Ngày giao hàng(DD/MM/YYYY)*',
      'Thời gian về kho dự kiến(DD/MM/YYYY) *',
      'Người phụ trách *',
      'Kho hàng dự kiến nhập *',
    ]
    const headerRow1 = worksheet1.addRow(header1)

    this.formatHeader(headerRow1, worksheet1)

    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_NHAP_KHO_${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
  }

  formatHeader(headerRow: any, worksheet: any) {
    // Cell Style : Fill and Border
    headerRow.eachCell((cell: any, colNumber: number) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '3085FE' },
        bgColor: { argb: '3085FE' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' }, size: 13 }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 70
          break
        default:
          worksheet.getColumn(colNumber).width = 35
          worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
          break
      }
    })
  }

  clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ((event as any).target as any).files[0]

    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['name', 'poCode', 'deliveryDate', 'dateArrivalWarehouse', 'employeeInchargeCode', 'expectWarehouseCode'],
      })

      // fix lỗi k import 2 lần đc
      // ;(<HTMLInputElement>document.getElementById('file-emp')).value = ''
      const fileInput = document.getElementById('file')
      if (fileInput) {
        ;(<HTMLInputElement>fileInput).value = ''
      } else {
        console.error('Element with ID file-emp not found')
      }

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''

      strErr += this.validateSheet(jsonData)

      const payloadExcel = {
        listInbound: jsonData,
      }

      if (strErr.length > 0) {
        this.notifyService.hideloading()
        this.notifyService.showError(strErr)
        return
      }

      this.apiService.post(this.apiService.INBOUND.IMPORT_EXCEL, payloadExcel).then((res: any) => {
        this.notifyService.showSuccess('Thêm Mới File Excel Thành Công')
        this.searchData()
      })
    }
  }

  validateSheet(jsonData: any) {
    let strErr = ''
    for (const row of jsonData) {
      let idx = jsonData.indexOf(row) + 2
      if (row.name == null || (typeof row.name === 'string' && row.name.trim().length == 0)) {
        strErr += 'Dòng ' + idx + ' - Ngày hết hạn không được để trống <br>'
      }

      if (row.poCode == null || (typeof row.poCode === 'string' && row.poCode.trim().length == 0)) {
        strErr += 'Dòng ' + idx + ' - Mã PO không được để trống <br>'
      }

      if (row.deliveryDate == null || (typeof row.deliveryDate === 'string' && row.deliveryDate.trim().length == 0)) {
        strErr += 'Dòng ' + idx + ' - Ngày giao hàng không được để trống <br>'
      }
      if (row.deliveryDate) {
        row.deliveryDate = this.coreService.convertToDate(row.deliveryDate)
        if (!row.deliveryDate) {
          strErr += 'Dòng ' + idx + ' - Ngày giao hàng  không đúng định dạng <br>'
        }
      }

      if (row.expectWarehouseCode == null || (typeof row.expectWarehouseCode === 'string' && row.expectWarehouseCode.trim().length == 0)) {
        strErr += 'Dòng ' + idx + ' - Kho nhận dự kiến không được để trống <br>'
      }

      if (row.employeeInchargeCode == null || (typeof row.employeeInchargeCode === 'string' && row.employeeInchargeCode.trim().length == 0)) {
        strErr += 'Dòng ' + idx + ' - Người phụ trách không được để trống <br>'
      }

      if (row.dateArrivalWarehouse == null || (typeof row.dateArrivalWarehouse === 'string' && row.dateArrivalWarehouse.trim().length == 0)) {
        strErr += 'Dòng ' + idx + ' - Ngày về kho dự kiến không được để trống <br>'
      }
      if (row.dateArrivalWarehouse) {
        row.dateArrivalWarehouse = this.coreService.convertToDate(row.dateArrivalWarehouse)
        if (!row.dateArrivalWarehouse) {
          strErr += 'Dòng ' + idx + ' - Ngày về kho dự kiến không đúng định dạng <br>'
        }
      }
    }
    return strErr
  }

  clickShowErrorImport() {
    this.isVisibleError = true
  }

  closeModelError() {
    this.isVisibleError = false
  }

  viewHistory(data: any) {
    this.router.navigate(['/inbound/inbound/inbound-history'])
  }

  clickView(object: any) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['detail'], {
        relativeTo: this.route,
        queryParams: { id: object.id, isContract: true },
      })
    )
    this.router.navigateByUrl(url)
  }

  showDetail(data: any) {
    this.dialog
      .open(InboundDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }
}
