<nz-row>
  <nz-col nzSpan="24" class="text-center fs-24 fw-600">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<!--Mã inbound  -->
<form nz-form #frmAdd="ngForm" class="form-deco-full">
  <nz-row nzGutter="32">
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã inbound</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          <input disabled nz-input name="code" [(ngModel)]="dataObject.code" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <!-- Tiêu đề inbound -->
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiêu đề inbound</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tiêu đề inbound (1-250 kí tự)!">
          <input nz-input name="name" [(ngModel)]="dataObject.name" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- PO -->
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" nzRequired>PO</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn PO!">
          <nz-select
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.poId"
            name="poId"
            nzPlaceHolder="Chọn PO"
            required
            (ngModelChange)="onChangePO($event)"
          >
            <nz-option *ngFor="let item of dataPO" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Ngày giao hàng -->
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày giao hàng</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập ngày giao hàng!">
          <nz-date-picker
            nzFormat="dd-MM-yyyy"
            [(ngModel)]="dataObject.deliveryDate"
            [nzPlaceHolder]="language_key?.ASN_DATE_ENTER || 'ngày giao hàng'"
            name="deliveryDate"
            required
          >
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired> Thời gian về kho dự kiến</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập thời gian về kho dự kiến!">
          <nz-date-picker
            nzFormat="dd-MM-yyyy"
            [(ngModel)]="dataObject.dateArrivalWarehouse"
            [nzPlaceHolder]="language_key?.ASN_DATE_ENTER || 'Thời gian về kho dự kiến'"
            name="dateArrivalWarehouse"
            required
          >
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Người phụ trách -->
    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" nzRequired>Người phụ trách</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Người phụ trách!">
          <nz-select
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.employeeInchargeId"
            name="employeeInchargeId"
            nzPlaceHolder="Chọn Người phụ trách"
            required
          >
            <nz-option *ngFor="let item of dataEmployeeIncharge" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="'Danh sách Items'" [nzActive]="true">
      <!-- DS Items -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstProduct.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã vật tư</th>
                <th>Vật tư</th>
                <th>Tên hàng hóa</th>
                <th>Mô tả hàng hóa</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Thành tiền</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstProduct; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.itemCode }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.price | number }}</td>
                <td>{{ data.money | number }}</td>
                <td>{{ data.note }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="'Danh sách Container'" [nzActive]="true">
      <!-- Add mới -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24" class="my-3">
          <button nz-button (click)="onAddListContainer()" nzType="primary" class="mr-2"><span nz-icon nzType="plus"></span>Thêm mới</button>
          <button nz-button (click)="onDeleteSelectedContainers()" nzType="primary" class="mr-2 button-exit" [disabled]="!hasSelectedContainers()">
            <span nz-icon nzType="delete"></span>Xoá
          </button>
        </nz-col>
      </nz-row>

      <!-- DS Container -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="['']" [nzShowPagination]="false" #ajaxTable nzBordered>
            <thead>
              <tr>
                <th class="text-center">
                  <input type="checkbox" [ngModelOptions]="{ standalone: true }" [(ngModel)]="selectAll" (change)="toggleSelectAll()" />
                </th>
                <th>STT</th>
                <th>Số Cont</th>
                <th>Số seal hãng tàu</th>
                <th>Số seal</th>
                <th>Loại cont</th>
                <th>Số kiện trên cont</th>
                <th>Tác vụ</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let containerItem of dataObject.listContainer; let idx = index">
                <td class="text-center"><input type="checkbox" [(ngModel)]="containerItem.isChecked" [ngModelOptions]="{ standalone: true }" /></td>

                <td>{{ idx + 1 }}</td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.containerNumber"
                    [name]="'containerNumber' + idx"
                    placeholder="Số cont"
                    class="input-table"
                  />
                </td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.shipSealNumber"
                    [name]="'shipSealNumber' + idx"
                    placeholder="Số seal hãng tàu"
                    class="input-table"
                  />
                </td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.sealNumber"
                    [name]="'sealNumber' + idx"
                    placeholder="Số seal"
                    class="input-table"
                  />
                </td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.containerType"
                    [name]="'containerType' + idx"
                    placeholder="Loại cont"
                    class="input-table"
                  />
                </td>
                <td>
                  <nz-input-number
                    style="width: 100%"
                    [nzMin]="1"
                    [nzFormatter]="formatterNull"
                    [nzParser]="parser"
                    [(ngModel)]="containerItem.packageQuantity"
                    [ngModelOptions]="{ standalone: true }"
                    [nzPlaceHolder]="'Số kiện trên cont'"
                    required
                    class="input-table"
                  ></nz-input-number>
                </td>
                <td>
                  <button nzShape="circle" nz-button (click)="onDeleteListContainer(idx)" nzDanger><span nz-icon nzType="delete"></span></button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <!-- form action -->

  <nz-row>
    <nz-col nzSpan="24" class="text-center mt-4">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit"><span nz-icon nzType="close"></span>Thoát</button>
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="submitForm()"><span nz-icon nzType="save"></span> Lưu</button>
    </nz-col>
  </nz-row>
</form>
