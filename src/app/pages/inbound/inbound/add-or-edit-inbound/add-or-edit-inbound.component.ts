import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  templateUrl: './add-or-edit-inbound.component.html',
  styleUrls: ['./add-or-edit-inbound.component.scss'],
})
export class AddOrEditInboundComponent {
  modalTitle = 'Thêm mới inbound'
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  dataObject: any = {}
  listData = []
  isEditItem: boolean = false
  maxSizeUpload = enumData.maxSizeUpload
  isVisiblePopupChooseAddress = false
  isLoadCountry = false
  isLoadRegion = false
  dataCountry: any[] = []
  dataContract: any[] = []
  dataPO: any[] = []
  dataEmployeeIncharge: any[] = []
  dataExpectWarehouse: any[] = []
  dataDistrict: any[] = []
  dataWard: any[] = []
  dataFactorySupplier: any = {}
  regionId!: string
  districtId!: string
  wardId!: string
  address!: string
  fieldCurrent: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumData: any
  enumProject: any
  enumRole: any
  supplierType: any[] = this.coreService.convertObjToArray(enumData.supplierType)
  action: any
  lst: any[] = []
  dataReference: any[] = []

  formatterNull = (value: any) => `${value ? value : ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // Parser chặn nhập chữ
  parser = (value: string) => value.replace(/[^0-9]/g, '')
  selectAll: boolean = false
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private route: ActivatedRoute,
    private router: Router,
    private storageService: StorageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))

    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  async ngOnInit() {
    this.loadDataSelect()

    const id = this.route.snapshot.paramMap.get('id')
    this.dataObject.listContainer = []
    this.dataObject.lstProduct = []
    if (id) {
      this.isEditItem = true
      this.loadDetail(id)
      this.modalTitle = 'Chỉnh sửa inbound'
    }
  }

  loadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.INBOUND.LOAD_DETAIL, { id: id }).then((result) => {
      this.notifyService.hideloading()
      this.dataObject = result
    })
  }

  submitForm() {
    this.dataObject.dateArrivalWarehouse = new Date(this.dataObject.dateArrivalWarehouse)
    this.dataObject.deliveryDate = new Date(this.dataObject.deliveryDate)
    /** Ngày về kho dự kiến  phải lớn hơn ngày giao hàng */
    if (this.dataObject.dateArrivalWarehouse.getTime() < this.dataObject.deliveryDate.getTime()) {
      this.notifyService.showError('Ngày dự kiến về  kho dự kiến phải lớn hơn ngày giao hàng')
      return
    }

    this.notifyService.showloading()
    if (!this.dataObject.id) {
      this.apiService.post(this.apiService.INBOUND.CREATE_DATA, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      })
    } else {
      this.apiService.post(this.apiService.INBOUND.UPDATE_DATA, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      })
    }
    //#endregion
  }

  closeDialog() {
    this.router.navigate(['/inbound/inbound'])
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) {
          this.dataObject[fieldName] = res[0]
          this.dataObject.fileName = fileToUpload.name
        } else {
          this.dataObject[fieldName] = ''
          this.dataObject.fileName = ''
        }
      })
    }
  }

  async loadDataSelect() {
    Promise.all([
      this.apiService.post(this.apiService.PO.FIND, {}),
      this.apiService.post(this.apiService.EMPLOYEE.FIND, {}),
      this.apiService.post(this.apiService.WAREHOUSE.FIND, {}),
    ]).then((res) => {
      this.dataPO = res[0]
      this.dataEmployeeIncharge = res[1]
      this.dataExpectWarehouse = res[2]
    })
  }

  onDeleteListContainer(index: number) {
    this.dataObject.listContainer = this.dataObject.listContainer.filter((_: any, idx: any) => idx !== index)
  }

  // Hàm thêm ngân hàng vào dataObject
  onAddListContainer() {
    if (!this.dataObject.listContainer) {
      this.dataObject.listContainer = []
    }

    this.dataObject.listContainer = [
      ...this.dataObject.listContainer,
      {
        containerNumber: null,
        shipSealNumber: null,
        sealNumber: null,
        containerType: null,
        packageQuantity: 0,
        isChecked: false,
      },
    ]
  }

  onChangePO(poId: any) {
    if (!poId) return
    this.dataObject.poId = poId
    Promise.all([this.apiService.post(this.apiService.PO.LOAD_PO_PRODUCT, { poId: poId })]).then(async (res) => {
      this.dataObject.lstProduct = res[0]
    })
  }

  onDeleteSelectedContainers() {
    this.dataObject.listContainer = this.dataObject.listContainer.filter((item: any) => !item.isChecked)
    this.selectAll = false
  }

  toggleSelectAll() {
    this.dataObject.listContainer.forEach((item: any) => (item.isChecked = this.selectAll))
  }

  hasSelectedContainers() {
    return this.dataObject.listContainer.some((item: any) => item.isChecked)
  }
}
