import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  templateUrl: './inbound-detail.component.html',
  styleUrls: ['./inbound-detail.component.scss'],
})
export class InboundDetailComponent {
  modalTitle = 'Xem chi tiết'
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  dataObject: any = {}
  listData = []

  maxSizeUpload = enumData.maxSizeUpload
  isVisiblePopupChooseAddress = false
  dataContract: any[] = []
  dataPO: any[] = []
  dataEmployeeIncharge: any[] = []
  dataExpectWarehouse: any[] = []
  fieldCurrent: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumData: any
  enumProject: any
  enumRole: any
  action: any
  lst: any[] = []

  inboundId: any
  formatterNull = (value: any) => `${value ? value : ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // Parser chặn nhập chữ
  parser = (value: string) => value.replace(/[^0-9]/g, '')

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private route: ActivatedRoute,
    private router: Router,
    private storageService: StorageService,
    private dialogRef: MatDialogRef<InboundDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))

    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })

    this.dataObject.lstProduct = []
    this.language_key = this.coreService.getLanguage()
    this.loadDetail(this.data.id)
  }

  loadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.INBOUND.LOAD_DETAIL, { id: id }).then((result) => {
      this.notifyService.hideloading()
      this.dataObject = result
    })
  }

  submitForm() {
    this.notifyService.showloading()
    if (!this.inboundId) {
      this.apiService.post(this.apiService.INBOUND.CREATE_DATA, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      })
    } else {
      this.apiService.post(this.apiService.INBOUND.UPDATE_DATA, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      })
    }
    //#endregion
  }

  closeDialog() {
    this.dialogRef.close()
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }

  loadDataSelect() {
    Promise.all([
      this.apiService.post(this.apiService.CONTRACT.FIND, {}),
      this.apiService.post(this.apiService.PO.FIND, {}),
      this.apiService.post(this.apiService.EMPLOYEE.FIND, {}),
      this.apiService.post(this.apiService.WAREHOUSE.FIND, {}),
    ]).then((res) => {
      this.dataContract = res[0]
      this.dataPO = res[1]
      this.dataEmployeeIncharge = res[2]
      this.dataExpectWarehouse = res[3]
    })
  }

  // Hàm thêm ngân hàng vào dataObject
  onAddListShipCost() {
    if (!this.dataObject.listCost) {
      this.dataObject.listCost = []
    }

    this.dataObject.listCost = [
      ...this.dataObject.listCost,
      {
        content: null,
        costPlan: null,
        costReal: null,
        differentPercentCost: null,
        differentPercentCostComplete: null,
        supplierPartner: null,
      },
    ]
  }

  // Hàm thêm ngân hàng vào dataObject
  onAddListItem() {
    if (!this.dataObject.listItem) {
      this.dataObject.listItem = []
    }

    this.dataObject.listItem = [
      ...this.dataObject.listItem,
      {
        content: null,
        costPlan: null,
        costReal: null,
        differentPercentCost: null,
        differentPercentCostComplete: null,
        supplierPartner: null,
      },
    ]
  }

  onDeleteListItem(index: number) {
    this.dataObject.listItem = this.dataObject.listItem.filter((_: any, idx: any) => idx !== index)
  }

  // Hàm thêm ngân hàng vào dataObject
  onAddListContainer() {
    if (!this.dataObject.listContainer) {
      this.dataObject.listContainer = []
    }

    this.dataObject.listContainer = [
      ...this.dataObject.listContainer,
      {
        content: null,
        costPlan: null,
        costReal: null,
        differentPercentCost: null,
        differentPercentCostComplete: null,
        supplierPartner: null,
      },
    ]
  }
}
