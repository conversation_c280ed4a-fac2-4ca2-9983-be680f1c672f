<form nz-form #frmAdd="ngForm" class="form-deco-full"></form>

<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>
<div matDialogContent>
  <nz-row nzGutter="32">
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left">Mã inbound</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.code }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left">Tiêu đề inbound</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.name }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24">PO</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.poNumber }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left">Ngày giao hàng</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.deliveryDate | date : 'dd/MM/yyyy: hh:mm' }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left"> Thời gian về kho dự kiến</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.dateArrivalWarehouse | date : 'dd/MM/yyyy: hh:mm' }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24">Người phụ trách</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.employeeInchargeName }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="'Danh sách hàng hóa'" [nzActive]="true">
      <!-- DS Items -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstProduct.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã vật tư</th>
                <th>Vật tư</th>
                <th>Tên hàng hóa</th>
                <th>Mô tả hàng hóa</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Thành tiền</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstProduct; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.itemCode }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.price | number }}</td>
                <td>{{ data.money | number }}</td>
                <td>{{ data.note }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="'Danh sách Container'" [nzActive]="true">
      <!-- DS Container -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="['']" [nzShowPagination]="false" #ajaxTable nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Số Cont</th>
                <th>Số seal hãng tàu</th>
                <th>Số seal</th>
                <th>Loại cont</th>
                <th>Số kiện</th>
                <th>Số kiện trên cont</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let containerItem of dataObject.listContainer; let idx = index">
                <td>{{ idx + 1 }}</td>
                <td>
                  {{ containerItem.containerNumber }}
                </td>
                <td>
                  {{ containerItem.sealNumber }}
                </td>
                <td>
                  {{ containerItem.sealNumber }}
                </td>
                <td>
                  {{ containerItem.containerType }}
                </td>
                <td>
                  {{ containerItem.packageQuantity }}
                </td>
                <td>
                  {{ containerItem.packageQuantity }}
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<!-- form action -->
<nz-row matDialogActions class="mt-5">
  <nz-col nzSpan="24" class="text-center mt-6">
    <button (click)="closeDialog()" nz-button class="mr-4 button-exit"><span nz-icon nzType="close"></span>Thoát</button>
  </nz-col>
</nz-row>
