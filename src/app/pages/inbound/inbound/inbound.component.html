<nz-collapse>
  <nz-collapse-panel [nzHeader]="language_key?.SEARCH_COMPANY_INFO || 'T<PERSON><PERSON> kiếm thông tin Inbound'">
    <nz-row nzGutter="32">
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> Số IB PMS </nz-form-label>
          <input nz-input placeholder="Số IB PMS" [(ngModel)]="dataSearch.code" name="code" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> Số PO </nz-form-label>
          <input nz-input [placeholder]="'Số PO'" [(ngModel)]="dataSearch.poCode" name="poCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> Số shipment </nz-form-label>
          <input nz-input [placeholder]="'Số shipment'" [(ngModel)]="dataSearch.shipmentCode" name="shipmentCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> Số shipment cost </nz-form-label>
          <input nz-input [placeholder]="'Số shipment cost'" [(ngModel)]="dataSearch.shipmentCostCode" name="shipmentCostCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> Mã nhà cung cấp </nz-form-label>
          <input nz-input [placeholder]="'Mã nhà cung cấp'" [(ngModel)]="dataSearch.supplierCode" name="supplierCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
            <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày giao hàng - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.deliveryDateFrom" nzPlaceHolder="Nhập Từ ngày "> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày giao hàng - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.deliveryDateTo" nzPlaceHolder="Nhập Đến ngày"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.createdAtFrom" nzPlaceHolder="Nhập Từ ngày "> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.createdAtTo" nzPlaceHolder="Nhập Đến ngày"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24" class="text-center">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true, true)" class="mr-2">
          <span nz-icon nzType="redo"></span>Xóa bộ lọc
        </button>
        <button nzShape="round" class="mr-2" nz-button (click)="searchData(true)" nzType="primary" nzGhost>
          <span nz-icon nzType="search"></span>Tìm kiếm
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" nzGhost (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ language_key?.CREATE_SUPPLIER || 'Tạo mới inbound' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #basicTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    [nzScroll]="{ x: '2200px' }"
  >
    <thead>
      <tr class="text-nowrap">
        <th nzWidth="160px">{{ 'Mã PMS' }}</th>
        <th nzWidth="180px">{{ 'Số PO' }}</th>
        <th nzWidth="180px">{{ 'NCC' }}</th>
        <th nzWidth="150px">{{ 'Ngày giao hàng' }}</th>
        <th nzWidth="150px">{{ 'Ngày về kho dự kiến' }}</th>
        <th nzWidth="180px">{{ 'Ngày tạo' }}</th>
        <th nzWidth="180px">{{ 'Người tạo' }}</th>
        <th nzWidth="180px">{{ 'Người phụ trách' }}</th>
        <th class="text-center" nzWidth="180px">{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th nzRight nzWidth="200px">{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td>{{ data.code }}</td>
        <td>{{ data.poNumber }}</td>
        <td>{{ data.supplierName }}</td>
        <td>{{ data.deliveryDate | date : 'dd/MM/yyyy hh:mm' }}</td>
        <td>{{ data.dateArrivalWarehouse | date : 'dd/MM/yyyy hh:mm' }}</td>
        <td nzAlign="center">{{ data.createdAt | date : 'dd/MM/yyyy hh:mm' }}</td>
        <td>{{ data.createdByName || data.supplierName }}</td>
        <td>{{ data.employeeInchargeName }}</td>
        <td nzAlign="center">
          <nz-tag class="status-tag" [ngStyle]="data.statusStyle" [nzColor]="data.tagStatusColor">
            <div class="status-tag__container">
              <div [ngStyle]="data.statusDotStyle"></div>
              <span class="ml-1">{{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>

        <td nzRight class="text-nowrap text-center" *ngIf="data.isSupplierCreate">
          <button
            nz-button
            nzShape="circle"
            (click)="showDetail(data)"
            class="mr-2 mt-2 btn-primary"
            nzTooltipTitle="Xem chi tiết"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.status === enumData.InboundStatus.NEW.code"
            nz-button
            nzShape="circle"
            class="mr-2 mt-2 btn-primary"
            [nzTooltipTitle]="'Bổ sung thông tin'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="clickEdit(data)"
          >
            <i nz-icon nzType="edit" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === enumData.InboundStatus.NEW.code"
            nz-button
            nzShape="circle"
            class="mr-2 mt-2 btn-primary"
            [nzTooltipTitle]="'Xác nhận thông tin'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="confirmInfo(data)"
          >
            <span nz-icon nzType="check-square" nzTheme="outline" class="text-icon"></span>
          </button>
        </td>

        <td nzRight class="text-nowrap text-center" *ngIf="!data.isSupplierCreate">
          <button
            nz-button
            nzShape="circle"
            (click)="showDetail(data)"
            class="mr-2 mt-2 btn-primary"
            nzTooltipTitle="Xem chi tiết"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.status === enumData.InboundStatus.NEW.code || data.status === enumData.InboundStatus.WAITING.code"
            nz-button
            nzShape="circle"
            class="mr-2 mt-2 btn-primary"
            [nzTooltipTitle]="'Chỉnh sửa'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="clickEdit(data)"
          >
            <i nz-icon nzType="edit" class="text-icon"></i>
          </button>
          <button
            nz-button
            nzShape="circle"
            class="mr-2 mt-2 btn-primary"
            [nzTooltipTitle]="'Xác nhận thông tin'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="confirmInfo(data)"
            *ngIf="data.status === enumData.InboundStatus.NEW.code"
          >
            <span nz-icon nzType="check-square" nzTheme="outline" class="text-icon"></span>
          </button>
          <!-- Huỷ -->
          <button
            nz-button
            nzShape="circle"
            class="mr-2 mt-2 btn-primary"
            [nzTooltipTitle]="'Huỷ'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="destroy(data)"
            *ngIf="data.status === enumData.InboundStatus.NEW.code"
          >
            <i nz-icon nzType="play-circle" class="text-icon"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
