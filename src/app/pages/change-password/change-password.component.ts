import { Component, OnInit } from '@angular/core'
import { AuthenticationService, CoreService, NotifyService, StorageService } from '../../services'
import { first } from 'rxjs/operators'
import { Subscription } from 'rxjs'
@Component({ templateUrl: './change-password.component.html' })
export class ChangePasswordComponent implements OnInit {
  dataObject: any = {}
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumData: any
  enumProject: any
  enumRole: any
  action: any
  currentUser: any
  constructor(
    private authService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.enumRole = this.enumProject.Features.CHANGE_PASSWORD_001.code
    this.action = this.enumProject.Action
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }
  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.newPassword !== this.dataObject.confirmNewPassword) {
      this.notifyService.showError('Mật khẩu không khớp!')
      return
    }
    this.authService
      .updatePassword(this.dataObject.currentPassword, this.dataObject.newPassword, this.dataObject.confirmNewPassword)
      .pipe(first())
      .subscribe((data: any) => {
        this.dataObject = {}
        this.notifyService.showSuccess(data.message)
      })
  }
}
