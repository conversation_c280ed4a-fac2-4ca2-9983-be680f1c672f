import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzCardModule } from 'ng-zorro-antd/card'
import { ChangePasswordComponent } from './change-password.component'
import { ChangePasswordRoutingModule } from './change-password-routing.module'
import { FormsModule } from '@angular/forms'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    ChangePasswordRoutingModule,
    NzButtonModule,
    NzStatisticModule,
    NzGridModule,
    NzCardModule,
    NzIconModule,
    NzFormModule,
    NzInputModule,
  ],
  declarations: [ChangePasswordComponent],
  exports: [ChangePasswordComponent],
  bootstrap: [ChangePasswordComponent],
})
export class ChangePasswordModule {}
