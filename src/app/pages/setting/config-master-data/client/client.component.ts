import { Component } from '@angular/core'
import { User } from '../../../../models'
import { AuthenticationService } from '../../../../services'

@Component({
  selector: 'app-client',
  templateUrl: './client.component.html',
})
export class ClientComponent {
  currentUser!: User
  enumProject: any
  enumRole: any
  action: any
  constructor(public authenticationService: AuthenticationService) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_021.code
  }
}
