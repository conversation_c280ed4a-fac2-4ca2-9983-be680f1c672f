import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../../../../core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ApiService, NotifyService, CoreService, StorageService } from '../../../../../../services'

@Component({ templateUrl: './add-or-edit-footer.component.html' })
export class AddOrEditFooterComponent implements OnInit {
  dataObject: any = {}
  // lstType = this.coreService.convertObjToArray(enumData.SettingStringClientType)
  lstType = [enumData.SettingStringClientType.Footer1, enumData.SettingStringClientType.Footer2, enumData.SettingStringClientType.Footer3]
  language_key: any
  subscriptions: Subscription = new Subscription()
  modalTitle = 'Thêm Footer'

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditFooterComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.modalTitle = 'Chỉnh sửa Footer'
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SETTING_STRING_CLIENT.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SETTING_STRING_CLIENT.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
