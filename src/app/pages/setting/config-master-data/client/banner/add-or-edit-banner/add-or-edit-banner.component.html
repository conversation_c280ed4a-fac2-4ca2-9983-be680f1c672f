<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Tiêu đề</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập tiêu đề (1-50 kí tự)!">
            <input nz-input placeholder="Nhập 1-50 kí tự" [(ngModel)]="dataObject.name" name="name" required
              pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Loại</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn loại!">
            <nz-select nzPlaceHolder="Chọn loại" [(ngModel)]="dataObject.type" name="type" required
              (ngModelChange)="onChangeType()">
              <nz-option *ngFor="let option of lstType" [nzValue]="option.code" [nzLabel]="option.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="dataObject.type === img">
        <nz-form-item nzFlex>
          <nz-form-label nz-col [nzSm]="6" [nzXs]="24" [nzRequired]="!dataObject.url">Ảnh banner</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <label for="url" class="custom-file-upload">
              <span nz-icon nzType="upload"></span> Upload File
            </label>
            <input class="hidden" type="file" accept="image/*" id="url" (change)="handleFileInput($event)" />
            <div class="tooltip" *ngIf="dataObject.url?.length > 0">
              <a href="{{ dataObject.url }}" target="_blank"> Xem ảnh banner </a>
              <span class="tooltipelement"><img src="{{ dataObject.url }}" height="auto" width="240px" /></span>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="dataObject.type === video">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Link video banner</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzFlex>
            <input nz-input placeholder="Nhập không quá 250 kí tự" [(ngModel)]="dataObject.url" name="url"
              pattern=".{0,250}" />
            <div class="tooltip" *ngIf="dataObject.url && dataObject.url.length > 0">
              <a href="{{ dataObject.url }}" target="_blank"> Xem video banner </a>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Vị trí</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn vị trí!">
            <nz-select nzPlaceHolder="Chọn vị trí" [(ngModel)]="dataObject.position" name="position" required>
              <nz-option *ngFor="let option of lstPosition" [nzValue]="option.code" [nzLabel]="option.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>ATR</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập ATR (1-250 kí tự)!">
            <input nz-input placeholder="Nhập ATR 1-250 kí tự" [(ngModel)]="dataObject.atr" name="atr" required
              pattern=".{1,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Mô tả</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập mô tả không quá 250 kí tự!">
            <input nz-input placeholder="Nhập mô tả không quá 250 kí tự" [(ngModel)]="dataObject.description"
              name="description" pattern=".{0,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>