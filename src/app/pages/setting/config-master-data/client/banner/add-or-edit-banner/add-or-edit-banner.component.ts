import { Component, OnInit, Inject, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../../core'
import { FormGroup } from '@angular/forms'
import { Subscription } from 'rxjs'
import { ApiService, NotifyService, CoreService, StorageService } from '../../../../../../services'

@Component({ templateUrl: './add-or-edit-banner.component.html' })
export class AddOrEditBannerComponent implements OnInit {
  dataObject: any = {}
  modalTitle = 'Thêm Banner'
  maxSizeUpload = enumData.maxSizeUpload
  validateForm: any
  panelForm: FormGroup[] = []
  lstType = this.coreService.convertObjToArray(enumData.BannerClientType)
  img = enumData.BannerClientType.Image.code
  video = enumData.BannerClientType.Video.code
  lstPosition = this.coreService.convertObjToArray(enumData.BannerClientPosition)
  fileToUpload!: File | undefined
  isChangeFile = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditBannerComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.modalTitle = 'Chỉnh sửa Banner'
    } else {
      this.dataObject.type = this.img
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.isChangeFile) {
      if (this.fileToUpload && this.fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
        this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
        return
      }

      if (this.fileToUpload) {
        const formData: FormData = new FormData()
        formData.append('file', this.fileToUpload, this.fileToUpload.name)

        this.apiService
          .post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData)
          .then((res) => {
            this.notifyService.hideloading()
            if (res && res.length) {
              this.dataObject.url = res[0]
              this.saveObject()
            }
          })
          .catch((err) => {
            this.notifyService.showError(err)
          })
      }
    } else {
      if (!this.dataObject.url) {
        this.notifyService.showError('Vui lòng chọn file upload!')
        return
      }
      this.saveObject()
    }
  }

  saveObject() {
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BANNER_CLIENT.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BANNER_CLIENT.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  async handleFileInput(event: any) {
    this.fileToUpload = event.target.files[0]
    this.isChangeFile = true
    this.dataObject.url = ''
  }

  onChangeType() {
    this.dataObject.url = ''
  }
}
