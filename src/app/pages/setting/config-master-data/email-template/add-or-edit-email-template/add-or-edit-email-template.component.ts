import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData, enumEmailType } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({ templateUrl: './add-or-edit-email-template.component.html' })
export class AddOrEditEmailTemplateComponent implements OnInit {
  dataObject: any = {}
  modalTitle = `Thêm mới mẫu email`
  lstEmailType = this.coreService.convertObjToArray(enumEmailType)
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditEmailTemplateComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.modalTitle = `Chỉnh sửa mẫu email`
    }
  }

  onChangeCode() {
    const objEnum = this.lstEmailType.find((c) => c.code == this.dataObject.code)
    if (objEnum) {
      this.dataObject.name = objEnum.name
      this.dataObject.description = objEnum.default
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.EMAIL_TEMPLATE.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.EMAIL_TEMPLATE.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
