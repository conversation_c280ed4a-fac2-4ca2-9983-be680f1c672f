<nz-row>
  <nz-col nzSpan="24">
    <button *ngIf="isDisplayAdd && authenticationService.checkPermission([enumRole], action.Create.code)" nz-button
      nzType="primary" (click)="clickAdd()">
      <span nz-icon nzType="plus"></span> {{ language_key?.ADD || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tiêu đề</th>
        <th>Loại template</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25" (click)="clickEdit(data)">{{ data.name }}</td>
        <td (click)="clickEdit(data)">{{ data.typeName }}</td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>