<form nz-form #frmAdd="ngForm" autocomplete="off">
<div matDialogContent>
  <div nz-row nzAlign="bottom">
    <!-- Nhà cung cấp -->
    <div nz-col [nzSpan]="6">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Nhà cung cấp</nz-form-label>
        <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Nhà cung cấp!">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="currentProductProvider.companyId" name="companyId"
            nzPlaceHolder="Nhà cung cấp" class="mr-3" required (ngModelChange)="onChangeWarehouseCate()">
            <nz-option *ngFor="let item of listWarehouseCate" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- Lead time -->
    <div nz-col [nzSpan]="6">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Tên Lead time </nz-form-label>
        <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Tên Lead time !">
          <input nz-input placeholder="Nhập tên ít nhất 1 kí tự" [(ngModel)]="currentProductProvider.leadTime"
            name="leadTime" required pattern=".{1,100}" />
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- Ghi chú -->
    <div nz-col [nzSpan]="6">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ghi chú</nz-form-label>
        <nz-form-control nzErrorTip="Vui lòng nhập mô tả không quá 250 kí tự!">
          <input nz-input placeholder="Nhập ghi chú" [(ngModel)]="currentProductProvider.description"
            name="description" />
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- save-->
    <div nz-col [nzSpan]="6">
      <nz-form-item nzFlex>
        <button nz-button nzType="primary" class="btn btn-primary ml-2">Thêm</button>
      </nz-form-item>
    </div>

  </div>
  <!-- Table section -->
  <nz-row class="mt-1">
    <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]=""
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th>STT</th>
          <th>Mã NCC</th>
          <th>Tên nhà cung cấp</th>
          <th>Lead time</th>
          <th>Ghi chú đặt hàng</th>
          <th>Thao tác</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td class="text-left">{{ data.code }}</td>
          <td class="text-left">{{ data.name }}</td>
          <td class="text-left">{{ data.providerName }}</td>
          <td class="text-left">{{ data.leadTime }}</td>
          <td class="text-left">{{ data.description }}</td>
          <td class="text-center">
            <button nz-button nzShape="circle" nzType="default" nzTooltipTitle="Xem chi tiết" nz-tooltip
              class="mr-2 bg-danger text-white">
              <span nz-icon nzType="delete" nzTheme="outline"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
  <nz-row>
    <nz-pagination class="mt-2" [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-row>
</div>
<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button class="mr-2" nzShape="round" nz-button nzType="primary" (click)="onSubmit()">
      <span nz-icon nzType="save"></span>Lưu
    </button>
  </nz-col>
</nz-row>
</form>