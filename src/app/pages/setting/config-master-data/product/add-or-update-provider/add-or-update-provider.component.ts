import { Component } from '@angular/core';
import { enumData } from 'src/app/core';

@Component({
  selector: 'app-add-or-update-provider',
  templateUrl: './add-or-update-provider.component.html',
})
export class AddOrUpdateProviderComponent {
  currentProductProvider: any = {};
  listWarehouseCate: any = [];
  listWarehouseIndustry: any = [];
  listWarehouseGroupOfGoods: any = [];
  listOfData: any = [
    {
      code: 'NCC-01',
      name: 'Greenhouse VN',
      leadTime: '07:30',
      providerName: 'Greenhouse Việt Nam',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-02',
      name: 'Warehouse System',
      leadTime: '07:30',
      providerName: 'Hệ thống phân phối warehouse',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-03',
      name: 'Tiki Logistic',
      leadTime: '07:30',
      providerName: 'Công ty Tiki logistic',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-04',
      name: 'Apetech food',
      leadTime: '07:30',
      providerName: 'Công ty Apetechs food',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-05',
      name: 'Shoppee Food',
      leadTime: '07:30',
      providerName: 'Công ty thức ăn shopee',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-06',
      name: 'NTSS Warehouse',
      leadTime: '07:30',
      providerName: 'Công ty NTSS warehouse',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-07',
      name: 'USA Warehouse',
      leadTime: '07:30',
      providerName: 'Công ty USA Warehouse',
      description: 'Từ kho gốc',
    },
    {
      code: 'NCC-08',
      name: 'Japanse Warehouse',
      leadTime: '07:30',
      providerName: 'Công ty Japanse Warehouse',
      description: 'Từ kho gốc',
    },
  ];
  // table
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total

  constructor() {}

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
  }
  onSubmit() {}

  async searchData(reset = false, clearFilter = false) {}

  onChangeWarehouseCate() {}

  onChangeWarehouseIndustry(data: any) {}

  onChangeGroupOfGoods(data: any) {}
}
