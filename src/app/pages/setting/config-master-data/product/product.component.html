
<nz-collapse nzBordered="false">
  <nz-collapse-panel nzHeader="Tìm kiếm">
    <nz-row nzGutter="12" class="mt-2">
      <!-- <PERSON><PERSON> sản phẩm -->
      <nz-col nzSpan="6">
        <label>M<PERSON> sản phẩm</label>
        <input nz-input (keydown.enter)="searchData()" [(ngModel)]="dataSearch.code" name="code" />
      </nz-col>

      <!-- Tên sản phẩm -->
      <nz-col nzSpan="6">
        <label>Tên sản phẩm</label>
        <input nz-input (keydown.enter)="searchData()" [(ngModel)]="dataSearch.name" name="name" />
      </nz-col>
      <!-- Nhóm hàng -->
      <nz-col nzSpan="6">
        <label>Nhóm hàng</label>
        <nz-select nzShowSearch nzAllowClear name="groupOfGoods" [(ngModel)]="dataObject.groupOfGoods">
          <nz-option *ngFor="let item of lstCity" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
        </nz-select>
      </nz-col>

      <!-- Người phụ trách-->
      <nz-col nzSpan="6">
        <label>Người phụ trách</label>
        <nz-select nzShowSearch nzAllowClear name="cityId" [(ngModel)]="dataObject.cityId">
          <nz-option *ngFor="let item of lstCity" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
        </nz-select>
      </nz-col>

    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button nzType="dashed" class="mr-2"><span nz-icon nzType="redo"></span>Xóa
          bộ lọc</button>
        <button nz-button><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<!-- List of buttons -->
<nz-row class="my-2">
  <button class="mr-2" nz-button nzType="primary" (click)="clickAdd()">
    <span nz-icon nzType="plus"></span>Thêm mới
  </button>

  <button class="mr-2 btn-dash-primary" nz-button (click)="clickDownloadTemplateExcel()"><span nz-icon
      nzType="download"></span>Tải mẫu</button>
  <input class="hidden" type="file" id="file-emp" (change)="clickImportExcel($event)" placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
  <label nz-button for="file-emp" class="lable-custom-file mr-2 btn-dash-primary"> <span nz-icon nzType="upload"></span>
    Nhập excel </label>
  <button class="mr-2" nzType="primary" nz-button (click)="onDownloadExcel()"><span nz-icon nzType="download"></span>
    Tải Excel</button>

</nz-row>

<!-- Table section -->
<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered [nzScroll]="{ y: '1200px'  }">
    <thead>
      <tr>
        <th>Mã sản phẩm</th>
        <th>Tên sản phẩm</th>
        <th>Nhóm hàng</th>
        <th>Kho</th>
        <th>Đơn vị tính</th>
        <th>Nhân viên phụ trách</th>
        <th>Trạng thái</th>
        <th nzWidth="230px">Thao tác</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-left">{{ data.code }}</td>
        <td class="text-left">{{ data.name }}</td>
        <td class="text-left">{{ data.groupOfGoods }}</td>
        <td class="text-left">{{ data.warehouse }}</td>
        <td class="text-left">{{ data.unit }}</td>
        <td class="text-left">{{ data.employeeName }}</td>
        <td class="text-left">{{ data.status }}</td>
        <td class="text-center">
          <button nz-button nzShape="circle" nzType="primary" nzTooltipTitle="Xem chi tiết" nz-tooltip class="mr-2">
            <i nz-icon nzType="eye" nzTheme="outline"></i>
          </button>

          <button (click)="clickEdit(data)" nz-tooltip nzTooltipTitle="Chỉnh sửa" nz-button
            class="mr-2 btn-primary">
            <span nz-icon nzType="edit"></span>
          </button>

          <button *ngIf="data.isDeleted"
          nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="handleUpdateActive(data)" nz-tooltip
          nzTooltipTitle="Ngưng hoạt động" nz-button class="mr-2">
          <span nz-icon nzType="play-circle"></span>
        </button>
        <button *ngIf="!data.isDeleted"
          nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="handleUpdateActive(data)" nz-tooltip nzTooltipTitle="Hoạt động lại"
          nz-button nzType="primary" nzDanger class="mr-2">
          <span nz-icon nzType="stop"></span>
        </button>

        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>