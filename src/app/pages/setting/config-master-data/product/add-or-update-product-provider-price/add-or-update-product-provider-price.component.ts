import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-add-or-update-product-provider-price',
  templateUrl: './add-or-update-product-provider-price.component.html',
})
export class AddOrUpdateProductProviderPriceComponent {
  title: string = 'Thêm mới sản phẩm';

  constructor(private router: Router) {}

  // closeAddOrUpdate() {
  //   this.router.navigate(['/settings/warehouse/products']);
  // }

}
