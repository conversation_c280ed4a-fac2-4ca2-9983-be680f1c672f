import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ApiService, AuthenticationService, CoreService, NotifyService } from 'src/app/services';
@Component({
  selector: 'app-add-or-update-product',
  templateUrl: './add-or-update-product.component.html',
})
export class AddOrUpdateProductComponent {
  currentProduct: any = {};
  listPosition: any = [];
  listDepartment: any = [];
  listPart: any = [];
  listPermissionGroup: any = [];
  listEmployeeLevel: any = [];
  listChildrenCompany: any = [];
  listBrandOfChildCompany: any = [];
  listBranchOfBrands: any = [];
  previewImage: string | undefined = '';
  previewVisible = false;
  listOfCompany: any = [];
  isUploading = false;
  listBranch: any = [];
  isCreate: boolean = true;
  isConfirmAccept: boolean = false;
  isSubmittedConfirmAccept: boolean = true;
  title: string = 'Thêm mới sản phẩm';
  firstLoadData: boolean = true;
  uploadUrl: any
  passwordVisible = false;
  passwordVisible2 = false;
  toogleProbationary = false;
  toogleTransfer = true;
  enumData: any;
  dataGender: any;
  workingMode: any;
  toogleTimekeeping = true;
  // IS MULTIPLE COMPANY
  isMultipleCompany: boolean = false;
  isLoadingMultipleCompanyTable: boolean = false;
  listDataOfMultipleCompany: any = [];

  checkedMainCompany: any = {};
  isEditableNumOfMonthProbation: boolean = false;
  listOfContractType: any[] = [];
  filesUploaded = false;

  constructor(
    private apiService: ApiService,
    private coreService: CoreService,
    private notifyService: NotifyService,
    private authenticationService: AuthenticationService,
    private router: Router,
    private dialog: MatDialog,
  ) {
    this.authenticationService.currentUser.subscribe(
      (x: any) => (this.enumData = x?.enumData)
    );
  }

  ngOnInit() {
    this.currentProduct = { ...history.state.item };
    this.dataGender = this.coreService.convertObjToArray(this.enumData.Gender);
    this.workingMode = this.coreService.convertObjToArray(
      this.enumData.WorkingMode
    );
    this.listEmployeeLevel = this.coreService.convertObjToArray(
      this.enumData.EmployeeLevel
    );

    if (history.state.status === 'confirmAccept') {
      this.title = 'Xác nhận duyệt sản phẩm';
      this.toogleTransfer = true;
      this.toogleTimekeeping = true;
      this.toogleProbationary = this.currentProduct.isProbationary;
      this.currentProduct.isProbationary = this.toogleProbationary;
      this.currentProduct.useTransfer = this.toogleTransfer;
      this.currentProduct.timekeeping = this.toogleTimekeeping;
      this.getDataForEdit(
        this.currentProduct.companyId,
        this.currentProduct.departmentId
      );
      this.isConfirmAccept = true;
      this.isCreate = false;
      this.loadImg();
    } else if (history.state.status === 'edit') {
      // if update employee
      this.getDataForEdit(
        this.currentProduct.companyId,
        this.currentProduct.departmentId
      );
      this.title = 'Cập nhật sản phẩm';
      this.isCreate = false;
      this.loadImg();
      this.toogleProbationary = this.currentProduct.isProbationary;
      this.toogleTransfer = this.currentProduct.useTransfer;
      this.toogleTimekeeping = this.currentProduct.timekeeping;
      this.currentProduct.isProbationary = this.toogleProbationary;
      this.currentProduct.useTransfer = this.toogleTransfer;
      this.currentProduct.timekeeping = this.toogleTimekeeping;
    } else if (history.state.status === 'add') {
      this.currentProduct.listDataOfMultipleCompany = []; // The first time assign list employee's company to empty array
      this.currentProduct.isProbationary = this.toogleProbationary;
      this.currentProduct.useTransfer = this.toogleTransfer;
      this.currentProduct.timekeeping = this.toogleTimekeeping;
    }
    this.loadDataSelect();
  }

  loadImg() {
    if (this.currentProduct?.avatarUrl) {
      const url = this.currentProduct.avatarUrl;
      this.currentProduct.imageFace0 = [];
      this.currentProduct.imageFace0.push({
        uid: 'uuid1',
        name: 'hinh anh mat',
        status: 'done',
        url: url,
        response: [url],
      });
    }
    if (this.currentProduct?.backIdCardUrl) {
      const url = this.currentProduct.backIdCardUrl;
      this.currentProduct.lstImageIdNo2 = [];
      this.currentProduct.lstImageIdNo2.push({
        uid: 'uuid1',
        name: 'hinh anh mat',
        status: 'done',
        url: url,
        response: [url],
      });
    }
    if (this.currentProduct?.frontIdCardUrl) {
      const url = this.currentProduct.frontIdCardUrl;
      this.currentProduct.lstImageIdNo1 = [];
      this.currentProduct.lstImageIdNo1.push({
        uid: 'uuid1',
        name: 'hinh anh mat',
        status: 'done',
        url: url,
        response: [url],
      });
    }
  }

  async getDataForEdit(companyId: string, departmentId: string) {
    // await this.apiService
    //   .post(this.apiService.DEPARTMENT.LOAD_DATA_BY_COMPANY, { id: companyId })
    //   .then((result) => {
    //     if (result) {
    //       this.listDepartment = result;
    //     }
    //   });
    // await this.apiService
    //   .post(this.apiService.POSITION.LOAD_DATA_BY_DEPARTMENT, {
    //     id: departmentId,
    //   })
    //   .then((result) => {
    //     if (result) {
    //       this.listPosition = result;
    //     }
    //   });
  }
  async loadDataSelect() {
    // await this.apiService
    //   .post(this.apiService.COMPANY.LOAD_DATA, {})
    //   .then((result) => {
    //     if (result) {
    //       this.listOfCompany = result;
    //     }
    //   });

    // if (this.currentProduct.companyId) {
    //   await this.apiService
    //     .post(this.apiService.BRANCH.LOAD_DATA_BY_COMPANY, {
    //       id: this.currentProduct.companyId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listBranch = result;
    //       }
    //     });

    //   await this.apiService
    //     .post(this.apiService.DEPARTMENT.LOAD_DATA_BY_COMPANY, {
    //       id: this.currentProduct.companyId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listDepartment = result;
    //       }
    //     });
    // }
    // // Load data select part!
    // if (this.currentProduct.departmentId) {
    //   await this.apiService
    //     .post(this.apiService.PART.LOAD_DATA_SELECT_BY_DEPARTMENT, {
    //       id: this.currentProduct.departmentId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listPart = result;
    //       }
    //     });
    // }

    // if (this.currentProduct.branchId) {
    //   await this.apiService
    //     .post(this.apiService.POSITION.LOAD_DATA_BY_BRANCH, {
    //       id: this.currentProduct.branchId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listPosition = result;
    //       }
    //     });
    // }

    // if (
    //   this.currentProduct.level === this.enumData.EmployeeLevel.MANAGER.code
    // ) {
    //   // Call API for list brand of child company
    //   this.apiService
    //     .post(this.apiService.BRAND.LOAD_DATA_BY_COMPANY, {
    //       id: this.currentProduct.companyId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listBrandOfChildCompany = result;
    //       }
    //     });

    //   // Call API for list branch id of brands
    //   if (
    //     this.currentProduct.listBrandOfChildCompanyId &&
    //     this.currentProduct.listBrandOfChildCompanyId.length > 0
    //   ) {
    //     this.apiService
    //       .post(this.apiService.BRANCH.LOAD_SELECT_BY_BRAND_LIST, {
    //         listBrandId: this.currentProduct.listBrandOfChildCompanyId,
    //       })
    //       .then((result) => {
    //         if (result) {
    //           this.listBranchOfBrands = result;
    //         }
    //       });
    //   }
    // }
  }

  onSubmit(): void {
    if (history.state.status === 'add') {
      this.createEmployee();
    } else if (history.state.status === 'edit') {
      // Trường hợp cập nhật sản phẩm
      this.updateEmployee();
    }
  }

  updateEmployee() {
    let newPhone = this.currentProduct.phone.toString();
    if (newPhone.slice(0, 2) === '84') {
      newPhone = newPhone.slice(2);
    }
    if (newPhone.slice(0, 1) !== '0') {
      newPhone = '0' + newPhone;
    }

    if (!this.validateForm()) {
      return;
    }

    if (this.currentProduct.isMultipleCompany) {
      this.notifyService.showloading();
      this.apiService
        // .post(this.apiService.EMPLOYEE.UPDATE_WITH_MUTIPLE_COMPANY, {
        //   ...this.currentProduct,
        //   phone: newPhone,
        //   listCompany: this.currentProduct.listDataOfMultipleCompany,
        // })
        // .then((result) => {
        //   this.notifyService.showSuccess('Cập nhật sản phẩm thành công');
        //   this.router.navigate(['/human-resource/employee']);
        //   this.notifyService.hideloading();
        // });
    } else {
      // handle update employee
      this.notifyService.showloading();

      // Trường hợp chỉnh sửa cho sản phẩm đang thử việc
      if (this.currentProduct.isProbationary) {
        if (
          this.currentProduct.numOfMonthProbation <
          this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation
        ) {
          this.notifyService.showError(
            'Số tháng thử việc phải bằng số bảng công đã xác nhân là ' +
              this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation
          );
          return;
        }
      }
      this.apiService
        .post(this.apiService.EMPLOYEE.UPDATE, {
          ...this.currentProduct,
          phone: newPhone,
        })
        .then((result) => {
          this.notifyService.showSuccess('Cập nhật sản phẩm thành công');
          this.router.navigate(['/human-resource/employee']);
          this.notifyService.hideloading();
        });
    }
  }

  createEmployee() {
    let newPhone = this.currentProduct.phone.toString();
    if (newPhone.slice(0, 2) === '84') {
      newPhone = newPhone.slice(2);
    }
    if (newPhone.slice(0, 1) !== '0') {
      newPhone = '0' + newPhone;
    }

    if (!this.validateForm()) {
      return;
    }

    // Trường hợp thêm sản phẩm
    if (this.isCreate === true) {
      this.notifyService.showloading();
      // if (this.currentProduct.password !== this.currentProduct.confirmPassword) {
      //   this.notifyService.showError('Xác nhận mật khẩu không đúng')
      //   return
      // }

      // TRƯỜNG HỢP THÊM MỚI NHIỀU CÔNG TY
      if (this.currentProduct.isMultipleCompany) {
        this.apiService
          // .post(this.apiService.EMPLOYEE.CREATE_WITH_MUTIPLE_COMPANY, {
          //   ...this.currentProduct,
          //   phone: newPhone,
          //   listCompany: this.currentProduct.listDataOfMultipleCompany,
          // })
          // .then((res) => {
          //   this.notifyService.showSuccess('Thêm sản phẩm thành công');
          //   this.router.navigate(['/human-resource/employee']);
          //   this.notifyService.hideloading();
          // });
      } else {
        this.apiService
          .post(this.apiService.EMPLOYEE.CREATE, {
            ...this.currentProduct,
            phone: newPhone,
          })
          .then((result) => {
            this.notifyService.showSuccess('Thêm sản phẩm thành công');
            this.router.navigate(['/human-resource/employee']);
            this.notifyService.hideloading();
          });
      }
      // TRƯỜNG HỢP THÊM MỚI MỘT CÔNG TY
      return;
    }
  }

  acceptEmployee() {
    let newPhone = this.currentProduct.phone.toString();
    if (newPhone.slice(0, 2) === '84') {
      newPhone = newPhone.slice(2);
    }
    if (newPhone.slice(0, 1) !== '0') {
      newPhone = '0' + newPhone;
    }

    if (!this.validateForm()) {
      return;
    }

    if (this.currentProduct.isMultipleCompany) {
      this.notifyService.showloading();
      this.apiService
        // .post(this.apiService.EMPLOYEE.UPDATE_WITH_MUTIPLE_COMPANY, {
        //   ...this.currentProduct,
        //   phone: newPhone,
        //   listCompany: this.currentProduct.listDataOfMultipleCompany,
        // })
        // .then((result) => {
        //   this.apiService
        //     .post(this.apiService.EMPLOYEE.ACCEPT, {
        //       id: this.currentProduct.id,
        //     })
        //     .then((res: any) => {
        //       this.notifyService.showSuccess('Duyệt sản phẩm thành công!');
        //       this.notifyService.hideloading();
        //       this.router.navigate(['/human-resource/employee']);
        //       // this.searchData()
        //     });
        // });
    } else {
      // handle update employee
      this.notifyService.showloading();

      // Trường hợp chỉnh sửa cho sản phẩm đang thử việc
      if (this.currentProduct.isProbationary) {
        if (
          this.currentProduct.numOfMonthProbation <
          this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation
        ) {
          this.notifyService.showError(
            'Số tháng thử việc phải bằng số bảng công đã xác nhân là ' +
              this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation
          );
          return;
        }
      }
      this.apiService
        // .post(this.apiService.EMPLOYEE.UPDATE, {
        //   ...this.currentProduct,
        //   phone: newPhone,
        // })
        // .then((result) => {
        //   this.apiService
        //     .post(this.apiService.EMPLOYEE.ACCEPT, {
        //       id: this.currentProduct.id,
        //     })
        //     .then((res: any) => {
        //       this.notifyService.showSuccess('Duyệt sản phẩm thành công!');
        //       this.router.navigate(['/human-resource/employee']);
        //       this.notifyService.hideloading();
        //       // this.searchData()
        //     });
        // });
    }
  }

  onSubmitConfirmAccept() {
    // Trường hợp xác nhận sản phẩm

    this.notifyService.showloading();
    this.validateForm();
    this.acceptEmployee();
  }

  async getBranchData(data: any) {
    // Reset data
    this.listBranch = [];
    this.listDepartment = [];
    this.listPosition = [];
    this.currentProduct.branchId = '';
    this.currentProduct.departmentId = '';
    this.currentProduct.positionId = '';
    this.currentProduct.workingMode = '';
    if (data)
      await this.apiService
        // .post(this.apiService.BRANCH.LOAD_DATA_BY_COMPANY, { id: data })
        // .then((result) => {
        //   if (result) {
        //     this.listBranch = result;
        //   }
        // });
  }
  async getDepartmentData(data: any) {
    // Reset data
    this.listDepartment = [];
    this.currentProduct.departmentId = '';
    this.currentProduct.positionId = '';
    this.listPosition = [];
    // if (data)
    //   await this.apiService
    //     .post(this.apiService.DEPARTMENT.LOAD_DATA_BY_BRANCH, { id: data })
    //     .then((result) => {
    //       if (result) {
    //         this.listDepartment = result;
    //       }
    //     });
  }

  async onChangeBranch(data: any) {
    this.currentProduct.positionId = '';
    this.listPosition = [];
    // Trường hợp có cả partId và branchId
    // if (data && this.currentProduct.partId) {
    //   await this.apiService
    //     .post(this.apiService.POSITION.LOAD_DATA_BY_BRANCH_AND_PART, {
    //       branchId: data,
    //       partId: this.currentProduct.partId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listPosition = result;
    //       }
    //     });
    //   // Trường hợp không có branchId và chỉ có partId
    // } else if (this.currentProduct.partId) {
    //   await this.apiService
    //     .post(this.apiService.POSITION.LOAD_DATA_BY_PART, {
    //       id: this.currentProduct.partId,
    //     })
    //     .then((result) => {
    //       if (result) {
    //         this.listPosition = result;
    //       }
    //     });
    // }
  }

  async onChangeDepartment(data: any) {
    this.currentProduct.positionId = '';
    this.currentProduct.partId = '';
    this.listPosition = [];
    this.listPart = [];
    // if (data) {
    //   await this.apiService
    //     .post(this.apiService.POSITION.LOAD_DATA_BY_DEPARTMENT, { id: data })
    //     .then((result) => {
    //       if (result) {
    //         this.listPosition = result;
    //       }
    //     });
    //   await this.apiService
    //     .post(this.apiService.PART.LOAD_DATA_SELECT_BY_DEPARTMENT, { id: data })
    //     .then((result) => {
    //       if (result) {
    //         this.listPart = result;
    //       }
    //     });
    // }
  }
  async onChangePart(data: any) {
    this.currentProduct.positionId = '';
    this.listPosition = [];
    if (data) {
      // branchid
      // if (!this.currentProduct.branchId) {
      //   await this.apiService
      //     .post(this.apiService.POSITION.LOAD_DATA_BY_PART, { id: data })
      //     .then((result) => {
      //       if (result) {
      //         this.listPosition = result;
      //       }
      //     });
      // } else {
      //   // partid
      //   await this.apiService
      //     .post(this.apiService.POSITION.LOAD_DATA_BY_BRANCH_AND_PART, {
      //       partId: data,
      //       branchId: this.currentProduct.branchId,
      //     })
      //     .then((result) => {
      //       if (result) {
      //         this.listPosition = result;
      //       }
      //     });
      // }
    }
  }

  async onChangePosition(positionId: string) {
    this.currentProduct.permissionGroupId = '';
    this.listPermissionGroup = [];
  }

  onChangeEmployee(value: any): void {}

  changeSwich() {
    this.currentProduct.isProbationary = !this.toogleProbationary;
  }
  changeSwichTransfer() {
    this.currentProduct.useTransfer = !this.toogleTransfer;
  }

  changeSwichTimeKeeping() {
    this.currentProduct.timekeeping = !this.toogleTimekeeping;
  }

  changeSwitchMultipleCompany() {
    // this.toggleMultipleCompany = !this.toggleMultipleCompany
  }

  handlePreview = async (file: NzUploadFile) => {
    if (!file.url && !file['preview']) {
      file['preview'] = await this.coreService.getBase64(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  /** thay đổi ảnh */
  handleChange(
    info: { file: NzUploadFile; fileList: NzUploadFile[] },
    lstMedia: any[]
  ) {
    let arr = [];
    switch (info.file.status) {
      case 'uploading':
        this.isUploading = true;
        break;
      case 'done':
        this.isUploading = false;
        {
          if (info.fileList) {
            for (let item of info.fileList) {
              arr.push({
                fileName: item.name ? item.name : item?.originFileObj?.name,
                name: item.name ? item.name : item?.originFileObj?.name,
                url: item.url
                  ? item.url
                  : item.response
                  ? item.response[0]
                  : '',
                fileUrl: item.url
                  ? item.url
                  : item.response
                  ? item.response[0]
                  : '',
                uid: item.uid,
                type: item.type,
                dataType: item.type,
              });
            }
            lstMedia = arr;
          }
        }
        break;
      case 'error':
        this.isUploading = false;
        break;
      case 'removed':
        {
          lstMedia.forEach((item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid) lstMedia.splice(index, 1);
          });
        }
        break;
    }
  }

  closeAddOrUpdate() {
    this.router.navigate(['/human-resource/employee']);
  }

  removeOneCompany(data: any) {
    this.currentProduct.listDataOfMultipleCompany =
      this.currentProduct.listDataOfMultipleCompany.filter(
        (company: any) => company.id !== data.id
      );
  }

  openModalAddMutipleCompany() {}

  onEditOneCompany(data: any) {}

  onEditOneProbationCompany(data: any, workingMode: string) {}

  onChangeIsMainCompany(data: any) {
    this.notifyService.showError(
      'Bạn không thể thêm công ty bằng thao tác này'
    );
  }

  async checkTypeOfCurrentCompany(companyId: string) {
    // return this.apiService
    //   .post(this.apiService.COMPANY.CHECK_TYPE_OF_CURRENT_COMPANY, {
    //     id: companyId,
    //   })
    //   .then((res: any) => {
    //     if (res) {
    //       return res.message;
    //     }
    //   })
    //   .catch(() => {});
  }

  async onChangeEmployeeLevel(data: any) {
    // Data here is employee Level (Enum)
    // Nếu là cấp quản lý
    if (data === this.enumData.EmployeeLevel.MANAGER.code) {
      const typeOfCompany = await this.checkTypeOfCurrentCompany(
        this.currentProduct.companyId
      );
      if (typeOfCompany === this.enumData.CompanyType.Parent.code) {
        this.currentProduct.currentCompanyType =
          this.enumData.CompanyType.Parent.code;

        // Call API for list children company of root company
      //   this.apiService
      //     .post(this.apiService.COMPANY.LOAD_DATA_SELECT_CHILDREN_COMPANY, {
      //       id: this.currentProduct.companyId,
      //     })
      //     .then((result) => {
      //       if (result) {
      //         this.listChildrenCompany = result;
      //       }
      //     });
      // } else if (typeOfCompany === this.enumData.CompanyType.Children.code) {
      //   this.currentProduct.currentCompanyType =
      //     this.enumData.CompanyType.Children.code;

      //   // Call API for list brand of child company
      //   this.apiService
      //     .post(this.apiService.BRAND.LOAD_DATA_BY_COMPANY, {
      //       id: this.currentProduct.companyId,
      //     })
      //     .then((result) => {
      //       if (result) {
      //         this.listBrandOfChildCompany = result;
      //       }
      //     });
      } else if (typeOfCompany === this.enumData.CompanyType.Independent.code) {
        this.currentProduct.currentCompanyType =
          this.enumData.CompanyType.Independent.code;
      }
    } else if (data === this.enumData.EmployeeLevel.EMP.code) {
      // Nếu là cấp sản phẩm
      this.currentProduct.currentCompanyType = '';
    }
  }

  async onChangeListBrandOfChildCompany(data: any) {
    // Call API for list brand of child company
    // this.apiService
    //   .post(this.apiService.BRANCH.LOAD_SELECT_BY_BRAND_LIST, {
    //     listBrandId: data,
    //   })
    //   .then((result) => {
    //     if (result) {
    //       this.listBranchOfBrands = result;
    //     }
    //   });
  }

  onChangeWarehouseCate() {
    this.currentProduct.branchId = '';
    this.currentProduct.departmentId = '';
    this.currentProduct.positionId = '';
    this.currentProduct.level = '';
    this.listBranch = [];
    this.listDepartment = [];
    this.listPosition = [];
    this.listEmployeeLevel = [];

    // this.apiService
    //   .post(this.apiService.BRANCH.LOAD_DATA_BY_COMPANY, {
    //     id: this.currentProduct.companyId,
    //   })
    //   .then((result) => {
    //     if (result) {
    //       this.listBranch = result;
    //     }
    //   });
    // this.apiService
    //   .post(this.apiService.DEPARTMENT.LOAD_DATA_BY_COMPANY, {
    //     id: this.currentProduct.companyId,
    //   })
    //   .then((result) => {
    //     if (result) {
    //       this.listDepartment = result;
    //     }
    //   });
    this.listEmployeeLevel = this.coreService.convertObjToArray(
      this.enumData.EmployeeLevel
    );
  }

  changeNumOfMonthProbation(event: any) {
    if (history.state.status === 'edit') {
      if (event) {
        if (
          this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation &&
          Number(event) <
            this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation
        ) {
          this.notifyService.showError(
            'Không thể chỉnh sửa số tháng thử việc vì số tháng thử việc phải lớn hơn ' +
              this.currentProduct.numberOfTimeKeepingHaveConfirmedInProbation
          );
        }
      }
    }
  }

  validateForm(): boolean {
    if (!this.currentProduct.birthday) {
      this.notifyService.showError('Vui lòng nhập ngày sinh!');
      return false;
    }

    if (!this.currentProduct.imageFace0) {
      this.notifyService.showError('Vui lòng nhập hình ảnh khuôn mặt');
      this.notifyService.hideloading();
      return false;
    }
    if (!this.currentProduct.lstImageIdNo1) {
      this.notifyService.showError(
        'Vui lòng nhập ảnh Ảnh CCCD/CMND/Passport mặt trước'
      );
      this.notifyService.hideloading();
      return false;
    }
    if (!this.currentProduct.lstImageIdNo2) {
      this.notifyService.showError(
        'Vui lòng nhập Ảnh CCCD/CMND/Passport mặt sau '
      );
      this.notifyService.hideloading();
      return false;
    }

    return true;
  }

  milliSecondInDay = 86400000;
  disabledEffectDate = (current: Date): boolean => {
    if (this.currentProduct.dateTo) {
      const dateTo = new Date(this.currentProduct.dateValid);
      const date = new Date();
      return (
        current.getTime() < date.getTime() - this.milliSecondInDay ||
        current.getTime() > dateTo.getTime()
      );
    }
    const date = new Date();
    return current.getTime() < date.getTime() - this.milliSecondInDay;
  };

  disabledExpiredDate = (current: Date): boolean => {
    if (this.currentProduct.dateExpire) {
      const dateFrom = new Date(this.currentProduct.dateExpire);
      return current.getTime() < dateFrom.getTime() - this.milliSecondInDay;
    }
    const date = new Date();
    return current.getTime() < date.getTime() - this.milliSecondInDay;
  };

  handleChangeFile(info: any) {
    this.filesUploaded = info.fileList.length > 0;

    if (info.file.status !== 'uploading') {
    }
    if (info.file.status === 'done') {
      (this.currentProduct.listOfFiles = info.fileList.map((file: any) => {
        return {
          name: file.name ?? file.fileName,
          fileUrl: file.response ? file.response.fileUrl : '',
          fileName: file.name ?? file.fileName,
          dataType: file.type ?? file.dataType,
          uid: file.uid,
        };
      })),
        this.notifyService.showSuccess(
          `${info.file.name} file uploaded successfully`
        );
    } else if (info.file.status === 'removed') {
      // Handle file removal
      {
        this.currentProduct.listOfFiles.forEach(
          (item: { uid: string }, index: any) => {
            if (item.uid === info.file.uid)
              this.currentProduct.listOfFiles.splice(index, 1);
          }
        );
      }
    } else if (info.file.status === 'error') {
      this.notifyService.showError(`${info.file.name} file upload failed.`);
    }
  }

  beforeUpload = (file: NzUploadFile): boolean => {
    const isLessThan10M = file.size! / 1024 / 1024 < 10;
    if (!isLessThan10M) {
      // You can alert the user or handle the error as needed
      // this.message.error('File upload phải nhỏ hơn 10MB!');
    }
    return isLessThan10M;
  };

  async setDataForCurrentMainCompany(data: any) {}
}
