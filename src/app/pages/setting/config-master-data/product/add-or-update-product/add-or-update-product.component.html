<form nz-form #frmAdd="ngForm" autocomplete="off">
  <div matDialogContent>
    <div nz-row>
      
      
      <!-- <PERSON><PERSON> sản phẩm -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Mã sản phẩm</nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng nhập Mã sản pẩhm (Ít nhất 1-100 kí tự)!">
            <input nz-input placeholder="Nhập Mã sản pẩhm ít nhất 1 kí tự" [(ngModel)]="currentProduct.code" name="code"
              required pattern=".{1,100}" />
          </nz-form-control>
        </nz-form-item>

      </div>
      <!-- Tên sản phẩm -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Tên sản phẩm</nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng nhập tên (Ít nhất 1-100 kí tự)!">
            <input nz-input placeholder="Nhập tên ít nhất 1 kí tự" [(ngModel)]="currentProduct.name" name="name"
              required pattern=".{1,100}" />
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Nhóm hàng -->
        <div nz-col [nzSpan]="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Nhóm hàng</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Nhóm hàng!">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="currentProduct.groupOfGoodsId" name="groupOfGoodsId"
                nzPlaceHolder="Nhóm hàng" class="mr-3" (ngModelChange)="onChangePart(currentProduct.groupOfGoodsId)"
                required>
                <nz-option *ngFor="let item of listPart" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Bộ phận -->
        <div nz-col [nzSpan]="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Bộ phận sử dụng</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Bộ phận sử dụng!">
              <nz-select #positionSelect nzShowSearch nzAllowClear [(ngModel)]="currentProduct.partId" name="partId"
                nzPlaceHolder="Bộ phận" class="mr-3" required (ngModelChange)="onChangePosition($event)">
                <nz-option *ngFor="let item of listPosition" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      <!-- Thuộc kho -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Thuộc kho </nz-form-label>
          <nz-form-control [nzSm]="23" [nzXs]="23" nzErrorTip="Vui lòng chọn kho!">
            <nz-select #branchSelect nzShowSearch nzAllowClear [(ngModel)]="currentProduct.warehourseId"
              name="warehourseId" nzPlaceHolder="Kho" class="mr-3"
              (ngModelChange)="onChangeBranch(currentProduct.warehourseId)">
              <nz-option *ngFor="let item of listBranch" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Chiều dài -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Chiều dài
          </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng nhập Chiều dài">
            <nz-input-number required placeholder="Chiều dài" [(ngModel)]="currentProduct.productLength"
              name="productLength" style="width: 100%" [nzMin]="1" [nzStep]="1"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- chiều rộng -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Chiều rộng
          </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng nhập Chiều rộng">
            <nz-input-number required placeholder="Chiều rộng" [(ngModel)]="currentProduct.productWidth"
              name="productWidth" style="width: 100%" [nzMin]="1" [nzStep]="1"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Chiều cao -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Chiều cao
          </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng nhập Chiều cao">
            <nz-input-number required placeholder="Chiều cao" [(ngModel)]="currentProduct.productHeight"
              name="productHeight" style="width: 100%" [nzMin]="1" [nzStep]="1"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Khối lượng -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Khối lượng
          </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Khối lượng">
            <nz-input-number required placeholder="Khối lượng" [(ngModel)]="currentProduct.productKg" name="productKg"
              style="width: 100%" [nzMin]="1" [nzStep]="1"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Đơn vị tính -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Đơn vị tính </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Đơn vị tính!">
            <nz-select #departmentSelect nzShowSearch nzAllowClear [(ngModel)]="currentProduct.warehouseUnitId"
              name="warehouseUnitId" nzPlaceHolder="Đơn vị tính" class="mr-3"
              (ngModelChange)="onChangeDepartment(currentProduct.warehouseUnitId)" required>
              <nz-option *ngFor="let item of listDepartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Đơn vị cơ sở -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Đơn vị cơ sở</nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Đơn vị cơ sở!">
            <nz-select #departmentSelect nzShowSearch nzAllowClear [(ngModel)]="currentProduct.baseUnitId"
              name="baseUnitId" nzPlaceHolder="Đơn vị cơ sở" class="mr-3"
              (ngModelChange)="onChangeDepartment(currentProduct.baseUnitId)" required>
              <nz-option *ngFor="let item of listDepartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Sai lệch -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Sai lệch (%)
          </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Sai lệch">
            <nz-input-number required placeholder="Sai lệch" [(ngModel)]="currentProduct.deviation" name="deviation"
              style="width: 100%" [nzMin]="1" [nzStep]="1"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>


      <!-- Nhân viên quản lý -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Nhân viên quản lý</nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng chọn Nhân viên quản lý!">
            <nz-select #positionSelect nzShowSearch nzAllowClear [(ngModel)]="currentProduct.employeeId"
              name="employeeId" nzPlaceHolder="Nhân viên quản lý" class="mr-3" required
              (ngModelChange)="onChangePosition($event)">
              <nz-option *ngFor="let item of listPosition" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Quy đổi -->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Quy đổi</nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Vui lòng nhập  (Ít nhất 1-100 kí tự)!">
            <input nz-input placeholder="Nhập ít nhất 1 kí tự" [(ngModel)]="currentProduct.convert" name="convert"
              required pattern=".{1,100}" />
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Số lượng tồn kho an toàn-->
      <div nz-col [nzSpan]="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Số lượng tồn kho an toàn
          </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="22" nzErrorTip="Số lượng tồn kho an toàn">
            <nz-input-number required placeholder="Số lượng tồn kho an toàn" [(ngModel)]="currentProduct.inventoryQty"
              name="inventoryQty" style="width: 100%" [nzMin]="1" [nzStep]="1"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- Ghi chú  -->
      <div nz-col [nzSpan]="18" class="text-center">
        <nz-form-item nzFlex class="pr-4">
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mô tả</nz-form-label>
          <nz-form-control nzErrorTip="Vui lòng nhập mô tả không quá 250 kí tự!">
            <textarea rows="2" nz-input placeholder="Nhập Mô tả" [(ngModel)]="currentProduct.description"
              name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col [nzSpan]="24">
        <div nz-row>
          <!-- Hình ảnh sản phẩm -->
          <div nz-col [nzSpan]="8">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Hình ảnh sản phẩm 1</nz-form-label>
              <nz-form-control [nzSm]="22" [nzXs]="22">
                <nz-upload nzListType="picture-card" [(nzFileList)]="currentProduct.productImage1"
                  [nzAction]="uploadUrl" [nzPreview]="handlePreview" [nzMultiple]="true"
                  (nzChange)="handleChange($event, currentProduct.productImage1)"
                  nzAccept=".png, .jpg, .jpeg, .svg, .webp" [nzShowButton]="currentProduct.productImage1?.length < 1"
                  required>
                  <div>
                    <i nz-icon nzType="plus"></i>
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </nz-upload>
                <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
                  (nzOnCancel)="previewVisible = false">
                  <ng-template #modalContent>
                    <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
                  </ng-template>
                </nz-modal>
              </nz-form-control>
            </nz-form-item>
          </div>

          <!-- Ảnh CCCD/CMND mặt trước -->
          <div nz-col [nzSpan]="8">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Hình ảnh sản phẩm 2</nz-form-label>
              <nz-form-control [nzSm]="22" [nzXs]="22">
                <nz-upload nzListType="picture-card" [(nzFileList)]="currentProduct.productImage2"
                  [nzAction]="uploadUrl" [nzPreview]="handlePreview" [nzMultiple]="true"
                  (nzChange)="handleChange($event, currentProduct.productImage2)"
                  nzAccept=".png, .jpg, .jpeg, .svg, .webp" [nzShowButton]="currentProduct.productImage2?.length < 1"
                  required>
                  <div>
                    <i nz-icon nzType="plus"></i>
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </nz-upload>
                <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
                  (nzOnCancel)="previewVisible = false">
                  <ng-template #modalContent>
                    <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
                  </ng-template>
                </nz-modal>
              </nz-form-control>
            </nz-form-item>
          </div>
          <!-- Ảnh sản phẩm mặt sau -->
          <div nz-col [nzSpan]="8">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Hình ảnh sản phẩm 3</nz-form-label>
              <nz-form-control [nzSm]="22" [nzXs]="22">
                <nz-upload nzListType="picture-card" [(nzFileList)]="currentProduct.productImage3"
                  [nzAction]="uploadUrl" [nzPreview]="handlePreview" [nzMultiple]="true"
                  (nzChange)="handleChange($event, currentProduct.productImage3)"
                  nzAccept=".png, .jpg, .jpeg, .svg, .webp" [nzShowButton]="currentProduct.productImage3?.length < 1"
                  required>
                  <div>
                    <i nz-icon nzType="plus"></i>
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </nz-upload>
                <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
                  (nzOnCancel)="previewVisible = false">
                  <ng-template #modalContent>
                    <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
                  </ng-template>
                </nz-modal>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
      </div>
    </div>
  </div>
  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button class="mr-2" nzShape="round" nz-button  nzType="primary"
        (click)="onSubmit()">
        <span nz-icon nzType="save"></span>Lưu
      </button>
   
    </nz-col>
  </nz-row>
</form>