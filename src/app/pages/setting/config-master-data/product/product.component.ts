import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { enumData } from 'src/app/core/enumData';

import { ApiService, AuthenticationService, CoreService, NotifyService } from 'src/app/services';
import { AddOrUpdateProductProviderPriceComponent } from './add-or-update-product-provider-price/add-or-update-product-provider-price.component';
@Component({
  selector: 'app-product',
  templateUrl: './product.component.html',
})
export class ProductComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  dataObject: any = {};
  listOfData: any[] = [
    {
      code: 'ITM-0001',
      name: '<PERSON>pt<PERSON> (15.6 inch)',
      groupOfGoods: 'Electronics',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: '<PERSON>',
      status: 'Đang hoạt động',
    },
    {
      code: 'AP-0002',
      name: '<PERSON><PERSON> (Red)',
      groupOfGoods: 'Produce',
      warehouse: 'Cold Storage',
      unit: 'Crate',
      employeeName: 'Michael <PERSON>',
      status: 'Đang hoạt động',
    },
    {
      code: 'TOOL-0003',
      name: 'Hammer',
      groupOfGoods: 'Hardware',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Alex Davis',
      status: 'Đang hoạt động',
    },
    {
      code: 'CHEM-0004',
      name: 'Cleaning Solution',
      groupOfGoods: 'Supplies',
      warehouse: 'Main Warehouse',
      unit: 'Bottle',
      employeeName: 'Olivia Clark',
      status: 'Đang hoạt động',
    },
    {
      code: 'FAB-0005',
      name: 'Cotton Fabric',
      groupOfGoods: 'Textiles',
      warehouse: 'Fabric Storage',
      unit: 'Roll',
      employeeName: 'Emily Williams',
      status: 'Đang hoạt động',
    },
    {
      code: 'PACK-0006',
      name: 'Cardboard Boxes',
      groupOfGoods: 'Packaging',
      warehouse: 'Main Warehouse',
      unit: 'Bundle',
      employeeName: 'Peter Thompson',
      status: 'Đang hoạt động',
    },
    {
      code: 'FURN-0007',
      name: 'Office Chair',
      groupOfGoods: 'Furniture',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Noah Brown',
      status: 'Đang hoạt động',
    },
    {
      code: 'MED-0008',
      name: 'First Aid Kit',
      groupOfGoods: 'Medical',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Jessica Miller',
      status: 'Đang hoạt động',
    },
    {
      code: 'TOY-0009',
      name: 'Building Blocks',
      groupOfGoods: 'Toys',
      warehouse: 'Main Warehouse',
      unit: 'Set',
      employeeName: 'Jacob Wilson',
      status: 'Đang hoạt động',
    },
    {
      code: 'SPORT-0010',
      name: 'Soccer Ball',
      groupOfGoods: 'Sporting Goods',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Mia Taylor',
      status: 'Đang hoạt động',
    },

    // --- New Data ---
    {
      code: 'FOOD-0011',
      name: 'Canned Tuna',
      groupOfGoods: 'Food',
      warehouse: 'Dry Storage',
      unit: 'Can',
      employeeName: 'Thomas Green',
      status: 'Đang hoạt động',
    },
    {
      code: 'BEV-0012',
      name: 'Bottled Water',
      groupOfGoods: 'Beverages',
      warehouse: 'Dry Storage',
      unit: 'Case',
      employeeName: 'Sophia Garcia',
      status: 'Đang hoạt động',
    },
    {
      code: 'TOOL-0013',
      name: 'Wrench Set',
      groupOfGoods: 'Hardware',
      warehouse: 'Main Warehouse',
      unit: 'Set',
      employeeName: 'Daniel Martinez',
      status: 'Đang hoạt động',
    },
    {
      code: 'CHEM-0014',
      name: 'Disinfectant Spray',
      groupOfGoods: 'Supplies',
      warehouse: 'Main Warehouse',
      unit: 'Bottle',
      employeeName: 'Ava Lopez',
      status: 'Đang hoạt động',
    },
    {
      code: 'FAB-0015',
      name: 'Silk Fabric',
      groupOfGoods: 'Textiles',
      warehouse: 'Fabric Storage',
      unit: 'Yard',
      employeeName: 'Isabella Rodriguez',
      status: 'Đang hoạt động',
    },
    {
      code: 'PACK-0016',
      name: 'Shipping Envelopes',
      groupOfGoods: 'Packaging',
      warehouse: 'Main Warehouse',
      unit: 'Pack',
      employeeName: 'William Anderson',
      status: 'Đang hoạt động',
    },
    {
      code: 'FURN-0017',
      name: 'Desk Lamp',
      groupOfGoods: 'Furniture',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Chloe Martin',
      status: 'Đang hoạt động',
    },
    {
      code: 'TECH-0018',
      name: 'Wireless Mouse',
      groupOfGoods: 'Electronics',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Ethan Perez',
      status: 'Đang hoạt động',
    },
    {
      code: 'TOY-0019',
      name: 'Plush Teddy Bear',
      groupOfGoods: 'Toys',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Lucas Hernandez',
      status: 'Đang hoạt động',
    },
    {
      code: 'SPORT-0020',
      name: 'Basketball',
      groupOfGoods: 'Sporting Goods',
      warehouse: 'Main Warehouse',
      unit: 'Each',
      employeeName: 'Emma Jackson',
      status: 'Đang hoạt động',
    },
  ];

  lstWard: any = [];
  lstCity: any = [];
  // Load data section var
  loading = false;
  dataSearch: any = {};

  // Selection table props
  checked = false;
  indeterminate = false;
  listOfCurrentPageData: any = [];
  setOfCheckedId = new Set<number>();

  constructor(
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiService: ApiService,
    private coreService: CoreService,
    private router: Router
  ) {}

  ngOnInit() {}

  async searchData(reset = false, clearFilter = false) {
    this.loading = true;
    if (reset) this.pageIndex = 1;
    if (clearFilter) this.dataSearch = {};

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    };
    await this.apiService
      .post(this.apiService.EMPLOYEE.PAGINATION, dataSearch)
      .then((res: any) => {
        if (res) {
          this.loading = false;
          this.total = res[1];
          this.listOfData = res[0];
          this.notifyService.hideloading();
        }
      });
  }

  clickAdd() {
    this.dialog
      .open(AddOrUpdateProductProviderPriceComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrUpdateProductProviderPriceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  handleUpdateActive(data: any) {
    // this.notifyService.showloading();
    // this.apiService
    //   .post(this.apiService.POSITION.DELETE, { id: data.id })
    //   .then(() => {
    //     this.notifyService.showSuccess('Cập nhật trạng thái thành công');
    //     this.searchData();
    //   });
  }

  viewHistories(data: any) {
    // this.dialog
    //   .open(ListPositionHistoriesComponent, { disableClose: false, data })
    //   .afterClosed()
    //   .subscribe((result) => {
    //     if (result) {
    //       this.searchData()
    //     }
    //   })
  }

  // Excel section!

  clickDownloadTemplateExcel() {}

  clickImportExcel(data: any) {}

  onDownloadExcel() {}

  refreshCheckedStatus(): void {
    // const listOfEnabledData = this.listOfCurrentPageData.filter(
    //   ({ disabled }) => !disabled
    // );
    // this.checked = listOfEnabledData.every(({ id }) =>
    //   this.setOfCheckedId.has(id)
    // );
    // this.indeterminate =
    //   listOfEnabledData.some(({ id }) => this.setOfCheckedId.has(id)) &&
    //   !this.checked;
  }

  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onCurrentPageDataChange(listOfCurrentPageData: any): void {
    this.listOfCurrentPageData = listOfCurrentPageData;
    this.refreshCheckedStatus();
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }
}
