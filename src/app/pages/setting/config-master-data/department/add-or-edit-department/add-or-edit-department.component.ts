import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-department.component.html' })
export class AddOrEditDepartmentComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  modalTitle = 'Thêm phòng ban'
  dataBranch: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditDepartmentComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sửa phòng ban'
    }
    this.loadBranch()
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.isDeleted = false
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.DEPARTMENT.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.DEPARTMENT.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  loadBranch() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BRANCH.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataBranch = result
    })
  }
}
