import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-add-or-edit-bill-lookup',
  templateUrl: './add-or-edit-bill-lookup.component.html',
  styleUrls: ['./add-or-edit-bill-lookup.component.scss'],
})
export class AddOrEditBillLookupComponent {
  modalTitle = 'Tra cứu hóa đơn'
  isEditMode = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  dataObject: any = {}

  constructor(
    readonly apiService: ApiService,
    readonly coreService: CoreService,
    readonly storageService: StorageService,
    readonly notifyService: NotifyService,
    readonly dialogRef: MatDialogRef<AddOrEditBillLookupComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) readonly data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data?.id) {
      this.modalTitle = `Chỉnh sửa ${this.modalTitle}`
      this.dataObject = { ...this.data }
      this.isEditMode = true
    } else {
      this.modalTitle = `Thêm mới ${this.modalTitle}`
      this.dataObject = {}
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
    this.notifyService.hideloading()
  }

  addObject() {
    this.apiService.post(this.apiService.BILL_LOOKUP.CREATE_DATA, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.BILL_LOOKUP.UPDATE_DATA, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
