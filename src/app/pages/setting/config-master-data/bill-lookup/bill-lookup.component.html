<!-- form search -->
<nz-collapse class="mt-3">
  <nz-collapse-panel nzHeader="Tìm kiếm nâng cao">
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tìm theo mã tra cứu hóa đơn</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.code" placeholder="Tìm theo mã tra cứu hóa đơn" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tìm theo tên tra cứu hóa đơn</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo tên tra cứu hóa đơn" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-label class="text-left" nzSpan="24">Trạng thái hoạt động</nz-form-label>

        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.statusId"
          name="statusId"
          [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'"
        >
          <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSpan="24" class="text-center mt-3">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true, true)" class="mr-2">
          <span nz-icon nzType="redo"></span>Xóa bộ lọc
        </button>
        <button nzShape="round" nz-button (click)="searchData(true)" nzType="primary" nzGhost><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>
<!-- result table -->

<nz-row nzGutter="8" class="mt-2">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" (click)="clickAdd()"><span nz-icon nzType="plus"></span>{{ language_key?.ADD || 'Thêm mới' }}</button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
  >
    <thead>
      <tr>
        <th nzWidth="150px">Mã</th>
        <th nzWidth="200px">Tên</th>
        <th nzWidth="200px">Link</th>
        <th nzWidth="200px">Tên viết tắt</th>
        <th nzWidth="300px">Mô tả</th>
        <th nzWidth="200px">Trạng thái</th>
        <th nzWidth="150px">Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td>{{ data.code }}</td>
        <td>{{ data.name }}</td>
        <td>{{ data.link }}</td>
        <td>{{ data.abbreviation }}</td>
        <td>{{ data.description }}</td>
        <td class="text-center">
          <nz-tag
            *ngIf="data.isDeleted"
            [ngStyle]="{
              color: 'red',
              border: '1px solid #ffa39e'
            }"
            [nzColor]="'#fff1f0'"
            style="width: 200px; font-weight: 600; border-radius: 30px"
          >
            <div style="display: flex; align-items: center; justify-content: center">
              <div class="dot" [ngStyle]="{ background: 'red' }"></div>
              <span class="ml-1">Ngưng hoạt động</span>
            </div>
          </nz-tag>

          <nz-tag
            *ngIf="!data.isDeleted"
            [ngStyle]="{
              color: '#0063D8',
              border: '1px solid #2A7DDF'
            }"
            [nzColor]="'#DCEEFF'"
            style="width: 200px; font-weight: 600; border-radius: 30px"
          >
            <div style="display: flex; align-items: center; justify-content: center">
              <div class="dot" [ngStyle]="{ background: '#0063D8' }"></div>
              <span class="ml-1">Đang hoạt động</span>
            </div>
          </nz-tag>
        </td>
        <td class="text-center">
          <button
            nz-button
            nzShape="circle"
            (click)="clickEdit(data)"
            class="mr-1 mt-1 mb-1 btn-primary"
            nzTooltipTitle="Cập nhật"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="edit"></span>
          </button>
          <!-- ngưng hoạt động -->

          <button
            *ngIf="!data.isDeleted"
            nz-button
            nzShape="circle"
            class="mr-1 mt-1 mb-1 btn-primary"
            [nzTooltipTitle]="'Ngưng hoạt động'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="updateActiveStatus(data)"
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="play-circle"></span>
          </button>

          <!-- Hoạt động lại -->
          <button
            *ngIf="data.isDeleted"
            nz-button
            nzShape="circle"
            class="mr-1 mt-1 mb-1 btn-primary"
            [nzTooltipTitle]="'Hoạt động lại'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="updateActiveStatus(data)"
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="stop"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>
<nz-row>
  <nz-col nzSpan="24" class="text-center mt-2">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
