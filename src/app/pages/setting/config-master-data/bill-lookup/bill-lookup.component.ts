import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import * as XLSX from 'xlsx'
import * as fs from 'file-saver'
import { Workbook } from 'exceljs'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { User } from 'src/app/models'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { AddOrEditBillLookupComponent } from './add-or-edit-bill-lookup/add-or-edit-bill-lookup.component'

@Component({
  selector: 'app-bill-lookup',
  templateUrl: './bill-lookup.component.html',
  styleUrls: ['./bill-lookup.component.scss'],
})
export class BillLookupComponent {
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  dataUploadExcel: any
  listOfData: any[] = []
  dataSearch: any = {}
  dataStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  loading = true
  currentUser!: User
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any

  constructor(
    readonly authenticationService: AuthenticationService,
    readonly apiService: ApiService,
    readonly coreService: CoreService,
    readonly storageService: StorageService,
    readonly notifyService: NotifyService,
    readonly dialog: MatDialog,
    private router: Router
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  /**  */
  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.searchData()
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    if (clearFilter) this.dataSearch = {}
    if (reset) this.pageIndex = 1
    this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (!this.dataSearch.statusId || this.dataSearch.statusId) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }

    const dataSearch: any = {
      where: where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.BILL_LOOKUP.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        this.notifyService.hideloading()
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditBillLookupComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(data: any) {
    this.dialog
      .open(AddOrEditBillLookupComponent, { disableClose: false, data: data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BILL_LOOKUP.UPDATE_ACTIVE_STATUS, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
    this.notifyService.hideloading()
  }

  async onDownload() {
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.apiService.post(this.apiService.BILL_LOOKUP.PAGINATION, dataSearch).then((res: any) => {
      this.notifyService.hideloading()
      if (res) {
        let date = new Date().toISOString()
        const fileName = `DS_Tra_Cuu_${date}.xlsx`
        let dataExcel: any = []
        res[0].forEach((s: any) => {
          dataExcel.push({
            'Mã tra cứu': s.code,
            'Tên tra cứu': s.name,
            'Tên viết tắt': s.abbreviation,
            Link: s.link,
            'Mô tả': s.description,
          })
        })
        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
        const wb: XLSX.WorkBook = XLSX.utils.book_new()
        ws['!cols'] = Array(Object.keys(dataExcel[0]).length).fill({ width: 30 })
        XLSX.utils.book_append_sheet(wb, ws, 'DS_Tra_Cuu')
        XLSX.writeFile(wb, fileName)
      }
      this.notifyService.hideloading()
    })
  }

  //template excel
  clickDownloadTemplateExcel() {
    this.notifyService.showloading()
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('DS_Tra_Cuu')
    //#region Body Table
    const header = ['Tên *', 'Link *', 'Tên viết tắt', 'Mô tả']

    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '001E3E' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' }, size: 13 }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 50
          break
        case 4:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 35
          worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
          break
      }
    })

    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `Template_Tra_Cuu_${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
  }

  clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ((event as any).target as any).files[0]

    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['name', 'link', 'abbreviation', 'description'],
      })
      // fix lỗi k import 2 lần đc
      // ;(<HTMLInputElement>document.getElementById('file-emp')).value = ''
      const fileInput = document.getElementById('file-emp')
      if (fileInput) {
        ;(<HTMLInputElement>fileInput).value = ''
      } else {
        console.error('Element with ID file-emp not found')
      }

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''

      const inputData: any = {}
      inputData.lstData = []
      for (const row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.name == null || (typeof row.name === 'string' && row.name.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tên không được để trống <br>'
        }
        if (row.link == null || (typeof row.link === 'string' && row.link.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - đường link không được để trống <br>'
        }
      }
      if (strErr.length > 0) {
        this.notifyService.hideloading()
        this.notifyService.showError(strErr)
        return
      }
      this.apiService.post(this.apiService.BILL_LOOKUP.IMPORT_EXCEL, jsonData).then((res: any) => {
        this.notifyService.showSuccess('Thêm Mới File Excel Thành Công')
        this.searchData()
      })
    }
  }

  updateActiveStatus(data: any) {
    this.apiService.post(this.apiService.BILL_LOOKUP.UPDATE_ACTIVE_STATUS, data).then((res) => {
      this.notifyService.showSuccess('Cập nhật trạng thái thành công')
      this.searchData()
    })
  }
}
