<nz-row nzGutter="8">
  <nz-col nzSpan="6">
    <input nz-input [(ngModel)]="dataSearch.filterText" placeholder="Tìm theo mã hoặc tên" />
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear (ngModelChange)="serviceChange($event)" [(ngModel)]="dataSearch.parent1"
      name="parent1" nzPlaceHolder="Chọn cấp 1">
      <nz-option *ngFor="let item of dataService" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.parent2" name="parent1" nzPlaceHolder="Chọn cấp 2">
      <nz-option *ngFor="let item of dataServiceChild" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
      [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
      <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
    </nz-select>
  </nz-col>
</nz-row>
<nz-row class="mt-2">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-2">
  <nz-col nzSpan="24">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button nzType="primary"
      (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ language_key?.ADD || 'Thêm mới' }}
    </button>
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="mr-2" nz-button
      (click)="clickExportExcelTemplate()">
      <span nz-icon nzType="download"></span>Xuất template excel
    </button>
    <input *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="hidden" type="file"
      id="file" (change)="clickImportExcel($event)" onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
    <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file"
      class="lable-custom-file mr-2">
      <span nz-icon nzType="upload"></span> Nhập excel
    </label>
    <button *ngIf="lstErrorImport?.length" class="mr-2" nz-button (click)="clickShowErrorImport()" nzType="primary"
      nzDanger>
      <span nz-icon nzType="warning"></span>Danh sách lỗi import
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Cấp 1</th>
        <th>Cấp 2</th>
        <th>Mã LVMH</th>
        <th>Tên LVMH</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th>Tùy chọn</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td class="mw-25">
          {{ data.__parent__?.__parent__?.code || data.__parent__?.code || '' }}
        </td>
        <td class="mw-25">
          {{ data.__parent__?.parentId ? data.__parent__?.code : '' }}
        </td>
        <td>{{ data.code }}</td>
        <td class="mw-25">{{ data.name }}</td>
        <td class="mw-25">
          <nz-tag nzColor="#f50" *ngIf="data.isDeleted">Không hoạt động</nz-tag>
          <nz-tag nzColor="#87d068" *ngIf="!data.isDeleted">Đang hoạt động</nz-tag>
        </td>
        <td>
          <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-tooltip
            nzTooltipTitle="Chỉnh sửa" (click)="clickEdit(data)" class="mr-2" nz-button nzType="dashed">
            <span nz-icon nzType="form"></span>
          </button>
          <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip
            [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
            <span nz-icon nzType="stop"></span>
          </button>
          <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
            nz-button nzType="primary">
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisibleError" [nzWidth]="'60vw'" nzTitle="Danh sách lỗi nhập excel"
  (nzOnCancel)="closeModelError()" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="lstErrorImport" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false"
        nzBordered>
        <thead>
          <tr>
            <th>{{ language_key?.NO || 'STT' }}</th>
            <th>Nội dung lỗi</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of lstErrorImport">
            <td>{{ data.zenId }}</td>
            <td>{{ data.errorMessage }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>