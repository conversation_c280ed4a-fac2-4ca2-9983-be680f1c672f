import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({ templateUrl: './add-or-edit-service.component.html' })
export class AddOrEditServiceComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  dataEmployee: any[] = []
  dataService: any[] = []
  dataServiceChild: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  modalTitle = 'Thông tin lĩnh vực mua hàng'

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditServiceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    await this.loadService()
    if (this.data && this.data !== null) {
      this.isEditItem = true
      this.dataObject = { ...this.data }
      this.dataObject.code = this.data.code
      this.dataObject.name = this.data.name
      if (this.data.level === 1) {
        this.dataObject.parent1 = null
        this.dataObject.parent2 = null
      }
      if (this.data.level === 2) {
        this.dataObject.parent1 = this.data.parentId
        await this.loadServiceChild(this.dataObject.parent1)
        this.dataObject.parent2 = null
      }
      if (this.data.level === 3) {
        this.dataObject.parent1 = this.data.__parent__.parentId
        await this.loadServiceChild(this.dataObject.parent1)
        this.dataObject.parent2 = this.data.parentId
      }
      this.dataObject.description = this.data.description
      this.dataObject.serviceAccess = null
      if (this.data.__serviceAccess__ && this.data.__serviceAccess__.length > 0) {
        this.dataObject.serviceAccess = this.data.__serviceAccess__.map((c: any) => c.employeeId)
      }
      this.dataObject.approveById = this.data.approveById
    }

    await this.loadFullEmployee()
  }

  async loadService() {
    await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 }).then((result) => {
      this.dataService = result
      if (this.data && this.data.id) {
        this.dataService = this.dataService.filter((c) => c.id !== this.data.id)
      }
    })
  }

  async loadFullEmployee() {
    await this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((result) => {
      this.dataEmployee = result
    })
  }

  async loadServiceChild(parentId: any) {
    this.dataServiceChild = []

    await this.apiService.post(this.apiService.SERVICE.FIND, { level: 2, parentId }).then((result) => {
      this.dataServiceChild = result
      if (this.data && this.data.id) {
        this.dataServiceChild = this.dataServiceChild.filter((c) => c.id !== this.data.id)
      }
    })
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.isDelete = false
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SERVICE.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SERVICE.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
