<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzFor="parent1" class="text-left">Cấp 1</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch id="parent1" nzAllowClear (ngModelChange)="loadServiceChild($event)"
              [(ngModel)]="dataObject.parent1" name="parent1" nzPlaceHolder="Chọn lĩnh vực cấp 1">
              <nz-option *ngFor="let item of dataService" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzFor="parent2" class="text-left">Cấp 2</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch id="parent2" nzAllowClear [(ngModel)]="dataObject.parent2" name="parent2"
              nzPlaceHolder="Chọn lĩnh vực cấp 2">
              <nz-option *ngFor="let item of dataServiceChild" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Mã Lĩnh vực mua hàng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập lĩnh vực mua hàng (1-50 kí tự)!">
            <input nz-input placeholder="Nhập mã lĩnh vực mua hàng 1-50 kí tự" [disabled]="isEditItem"
              [(ngModel)]="dataObject.code" name="code" required pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Tên Lĩnh vực mua hàng
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mô tả (1-50 kí tự)!">
            <input nz-input placeholder="Nhập tên lĩnh vực mua hàng 1-50 kí tự" [(ngModel)]="dataObject.name"
              name="name" pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Nhân viên phụ trách</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzMode="multiple" nzPlaceHolder="Chọn nhân viên"
              [(ngModel)]="dataObject.serviceAccess" name="serviceAccess">
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Người duyệt</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người duyệt!">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn nhân viên" [(ngModel)]="dataObject.approveById"
              name="approveById" required>
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>


    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>