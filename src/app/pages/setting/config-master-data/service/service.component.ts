import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditServiceComponent } from './add-or-edit-service/add-or-edit-service.component'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { User } from 'src/app/models'

@Component({
  selector: 'app-service',
  templateUrl: './service.component.html',
})
export class ServiceComponent implements OnInit {
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataServiceChild: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  listOfData: any[] = []
  currentUser!: User
  isVisibleError = false
  lstErrorImport: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_013.code
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.loadService()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.filterText && this.dataSearch.filterText !== '') {
      where.filterText = this.dataSearch.filterText
    }

    if (this.dataSearch.parent1 && this.dataSearch.parent1 !== '') {
      where.parentId = this.dataSearch.parent1
    }
    if (this.dataSearch.parent2 && this.dataSearch.parent2 !== '') {
      where.parentId = this.dataSearch.parent2
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }

    this.loading = true
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.SERVICE.PAGINATION, dataSearch).then((res) => {
      if (res) {
        this.loading = false
        this.total = res[1]
        this.listOfData = res[0]
      }
    })
  }

  serviceChange(event: any) {
    this.loadServiceChild(event)
  }

  async loadService() {
    this.dataService = []
    this.dataServiceChild = []
    await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 }).then((result) => {
      this.dataService = result
    })
  }

  async loadServiceChild(parentId: any) {
    await this.apiService.post(this.apiService.SERVICE.FIND, { level: 2, parentId }).then((result) => {
      this.dataServiceChild = result
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditServiceComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.searchData()
          this.loadService()
        }
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditServiceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.searchData()
          this.loadService()
        }
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
      this.loadService()
    })
  }

  //#region Import excel service
  clickExportExcelTemplate() {
    this.notifyService.showloading()
    let lstDataExport = []
    //#region header
    const header: any = {
      zenId: this.language_key?.NO || 'STT' + ' *',
      code: 'Mã Lĩnh vực mua hàng *',
      name: 'Tên Lĩnh vực mua hàng *',
      accessByName: 'Nhân viên phụ trách *',
      approveByName: 'Người duyệt *',
      parentCode: 'Thuộc lĩnh vực mua hàng',
    }
    lstDataExport.push(header)
    //#endregion

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `Template import service.xlsx`
    XLSX.utils.book_append_sheet(wb, ws)

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  clickImportExcel(ev: any) {
    this.notifyService.showloading()
    this.lstErrorImport = []
    let workBook = null
    let jsonData: any[] = []
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['zenId', 'code', 'name', 'accessByName', 'approveByName', 'parentCode'],
      })
      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()
      // Kiểm tra header
      if (
        header.zenId !== (this.language_key?.NO || 'STT' + ' *') ||
        header.code !== 'Mã Lĩnh vực mua hàng *' ||
        header.name !== 'Tên Lĩnh vực mua hàng *' ||
        header.accessByName !== 'Nhân viên phụ trách *' ||
        header.approveByName !== 'Người duyệt *' ||
        header.parentCode !== 'Thuộc lĩnh vực mua hàng'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(`File không đúng template import service`)
        return
      }

      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có LVMH cần import!`)
        return
      }

      jsonData = jsonData.filter((c) => c.zenId != null && c.zenId !== '' && (c.zenId > 0 || c.zenId.trim() !== ''))
      if (jsonData.length == 0) {
        this.notifyService.showError(`File không có LVMH cần import. Lưu ý, chỉ dòng có STT mới được import!`)
        return
      }

      this.apiService.post(this.apiService.SERVICE.IMPORT, { lstData: jsonData }).then((res) => {
        if (res) {
          // Lỗi khi kiểm tra trước lúc vào transaction (không import)
          if (res.isCheckError) {
            this.notifyService.showError(`${res.message}`)
            this.lstErrorImport = res.lstError
            this.isVisibleError = true
          }
          // Lỗi khi vào transaction (import 1 phần)
          else if (res.lstError.length > 0) {
            this.notifyService.showError(`${res.message}`)
            this.lstErrorImport = res.lstError
            this.isVisibleError = true
            this.searchData(true)
          }
          // import 100%
          else {
            this.notifyService.showSuccess(`${res.message}`)
            this.searchData(true)
            this.lstErrorImport = []
          }
        }
      })
    }
  }

  clickShowErrorImport() {
    this.isVisibleError = true
  }

  closeModelError() {
    this.isVisibleError = false
  }
  //#endregion
}
