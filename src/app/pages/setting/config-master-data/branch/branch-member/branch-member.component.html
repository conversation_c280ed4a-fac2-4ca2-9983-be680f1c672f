<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{modalTitle | uppercase}}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8" class="mt-2">
    <nz-col nzSpan="24">
      <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
        (click)="clickAdd()">
        <span nz-icon nzType="plus"></span>{{ language_key?.ADD || 'Thêm mới' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-1">
    <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
      [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
      <thead>
        <tr class="text-nowrap">
          <th>Nh<PERSON> viên</th>
          <th><PERSON><PERSON><PERSON> vụ</th>
          <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td class="mw-25" (click)="clickEdit(data)">{{ data.employeeName }}</td>
          <td class="mw-25" (click)="clickEdit(data)">{{ data.typeName }}</td>
          <td class="text-nowrap">
            <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="setActiveItem(data)" nz-tooltip
              [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
              <span nz-icon nzType="stop"></span>
            </button>
            <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
              nz-button nzType="primary">
              <span nz-icon nzType="play-circle"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-row>
</div>