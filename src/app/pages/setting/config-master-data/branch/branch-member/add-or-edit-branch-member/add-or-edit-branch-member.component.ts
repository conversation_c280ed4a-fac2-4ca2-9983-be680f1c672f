import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../../services'

@Component({ templateUrl: './add-or-edit-branch-member.component.html' })
export class AddOrEditBranchMemberComponent implements OnInit {
  dataObject: any = {}
  dataEmployee: any[] = []
  dataType = this.coreService.convertObjToArray(enumData.BranchMember)
  modalTitle = 'Thêm nhân viên quản lý'
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditBranchMemberComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    if (this.data?.id) this.modalTitle = 'Sửa nhân viên quản lý'
    this.dataObject = { ...this.data }

    this.loadEmployee()
  }

  loadEmployee() {
    // branchId: this.data.id
    this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((result) => {
      this.dataEmployee = result
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (!this.dataObject.employeeId) {
      this.notifyService.showError('Vui lòng chọn nhân viên trước.')
      return
    }
    if (!this.dataObject.type) {
      this.notifyService.showError('Vui lòng chọn chức vụ cho nhân viên.')
      return
    }

    this.apiService.post(this.apiService.BRANCH_MEMBER.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
