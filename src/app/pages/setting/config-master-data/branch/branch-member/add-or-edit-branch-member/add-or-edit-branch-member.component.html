<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{modalTitle | uppercase}}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row>
      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Nhân viên</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select required nzShowSearch nzAllowClear [(ngModel)]="dataObject.employeeId" name="employeeId"
              nzPlaceHolder="Chọn nhân viên">
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Chức vụ</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select required nzShowSearch nzAllowClear [(ngModel)]="dataObject.type" name="type"
              nzPlaceHolder="Chọn chức vụ">
              <nz-option *ngFor="let item of dataType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>