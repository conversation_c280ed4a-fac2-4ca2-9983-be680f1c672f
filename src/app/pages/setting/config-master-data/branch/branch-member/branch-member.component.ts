import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { AddOrEditBranchMemberComponent } from './add-or-edit-branch-member/add-or-edit-branch-member.component'

@Component({ templateUrl: './branch-member.component.html' })
export class BranchMemberComponent implements OnInit {
  modalTitle = `Danh sách nhân viên quản lý chi nhánh`
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  listOfData: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  loading = true
  isDisplayAdd = true
  dataBranchLv1: any[] = []
  dataBranchLv2: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.modalTitle += ` ${this.data.name}`
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_003.code
    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = { branchId: this.data.id }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.BRANCH_MEMBER.PAGINATION, dataSearch).then((res) => {
      this.loading = false
      this.total = res[1]
      this.listOfData = res[0]
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditBranchMemberComponent, { disableClose: false, data: { branchId: this.data.id } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    if (!this.authenticationService.checkPermission(this.enumRole, this.action.Update.code)) return
    this.dialog
      .open(AddOrEditBranchMemberComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(object: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BRANCH_MEMBER.UPDATE_ACTIVE, { id: object.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }
}
