<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{modalTitle | uppercase}}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="12">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Cấp 1</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.parent1"
              (ngModelChange)="loadBranchLv2($event)" name="parent1" nzPlaceHolder="Chọn công ty thành viên cấp 1">
              <nz-option *ngFor="let item of dataBranchLv1" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Cấp 2</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.parent2" name="parent2"
              nzPlaceHolder="Chọn công ty thành viên cấp 2">
              <nz-option *ngFor="let item of dataBranchLv2" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Chọn loại</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.type" name="type" nzPlaceHolder="Chọn loại">
              <nz-option *ngFor="let item of dataType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Mã công ty thành viên</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Mã công ty thành viên (1-250 kí tự)!">
            <input nz-input [disabled]="isEditItem" placeholder="Nhập Mã công ty thành viên 1-250 kí tự"
              [(ngModel)]="dataObject.code" name="code" required pattern=".{1,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Tên công ty thành viên</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tên công ty thành viên (1-250 kí tự)!">
            <input nz-input placeholder="Nhập Tên công ty thành viên 1-250 kí tự" [(ngModel)]="dataObject.name"
              name="name" required pattern=".{1,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
          <nz-form-control nzSpan="24">
            <textarea nz-input rows="3" auto placeholder="Nhập mô tả" [(ngModel)]="dataObject.description"
              name="description">
            </textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>