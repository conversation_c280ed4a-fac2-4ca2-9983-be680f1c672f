import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({ templateUrl: './add-or-edit-branch.component.html' })
export class AddOrEditBranchComponent implements OnInit {
  modalTitle = `Thêm chi nhánh`
  dataObject: any = {}
  isEditItem = false
  dataBranchLv1: any[] = []
  dataBranchLv2: any[] = []
  dataType = this.coreService.convertObjToArray(enumData.BranchType)
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditBranchComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadBranchLv1()
    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.modalTitle = `Chỉnh sửa chi nhánh`
      this.isEditItem = true
      if (this.data.level > 1) {
        this.loadBranchLv2(this.dataObject.parent1)
      }
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.BRANCH.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.BRANCH.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  loadBranchLv1() {
    this.apiService.post(this.apiService.BRANCH.FIND, { level: 1 }).then((result) => {
      this.dataBranchLv1 = result
    })
  }

  loadBranchLv2(parentId: any) {
    this.dataBranchLv2 = []
    if (!parentId) return

    this.apiService.post(this.apiService.BRANCH.FIND, { level: 2, parentId }).then((result) => {
      this.dataBranchLv2 = result
    })
  }
}
