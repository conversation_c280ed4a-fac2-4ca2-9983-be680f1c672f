<nz-row nzGutter="8">
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear (ngModelChange)="loadBranchLv2($event)" [(ngModel)]="dataSearch.parent1"
      name="parent1" nzPlaceHolder="Chọn cấp 1">
      <nz-option *ngFor="let item of dataBranchLv1" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.parent2" name="parent2" nzPlaceHolder="Chọn cấp 2">
      <nz-option *ngFor="let item of dataBranchLv2" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm theo tên" />
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
      [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
      <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="24" class="mt-2 text-center">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row nzGutter="8" class="mt-2">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
      (click)="clickAdd()">
      <span nz-icon nzType="plus"></span>{{ language_key?.ADD || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>Cấp 1</th>
        <th>Cấp 2</th>
        <th>Mã công ty thành viên</th>
        <th>Tên công ty thành viên</th>
        <th>Mô tả</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25" (click)="clickEdit(data)">{{ data.branchLv1Code }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.branchLv2Code }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.name }}</td>
        <td class="mw-50" (click)="clickEdit(data)">{{ data.description }}</td>
        <td class="text-nowrap">

          <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-tooltip
            nzTooltipTitle="Danh sách quản lý công ty thành viên" nz-button class="ant-btn-warning mr-2"
            (click)="listMember(data)">
            <span nz-icon nzType="solution"></span>
          </button>

          <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip
            [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
            <span nz-icon nzType="stop"></span>
          </button>
          <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
            nz-button nzType="primary">
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>