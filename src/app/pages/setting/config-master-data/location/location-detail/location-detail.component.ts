import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({ templateUrl: './location-detail.component.html' })
export class LocationDetailComponent implements OnInit {
  modalTitle = 'CHI TIẾT VỊ TRÍ'
  dataObject: any = {}
  dataHistoryParent: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentStatus: any = {}
  enumProject: any
  action: any
  currentUser: any
  constructor(
    public authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<LocationDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.dataObject = this.data
    this.loadDetail()
  }

  async loadDetail() {
  
    this.dataObject.lstAssetDetail = this.dataObject.lstAssetDetail.filter((e: any) => e.assetCode == this.data.code)

    if (this.data.warehouseCode) {
      this.dataObject.lstAssetDetail = this.dataObject.lstAssetDetail.filter((e: any) => e.warehouseCode == this.data.warehouseCode)
    }
    this.dataObject.quantity = 0
    for (let e of this.dataObject.lstAssetDetail) {
      this.dataObject.quantity += e.quantity
    }
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
