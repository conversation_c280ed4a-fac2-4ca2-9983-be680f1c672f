import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { AddOrEditLocationComponent } from './add-or-edit-location/add-or-edit-location.component'
import { enumData } from 'src/app/core'
import { LocationDetailComponent } from './location-detail/location-detail.component'
import { User } from 'src/app/models'

@Component({
  templateUrl: './location.component.html',
})
export class LocationComponent implements OnInit {
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  listOfData: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  loading = false
  currentUser!: User
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_012.code

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    // this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    // this.apiService.post(this.apiService.WAREHOUSE.PAGINATION, dataSearch).then((data) => {
    //   if (data) {
    //     this.loading = false
    //     this.total = data[1]
    //     this.listOfData = data[0]
    //   }
    // })

    this.listOfData = [
      {
        code: 'NHAMAYLONGAN',
        name: 'Nhà máy Long An',
        employeeName: 'Hồng Hồng',
        isDeleted: 0,
        statusName: 'Đang hoạt động',
        statusColor: 'green'
      },
      {
        code: 'NHAMAYBINHTAN',
        name: 'Nhà máy Bình Tân',
        employeeName: 'Ân',
        isDeleted: 0,
        statusName: 'Đang hoạt động',
        statusColor: 'green'
      },
    ]
    this.total = this.listOfData.length
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditLocationComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    this.dialog
      .open(AddOrEditLocationComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    data.isDeleted = !data.isDeleted
    this.notifyService.showSuccess(`Cập nhật trạng thái hoạt động thành công`)
    this.searchData()
    // this.apiService.post(this.apiService.ASSET.DELETE, { id: data.id }).then((res) => {
    // })
  }

  clickDetail(data: any) {
    const dt = { ...data }
    if (this.dataSearch.warehouseCode) dt.warehouseCode = this.dataSearch.warehouseCode
    this.dialog
      .open(LocationDetailComponent, { disableClose: false, data: dt })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }
}
