import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({ templateUrl: './add-or-edit-location.component.html' })
export class AddOrEditLocationComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  modalTitle = 'Thêm mới vị trí'
  dataEmployee: any[]=[]
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditLocationComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataObject.lstAssetLevel2 = []
    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sửa vị trí'
    }
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.isDeleted = false
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
    this.closeDialog(true)
    // this.apiService.post(this.apiService.ASSET.CREATE, this.dataObject).then((result) => {
    //   if (result) {
    //     this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
    //     this.closeDialog(true)
    //   }
    // })
  }

  updateObject() {
    this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
    this.closeDialog(true)
    // this.apiService.post(this.apiService.ASSET.UPDATE, this.dataObject).then((result) => {
    //   if (result) {
    //     this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
    //     this.closeDialog(true)
    //   }
    // })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
