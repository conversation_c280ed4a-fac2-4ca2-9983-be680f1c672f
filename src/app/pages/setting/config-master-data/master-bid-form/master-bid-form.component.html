<nz-row nzGutter="8">
  <nz-col nzSpan="8">
    <input nz-input [(ngModel)]="dataSearch.name" placeholder="<PERSON><PERSON><PERSON> tên hình thức" />
  </nz-col>
  <nz-col nzSpan="8">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
      [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
      <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="8">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'T<PERSON><PERSON> kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
      (click)="clickAdd()">
      <span nz-icon nzType="plus"></span>{{ language_key?.ADD || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Mã hình thức</th>
        <th>Tên hình thức</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td (click)="clickEdit(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.name }}</td>
        <td>
          <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip
            [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
            <span nz-icon nzType="stop"></span>
          </button>
          <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
            nz-button nzType="primary">
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>