import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({ templateUrl: './add-or-edit-bid-form.component.html' })
export class AddOrEditBidFormModelComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  modalTitle = 'Thêm mới hình thức đấu thầu'

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditBidFormModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sừa hình thức đấu thầu'
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.description && this.dataObject.description.length > 250) {
      this.notifyService.showError('Vui lòng nhập mô tả không quá 250 kí tự!')
      return
    }

    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.BID_TYPE.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.BID_TYPE.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
