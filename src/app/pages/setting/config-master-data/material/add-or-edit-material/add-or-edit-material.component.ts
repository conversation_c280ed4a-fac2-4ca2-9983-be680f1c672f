import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'

@Component({
  selector: 'app-add-or-edit-material',
  templateUrl: './add-or-edit-material.component.html',
  styleUrls: ['./add-or-edit-material.component.scss'],
})
export class AddOrEditMaterialComponent implements OnInit {
  modalTitle = 'Thêm Mới Item'
  dataObject: any = {}
  isEditItem = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditMaterialComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.isEditItem = true
      this.modalTitle = 'Chỉnh Sửa Đối Tượng'
    }
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.isDeleted = false
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.MATERIAL.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.MATERIAL.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
