import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { NotifyService, CoreService, StorageService, ApiService } from 'src/app/services'
@Component({ templateUrl: './add-or-edit-warehouse.component.html' })
export class AddOrEditWarehouseComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  modalTitle = 'Thêm mới kho'
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditWarehouseComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}
  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sửa kho'
    }
  }
  onSave() {
    this.notifyService.showloading()
    this.dataObject.isDeleted = false
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }
  addObject() {
    this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
    this.closeDialog(true)
    this.apiService.post(this.apiService.WAREHOUSE.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }
  updateObject() {
    this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
    this.closeDialog(true)
    this.apiService.post(this.apiService.WAREHOUSE.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }
  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
