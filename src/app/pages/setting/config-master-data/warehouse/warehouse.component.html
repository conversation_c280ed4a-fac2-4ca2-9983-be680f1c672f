<nz-collapse>
  <nz-collapse-panel nzHeader="Tìm kiếm nâng cao" class="ant-bg-antiquewhite">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            Mã kho
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.code" name="code" placeholder="Nhập mã" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            Tên kho
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.name" name="name" placeholder="Nhập tên" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            Trạng thái hoạt động
          </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status"
              nzPlaceHolder="Chọn trạng thái">
              <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>
<nz-row class="mt-2">
  <nz-col nzSpan="24">
    <button nz-button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nzType="primary"
      (click)="clickAdd()">
      {{ language_key?.ADD || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>
<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Mã</th>
        <th>Tên</th>
        <th>Mô tả</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th>Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td (click)="clickEdit(data)">{{ data.code }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.name }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.description }}</td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.statusName }}</td>
        <td>
          <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip
            [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
            <span nz-icon nzType="stop"></span>
          </button>
          <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code) "
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
            nz-button nzType="primary">
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>