import { Component, OnInit, Inject, Optional } from '@angular/core'
import { enumData } from '../../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './change-password-employee.component.html' })
export class ChangePasswordEmployeeComponent implements OnInit {
  modalTitle = `Đ<PERSON>i mật khẩu`
  dataObject: any = {}
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private dialogRef: MatDialogRef<ChangePasswordEmployeeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.newPassword !== this.dataObject.confirmNewPassword) {
      this.notifyService.showError('Mật khẩu không khớp!')
      return
    }
    this.apiService.post(this.apiService.EMPLOYEE.UPDATE_PASSWORD, this.dataObject).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
