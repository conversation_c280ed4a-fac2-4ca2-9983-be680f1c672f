import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialog } from '@angular/material/dialog'
import { AddOrEditEmployeeComponent } from './add-or-edit-employee/add-or-edit-employee.component'
import { ChangePasswordEmployeeComponent } from './change-password-employee/change-password-employee.component'
import { User } from '../../../../models'
import * as XLSX from 'xlsx'
import * as fs from 'file-saver'
import { Workbook } from 'exceljs'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-employee',
  templateUrl: './employee.component.html',
})
export class EmployeeComponent implements OnInit {
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  listOfData: any[] = []
  dataUploadExcel: any
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  loading = true
  currentUser!: User
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_005.code

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.EMPLOYEE.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditEmployeeComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(data: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    this.dialog
      .open(AddOrEditEmployeeComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.EMPLOYEE.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  changePassword(data: any) {
    this.dialog.open(ChangePasswordEmployeeComponent, { disableClose: false, data })
  }

  //template excel
  clickDownloadTemplateExcel() {
    this.notifyService.showloading()
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách loại phiếu thu')
    //#region Body Table
    const header = [
      'Mã nhân viên*',
      'Họ & tên*',
      'Email*',
      'Tài khoản*',
      'Mật khẩu*',
      'Xác nhận mật khẩu*',
      'Chi nhánh *',
      'Phòng ban*',
      'Chỉ tiêu NCC*',
    ]

    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '001E3E' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' }, size: 13 }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 50
          break
        case 4:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 35
          worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
          break
      }
    })

    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `NHAN_VIEN_${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
  }

  clickImportExcel(event: any) {
    // this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null

    this.dataUploadExcel = []
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['code', 'name', 'email', 'username', 'password', 'confimPassword', 'branchCode', 'departmentCode'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file-emp')).value = ''
      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      const inputData: any = {}
      inputData.lstData = []
      for (const row of jsonData) {
        let idx = jsonData.indexOf(row) + 2

        if (row.code == null || (typeof row.code === 'string' && row.code.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã nhân viên không được để trống <br>'
        }
        if (row.name == null || (typeof row.name === 'string' && row.name.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tên nhân viên không được để trống <br>'
        }
        if (row.email == null || (typeof row.email === 'string' && row.email.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Email không được để trống <br>'
        }
        if (row.username == null || (typeof row.username === 'string' && row.username.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tài khoản không được để trống <br>'
        }
        if (row.password) row.password += ''
        if (!row.password) {
          strErr += 'Dòng ' + idx + ' - Mật khẩu không được để trống <br>'
        } else if (!this.coreService.isPass(row.password)) {
          strErr += 'Dòng ' + idx + ' - Mật khẩu phải ít nhất 4 kí tự <br>'
        }

        if (row.confimPassword) row.confimPassword += ''
        if (!row.confimPassword) {
          strErr += 'Dòng ' + idx + ' - Xác nhận mật khẩu không được để trống <br>'
        } else if (row.confimPassword !== row.password) {
          strErr += 'Dòng ' + idx + ' - Mật khẩu và xác nhận mật khẩu không giống nhau <br>'
        }

        if (row.departmentCode) row.departmentCode += ''
        if (!row.departmentCode) {
          strErr += 'Dòng ' + idx + ' - Phòng ban không được để trống <br>'
        }
        if (!this.coreService.isFreeText1_50(row.code)) {
          strErr += 'Dòng ' + idx + ' - Code không đúng định dạng <br>'
        }
        if (!this.coreService.isFreeText1_50(row.name)) {
          strErr += 'Dòng ' + idx + ' - Tên không đúng định dạng <br>'
        }
        if (!this.coreService.isFreeText1_50(row.username)) {
          strErr += 'Dòng ' + idx + ' - Tên đăng nhập không đúng định dạng <br>'
        }
        if (!this.coreService.validateEmail(row.email)) {
          strErr += 'Dòng ' + idx + ' - Email không đúng định dạng <br>'
        }
      }
      if (strErr.length > 0) {
        this.notifyService.hideloading()
        this.notifyService.showError(strErr)
        return
      }
      this.apiService.post(this.apiService.EMPLOYEE.CREATE_BY_EXCEL, jsonData).then((res: any) => {
        this.notifyService.showSuccess('Thêm nhân viên mới thành công')
        this.searchData()
        this.dataUploadExcel = []
      })
    }
  }
}
