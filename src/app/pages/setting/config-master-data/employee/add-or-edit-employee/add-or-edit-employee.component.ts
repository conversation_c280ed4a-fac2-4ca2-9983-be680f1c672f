import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { enumData } from '../../../../../core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-employee.component.html' })
export class AddOrEditEmployeeComponent implements OnInit {
  dataObject: any = {}
  modalTitle = `Thêm mới nhân viên`
  isEditItem = false
  dataDepartment: any[] = []
  dataBranch: any[] = []
  dataDepartmentSource: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditEmployeeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDepartment()
    this.loadBranch()
    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.dataObject.username = this.data.username
      this.isEditItem = true
      this.modalTitle = `Chỉnh sửa nhân viên`
    }
  }

  loadBranch() {
    this.apiService.post(this.apiService.BRANCH.FIND, {}).then((result) => {
      this.dataBranch = result
    })
  }

  onChangeBranch() {
    this.dataObject.departmentId = null
    if (this.dataObject.branchId) {
      this.dataDepartment = this.dataDepartmentSource.filter((s) => s.branchId === this.dataObject.branchId)
    } else {
      this.dataDepartment = this.dataDepartmentSource
    }
  }

  loadDepartment() {
    this.apiService.post(this.apiService.DEPARTMENT.FIND, {}).then((result) => {
      this.dataDepartment = result
      this.dataDepartmentSource = result
    })
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.isDeleted = false
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    if (this.dataObject.password !== this.dataObject.confimPassword) {
      this.notifyService.showError('Mật khẩu không khớp!')
      return
    }
    this.apiService.post(this.apiService.EMPLOYEE.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.EMPLOYEE.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
