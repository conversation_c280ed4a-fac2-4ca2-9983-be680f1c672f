<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Mã nhân viên</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mã nhân viên (1-50 kí tự)!">
            <input nz-input placeholder="Nhập mã 1-50 kí tự" [(ngModel)]="dataObject.code" [disabled]="isEditItem"
              name="code" required pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Họ & tên</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập họ & tên (1-50 kí tự)!">
            <input nz-input placeholder="Nhập 1-50 kí tự" [(ngModel)]="dataObject.name" name="name" required
              pattern=".{1,50}" autocomplete="off" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Email</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập email hợp lệ!">
            <input nz-input type="email" placeholder="Nhập email hợp lệ" [(ngModel)]="dataObject.email" name="email"
              pattern="^[A-Za-z0-9_.]{2,32}@([a-zA-Z0-9]{2,12})(.[a-zA-Z]{2,12})+$" required autocomplete="off" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Tài khoản</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tài khoản (1-50 kí tự)!">
            <input nz-input placeholder="Nhập 1-50 kí tự" [(ngModel)]="dataObject.username" [disabled]="isEditItem"
              name="username" required pattern=".{1,50}" autocomplete="off" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8" *ngIf="!data">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Mật khẩu</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mật khẩu ít nhất 4 kí tự!">
            <input type="password" nz-input placeholder="Nhập ít nhất 4 kí tự" [(ngModel)]="dataObject.password"
              name="password" required pattern=".{4,}" autocomplete="off" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8" *ngIf="!data">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Xác nhận mật khẩu</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập xác nhận mật khẩu ít nhất 4 kí tự!">
            <input type="password" nz-input placeholder="Nhập lại mật khẩu" [(ngModel)]="dataObject.confimPassword"
              name="confimPassword" required pattern=".{4,}" autocomplete="off" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">
            {{ language_key?.BRANCH || 'Chi nhánh' }}
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Chi nhánh!">
            <nz-select nzShowSearch nzAllowClear [nzPlaceHolder]="language_key?.BRANCH_ENTER || 'Chọn chi nhánh' "
              [(ngModel)]="dataObject.branchId" name="branchId" required (ngModelChange)="onChangeBranch()">
              <nz-option *ngFor=" let item of dataBranch" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Phòng ban</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn phòng ban!">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn phòng ban" [(ngModel)]="dataObject.departmentId"
              name="departmentId" required [disabled]="!dataObject.branchId" style="font-weight: bold;">
              <nz-option *ngFor="let item of dataDepartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">

      <button (click)="closeDialog(1)"  nz-button nzDanger>
        <span nz-icon nzType="close"></span> Đóng
      </button>

      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>