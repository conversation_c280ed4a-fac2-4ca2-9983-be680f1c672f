import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialog } from '@angular/material/dialog'
import { User } from '../../../../models'
import { AddOrEditFaqComponent } from './add-or-edit-faq/add-or-edit-faq.component'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-faq',
  templateUrl: './faq.component.html',
})
export class FaqComponent implements OnInit {
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  listOfData: any[] = []
  dataCategory: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  loading = true
  currentUser!: User
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_020.code
    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.loadCategory()
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const where: any = {}
    if (this.dataSearch.title && this.dataSearch.title !== '') {
      where.title = this.dataSearch.title
    }
    if (this.dataSearch.categoryId && this.dataSearch.categoryId !== '') {
      where.categoryId = this.dataSearch.categoryId
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.FAQ.PAGINATION, dataSearch).then((data) => {
      this.loading = false
      this.total = data[1]
      this.listOfData = data[0]
    })
  }

  loadCategory() {
    this.apiService.post(this.apiService.FAQ_CATEGORY.FIND, {}).then((res) => {
      this.dataCategory = res
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditFaqComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    this.dialog
      .open(AddOrEditFaqComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(object: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.FAQ.DELETE, { id: object.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }
}
