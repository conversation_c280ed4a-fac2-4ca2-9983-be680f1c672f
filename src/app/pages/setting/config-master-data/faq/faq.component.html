<nz-row nzGutter="8">
  <nz-col nzSpan="6">
    <input nz-input [(ngModel)]="dataSearch.title" placeholder="Tìm theo tiêu đề" />
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.categoryId" name="categoryId"
      nzPlaceHolder="Chọn danh mục">
      <nz-option *ngFor="let item of dataCategory" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
      [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
      <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
      (click)="clickAdd()">
      <span nz-icon nzType="plus"></span>{{ language_key?.ADD || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-1">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>Tiêu đề</th>
        <th>Nội dung</th>
        <th>Thuộc danh mục</th>
        <th>{{ language_key?.STATUS || 'Trạng thái' }}</th>
        <th>Thao tác</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25" (click)="clickEdit(data)">{{ data.title }}</td>
        <td class="mw-25" (click)="clickEdit(data)">
          <pre style="white-space: pre-wrap;">{{ data.description }}</pre>
        </td>
        <td class="mw-25" (click)="clickEdit(data)">{{ data.categoryName }}</td>
        <td class="mw-25" (click)="clickEdit(data)">
          <nz-tag nzColor="#f50" *ngIf="data.isDeleted">Không hoạt động</nz-tag>
          <nz-tag nzColor="#87d068" *ngIf="!data.isDeleted">Đang hoạt động</nz-tag>
        </td>
        <td>
          <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip
            [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
            <span nz-icon nzType="stop"></span>
          </button>
          <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
            nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
            nz-button nzType="primary">
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>