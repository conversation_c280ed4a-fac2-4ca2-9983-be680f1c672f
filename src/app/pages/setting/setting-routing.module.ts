import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { AddressComponent } from './config-master-data/address/address.component'
import { BillLookupComponent } from './config-master-data/bill-lookup/bill-lookup.component'
import { BranchComponent } from './config-master-data/branch/branch.component'
import { ClientComponent } from './config-master-data/client/client.component'
import { CompanyComponent } from './config-master-data/company/company.component'
import { CurrencyComponent } from './config-master-data/currency/currency.component'
import { DepartmentComponent } from './config-master-data/department/department.component'
import { EmailTemplateComponent } from './config-master-data/email-template/email-template.component'
import { EmployeeComponent } from './config-master-data/employee/employee.component'
import { FaqCategoryComponent } from './config-master-data/faq-category/faq-category.component'
import { FaqComponent } from './config-master-data/faq/faq.component'
import { LocationComponent } from './config-master-data/location/location.component'
import { MasterBidFormComponent } from './config-master-data/master-bid-form/master-bid-form.component'
import { MasterBidGuaranteeFormComponent } from './config-master-data/master-bid-guarantee-form/master-bid-guarantee-form.component'
import { MaterialComponent } from './config-master-data/material/material.component'
import { ObjectComponent } from './config-master-data/object/object.component'
import { ProductComponent } from './config-master-data/product/product.component'
import { ServiceComponent } from './config-master-data/service/service.component'
import { UnitComponent } from './config-master-data/unit/unit.component'
import { WarehouseComponent } from './config-master-data/warehouse/warehouse.component'
import { LanguageKeyComponent } from './language-key/language-key.component'
import { PermissionComponent } from './permission/permission.component'
import { ServiceManageComponent } from './service-manage/service-manage.component'
import { SupplierManageDeleteComponent } from './supplier-manage-delete/supplier-manage-delete.component'
import { CriteriaComponent } from './criteria/criteria.component'

const routes: Routes = [
  { path: 'service-manage', component: ServiceManageComponent },
  { path: 'material', component: MaterialComponent },

  { path: 'department', component: DepartmentComponent },
  { path: 'employee', component: EmployeeComponent },
  { path: 'address', component: AddressComponent },
  { path: 'company', component: CompanyComponent },
  { path: 'master-bid-form', component: MasterBidFormComponent },
  { path: 'master-bid-guarantee-form', component: MasterBidGuaranteeFormComponent },
  { path: 'client', component: ClientComponent },
  { path: 'unit', component: UnitComponent },
  { path: 'currency', component: CurrencyComponent },
  { path: 'email-template', component: EmailTemplateComponent },
  { path: 'faq-category', component: FaqCategoryComponent },
  { path: 'faq', component: FaqComponent },
  { path: 'supplier-manage-delete', component: SupplierManageDeleteComponent }, // không sửa route do link là bí mật
  { path: 'object', component: ObjectComponent },
  { path: 'warehouse', component: WarehouseComponent },
  { path: 'branch', component: BranchComponent },
  { path: 'permission', component: PermissionComponent },
  { path: 'language-key', component: LanguageKeyComponent },
  { path: 'location', component: LocationComponent },
  { path: 'service', component: ServiceComponent },
  { path: 'product', component: ProductComponent },
  { path: 'bill-lookup', component: BillLookupComponent },
  { path: 'criteria', component: CriteriaComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingRoutingModule {}
