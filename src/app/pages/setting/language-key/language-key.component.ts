import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core/enumData'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { NotifyService, CoreService, StorageService, ApiService } from '../../../services'
import * as moment from 'moment'
@Component({
  selector: 'app-language-key',
  templateUrl: './language-key.component.html',
  styleUrls: ['./language-key.component.scss']
})
export class LanguageKeyComponent implements OnInit {
  listOfData: any[] = []
  loading = true
  dataSearch: any = {}
  dataLanguageType = this.coreService.convertObjToArray(enumData.LangugeType)
  listOfDataFilter: any[] = []
  language_key: any = {}
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    this.getLanguage()
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.getLanguage()
    })
  }

  getLanguage() {
    this.language_key = this.coreService.getLanguage()
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }

  loadData() {
    this.loading = true
    this.listOfData = []
    if (!this.dataSearch.languageType) return
    this.apiService.post(this.apiService.LANGUAGE_KEY.LOAD_DATA_BY_TENANT, { languageType: this.dataSearch.languageType }).then((res: any) => {
      this.loading = false
      this.listOfData = res || []
      this.filterData()
    })
  }

  filterData() {
    this.listOfDataFilter = this.listOfData
    if (this.dataSearch.value) this.listOfDataFilter = this.listOfDataFilter.filter((c: any) => c.value.includes(this.dataSearch.value))
    if (this.dataSearch.key) this.listOfDataFilter = this.listOfDataFilter.filter((c: any) => c.key.includes(this.dataSearch.key))
    if (this.dataSearch.path) this.listOfDataFilter = this.listOfDataFilter.filter((c: any) => c.path.includes(this.dataSearch.path))
  }

  saveData() {
    this.notifyService.showloading()
    if (!this.dataSearch.languageType) {
      this.notifyService.showError('Vui lòng chọn loại ngôn ngữ trước!')
      return
    }
    const lstLanguage = this.listOfData.filter((c) => c.isEdit)
    if (lstLanguage.length == 0) {
      this.notifyService.showError('Vui lòng sửa ít nhất 1 dòng dữ liệu!')
      return
    }

    this.apiService.post(this.apiService.LANGUAGE_KEY.UPDATE, { languageType: this.dataSearch.languageType, items: lstLanguage }).then((res: any) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      for (const item of lstLanguage) delete item.isEdit
    })
  }

  refreshData() {
    this.notifyService.showloading()
    if (!this.dataSearch.languageType) {
      this.notifyService.showError('Vui lòng chọn loại ngôn ngữ trước!')
      return
    }
    this.apiService.post(this.apiService.LANGUAGE_KEY.REFRESH_DATA, { languageType: this.dataSearch.languageType }).then((res: any) => {
      if (res) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.loadData()
      }
    })
  }

  lstHeaderKey = ['STT', 'Loại ngôn ngữ', 'Mã', 'Giá trị', 'Đường dẫn', 'Ghi chú']
  lstDataKey = ['sort', 'languageType', 'key', 'value', 'path', 'description']
  onDownload() {
    if (!this.dataSearch.languageType) return
    const lstFieldName = this.lstHeaderKey
    const lstFieldCode = this.lstDataKey
    const columnFmt: any = {}

    const configExport: any = { lstFieldCode, lstFieldName }
    configExport.url = this.apiService.LANGUAGE_KEY.LOAD_DATA
    configExport.input = { languageType: this.dataSearch.languageType }

    let lstDataExport = []
    const header1: any = {
      sort: 'STT',
      languageType: 'Loại ngôn ngữ',
      key: 'Mã',
      value: 'Giá trị',
      path: 'Đường dẫn',
      description: 'Ghi chú',
    }
    const header2: any = {
      sort: 'sort',
      languageType: 'languageType',
      key: 'key',
      value: 'value',
      path: 'path',
      description: 'description',
    }
    lstDataExport.push(header1, header2)

    for (const item of this.listOfDataFilter) {
      const row: any = {}
      row.sort = item.sort > 0 ? item.sort : ''
      row.languageType = item.languageType
      row.key = item.key
      row.value = item.value
      row.path = item.path
      row.description = item.description
      lstDataExport.push(row)
    }

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const dateStr = moment(new Date()).format('YYYY-MM-DD')
    const fileName = `[${dateStr}] Template cấu hình ngôn ngữ.xlsx`
    const sheetName = 'Ngôn ngữ'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    wb.Sheets[sheetName]['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 5 } } /* A1:F1 */]

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  /** Áp dụng cho trường ghi chú, địa chỉ */
  isFreeText0_500(text: string) {
    const regularExpression = /^[\s\S]{0,500}$/
    return regularExpression.test(String(text || '').toLowerCase())
  }

  /** Áp dụng cho trường giá trị trong ngôn ngữ */
  isFreeText1_1000(text: string) {
    const regularExpression = /^.{1,1000}$/
    return regularExpression.test(String(text || '').toLowerCase())
  }

  onImport(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: this.lstDataKey,
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng header
      const header = jsonData.shift()
      if (header) {
        const arrayHeader = Object.values(header)
        if (JSON.stringify(this.lstHeaderKey) !== JSON.stringify(arrayHeader)) {
          this.notifyService.showError('Template không đúng định dạng. Vui lòng kiểm tra lại!')
          return
        }
      } else {
        this.notifyService.showError('Template không đúng định dạng. Vui lòng kiểm tra lại!')
        return
      }
      // bỏ dòng mã header
      jsonData.shift()

      let strErr = ''
      for (const row of jsonData) {
        let idx = jsonData.indexOf(row) + 3

        if (row.languageType) row.languageType = (row.languageType + '').trim()
        if (!row.languageType) {
          strErr += `[ Dòng ${idx} - Loại Ngôn Ngữ không được để trống ]<br>`
        }

        if (row.key) row.key = (row.key + '').trim()
        if (!row.key) {
          strErr += `[ Dòng ${idx} - Mã ngôn ngữ không được để trống ]<br>`
        }

        if (row.value) row.value = (row.value + '').trim()
        if (!this.isFreeText1_1000(row.value)) {
          strErr += `[ Dòng ${idx} - Giá trị - Vui lòng nhập nội dung có chiều dài từ 1-1000 ký tự ]<br>`
        }

        if (row.description) {
          row.description = (row.description + '').trim()
          if (!this.isFreeText0_500(row.description)) {
            strErr += `[ Dòng ${idx} - Ghi chú - Vui lòng nhập nội dung có chiều dài từ 0-500 ký tự ]<br>`
          }
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      if (jsonData.length === 0) {
        this.notifyService.showError('Vui lòng thêm ít nhất 1 dòng dữ liệu !')
        return
      }

      this.apiService.post(this.apiService.LANGUAGE_KEY.IMPORT, { languageType: this.dataSearch.languageType, items: jsonData }).then((res: any) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.loadData()
      })
    }
  }
}