<nz-row nzGutter="8" class="mt-2">
  <nz-col nzSpan="8">
    <nz-select
      nzShowSearch
      [nzFilterOption]="coreService.filterOption"
      nzAllowClear
      [(ngModel)]="dataSearch.languageType"
      name="languageType"
      [nzPlaceHolder]="'Chọn Lo<PERSON> ngôn ng<PERSON>'"
      (ngModelChange)="loadData()"
    >
      <nz-option *ngFor="let item of dataLanguageType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="16" *ngIf="dataSearch.languageType">
    <button nz-button nzType="primary" class="mr-2" (click)="saveData()"><span nz-icon nzType="save"></span> Lưu</button>

    <button nz-button nzType="primary" class="mr-2" (click)="onDownload()"><span nz-icon nzType="download"></span> Tải excel</button>

    <input
      class="hidden"
      type="file"
      id="file"
      (change)="onImport($event)"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <label nz-button for="file" class="ant-btn lable-custom-file"> <span nz-icon nzType="upload"></span> Nhập excel </label>
  </nz-col>
</nz-row>

<nz-row class="mt-2" *ngIf="dataSearch.languageType">
  <nz-table nz-col nzSpan="24" nzTemplateMode [nzLoading]="loading" nzBordered [nzFrontPagination]="false">
    <thead>
      <tr>
        <th nzWidth="50px">STT</th>
        <th>Loại ngôn ngữ</th>
        <th>
          Mã
          <input nz-input [(ngModel)]="dataSearch.key" (ngModelChange)="filterData()" name="key" />
        </th>
        <th>
          Giá Trị
          <input nz-input [(ngModel)]="dataSearch.value" (ngModelChange)="filterData()" name="value" />
        </th>
        <th>
          Đường dẫn
          <input nz-input [(ngModel)]="dataSearch.path" (ngModelChange)="filterData()" name="path" />
        </th>
        <th>Ghi chú</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfDataFilter">
        <td class="text-center">{{ data.sort }}</td>
        <td>{{ data.typeName }}</td>
        <td>{{ data.key }}</td>
        <td>
          <input nz-input [placeholder]="'Nhập Giá Trị'" [(ngModel)]="data.value" (ngModelChange)="data.isEdit = true" [name]="data.key + '_value'" />
        </td>
        <td>{{ data.path }}</td>
        <td>
          <input
            nz-input
            [placeholder]="'Nhập Ghi chú'"
            [(ngModel)]="data.description"
            (ngModelChange)="data.isEdit = true"
            [name]="data.key + '_description'"
          />
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>
