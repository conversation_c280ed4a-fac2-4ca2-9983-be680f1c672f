<nz-row nzGutter="8">
  <nz-col nzSpan="12">
    <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm kiếm theo mã hoặc tên lĩnh vực mua hàng" />
  </nz-col>
  <nz-col nzSpan="12">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <button
    *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
    class="ant-btn-blue mr-2"
    nz-button
    (click)="settingTemplate(0)"
  >
    <span nz-icon nzType="upload"></span>Excel năng lực
  </button>
  <button
    *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
    class="ant-btn-blue mr-2"
    nz-button
    (click)="settingTemplate(1)"
  >
    <span nz-icon nzType="upload"></span>Excel kỹ thuật
  </button>
  <button
    *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
    class="ant-btn-blue mr-2"
    nz-button
    (click)="settingTemplate(2)"
  >
    <span nz-icon nzType="upload"></span>Excel chào giá
  </button>
  <button
    *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
    class="ant-btn-blue mr-2"
    nz-button
    (click)="settingTemplate(3)"
  >
    <span nz-icon nzType="upload"></span>Excel cơ cấu giá
  </button>
  <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="ant-btn-blue" nz-button (click)="settingTemplate(4)">
    <span nz-icon nzType="upload"></span>Excel ĐKTM
  </button>
</nz-row>

<nz-row class="mt-2">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
  >
    <thead>
      <tr>
        <th nzWidth="300px">Lĩnh vực mua hàng</th>
        <th>Trạng thái</th>
        <th>Thông tin năng lực</th>
        <th>Yêu cầu kỹ thuật</th>
        <th>Bảng chào giá</th>
        <th>Cơ cấu giá</th>
        <th>Điều kiện thương mại</th>
        <th>Thiết lập tỉ trọng</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td nzBreakWord>{{ data.code }} - {{ data.name }}</td>
        <td class="text-nowrap">{{ data.statusCapacityName }}</td>
        <td>
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
            nz-tooltip
            nzTooltipTitle="Thông tin năng lực"
            nz-button
            class="ant-btn-warning"
            (click)="showModalCapacityRequest(data)"
          >
            <span nz-icon nzType="solution"></span>
          </button>
        </td>
        <td>
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
            nz-tooltip
            nzTooltipTitle="Yêu cầu kỹ thuật"
            nz-button
            nzType="primary"
            (click)="showModalTechRequest(data)"
          >
            <span nz-icon nzType="robot"></span>
          </button>
        </td>
        <td>
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
            nz-tooltip
            nzTooltipTitle="Bảng chào giá"
            nz-button
            class="ant-btn-blue"
            (click)="showModalServicePrice(data)"
          >
            <span nz-icon nzType="dollar"></span>
          </button>
        </td>
        <td>
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
            nz-tooltip
            nzTooltipTitle="Cơ cấu giá"
            nz-button
            class="ant-btn-orange"
            (click)="showModalServiceCustomPrice(data)"
          >
            <span nz-icon nzType="setting"></span>
          </button>
        </td>
        <td>
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
            nz-tooltip
            nzTooltipTitle="Điều kiện thương mại"
            nz-button
            class="ant-btn-success"
            (click)="showModalTradeCondition(data)"
          >
            <span nz-icon nzType="shopping"></span>
          </button>
        </td>
        <td>
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
            nz-tooltip
            nzTooltipTitle="Thiết lập tỉ trọng"
            nz-button
            nzType="dashed"
            (click)="showModalSettingRate(data)"
          >
            <span nz-icon nzType="percentage"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-row>

<nz-modal
  [(nzVisible)]="isShowSettingTemplate"
  [nzWidth]="'60vw'"
  [nzTitle]="modalTitle"
  (nzOnCancel)="isShowSettingTemplate = false"
  [nzFooter]="null"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-col nzSpan="24">
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button (click)="clickExportExcel()" class="mr-2">
          <span nz-icon nzType="download"></span>Xuất excel
        </button>
        <input
          *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
          class="hidden"
          type="file"
          id="file"
          (change)="clickImportExcel($event)"
          onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file" class="lable-custom-file">
          <span nz-icon nzType="upload"></span>
          {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
        </label>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="listAllData" [nzPageSize]="20" [nzScroll]="{ y: '400px' }" nzBordered>
        <thead>
          <tr>
            <th nzWidth="70px">Chọn</th>
            <th nzWidth="100px">{{ language_key?.NO || 'STT' }}</th>
            <th>Lĩnh vực kinh doanh</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data1 of listAllData; let i = index">
            <td><label nz-checkbox [(ngModel)]="data1.isChoose"></label></td>
            <td>{{ i + 1 }}</td>
            <td>{{ data1.code + ' - ' + data1.name }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>
