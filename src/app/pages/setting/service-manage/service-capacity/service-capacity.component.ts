import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { ServiceCapacityListDetailComponent } from './service-capacity-list-detail/service-capacity-list-detail.component'
import { AddOrEditServiceCapacityComponent } from './add-or-edit-service-capacity/add-or-edit-service-capacity.component'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
@Component({ templateUrl: './service-capacity.component.html' })
export class ServiceCapacityComponent implements OnInit {
  modalTitle = 'Thông tin năng lực'
  loading = false
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  sumPercent = 0
  listOfData: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  currentUser: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<ServiceCapacityComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_001.code
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = { serviceId: this.data.id }

    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        for (const item of this.listOfData) {
          // Lọc trạng thái cho lv2
          if (this.dataSearch.statusId > 0) {
            if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) {
              item.__childs__ = item.__childs__.filter((c: any) => c.isDeleted === false)
              item.__serviceCapacityListDetails__ = item.__serviceCapacityListDetails__.filter((c: any) => c.isDeleted === false)

              for (const child of item.__childs__) {
                child.__serviceCapacityListDetails__ = child.__serviceCapacityListDetails__.filter((c: any) => c.isDeleted === false)
              }
            }
            if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) {
              item.__childs__ = item.__childs__.filter((c: any) => c.isDeleted === true)
              item.__serviceCapacityListDetails__ = item.__serviceCapacityListDetails__.filter((c: any) => c.isDeleted === true)

              for (const child of item.__childs__) {
                child.__serviceCapacityListDetails__ = child.__serviceCapacityListDetails__.filter((c: any) => c.isDeleted === true)
              }
            }
          }
          // Tính tổng %
          if (
            item.__childs__.length > 0 &&
            item.__childs__.filter((c: any) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code).length > 0
          ) {
            item.sumPercent = 0
            for (const child of item.__childs__) {
              if (child.percent > 0) {
                item.sumPercent += child.percent
              }
            }
          }
        }
        this.getSumPercent()
        const config = data[2]
        this.data.isShowApproveCapacity = config?.isShowApproveCapacity
        this.data.isShowSendApproveCapacity = config?.isShowSendApproveCapacity
      }
    })
  }

  clickAdd() {
    const item = {
      name: '',
      type: enumData.DataType.String.code,
      percent: 0,
      isRequired: true,
      isCalUp: true,
      isChangeByYear: false,
      serviceId: this.data.id,
    }
    this.dialog
      .open(AddOrEditServiceCapacityComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    this.dialog
      .open(AddOrEditServiceCapacityComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  settingList(id: string) {
    this.dialog
      .open(ServiceCapacityListDetailComponent, { disableClose: false, data: id })
      .afterClosed()
      .subscribe(() => this.searchData())
  }

  getSumPercent() {
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_SUMPERCENT, { serviceId: this.data.id }).then((data) => {
      this.sumPercent = data
    })
  }

  clickDeleteAllRow() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_DELETEALL, { serviceId: this.data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  clickExportExcel() {
    this.notifyService.showloading()
    const where: any = { serviceId: this.data.id, isDeleted: false }

    const dataSearch: any = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_PAGINATION, dataSearch).then((res) => {
      const lstData = res[0]
      let lstDataExport: any[] = []

      //#region header
      const title: any = {
        zenId: 'CẤU HÌNH TEMPLATE',
        sort: '',
        name: '',
        percent: '',
        percentRule: '',
        type: '',
        isRequired: '',
        isChangeByYear: '',
        isCalUp: '',
        percentDownRule: '',
        blank: '',
        zenListId: 'CẤU HÌNH CÁC TRƯỜNG DANH SÁCH',
        nameList: '',
        valueList: '',
      }
      lstDataExport.push(title)

      const numColTable1 = 10
      const header: any = {
        zenId: 'Cột',
        sort: this.language_key?.NO || 'STT',
        name: 'Tên tiêu chí',
        percent: 'Tỉ trọng (%)',
        percentRule: 'Giá trị đạt',
        type: 'Kiểu dữ liệu',
        isRequired: 'Bắt buộc?',
        isChangeByYear: 'Có bổ sung theo năm?',
        isCalUp: 'Tính điểm thuận?',
        percentDownRule: 'Điều kiện liệt tỉ trọng',
        blank: '',
        zenListId: 'Cột',
        nameList: 'Tên',
        valueList: '% Tỉ trọng',
      }
      lstDataExport.push(header)
      //#endregion

      //#region custom data before export
      let i1 = 1
      const lstDataTable1: any[] = []
      const lstDataTable2: any[] = []
      const lstDataLv1 = lstData || []
      for (const data1 of lstDataLv1) {
        const dataTable1Lv1: any = {}
        dataTable1Lv1.zenId = '' + i1
        i1++
        dataTable1Lv1.sort = data1.sort > 0 ? data1.sort : ''
        dataTable1Lv1.name = data1.name
        dataTable1Lv1.percent = data1.percent
        dataTable1Lv1.percentRule = data1.percentRule
        dataTable1Lv1.type = data1.type
        dataTable1Lv1.isRequired = data1.isRequired
        dataTable1Lv1.isChangeByYear = data1.isChangeByYear
        dataTable1Lv1.isCalUp = data1.isCalUp
        dataTable1Lv1.percentDownRule = data1.percentDownRule

        lstDataTable1.push(dataTable1Lv1)

        if (data1.__serviceCapacityListDetails__ && data1.__serviceCapacityListDetails__.length > 0) {
          for (const detail of data1.__serviceCapacityListDetails__) {
            const dataTable2Lv1: any = {}
            dataTable2Lv1.zenListId = dataTable1Lv1.zenId
            dataTable2Lv1.nameList = detail.name
            dataTable2Lv1.valueList = detail.value
            lstDataTable2.push(dataTable2Lv1)
          }
        }

        let i2 = 1
        const lstDataLv2 = data1.__childs__ || []
        for (const data2 of lstDataLv2) {
          const dataTable1Lv2: any = {}
          dataTable1Lv2.zenId = dataTable1Lv1.zenId + '.' + i2
          i2++
          dataTable1Lv2.sort = data2.sort > 0 ? data2.sort : ''
          dataTable1Lv2.name = data2.name
          dataTable1Lv2.percent = data2.percent
          dataTable1Lv2.percentRule = data2.percentRule
          dataTable1Lv2.type = data2.type
          dataTable1Lv2.isRequired = data2.isRequired
          dataTable1Lv2.isChangeByYear = data2.isChangeByYear
          dataTable1Lv2.isCalUp = data2.isCalUp
          dataTable1Lv2.percentDownRule = data2.percentDownRule

          lstDataTable1.push(dataTable1Lv2)

          if (data2.__serviceCapacityListDetails__ && data2.__serviceCapacityListDetails__.length > 0) {
            for (const detail of data2.__serviceCapacityListDetails__) {
              const dataTable2Lv2: any = {}
              dataTable2Lv2.zenListId = dataTable1Lv2.zenId
              dataTable2Lv2.nameList = detail.name
              dataTable2Lv2.valueList = detail.value
              lstDataTable2.push(dataTable2Lv2)
            }
          }
        }
      }

      //#endregion

      let numRowData = lstDataTable1.length > lstDataTable2.length ? lstDataTable1.length : lstDataTable2.length
      for (let i = 0; i < numRowData; i++) {
        const dataTable1 = lstDataTable1[i] || {}
        const dataTable2 = lstDataTable2[i] || {}
        lstDataExport.push({ ...dataTable1, ...dataTable2 })
      }
      var ws = XLSX.utils.json_to_sheet(lstDataExport, {
        skipHeader: true,
      })
      var wb = XLSX.utils.book_new()
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template cấu hình thông tin năng lực của dịch vụ ${this.data.code}.xlsx`
      const sheetName = 'Năng lực'
      XLSX.utils.book_append_sheet(wb, ws, sheetName)
      wb.Sheets[sheetName]['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: numColTable1 - 1 } } /* A1:J1 */,
        { s: { r: 0, c: numColTable1 + 1 }, e: { r: 0, c: numColTable1 + 3 } } /* L1:N1 */,
      ]

      XLSX.writeFile(wb, fileName)

      setTimeout(() => {
        this.notifyService.hideloading()
      }, 100)
    })
  }

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'sort',
          'name',
          'percent',
          'percentRule',
          'type',
          'isRequired',
          'isChangeByYear',
          'isCalUp',
          'percentDownRule',
          'blank',
          'zenListId',
          'nameList',
          'valueList',
        ],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()

      // Kiểm tra header
      if (
        header.zenId !== 'Cột' ||
        header.sort !== (this.language_key?.NO || 'STT') ||
        header.name !== 'Tên tiêu chí' ||
        header.percent !== 'Tỉ trọng (%)' ||
        header.percentRule !== 'Giá trị đạt' ||
        header.type !== 'Kiểu dữ liệu' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.isChangeByYear !== 'Có bổ sung theo năm?' ||
        header.isCalUp !== 'Tính điểm thuận?' ||
        header.percentDownRule !== 'Điều kiện liệt tỉ trọng' ||
        header.zenListId !== 'Cột' ||
        header.nameList !== 'Tên' ||
        header.valueList !== '% Tỉ trọng'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError('File không đúng template thông tin năng lực LVMH ' + this.data.name)
        return
      }

      // Tách và kiểm tra data từng bảng
      const lstDataTable1: any[] = []
      const lstDataTable2: any[] = []
      let strErr = ''
      for (const row of jsonData) {
        // add data table 1
        if (row.zenId != null && row.zenId != '') {
          const dataTable1: any = {}
          dataTable1.zenId = (row.zenId + '').trim()
          dataTable1.sort = row.sort || 0
          dataTable1.name = (row.name || '') + ''
          dataTable1.type = (row.type || '') + ''

          dataTable1.isRequired = row.isRequired
          if (dataTable1.isRequired == null || dataTable1.isRequired === '' || typeof dataTable1.isRequired !== 'boolean') {
            dataTable1.isRequired = false
          }

          dataTable1.isChangeByYear = row.isChangeByYear
          if (dataTable1.isChangeByYear == null || dataTable1.isChangeByYear === '' || typeof dataTable1.isChangeByYear !== 'boolean') {
            dataTable1.isChangeByYear = false
          }
          if (dataTable1.isChangeByYear && dataTable1.percent > 0) {
            strErr += `Cột [${dataTable1.zenId}] thay đổi theo năm, không được thiết lập "Tỉ trọng (%)"<br>`
          }

          dataTable1.isCalUp = row.isCalUp
          if (dataTable1.isCalUp == null || dataTable1.isCalUp === '' || typeof dataTable1.isCalUp !== 'boolean') {
            dataTable1.isCalUp = false
          }
          if (!dataTable1.isCalUp) {
            dataTable1.percentDownRule = row.percentDownRule || 0
          } else if (dataTable1.percentDownRule > 0) {
            strErr += `Cột [${dataTable1.zenId}] tính điểm thuận, không được thiết lập "Điều kiện liệt tỉ trọng"<br>`
          } else dataTable1.percentDownRule = null

          if (dataTable1.type === enumData.DataType.Number.code || dataTable1.type === enumData.DataType.List.code) {
            dataTable1.percent = row.percent || 0
            if (dataTable1.type === enumData.DataType.Number.code) {
              dataTable1.percentRule = row.percentRule
            } else dataTable1.percentRule = null
          } else if (dataTable1.percent > 0) {
            strErr += `Cột [${dataTable1.zenId}] là kiểu [${dataTable1.type}], không được thiết lập "Tỉ trọng (%)"<br>`
          } else if (dataTable1.percentRule > 0) {
            strErr += `Cột [${dataTable1.zenId}] là kiểu [${dataTable1.type}], không được thiết lập "Điều kiện liệt tỉ trọng"<br>`
          } else {
            dataTable1.percent = null
            dataTable1.percentRule = null
          }

          const lstId = dataTable1.zenId.split('.')
          dataTable1.level = lstId.length
          if (dataTable1.level < 1 || dataTable1.level > 2) {
            strErr += `Cột [${dataTable1.zenId}] không hợp lệ, không xác định được level nào<br>`
          }
          for (const id of lstId) {
            try {
              let intId = parseInt(id)
              if (intId <= 0) {
                strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số dương<br>`
              }
            } catch {
              strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số<br>`
            }
          }
          if (dataTable1.level == 2) {
            dataTable1.parentZenId = lstId[0] + ''
            if (!lstDataTable1.some((c) => c.zenId == dataTable1.parentZenId)) {
              strErr += `Không tìm thấy cấp cha của cột [${dataTable1.zenId}] ở phía trên của dòng này<br>`
            }
          }

          if (dataTable1.name.trim() === '') {
            strErr += 'Tên tiêu chí không được để trống<br>'
          }

          if (dataTable1.type.trim() === '') {
            strErr += 'Kiểu dữ liệu không được để trống<br>'
          }
          if (dataTable1.type.trim().length > 0) {
            if (!this.lstDataType.some((c) => c.code === dataTable1.type)) {
              strErr += `Kiểu dữ liệu [${dataTable1.type}] không tồn tại trong [String, Number, List, File, Date]<br>`
            }
          }

          if (dataTable1.percentRule !== null && dataTable1.percentRule <= 0) {
            strErr += 'Giá trị đạt phải là số lớn hơn 0<br>'
          }

          if (dataTable1.percentDownRule !== null && dataTable1.percentDownRule <= 0) {
            strErr += 'Điều kiện liệt tỉ trọng phải là số lớn hơn 0<br>'
          }

          lstDataTable1.push(dataTable1)
        }
      }
      // check percent
      if (lstDataTable1.length > 0) {
        let sumPercent = 0
        const lstDataLv1 = lstDataTable1.filter((c) => c.level == 1)
        for (const dataLv1 of lstDataLv1) {
          if (dataLv1.percent > 0) sumPercent += dataLv1.percent
          var lstDataLv2 = lstDataTable1.filter((c) => c.level == 2 && c.parentZenId == dataLv1.zenId)
          if (lstDataLv2.length > 0) {
            let sumPercentLv2 = 0
            for (const dataLv2 of lstDataLv2) {
              if (dataLv2.percent > 0) sumPercentLv2 += dataLv2.percent
            }
            if (sumPercentLv2 > 100) {
              strErr += `Tổng tỉ trọng tiêu chí [${dataLv1.name}] vượt quá 100%<br>`
            }
          }
        }

        if (sumPercent > 100) {
          strErr += 'Tổng tỉ trọng vượt quá 100%<br>'
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (const row of jsonData) {
        // add data table 2
        if (row.zenListId != null && row.zenListId != '') {
          const dataTable2: any = {}
          dataTable2.zenListId = (row.zenListId + '').trim()
          dataTable2.nameList = (row.nameList || '') + ''
          dataTable2.valueList = row.valueList || 0
          const capacity = lstDataTable1.find((c) => c.zenId == dataTable2.zenListId)
          if (!capacity) {
            strErr += `Cột [${row.zenListId}] không tồn tại, không xác định thuộc tiêu chí nào<br>`
          }
          if (capacity.type != enumData.DataType.List.code) {
            strErr += `Cột [${row.zenListId}] không phải kiểu danh sách<br>`
          }
          if (dataTable2.nameList.trim() === '') {
            strErr += 'Tên không được để trống<br>'
          }
          if (dataTable2.valueList == null || dataTable2.valueList === '') {
            strErr += '% Tỉ trọng không được để trống<br>'
          }

          lstDataTable2.push(dataTable2)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      this.notifyService.showloading()
      this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_IMPORT(this.data.id), { lstDataTable1, lstDataTable2 }).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })
    }
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  sendApproveCapacity() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_SEND_APPROVE, { id: this.data.id }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.closeDialog(true)
    })
  }

  approveCapacity() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_APPROVE, { id: this.data.id }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.closeDialog(true)
    })
  }

  reCheckCapacity() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_RECHECK, { id: this.data.id }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.closeDialog(true)
    })
  }
}
