import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-service-capacity.component.html' })
export class AddOrEditServiceCapacityComponent implements OnInit {
  dataObject: any = {}
  numberType = enumData.DataType.Number.code
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  dataCapacity: any[] = []
  isShowPercent = false
  isShowChangeByYear = false
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditServiceCapacityComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDataServiceCapacity()
    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
      this.onChangeDataType()
    }
  }

  onChangeDataType() {
    if (this.dataObject.type === enumData.DataType.Number.code || this.dataObject.type === enumData.DataType.List.code) {
      this.isShowPercent = true
      this.dataObject.percent = this.dataObject.percent || 0
    } else {
      this.isShowPercent = false
      this.dataObject.percent = null
      this.dataObject.percentRule = null
    }

    if (
      this.dataObject.type === enumData.DataType.Number.code ||
      this.dataObject.type === enumData.DataType.String.code ||
      this.dataObject.type === enumData.DataType.File.code
    ) {
      this.isShowChangeByYear = true
    } else {
      this.isShowChangeByYear = false
      this.dataObject.isChangeByYear = false
    }
  }

  onChangeIsChangeByYear() {
    if (this.dataObject.isChangeByYear) {
      this.isShowPercent = false
      this.dataObject.percent = null
      this.dataObject.percentRule = null
    } else this.onChangeDataType()
  }

  loadDataServiceCapacity() {
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_FIND, { serviceId: this.data.serviceId }).then((result) => {
      this.dataCapacity = result
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.parentId && this.dataObject.parentId !== '') {
      this.dataObject.level = 2
    } else {
      this.dataObject.level = 1
    }

    if (this.dataObject.type !== this.numberType) this.dataObject.percentRule = null
    if (this.dataObject.percentRule !== null && this.dataObject.percentRule <= 0) {
      this.notifyService.showError('Vui lòng nhập giá trị đạt là số lớn hơn 0!')
      return
    }
    if (this.dataObject.percentDownRule !== null && this.dataObject.percentDownRule <= 0) {
      this.notifyService.showError('Vui lòng nhập điều kiện liệt tỉ trọng là số lớn hơn 0!')
      return
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.CAPACITY_UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
