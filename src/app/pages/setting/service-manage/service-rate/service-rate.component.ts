import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { Subscription } from 'rxjs'
@Component({ templateUrl: './service-rate.component.html' })
export class ServiceRateComponent implements OnInit {
  modalTitle = 'Thiết lập tỉ trọng'
  dataObject: any = {}
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  isWarningSumPercent = false
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<ServiceRateComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    if (this.data && this.data !== null) {
      this.dataObject = { ...this.data }
    }
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_001.code
    this.checkPercent()
  }

  onSave() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.RATE_UPDATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.dialogRef.close(1)
    })
  }

  checkPercent() {
    this.isWarningSumPercent =
      this.dataObject.percentTech != null &&
      this.dataObject.percentTrade != null &&
      this.dataObject.percentPrice != null &&
      this.dataObject.percentTech + this.dataObject.percentTrade + this.dataObject.percentPrice != 100
  }
}
