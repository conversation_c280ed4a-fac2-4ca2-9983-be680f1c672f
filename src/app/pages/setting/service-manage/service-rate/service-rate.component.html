\<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Tỉ trọng điểm đánh gi<PERSON> kỹ thuật
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số (0-100)!">
            <input nz-input type="number" [(ngModel)]="dataObject.percentTech" (ngModelChange)="checkPercent()"
              name="percentTech" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Tỉ trọng điểm đánh gi<PERSON>KTM
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số (0-100)!">
            <input nz-input type="number" [(ngModel)]="dataObject.percentTrade" (ngModelChange)="checkPercent()"
              name="percentTrade" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Tỉ trọng điểm đánh giá bảng giá
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số (0-100)!">
            <input nz-input type="number" [(ngModel)]="dataObject.percentPrice" (ngModelChange)="checkPercent()"
              name="percentPrice" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row nzGutter="8">
      <span class="text-danger" *ngIf="isWarningSumPercent">Tổng % các tỉ lệ là 100%</span>
    </nz-row>
  </div>


  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" nz-button
        [disabled]="!frmAdd.form.valid || isWarningSumPercent" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>