import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-service-trade.component.html' })
export class AddOrEditServiceTradeComponent implements OnInit {
  modalTitle = 'Thêm mới điều kiện thương mại'
  isShowPercent = false
  dataObject: any = {}
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  dataTrade: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditServiceTradeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDataServiceTrade()
    if (this.data && this.data !== null) {
      if (this.data.id) this.modalTitle = 'Chỉnh sửa điều kiện thương mại'
      this.dataObject = { ...this.data }
      this.onCheckShowPercent()
    }
  }

  onCheckShowPercent() {
    if (this.dataObject.type === enumData.DataType.Number.code || this.dataObject.type === enumData.DataType.List.code) {
      this.isShowPercent = true
      this.dataObject.percent = this.dataObject.percent || 0
      this.dataObject.percentRule = this.dataObject.percentRule || null
    } else {
      this.isShowPercent = false
      this.dataObject.percent = null
      this.dataObject.percentRule = null
    }
  }

  async loadDataServiceTrade() {
    await this.apiService.post(this.apiService.SERVICE_SETTING.TRADE_FIND, { serviceId: this.data.serviceId }).then((result) => {
      this.dataTrade = result || []
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.parentId && this.dataObject.parentId !== '') {
      this.dataObject.level = 2
    } else {
      this.dataObject.level = 1
    }
    if (this.dataObject.type !== this.dataType.Number.code) this.dataObject.percentRule = null
    if (this.dataObject.percentRule !== null && this.dataObject.percentRule <= 0) {
      this.notifyService.showError('Vui lòng nhập giá trị đạt là số lớn hơn 0!')
      return
    }
    if (this.dataObject.percentDownRule !== null && this.dataObject.percentDownRule <= 0) {
      this.notifyService.showError('Vui lòng nhập điều kiện liệt tỉ trọng là số lớn hơn 0!')
      return
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.TRADE_CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.TRADE_UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
