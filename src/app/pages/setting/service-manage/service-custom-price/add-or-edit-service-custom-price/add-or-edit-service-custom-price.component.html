<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ 'Thông tin cơ cấu giá' | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Tên hạng mục</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập tên hạng mục (3-250 kí tự)!">
            <input nz-input placeholder="Nhập 3-250 kí tự" [(ngModel)]="dataObject.name" name="name" required
              pattern=".{3,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Thứ tự sắp xếp</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng không nhập số nhỏ hơn 0!">
            <input nz-input placeholder="Nhập số" [(ngModel)]="dataObject.sort" name="sort" type="number" min="0" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Đơn vị tính</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn đơn vị tính!">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.unit" name="unit"
              nzPlaceHolder="Chọn đơn vị tính">
              <nz-option *ngFor="let item of lstUnit" [nzLabel]="item.code" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Đơn vị tiền tệ</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn đơn vị tiền tệ!">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.currency" name="currency"
              nzPlaceHolder="Chọn đơn vị tiền tệ">
              <nz-option *ngFor="let item of lstCurrency" [nzLabel]="item.code" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired> Số lượng </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng không nhập số nhỏ hơn 0!">
            <input nz-input [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'"
              [(ngModel)]="dataObject.number" name="number"  />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="3" [nzOffset]="6">
        <nz-form-item nzFlex>
          <nz-form-control nzSpan="24">
            <label nz-checkbox [(ngModel)]="dataObject.isRequired" name="isRequired"> Bắt buộc? </label>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>