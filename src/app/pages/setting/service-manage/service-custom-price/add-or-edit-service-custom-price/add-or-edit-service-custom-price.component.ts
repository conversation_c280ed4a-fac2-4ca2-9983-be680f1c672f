import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'
@Component({ templateUrl: './add-or-edit-service-custom-price.component.html' })
export class AddOrEditServiceCustomPriceComponent implements OnInit {
  dataObject: any = {}
  lstUnit: any[] = []
  lstCurrency: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditServiceCustomPriceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    await this.loadAllList()
    if (this.data && this.data !== null) {
      if (this.data.id) {
        // edit
        this.dataObject = { ...this.data }
      } else {
        this.dataObject.serviceId = this.data.serviceId
        this.dataObject.type = enumData.DataType.Number.code
        this.dataObject.isSetup = false
        this.dataObject.isTemplate = false
        this.dataObject.isRequired = true
        this.dataObject.number = 0
      }
    }
  }

  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.CUSTOMPRICE_CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.CUSTOMPRICE_UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
