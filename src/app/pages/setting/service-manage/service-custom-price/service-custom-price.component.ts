import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { AddOrEditServiceCustomPriceComponent } from './add-or-edit-service-custom-price/add-or-edit-service-custom-price.component'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-service-custom-price',
  templateUrl: './service-custom-price.component.html',
})
export class ServiceCustomPriceComponent implements OnInit {
  modalTitle = 'Thông tin cơ cấu giá'
  loading = false
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  lstUnit: any[] = []
  lstCurrency: any[] = []
  listOfData: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  currentUser: any
  isLoadData = false
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_001.code
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = { serviceId: this.data.id }
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.CUSTOMPRICE_PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    const item = {
      name: '',
      type: enumData.DataType.Number.code,
      isRequired: true,
      serviceId: this.data.id,
    }
    this.dialog
      .open(AddOrEditServiceCustomPriceComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    this.dialog
      .open(AddOrEditServiceCustomPriceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.CUSTOMPRICE_DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
    this.isLoadData = true
  }

  clickExportExcel() {
    this.notifyService.showloading()
    const where: any = { serviceId: this.data.id, isDeleted: false }

    const dataSearch: any = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.CUSTOMPRICE_PAGINATION, dataSearch).then((res) => {
      const lstData = res[0]
      let lstDataExport: any[] = []

      const title: any = { sort: 'CẤU HÌNH TEMPLATE', name: '', unit: '', currency: '', number: '', isRequired: '' }
      lstDataExport.push(title)
      const header: any = {
        sort: this.language_key?.NO || 'STT',
        name: 'Tên hạng mục',
        unit: 'Đơn vị tính',
        currency: 'Đơn vị tiền tệ',
        number: 'Số lượng',
        isRequired: 'Bắt buộc?',
      }
      lstDataExport.push(header)

      for (const item of lstData) {
        const row: any = {}
        row.sort = item.sort > 0 ? item.sort : ''
        row.name = item.name
        row.unit = item.unit
        row.currency = item.currency
        row.number = item.number
        row.isRequired = item.isRequired
        lstDataExport.push(row)
      }

      var ws = XLSX.utils.json_to_sheet(lstDataExport, {
        skipHeader: true,
      })
      var wb = XLSX.utils.book_new()
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template cấu hình cơ cấu giá của dịch vụ ${this.data.code}.xlsx`
      const sheetName = 'Cơ cấu giá'
      XLSX.utils.book_append_sheet(wb, ws, sheetName)

      wb.Sheets[sheetName]['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 5 } } /* A1:F1 */]

      XLSX.writeFile(wb, fileName)

      setTimeout(() => {
        this.notifyService.hideloading()
      }, 100)
    })
  }

  async clickImportExcel(ev: any) {
    if (!this.isLoadData) {
      await this.loadAllList()
    }
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['sort', 'name', 'unit', 'currency', 'number', 'isRequired'],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng đầu tiên
      let isErr = false
      const header: any = jsonData.shift()
      // Kiểm tra header
      if (
        header.sort !== (this.language_key?.NO || 'STT') ||
        header.name !== 'Tên hạng mục' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.unit !== 'Đơn vị tính' ||
        header.currency !== 'Đơn vị tiền tệ' ||
        header.number !== 'Số lượng'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError('File không đúng template cơ cấu giá LVMH ' + this.data.name)
        return
      }

      // Kiểm tra từng dòng
      let strErr = ''
      for (const row of jsonData) {
        if (row.sort == null || row.sort == '') {
          row.sort = 0
        }
        if (row.isRequired == null || row.isRequired == '' || typeof row.isRequired !== 'boolean') {
          row.isRequired = false
        }
        if (row.name == null || row.name == '') {
          strErr += 'Tên hạng mục không được để trống<br>'
        }
        row.unit = (row.unit || '') + ''
        if (row.unit != null && row.unit.length > 0) {
          const objUnit = this.lstUnit.find((c) => c.code === row.unit)
          if (!objUnit) {
            strErr += `Đơn vị tính [${row.unit}] không tồn tại<br>`
          }
        }
        row.currency = (row.currency || '') + ''
        if (row.currency != null && row.currency.length > 0) {
          const objCurrency = this.lstCurrency.find((c) => c.code === row.currency)
          if (!objCurrency) {
            strErr += `Đơn vị tiền tệ ${row.currency} không tồn tại<br>`
          }
        }
        if (row.number == null || typeof row.number !== 'number') {
          strErr += 'Số lượng là số, không được để trống<br>'
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      this.notifyService.showloading()
      this.apiService.post(this.apiService.SERVICE_SETTING.CUSTOMPRICE_IMPORT(this.data.id), { lstData: jsonData }).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })
    }
  }
}
