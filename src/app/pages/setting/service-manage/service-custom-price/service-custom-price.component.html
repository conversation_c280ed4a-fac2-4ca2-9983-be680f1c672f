<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8">
    <nz-col nzSpan="8">
      <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm kiếm theo tên hạng mục" />
    </nz-col>
    <nz-col nzSpan="8">
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
        [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
        <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="8">
      <button nz-button (click)="searchData(true)">
        <span nz-icon nzType="search"></span>
        {{ language_key?.SEARCH || 'Tìm kiếm' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-col nzSpan="24">
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button
        (click)="clickAdd()" nzType="primary" class="mr-2">
        <span nz-icon nzType="plus"></span>
        {{ language_key?.ADD || 'Thêm mới' }}
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button
        (click)="clickExportExcel()" class="mr-2">
        <span nz-icon nzType="download"></span>Xuất excel</button>
      <input class="hidden" type="file" id="file"
        *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
        (change)="clickImportExcel($event)" onclick="this.value=null"
        accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
      <label for="file" class="lable-custom-file"
        *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)">
        <span nz-icon nzType="upload"></span>
        {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
      </label>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-table nz-col nzSpan="24" class="mb-3" #expandTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
      [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
      <thead>
        <!-- header gid level 1 -->
        <tr>
          <th>{{ language_key?.NO || 'STT' }}</th>
          <th>Tên hạng mục</th>
          <th>Đơn vị tính</th>
          <th>Đơn vị tiền tệ</th>
          <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th>Bắt buộc?</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of expandTable.data">
          <td (click)="clickEdit(data)"> {{ data.sort > 0 ? data.sort : '' }} </td>
          <td (click)="clickEdit(data)" class="mw-25">{{ data.name }}</td>
          <td (click)="clickEdit(data)">{{ data.unit }}</td>
          <td (click)="clickEdit(data)">{{ data.currency }}</td>
          <td class="text-right" (click)="clickEdit(data)">{{
            data.number | number }}</td>
          <td (click)="clickEdit(data)">{{ data.isRequired ? 'Bắt buộc' : 'Không' }}</td>
          <td class="text-nowrap">
            <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="setActiveItem(data)" nz-tooltip
              [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" class="mr-2" nz-button nzDanger>
              <span nz-icon nzType="stop"></span>
            </button>
            <button *ngIf="!data.isDeleted  && authenticationService.checkPermission([enumRole], action.Active.code)"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="setActiveItem(data)" nz-tooltip [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
              class="mr-2" nz-button nzType="primary">
              <span nz-icon nzType="play-circle"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-row>
</div>