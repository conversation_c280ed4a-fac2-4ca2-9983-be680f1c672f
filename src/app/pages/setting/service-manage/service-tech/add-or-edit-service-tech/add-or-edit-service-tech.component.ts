import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-service-tech.component.html' })
export class AddOrEditServiceTechComponent implements OnInit {
  modalTitle = 'Thêm yêu cầu kỹ thuật'
  isShowPercent = false
  dataObject: any = {}
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  dataTech: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditServiceTechComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDataServiceTech()
    if (this.data && this.data !== null) {
      if (this.data.id) this.modalTitle = 'Chỉnh sửa yêu cầu kỹ thuật'
      this.dataObject = { ...this.data }
      this.onCheckShowPercent()
    }
  }

  onCheckShowPercent() {
    if (this.dataObject.type === enumData.DataType.Number.code || this.dataObject.type === enumData.DataType.List.code) {
      this.isShowPercent = true
      this.dataObject.percent = this.dataObject.percent || 0
    } else {
      this.isShowPercent = false
      this.dataObject.percent = null
      this.dataObject.percentRule = null
      this.dataObject.isHighlight = false
      this.dataObject.hightlightValue = null
      this.dataObject.requiredMin = null
    }
  }

  loadDataServiceTech() {
    this.apiService.post(this.apiService.SERVICE_SETTING.TECH_FIND, { serviceId: this.data.serviceId, isGetRelation: false }).then((result) => {
      this.dataTech = result
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.parentId && this.dataObject.parentId !== '') {
      this.dataObject.level = 2
    } else {
      this.dataObject.level = 1
    }
    if (this.dataObject.type !== this.dataType.Number.code) this.dataObject.percentRule = null
    if (this.dataObject.percentRule !== null && this.dataObject.percentRule <= 0) {
      this.notifyService.showError('Vui lòng nhập giá trị đạt là số lớn hơn 0!')
      return
    }
    if (this.dataObject.hightlightValue !== null && this.dataObject.hightlightValue <= 0) {
      this.notifyService.showError('Vui lòng nhập giá trị hightlight là số lớn hơn 0!')
      return
    }
    if (this.dataObject.requiredMin !== null && this.dataObject.requiredMin <= 0) {
      this.notifyService.showError('Vui lòng nhập giá trị min là số lớn hơn 0!')
      return
    }
    if (this.dataObject.percentDownRule !== null && this.dataObject.percentDownRule <= 0) {
      this.notifyService.showError('Vui lòng nhập điều kiện liệt tỉ trọng là số lớn hơn 0!')
      return
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.TECH_CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.TECH_UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
