<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8">
    <nz-col nzSpan="8">
      <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm kiếm theo tên tiêu chí kỹ thuật" />
    </nz-col>
    <nz-col nzSpan="8">
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.statusId" name="statusId"
        [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'">
        <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="8">
      <button nz-button (click)="searchData(true)">
        <span nz-icon nzType="search"></span>
        {{ language_key?.SEARCH || 'Tìm kiếm' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-col nzSpan="24">
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button
        (click)="clickAdd()" nzType="primary" class="mr-2">
        <span nz-icon nzType="plus"></span>
        {{ language_key?.ADD || 'Thêm mới' }}
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)" nz-button nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn xoá tất cả tiêu chí kỹ thuật?" nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="clickDeleteAllRow()" nzDanger class="mr-2">
        <span nz-icon nzType="delete"></span> Xoá tất cả tiêu chí kỹ thuật
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button
        (click)="clickExportExcel()" class="mr-2">
        <span nz-icon nzType="download"></span>Xuất excel</button>
      <input *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" class="hidden" type="file"
        id="file" (change)="clickImportExcel($event)" onclick="this.value=null"
        accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
      <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file"
        class="lable-custom-file">
        <span nz-icon nzType="upload"></span>
        {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
      </label>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <span *ngIf="sumPercent < 100" class="text-orange">Tổng tỉ trọng đạt {{ sumPercent }}%, chưa đủ 100%</span>
    <nz-table nz-col nzSpan="24" class="mb-3" #expandTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
      [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
      <thead>
        <!-- header gid level 1 -->
        <tr>
          <th>{{ language_key?.NO || 'STT' }}</th>
          <th>Tên tiêu chí</th>
          <th>Tỉ trọng(%)</th>
          <th>Giá trị đạt</th>
          <th>Kiểu dữ liệu</th>
          <th>Bắt buộc?</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of expandTable.data">
          <tr>
            <td (click)="clickEdit(data) ">
              {{ data.sort > 0 ? data.sort : '' }}
            </td>
            <td class="mw-25" (click)="clickEdit(data) ">
              {{ data.name }}
            </td>
            <td class="text-right" (click)="clickEdit(data) ">
              {{data.percent > 0 ? (data.percent | number: '1.0-2') : ''}}
            </td>
            <td class="text-right" (click)="clickEdit(data) ">{{
              data.percentRule | number }}</td>
            <td (click)="clickEdit(data) ">{{
              data.type }}</td>
            <td (click)="clickEdit(data) ">{{
              data.isRequired ? 'Bắt buộc' : 'Không' }}</td>
            <td class="text-nowrap">
              <button
                *ngIf="data.type === dataType.List.code && authenticationService.checkPermission([enumRole], action.Update.code)"
                nz-tooltip nzTooltipTitle="Cấu hình danh sách" class="mr-2" nz-button
                [nzType]="data.__serviceTechListDetails__?.length > 0 ? 'default' : 'dashed'"
                (click)="settingList(data.id)">
                <span nz-icon nzType="setting"></span>
              </button>
              <button *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="setActiveItem(data)" nz-tooltip
                [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" nz-button nzDanger>
                <span nz-icon nzType="stop"></span>
              </button>
              <button *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="setActiveItem(data)" nz-tooltip
                [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'" nz-button nzType="primary">
                <span nz-icon nzType="play-circle"></span>
              </button>
            </td>
          </tr>
          <ng-container>
            <td [nzIndentSize]="10" colspan="8" scope="colgroup" *ngIf="data.sumPercent < 100 && data.sumPercent >= 0"
              class="text-orange">
              Tổng tỉ trọng trong mục này đạt {{ data.sumPercent }}%, chưa đủ 100%
            </td>
            <tr *ngFor="let item of data.__childs__">
              <td (click)="clickEdit(item)" [nzIndentSize]="10">{{ item.sort > 0 ? item.sort : '' }}</td>
              <td class="mw-25" (click)="clickEdit(item)">
                {{ item.name }}
              </td>
              <td class="text-right" (click)="clickEdit(item)">
                {{item.percent > 0 ? (item.percent | number: '1.0-2') : ''}}
              </td>
              <td class="text-right" (click)="clickEdit(item)">
                {{ item.percentRule | number }}</td>
              <td (click)="clickEdit(item)">
                {{ item.type }}</td>
              <td (click)="clickEdit(item)">
                {{ item.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td class="text-nowrap">
                <button
                  *ngIf="item.type === dataType.List.code && authenticationService.checkPermission([enumRole], action.Update.code)"
                  nz-tooltip nzTooltipTitle="Cấu hình danh sách" class="mr-2" nz-button
                  [nzType]="item.__serviceTechListDetails__?.length > 0 ? 'default' : 'dashed'"
                  (click)="settingList(item.id)">
                  <span nz-icon nzType="setting"></span>
                </button>
                <button *ngIf="item.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?" nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(item)" nz-tooltip
                  [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'" class="mr-2" nz-button nzDanger>
                  <span nz-icon nzType="stop"></span>
                </button>
                <button *ngIf="!item.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?" nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(item)" nz-tooltip
                  [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'" class="mr-2" nz-button nzType="primary">
                  <span nz-icon nzType="play-circle"></span>
                </button>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-row>
</div>