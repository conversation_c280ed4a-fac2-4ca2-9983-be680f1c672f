import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { AddOrEditServicePriceComponent } from './add-or-edit-service-price/add-or-edit-service-price.component'
import { ServicePriceListDetailComponent } from './service-price-list-detail/service-price-list-detail.component'
import { ServicePriceColComponent } from './service-price-col/service-price-col.component'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-service-price',
  templateUrl: './service-price.component.html',
})
export class ServicePriceComponent implements OnInit {
  loading = false
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  servicePriceCol: any[] = []
  servicePriceColId: any[] = []
  lstUnit: any[] = []
  lstCurrency: any[] = []
  isVisible = false
  listOfData: any[] = []
  dataSearch: any = {}
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  dataPriceScoreCalculateWay = this.coreService.convertObjToArray(enumData.PriceScoreCalculateWay)
  typeNumber = enumData.DataType.Number.code
  lstDataType = [enumData.DataType.String, enumData.DataType.Address, enumData.DataType.Km, enumData.DataType.Time]
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  currency: any
  currentUser: any
  isLoadList = false
  isVisibleSettingWayCalScorePrice = false
  dataSettingWayCalScorePrice: any = {}

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_001.code
    this.loadAllList()
  }

  searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = { serviceId: this.data.id }

    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        this.servicePriceCol = data[2]
        this.servicePriceColId = this.servicePriceCol.map((c) => c.id)
      }
    })
  }

  clickAdd() {
    const item: any = {
      name: '',
      type: enumData.DataType.Number.code,
      isRequired: true,
      serviceId: this.data.id,
      currency: this.currency,
    }
    this.dialog
      .open(AddOrEditServicePriceComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    if (!this.authenticationService.checkPermission([this.enumRole], this.action.Update.code)) return
    this.dialog
      .open(AddOrEditServicePriceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickSetup(object: any) {
    this.dialog
      .open(AddOrEditServicePriceComponent, {
        disableClose: false,
        data: { parentId: object.id, serviceId: object.serviceId },
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  settingExInfo(id: string) {
    this.dialog.open(ServicePriceListDetailComponent, { disableClose: false, data: id })
  }

  settingPriceCol() {
    this.dialog
      .open(ServicePriceColComponent, { disableClose: false, data: this.data })
      .afterClosed()
      .subscribe(() => this.searchData())
  }

  //#region Cấu hình công thức tính đơn giá
  settingFomularCalValue() {
    this.isVisible = true
  }
  handleCancel() {
    this.isVisible = false
  }
  handleOk() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.SETTING_FOMULAR, this.data).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisible = false
    })
  }
  //#endregion

  //#region Cấu hình cách tính điểm giá
  settingPriceCalWay() {
    this.isVisibleSettingWayCalScorePrice = true
    this.dataSettingWayCalScorePrice = {
      id: this.data.id,
      wayCalScorePrice: this.data.wayCalScorePrice || enumData.PriceScoreCalculateWay.SumScore.code,
    }
  }
  cancelSettingPriceCalWay() {
    this.isVisibleSettingWayCalScorePrice = false
  }
  saveSettingPriceCalWay() {
    this.notifyService.showloading()
    if (!this.dataSettingWayCalScorePrice.wayCalScorePrice) {
      this.notifyService.showWarning(`Vui lòng chọn cách tính điểm giá!`)
      return
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.SETTING_WAY_CAL_SCORE_PRICE, this.dataSettingWayCalScorePrice).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisibleSettingWayCalScorePrice = false
      this.data.wayCalScorePrice = this.dataSettingWayCalScorePrice.wayCalScorePrice
    })
  }
  //#endregion

  clickDeleteAllRow() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_DELETEALL, { serviceId: this.data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.isLoadList = true
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
  }

  clickExportExcel() {
    this.notifyService.showloading()
    const where: any = {}
    where.serviceId = this.data.id
    where.isDeleted = false

    const dataSearch: any = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_PAGINATION, dataSearch).then((res) => {
      const lstData = res[0]
      let lstDataExport: any[] = []
      const servicePriceCol = res[2]

      //#region header
      const servicePriceColTitle: any = {}
      for (const col of servicePriceCol) {
        servicePriceColTitle[col.id] = ''
      }
      const title: any = {
        zenId: 'CẤU HÌNH TEMPLATE',
        sort: '',
        name: '',
        ...servicePriceColTitle,
        unit: '',
        currency: '',
        number: '',
        isRequired: '',
        blank: '',
        zenListId: 'CẤU HÌNH CÁC THÔNG TIN MỞ RỘNG',
        nameList: '',
        typeDataList: '',
        valueList: '',
      }
      lstDataExport.push(title)

      const servicePriceColHeader: any = {}
      for (const col of servicePriceCol) {
        servicePriceColHeader[col.id] = col.name
      }
      const numColTable1 = 3 + servicePriceCol.length + 4
      const header: any = {
        zenId: 'Cột',
        sort: this.language_key?.NO || 'STT',
        name: 'Tên hạng mục',
        ...servicePriceColHeader,
        unit: 'Đơn vị tính',
        currency: 'Đơn vị tiền tệ',
        number: 'Số lượng',
        isRequired: 'Bắt buộc?',
        blank: '',
        zenListId: 'Cột',
        nameList: 'Tên trường thông tin',
        typeDataList: 'Kiểu dữ liệu',
        valueList: 'Giá trị',
      }
      lstDataExport.push(header)
      //#endregion

      //#region custom data before export
      let i1 = 1
      const lstDataTable1: any[] = []
      const lstDataTable2: any[] = []
      const lstDataLv1 = lstData || []
      for (const data1 of lstDataLv1) {
        const dataTable1Lv1: any = {}
        dataTable1Lv1.zenId = '' + i1
        i1++
        dataTable1Lv1.sort = data1.sort > 0 ? data1.sort : ''
        dataTable1Lv1.name = data1.name
        dataTable1Lv1.unit = data1.unit
        dataTable1Lv1.currency = data1.currency
        dataTable1Lv1.number = data1.number
        dataTable1Lv1.isRequired = data1.isRequired
        for (const col of servicePriceCol) dataTable1Lv1[col.id] = data1[col.id]
        lstDataTable1.push(dataTable1Lv1)

        if (data1.__servicePriceListDetails__ && data1.__servicePriceListDetails__.length > 0) {
          for (const detail of data1.__servicePriceListDetails__) {
            const dataTable2Lv1: any = {}
            dataTable2Lv1.zenListId = dataTable1Lv1.zenId
            dataTable2Lv1.nameList = detail.name
            dataTable2Lv1.typeDataList = detail.type
            dataTable2Lv1.valueList = detail.value
            lstDataTable2.push(dataTable2Lv1)
          }
        }

        let i2 = 1
        const lstDataLv2 = data1.__childs__ || []
        for (const data2 of lstDataLv2) {
          const dataTable1Lv2: any = {}
          dataTable1Lv2.zenId = dataTable1Lv1.zenId + '.' + i2
          i2++
          dataTable1Lv2.sort = data2.sort > 0 ? data2.sort : ''
          dataTable1Lv2.name = data2.name
          dataTable1Lv2.unit = data2.unit
          dataTable1Lv2.currency = data2.currency
          dataTable1Lv2.number = data2.number
          dataTable1Lv2.isRequired = data2.isRequired
          for (const col of servicePriceCol) dataTable1Lv2[col.id] = data2[col.id]
          lstDataTable1.push(dataTable1Lv2)

          if (data2.__servicePriceListDetails__ && data2.__servicePriceListDetails__.length > 0) {
            for (const detail of data2.__servicePriceListDetails__) {
              const dataTable2Lv2: any = {}
              dataTable2Lv2.zenListId = dataTable1Lv2.zenId
              dataTable2Lv2.nameList = detail.name
              dataTable2Lv2.typeDataList = detail.type
              dataTable2Lv2.valueList = detail.value
              lstDataTable2.push(dataTable2Lv2)
            }
          }

          let i3 = 1
          const lstDataLv3 = data2.__childs__ || []
          for (const data3 of lstDataLv3) {
            const dataTable1Lv3: any = {}
            dataTable1Lv3.zenId = dataTable1Lv2.zenId + '.' + i3
            i3++
            dataTable1Lv3.sort = data3.sort > 0 ? data3.sort : ''
            dataTable1Lv3.name = data3.name
            dataTable1Lv3.unit = data3.unit
            dataTable1Lv3.currency = data3.currency
            dataTable1Lv3.number = data3.number
            dataTable1Lv3.isRequired = data3.isRequired
            for (const col of servicePriceCol) dataTable1Lv3[col.id] = data3[col.id]
            lstDataTable1.push(dataTable1Lv3)

            if (data3.__servicePriceListDetails__ && data3.__servicePriceListDetails__.length > 0) {
              for (const detail of data3.__servicePriceListDetails__) {
                const dataTable2Lv3: any = {}
                dataTable2Lv3.zenListId = dataTable1Lv3.zenId
                dataTable2Lv3.nameList = detail.name
                dataTable2Lv3.typeDataList = detail.type
                dataTable2Lv3.valueList = detail.value
                lstDataTable2.push(dataTable2Lv3)
              }
            }
          }
        }
      }

      //#endregion

      let numRowData = lstDataTable1.length > lstDataTable2.length ? lstDataTable1.length : lstDataTable2.length
      for (let i = 0; i < numRowData; i++) {
        const dataTable1 = lstDataTable1[i] || {}
        const dataTable2 = lstDataTable2[i] || {}
        lstDataExport.push({ ...dataTable1, ...dataTable2 })
      }
      var ws = XLSX.utils.json_to_sheet(lstDataExport, {
        skipHeader: true,
      })
      var wb = XLSX.utils.book_new()
      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template cấu hình bảng chào giá của dịch vụ ${this.data.code}.xlsx`
      const sheetName = 'Bảng chào giá'
      XLSX.utils.book_append_sheet(wb, ws, sheetName)
      wb.Sheets[sheetName]['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: numColTable1 - 1 } } /* A1:G1 */,
        { s: { r: 0, c: numColTable1 + 1 }, e: { r: 0, c: numColTable1 + 4 } } /* I1:L1 */,
      ]

      XLSX.writeFile(wb, fileName)

      setTimeout(() => {
        this.notifyService.hideloading()
      }, 100)
    })
  }

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = async () => {
      if (!this.isLoadList) {
        await this.loadAllList()
      }
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'sort',
          'name',
          ...this.servicePriceColId,
          'unit',
          'currency',
          'number',
          'isRequired',
          'blank',
          'zenListId',
          'nameList',
          'typeDataList',
          'valueList',
        ],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()
      // Kiểm tra header
      if (
        header.zenId !== 'Cột' ||
        header.sort !== (this.language_key?.NO || 'STT') ||
        header.name !== 'Tên hạng mục' ||
        header.unit !== 'Đơn vị tính' ||
        header.currency !== 'Đơn vị tiền tệ' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.number !== 'Số lượng' ||
        header.zenListId !== 'Cột' ||
        header.nameList !== 'Tên trường thông tin' ||
        header.typeDataList !== 'Kiểu dữ liệu' ||
        header.valueList !== 'Giá trị'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError('File không đúng template chào giá LVMH ' + this.data.name)
        return
      }

      // Tách và kiểm tra data từng bảng
      const lstDataTable1: any[] = []
      const lstDataTable2: any[] = []
      let strErr = ''
      for (const row of jsonData) {
        // add data table 1
        if (row.zenId != null && row.zenId != '') {
          const dataTable1: any = {}
          dataTable1.zenId = (row.zenId + '').trim()
          dataTable1.sort = row.sort || 0
          dataTable1.name = (row.name || '') + ''
          dataTable1.unit = (row.unit || '') + ''
          dataTable1.currency = (row.currency || '') + ''
          dataTable1.number = row.number || 0
          dataTable1.isRequired = row.isRequired
          if (dataTable1.isRequired == null || dataTable1.isRequired === '' || typeof dataTable1.isRequired !== 'boolean') {
            dataTable1.isRequired = false
          }
          for (const colId of this.servicePriceColId) {
            dataTable1[colId] = row[colId]
          }
          const lstId = dataTable1.zenId.split('.')
          dataTable1.level = lstId.length
          if (dataTable1.level < 1 || dataTable1.level > 3) {
            strErr += `Cột [${dataTable1.zenId}] không hợp lệ, không xác định được level nào<br>`
          }
          for (const id of lstId) {
            try {
              let intId = parseInt(id)
              if (intId <= 0) {
                strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số dương<br>`
              }
            } catch {
              strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số<br>`
            }
          }
          if (dataTable1.level == 2) {
            dataTable1.parentZenId = lstId[0] + ''
            if (!lstDataTable1.some((c) => c.zenId == dataTable1.parentZenId)) {
              strErr += `Không tìm thấy cấp cha của cột [${dataTable1.zenId}] ở phía trên của dòng này<br>`
            }
          }
          if (dataTable1.level == 3) {
            dataTable1.parentZenId = lstId[0] + '.' + lstId[1]
            if (!lstDataTable1.some((c) => c.zenId == dataTable1.parentZenId)) {
              strErr += `Không tìm thấy cấp cha của cột [${dataTable1.zenId}] ở phía trên của dòng này<br>`
            }
          }

          if (dataTable1.name.trim() === '') {
            strErr += 'Tên hạng mục không được để trống<br>'
          }
          if (dataTable1.unit.trim().length > 0) {
            if (!this.lstUnit.some((c) => c.code === dataTable1.unit)) {
              strErr += `Đơn vị tính [${dataTable1.unit}] không tồn tại<br>`
            }
          }
          if (dataTable1.currency.trim().length > 0) {
            if (!this.lstCurrency.some((c) => c.code === dataTable1.currency)) {
              strErr += `Đơn vị tiền tệ [${dataTable1.currency}] không tồn tại<br>`
            }
          }
          if (dataTable1.number == null || typeof dataTable1.number !== 'number') {
            strErr += 'Số lượng là số, không được để trống<br>'
          }
          for (const col of this.servicePriceCol) {
            if (col.isRequired && (dataTable1[col.id] == null || dataTable1[col.id] === '')) {
              strErr += `${col.name} không được để trống<br>`
            }
            if (dataTable1[col.id] != null && dataTable1[col.id] != '' && col.type === this.typeNumber && typeof dataTable1[col.id] !== 'number') {
              strErr += `${col.name} là số, vui lòng điền đúng<br>`
            }
          }

          lstDataTable1.push(dataTable1)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (const row of jsonData) {
        // add data table 2
        if (row.zenListId != null && row.zenListId != '') {
          const dataTable2: any = {}
          dataTable2.zenListId = (row.zenListId + '').trim()
          dataTable2.nameList = (row.nameList || '') + ''
          dataTable2.typeDataList = (row.typeDataList || '') + ''
          dataTable2.valueList = (row.valueList || '') + ''
          if (!lstDataTable1.some((c) => c.zenId == dataTable2.zenListId)) {
            strErr += `Cột [${row.zenListId}] không tồn tại, không xác định thuộc hạng mục nào<br>`
          }
          if (dataTable2.nameList.trim() === '') {
            strErr += 'Tên trường thông tin không được để trống<br>'
          }
          if (dataTable2.typeDataList.trim() === '') {
            strErr += 'Kiểu dữ liệu không được để trống<br>'
          }
          if (!this.lstDataType.some((c) => c.code === dataTable2.typeDataList)) {
            strErr += `Kiểu dữ liệu [${dataTable2.typeDataList}] không tồn tại trong [String, Address, Km, Time]<br>`
          }
          if (dataTable2.valueList == null || dataTable2.valueList === '') {
            strErr += 'Giá trị không được để trống<br>'
          }
          if (
            dataTable2.valueList != null &&
            dataTable2.valueList != '' &&
            (dataTable2.typeDataList == enumData.DataType.Km.code || dataTable2.typeDataList == enumData.DataType.Time.code)
          ) {
            try {
              const a = +dataTable2.valueList
              if (typeof a != 'number' || a <= 0 || isNaN(a) || !isFinite(a)) {
                strErr += `Giá trị kiểu ${dataTable2.typeDataList} phải là số dương<br>`
              }
            } catch {
              strErr += `Giá trị kiểu ${dataTable2.typeDataList} phải số dương<br>`
            }
          }

          lstDataTable2.push(dataTable2)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      this.notifyService.showloading()
      this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_IMPORT(this.data.id), { lstDataTable1, lstDataTable2 }).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })
    }
  }
}
