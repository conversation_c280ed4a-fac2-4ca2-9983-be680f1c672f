<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ 'Cấu hình thông tin mở rộng' | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8">
    <nz-col nzSpan="8">
      <input nz-input [(ngModel)]="dataSearch.name" placeholder="Lọc tên" />
    </nz-col>
    <nz-col nzSpan="8">
      <nz-select
        nzShowSearch
        nzAllowClear
        [(ngModel)]="dataSearch.statusId"
        name="statusId"
        [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'"
      >
        <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="8">
      <button nz-button (click)="searchData(true)">
        <span nz-icon nzType="search"></span>
        {{ language_key?.SEARCH || 'Tìm kiếm' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row nzGutter="8" class="mt-3">
    <button nz-button (click)="searchData(true)" class="mr-2">Làm mới</button>
    <button nz-button (click)="addRow()" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nzType="primary">
      <span nz-icon nzType="plus"></span> Thêm
    </button>
  </nz-row>

  <nz-row class="mt-1">
    <nz-table
      nz-col
      nzSpan="24"
      class="mb-3"
      #basicTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr>
          <th>Tên trường thông tin</th>
          <th>Kiểu dữ liệu</th>
          <th>Giá trị</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data">
          <td class="mw-40">
            <ng-container *ngIf="!editCache[data.id].edit; else nameInputTpl">
              {{ data.name }}
            </ng-container>
            <ng-template #nameInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[data.id].data.name" placeholder="Nhập 1-500 kí tự" />
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[data.id].edit; else typeInputTpl">
              {{ data.type }}
            </ng-container>
            <ng-template #typeInputTpl>
              <nz-select
                nzShowSearch
                nzAllowClear
                nz-col
                nzSpan="24"
                [(ngModel)]="editCache[data.id].data.type"
                name="type"
                required
                nzPlaceHolder="Chọn kiểu dữ liệu"
                class="mr-2"
              >
                <nz-option *ngFor="let item of lstDataType" [nzLabel]="item.code" [nzValue]="item.code"></nz-option>
              </nz-select>
            </ng-template>
          </td>

          <td>
            <ng-container *ngIf="!editCache[data.id].edit; else valueInputTpl">
              <p *ngIf="editCache[data.id].data.type === dataType.String.code || editCache[data.id].data.type === dataType.Address.code">
                {{ data.value }}
              </p>
              <p *ngIf="editCache[data.id].data.type === dataType.Km.code || editCache[data.id].data.type === dataType.Time.code">
                {{ data.value | number : '1.0-2' }}
              </p>
            </ng-container>
            <ng-template #valueInputTpl>
              <div>
                <div *ngIf="editCache[data.id].data.type === dataType.String.code">
                  <input nz-input [(ngModel)]="editCache[data.id].data.value" placeholder="Nhập kí tự" />
                </div>
                <div *ngIf="editCache[data.id].data.type === dataType.Address.code">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButtonSearch">
                    <input type="text" nz-input placeholder="Nhập địa chỉ" [(ngModel)]="editCache[data.id].data.value" />
                  </nz-input-group>
                  <ng-template #suffixIconButtonSearch>
                    <button nz-button nzType="primary" nzSearch>
                      <span nz-icon nzType="search"></span>
                    </button>
                  </ng-template>
                </div>
                <div *ngIf="editCache[data.id].data.type === dataType.Km.code">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButtonCal">
                    <input type="number" nz-input placeholder="Nhập khoảng cách (Km)" [(ngModel)]="editCache[data.id].data.value" />
                  </nz-input-group>
                  <ng-template #suffixIconButtonCal>
                    <button nz-button nzType="primary" nzSearch>
                      <span nz-icon nzType="calculator"></span>
                    </button>
                  </ng-template>
                </div>
                <div *ngIf="editCache[data.id].data.type === dataType.Time.code">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButtonCalTime">
                    <input type="number" nz-input placeholder="Nhập thời gian đi (Giờ)" [(ngModel)]="editCache[data.id].data.value" />
                  </nz-input-group>
                  <ng-template #suffixIconButtonCalTime>
                    <button nz-button nzType="primary" nzSearch>
                      <span nz-icon nzType="calculator"></span>
                    </button>
                  </ng-template>
                </div>
              </div>
            </ng-template>
          </td>

          <td class="text-nowrap">
            <div class="editable-row-operations">
              <ng-container *ngIf="!editCache[data.id].edit; else saveTpl">
                <button
                  nz-tooltip
                  nzTooltipTitle="Sửa"
                  class="mr-2"
                  nz-button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  (click)="startEdit(data.id)"
                >
                  <span nz-icon nzType="edit"></span>
                </button>
                <button
                  *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(data)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'"
                  class="mr-2"
                  nz-button
                  nzDanger
                >
                  <span nz-icon nzType="stop"></span>
                </button>
                <button
                  *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(data)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
                  class="mr-2"
                  nz-button
                  nzType="primary"
                >
                  <span nz-icon nzType="play-circle"></span>
                </button>
              </ng-container>
              <ng-template #saveTpl>
                <button
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.SAVE || 'Lưu'"
                  class="mr-2"
                  nz-button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  nzType="primary"
                  (click)="saveEdit(data.id)"
                >
                  <span nz-icon nzType="save"></span>
                </button>
                <button
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.CANCEL || 'Hủy'"
                  class="mr-2"
                  nz-button
                  nzDanger
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  (click)="cancelEdit(data.id)"
                >
                  <span nz-icon nzType="close"></span>
                </button>
              </ng-template>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
  </nz-row>
</div>
