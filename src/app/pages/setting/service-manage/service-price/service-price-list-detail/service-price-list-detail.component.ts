import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'
import * as uuid from 'uuid'

@Component({
  selector: 'app-service-price-list-detail',
  templateUrl: './service-price-list-detail.component.html',
})
export class ServicePriceListDetailComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  editCache: any = {}
  listOfData: any[] = []
  dataSearch: any = {}
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Address, enumData.DataType.Km, enumData.DataType.Time]
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  currentUser: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: string,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.statusId = enumData.StatusFilter.Active.value
    this.searchData()
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.SETTING_001.code
  }

  startEdit(id: string) {
    this.editCache[id].edit = true
  }

  cancelEdit(id: string) {
    const index = this.listOfData.findIndex((c) => c.id === id)
    if (this.editCache[id].data.isNew !== true) {
      this.editCache[id] = {
        data: { ...this.listOfData[index] },
        edit: false,
      }
    } else {
      this.listOfData = this.listOfData.filter((d) => d.id !== id)
      delete this.editCache[id]
    }
  }

  saveEdit(id: string) {
    this.notifyService.showloading()
    const index = this.listOfData.findIndex((c) => c.id === id)
    if (!this.editCache[id].data.name) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }
    if (this.editCache[id].data.name.length > 250) {
      this.notifyService.showError(enumData.Warnings.TooLong)
      return
    }
    if (!this.editCache[id].data.type) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }
    if (this.editCache[id].data.value) this.editCache[id].data.value += ''
    if (this.editCache[id].data.value == null || this.editCache[id].data.value.trim().length === 0) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }

    let url = this.apiService.SERVICE_SETTING.PRICELISTDETAIL_UPDATE
    if (this.editCache[id].data.isNew === true) {
      delete this.editCache[id].data.id
      url = this.apiService.SERVICE_SETTING.PRICELISTDETAIL_CREATE
    }

    this.apiService.post(url, this.editCache[id].data).then((result) => {
      if (result) {
        this.editCache[id].edit = false
        if (!this.editCache[id].data.isNew) {
          Object.assign(this.listOfData[index], this.editCache[id].data)
        } else if (result.id) {
          const item = this.editCache[id].data
          item.id = result.id
          delete item.isNew
          Object.assign(this.listOfData[index], item)

          delete this.editCache[id]
          this.editCache[item.id] = {
            edit: false,
            data: { ...item },
          }
        }
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      }
    })
  }

  updateEditCache() {
    this.listOfData.forEach((item) => {
      this.editCache[item.id] = {
        edit: false,
        data: { ...item },
      }
    })
  }

  addRow() {
    const item = {
      id: uuid.v4(),
      name: '',
      value: '',
      type: enumData.DataType.String.code,
      servicePriceId: this.data,
      isNew: true,
    }
    this.listOfData = [...this.listOfData, item]
    this.editCache[item.id] = {
      edit: true,
      data: { ...item },
    }
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true
    const where: any = {}
    where.servicePriceId = this.data
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }

    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICELISTDETAIL_PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        this.updateEditCache()
      }
    })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICELISTDETAIL_DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      data.isDeleted = !data.isDeleted
      this.editCache[data.id].data.isDeleted = !this.editCache[data.id].data.isDeleted
      if (this.dataSearch.statusId > 0) {
        if (
          (this.dataSearch.statusId === enumData.StatusFilter.Active.value && data.isDeleted) ||
          (this.dataSearch.statusId === enumData.StatusFilter.InActive.value && !data.isDeleted)
        ) {
          this.listOfData = this.listOfData.filter((c) => c.id !== data.id)
        }
      }
    })
  }
}
