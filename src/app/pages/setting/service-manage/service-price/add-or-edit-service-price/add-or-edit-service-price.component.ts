import { Component, OnInit, Inject, Optional } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../../services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './add-or-edit-service-price.component.html' })
export class AddOrEditServicePriceComponent implements OnInit {
  modalTitle = 'Thêm mới bảng chào giá'
  dataObject: any = {}
  lstUnit: any[] = []
  lstCurrency: any[] = []
  dataPrice: any[] = []
  dataPrice2: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditServicePriceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    await this.loadDataServicePrice()
    await this.loadAllList()
    if (this.data && this.data !== null) {
      if (this.data.id) {
        this.modalTitle = 'Chỉnh sửa bảng chào giá'
        // edit
        this.dataObject = { ...this.data }
      } else {
        this.dataObject.parentId = this.data.parentId
        this.dataObject.serviceId = this.data.serviceId
        this.dataObject.type = enumData.DataType.Number.code
        this.dataObject.currency = this.data.currency
        this.dataObject.isSetup = false
        this.dataObject.isTemplate = false
        this.dataObject.isRequired = true
        this.dataObject.number = 0
      }
    }
  }

  async loadDataServicePrice() {
    await this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_FIND, { serviceId: this.data.serviceId }).then((result) => {
      this.dataPrice = result
      this.dataPrice2 = []
    })
  }

  onChangeParent() {
    this.dataObject.parentId2 = null
    this.dataPrice2 = []
    if (this.dataObject.parentId) {
      var obj = this.dataPrice.find((c) => c.id == this.dataObject.parentId)
      if (obj) this.dataPrice2 = obj.__childs__
    }
  }

  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.parentId2 && this.dataObject.parentId2 !== '') {
      this.dataObject.level = 3
      this.dataObject.parentId = this.dataObject.parentId2
    } else if (this.dataObject.parentId && this.dataObject.parentId !== '') this.dataObject.level = 2
    else this.dataObject.level = 1

    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(true)
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.SERVICE_SETTING.PRICE_UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(true)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
