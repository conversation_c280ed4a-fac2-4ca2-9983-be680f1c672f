<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8">
    <nz-col nzSpan="8">
      <input nz-input [(ngModel)]="dataSearch.name" placeholder="Lọc tên" />
    </nz-col>

    <nz-col nzSpan="8">
      <nz-select
        nzShowSearch
        nzAllowClear
        [(ngModel)]="dataSearch.statusId"
        name="statusId"
        [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'"
      >
        <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
      </nz-select>
    </nz-col>

    <nz-col nzSpan="8">
      <button nz-button (click)="searchData(true)">
        <span nz-icon nzType="search"></span>
        {{ language_key?.SEARCH || 'Tìm kiếm' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row nzGutter="8" class="mt-2">
    <nz-col nzSpan="24">
      <button nz-button (click)="searchData(true)" class="mr-2">Làm mới</button>
      <button nz-button (click)="addRow()" *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nzType="primary">
        <span nz-icon nzType="plus"></span>Thêm
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-1">
    <nz-table
      nz-col
      nzSpan="24"
      class="mb-3"
      #basicTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr>
          <th class="mw-30">Mã cột</th>
          <th class="mw-30">Tên cột</th>
          <th class="mw-30">Công thức</th>
          <th>Kiểu dữ liệu</th>
          <th>Loại cột</th>
          <th nzWidth="50px">Bắt buộc?</th>
          <th nzWidth="80px">Thứ tự cột</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data">
          <td class="mw-30">
            <ng-container *ngIf="!editCache[data.id].edit; else codeInputTpl">
              {{ data.code }}
            </ng-container>
            <ng-template #codeInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[data.id].data.code" placeholder="Nhập 1-50 kí tự" />
            </ng-template>
          </td>
          <td class="mw-30">
            <ng-container *ngIf="!editCache[data.id].edit; else nameInputTpl">
              {{ data.name }}
            </ng-container>
            <ng-template #nameInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[data.id].data.name" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>
          <td class="mw-30">
            <ng-container *ngIf="!editCache[data.id].edit; else fomularInputTpl">
              {{ data.fomular }}
            </ng-container>
            <ng-template #fomularInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[data.id].data.fomular" placeholder="Nhập công thức" />
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[data.id].edit; else typeInputTpl">
              {{ data.typeName }}
            </ng-container>
            <ng-template #typeInputTpl>
              <nz-select
                nzShowSearch
                nzAllowClear
                nz-col
                nzSpan="24"
                [(ngModel)]="editCache[data.id].data.type"
                name="type"
                required
                nzPlaceHolder="Chọn kiểu dữ liệu"
                class="mr-2"
              >
                <nz-option *ngFor="let item of lstDataType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[data.id].edit; else colTypeInputTpl">
              {{ data.colTypeName }}
            </ng-container>
            <ng-template #colTypeInputTpl>
              <nz-select
                nzShowSearch
                nzAllowClear
                nz-col
                nzSpan="24"
                [(ngModel)]="editCache[data.id].data.colType"
                name="colType"
                required
                nzPlaceHolder="Chọn loại cột"
                class="mr-2"
              >
                <nz-option *ngFor="let item of dataColType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[data.id].edit; else isRequiredInputTpl">
              <label nz-checkbox nzDisabled [ngModel]="data.isRequired"></label>
            </ng-container>
            <ng-template #isRequiredInputTpl>
              <label nz-checkbox [(ngModel)]="editCache[data.id].data.isRequired"></label>
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[data.id].edit; else sortInputTpl">
              {{ data.sort > 0 ? data.sort : '' }}
            </ng-container>
            <ng-template #sortInputTpl>
              <input type="number" nz-input [(ngModel)]="editCache[data.id].data.sort" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>

          <td class="text-nowrap">
            <div class="editable-row-operations">
              <ng-container *ngIf="!editCache[data.id].edit; else saveTpl">
                <button
                  nz-tooltip
                  nzTooltipTitle="Sửa"
                  class="mr-2"
                  nz-button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  (click)="startEdit(data.id)"
                >
                  <span nz-icon nzType="edit"></span>
                </button>
                <button
                  *ngIf="data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(data)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'"
                  class="mr-2"
                  nz-button
                  nzDanger
                >
                  <span nz-icon nzType="stop"></span>
                </button>
                <button
                  *ngIf="!data.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(data)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
                  class="mr-2"
                  nz-button
                  nzType="primary"
                >
                  <span nz-icon nzType="play-circle"></span>
                </button>
              </ng-container>
              <ng-template #saveTpl>
                <button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.SAVE || 'Lưu'"
                  class="mr-2"
                  nz-button
                  nzType="primary"
                  (click)="saveEdit(data.id)"
                >
                  <span nz-icon nzType="save"></span>
                </button>
                <button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.CANCEL || 'Hủy'"
                  class="mr-2"
                  nz-button
                  nzDanger
                  (click)="cancelEdit(data.id)"
                >
                  <span nz-icon nzType="close"></span>
                </button>
              </ng-template>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
  </nz-row>
</div>
