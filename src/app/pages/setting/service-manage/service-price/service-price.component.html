<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ 'Thông tin bảng chào giá' | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="8">
    <nz-col nzSpan="8">
      <input nz-input [(ngModel)]="dataSearch.name" placeholder="Tìm kiếm theo tên hạng mục" />
    </nz-col>
    <nz-col nzSpan="8">
      <nz-select
        nzShowSearch
        nzAllowClear
        [(ngModel)]="dataSearch.statusId"
        name="statusId"
        [nzPlaceHolder]="language_key?.CHOOSE_STATUS || 'Chọn trạng thái'"
      >
        <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="8">
      <button nz-button (click)="searchData(true)">
        <span nz-icon nzType="search"></span>
        {{ language_key?.SEARCH || 'Tìm kiếm' }}
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-col nzSpan="21">
      <button
        *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
        nz-button
        (click)="clickAdd()"
        nzType="primary"
        class="mr-2"
      >
        <span nz-icon nzType="plus"></span>
        {{ language_key?.ADD || 'Thêm mới' }}
      </button>
      <button
        nz-button
        *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
        (click)="settingPriceCol()"
        nzType="dashed"
        class="mr-2"
      >
        <span nz-icon nzType="setting"></span> Cấu hình cột
      </button>
      <button
        nz-button
        *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
        (click)="settingFomularCalValue()"
        nzType="dashed"
        class="mr-2"
      >
        <span nz-icon nzType="calculator"></span>
        Cấu hình công thức tính đơn giá
      </button>
      <button
        nz-button
        *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)"
        (click)="settingPriceCalWay()"
        nzType="dashed"
        class="mr-2"
      >
        <span nz-icon nzType="calculator"></span>
        Cấu hình cách tính điểm giá
      </button>
      <button
        *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
        nz-button
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn xoá tất cả hạng mục chào giá?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="clickDeleteAllRow()"
        nzDanger
        class="mr-2"
      >
        <span nz-icon nzType="delete"></span> Xoá tất cả hạng mục chào giá
      </button>
      <button *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button (click)="clickExportExcel()" class="mr-2">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
      <input
        *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
        class="hidden"
        type="file"
        id="file"
        (change)="clickImportExcel($event)"
        onclick="this.value=null"
        accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
      />
      <label *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" for="file" class="lable-custom-file">
        <span nz-icon nzType="upload"></span>
        {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
      </label>
    </nz-col>
    <nz-col nzSpan="3">
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="currency" name="currency" [nzPlaceHolder]="'Chọn đơn vị tiền tệ'">
        <nz-option *ngFor="let item of lstCurrency" [nzLabel]="item.code" [nzValue]="item.code"></nz-option>
      </nz-select>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-table
      nz-col
      nzSpan="24"
      id="test-html-table"
      class="mb-3"
      #expandTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr>
          <th style="width: 120px">{{ language_key?.NO || 'STT' }}</th>
          <th>Tên hạng mục</th>
          <th *ngFor="let col of servicePriceCol" class="dynamic-col-mpo">
            {{ col.name }}
          </th>
          <th>Đơn vị tính</th>
          <th>Đơn vị tiền tệ</th>
          <th nz-tooltip nzTooltipTitle="Mã cột để làm công thức: [qty]">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th>Bắt buộc?</th>
          <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <!-- level 1 -->
        <ng-container *ngFor="let data1 of expandTable.data">
          <tr>
            <td (click)="clickEdit(data1)">{{ data1.sort > 0 ? data1.sort : '' }}</td>
            <td (click)="clickEdit(data1)" class="mw-25">{{ data1.name }}</td>
            <td *ngFor="let col of servicePriceCol">{{ data1[col.id] }}</td>
            <td (click)="clickEdit(data1)">{{ data1.unit }}</td>
            <td (click)="clickEdit(data1)">{{ data1.currency }}</td>
            <td class="text-right" (click)="clickEdit(data1)">{{ data1.number | number }}</td>
            <td (click)="clickEdit(data1)">{{ data1.isRequired ? 'Bắt buộc' : 'Không' }}</td>
            <td class="text-nowrap">
              <button
                *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                nz-tooltip
                nzTooltipTitle="Thiết lập các thông tin mở rộng"
                class="mr-2"
                nz-button
                [nzType]="data1.__servicePriceListDetails__ && data1.__servicePriceListDetails__.length > 0 ? 'default' : 'dashed'"
                (click)="settingExInfo(data1.id)"
              >
                <span nz-icon nzType="plus-circle"></span>
              </button>
              <button
                *ngIf="data1.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                nz-popconfirm
                nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
                nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="setActiveItem(data1)"
                nz-tooltip
                [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'"
                class="mr-2"
                nz-button
                nzDanger
              >
                <span nz-icon nzType="stop"></span>
              </button>
              <button
                *ngIf="!data1.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                nz-popconfirm
                nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
                nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="setActiveItem(data1)"
                nz-tooltip
                [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
                class="mr-2"
                nz-button
                nzType="primary"
              >
                <span nz-icon nzType="play-circle"></span>
              </button>
            </td>
          </tr>
          <!-- level 2 -->
          <ng-container *ngFor="let data2 of data1.__childs__">
            <tr>
              <td (click)="clickEdit(data2)" [nzIndentSize]="5">{{ data2.sort > 0 ? data2.sort : '' }}</td>
              <td class="mw-25" (click)="clickEdit(data2)">{{ data2.name }}</td>
              <td *ngFor="let col of servicePriceCol">{{ data2[col.id] }}</td>
              <td (click)="clickEdit(data2)">{{ data2.unit }}</td>
              <td (click)="clickEdit(data2)">{{ data2.currency }}</td>
              <td class="text-right" (click)="clickEdit(data2)">{{ data2.number | number }}</td>
              <td (click)="clickEdit(data2)">{{ data2.isRequired ? 'Bắt buộc' : 'Không' }}</td>
              <td class="text-nowrap">
                <button
                  *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                  nz-tooltip
                  nzTooltipTitle="Thiết lập các thông tin mở rộng"
                  class="mr-2"
                  nz-button
                  [nzType]="data2.__servicePriceListDetails__ && data2.__servicePriceListDetails__.length > 0 ? 'default' : 'dashed'"
                  (click)="settingExInfo(data2.id)"
                >
                  <span nz-icon nzType="plus-circle"></span>
                </button>
                <button
                  *ngIf="data2.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(data2)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'"
                  class="mr-2"
                  nz-button
                  nzDanger
                >
                  <span nz-icon nzType="stop"></span>
                </button>
                <button
                  *ngIf="!data2.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(data2)"
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
                  class="mr-2"
                  nz-button
                  nzType="primary"
                >
                  <span nz-icon nzType="play-circle"></span>
                </button>
              </td>
            </tr>
            <!-- level 3 -->
            <ng-container *ngFor="let data3 of data2.__childs__">
              <tr>
                <td (click)="clickEdit(data3)" [nzIndentSize]="30">{{ data3.sort > 0 ? data3.sort : '' }}</td>
                <td class="mw-25" (click)="clickEdit(data3)">{{ data3.name }}</td>
                <td *ngFor="let col of servicePriceCol">{{ data3[col.id] }}</td>
                <td (click)="clickEdit(data3)">{{ data3.unit }}</td>
                <td (click)="clickEdit(data3)">{{ data3.currency }}</td>
                <td class="text-right" (click)="clickEdit(data3)">{{ data3.number | number }}</td>
                <td (click)="clickEdit(data3)">{{ data3.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                <td class="text-nowrap">
                  <button
                    *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
                    nz-tooltip
                    nzTooltipTitle="Thiết lập các thông tin mở rộng"
                    class="mr-2"
                    nz-button
                    [nzType]="data3.__servicePriceListDetails__ && data3.__servicePriceListDetails__.length > 0 ? 'default' : 'dashed'"
                    (click)="settingExInfo(data3.id)"
                  >
                    <span nz-icon nzType="plus-circle"></span>
                  </button>
                  <button
                    *ngIf="data3.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                    nz-popconfirm
                    nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="setActiveItem(data3)"
                    nz-tooltip
                    [nzTooltipTitle]="language_key?.DISACTIVE || 'Ngưng hoạt động'"
                    nz-button
                    nzDanger
                  >
                    <span nz-icon nzType="stop"></span>
                  </button>
                  <button
                    *ngIf="!data3.isDeleted && authenticationService.checkPermission([enumRole], action.Active.code)"
                    nz-popconfirm
                    nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="setActiveItem(data3)"
                    nz-tooltip
                    [nzTooltipTitle]="language_key?.ACTIVE || 'Đang hoạt động'"
                    nz-button
                    nzType="primary"
                  >
                    <span nz-icon nzType="play-circle"></span>
                  </button>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
  </nz-row>
</div>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Thiết lập công thức tính đơn giá" (nzOnCancel)="handleCancel()" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Công thức:</h4>
      <nz-col nzSpan="24" class="text-center">
        <input nz-input placeholder="Nhập công thức tính đơn giá" [(ngModel)]="data.fomular" />
      </nz-col>
    </nz-row>
    <nz-row class="mt-5">
      <nz-col nzSpan="24" class="text-center">
        <button
          *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
          nz-popconfirm
          nzPopconfirmTitle="Xác nhận lưu công thức?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="handleOk()"
          nz-button
          class="ant-btn-blue"
        >
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>

<nz-modal
  [(nzVisible)]="isVisibleSettingWayCalScorePrice"
  nzTitle="Thiết lập cách tính điểm giá"
  (nzOnCancel)="cancelSettingPriceCalWay()"
  [nzFooter]="null"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Cách tính điểm giá:</h4>
      <nz-col nzSpan="24">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSettingWayCalScorePrice.wayCalScorePrice"
          name="wayCalScorePrice"
          [nzPlaceHolder]="'Chọn cách tính điểm giá'"
        >
          <nz-option *ngFor="let item of dataPriceScoreCalculateWay" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <nz-row class="mt-5">
      <nz-col nzSpan="24" class="text-center">
        <button
          *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
          nz-popconfirm
          nzPopconfirmTitle="Xác nhận lưu cách tính điểm giá?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="saveSettingPriceCalWay()"
          nz-button
          class="ant-btn-blue"
        >
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>
