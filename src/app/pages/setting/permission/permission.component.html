<nz-row nzGutter="8">
  <nz-col nzSpan="12">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.userId" name="userId" (ngModelChange)="loadData()" nzPlaceHolder="Chọn nhân viên">
      <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name" [nzValue]="item.userId"> </nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="12" *ngIf="dataObject.userId">
    <button nz-button nzType="primary" (click)="saveData()">
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3" *ngIf="dataObject.userId">
  <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th nzWidth="180px" class="text-center">
          Mã tính năng
          <div>
            <input (ngModelChange)="filterFeature()" nz-input [(ngModel)]="dataSearch.code" placeholder="Nhập mã tính năng" />
          </div>
        </th>
        <th nzWidth="450px" class="text-center">
          Tên tính năng
          <div>
            <input (ngModelChange)="filterFeature()" nz-input [(ngModel)]="dataSearch.name" placeholder="Nhập tên tính năng" />
          </div>
        </th>
        <th nzWidth="70px" class="text-center">
          View
          <div nz-checkbox [(ngModel)]="allView" (ngModelChange)="onCheckboxChange($event, 'isView')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          Create
          <div nz-checkbox [(ngModel)]="allCreate" (ngModelChange)="onCheckboxChange($event, 'isCreate')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          Update
          <div nz-checkbox [(ngModel)]="allUpdate" (ngModelChange)="onCheckboxChange($event, 'isUpdate')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          Delete
          <div nz-checkbox [(ngModel)]="allDelete" (ngModelChange)="onCheckboxChange($event, 'isDelete')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          Print
          <div nz-checkbox [(ngModel)]="allPrint" (ngModelChange)="onCheckboxChange($event, 'isPrint')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          Export
          <div nz-checkbox [(ngModel)]="allExport" (ngModelChange)="onCheckboxChange($event, 'isExport')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          Import
          <div nz-checkbox [(ngModel)]="allImport" (ngModelChange)="onCheckboxChange($event, 'isImport')"></div>
        </th>
        <th nzWidth="70px" class="text-center">
          SetActive
          <div nz-checkbox [(ngModel)]="allActive" (ngModelChange)="onCheckboxChange($event, 'isActive')"></div>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfDataFilter">
        <td>{{ data.code }}</td>
        <td>{{ data.name }}</td>
        <td class="text-center">
          <label nz-checkbox style="outline: none" *ngIf="data.showView" [(ngModel)]="data.isView"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showCreate" [(ngModel)]="data.isCreate"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showUpdate" [(ngModel)]="data.isUpdate"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showDelete" [(ngModel)]="data.isDelete"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showPrint" [(ngModel)]="data.isPrint"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showExport" [(ngModel)]="data.isExport"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showImport" [(ngModel)]="data.isImport"></label>
        </td>
        <td class="text-center">
          <label nz-checkbox *ngIf="data.showActive" [(ngModel)]="data.isActive"></label>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>
