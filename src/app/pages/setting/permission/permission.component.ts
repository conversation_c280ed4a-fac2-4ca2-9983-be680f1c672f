import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Subscription } from 'rxjs'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { FADE_CLASS_NAME_MAP } from 'ng-zorro-antd/modal'

@Component({ templateUrl: './permission.component.html' })
export class PermissionComponent implements OnInit, OnDestroy {
  pageSize = enumData.Page.pageSizeMax
  dataEmployee: any[] = []
  listOfData = this.coreService.convertObjToArray(enumData.Role)
  language_key: any
  subscriptions: Subscription = new Subscription()
  loading = true
  dataObject: any = {}
  enumProject: any
  currentUser: any
  isCanSave = false
  allView = false
  allCreate = false
  allUpdate = false
  allDelete = false
  allPrint = false
  allExport = false
  allImport = false
  allActive = false
  dataSearch: any = {}
  listOfDataFilter: any = []
  constructor(
    private coreService: CoreService,
    private notifyService: NotifyService,
    private apiService: ApiService,
    private storageService: StorageService,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => {
      this.currentUser = x
      this.enumProject = x.enumProject
    })
  }

  ngOnInit() {
    this.isCanSave = this.authenticationService.checkPermission([this.enumProject.Features.SETTING_006.code], this.enumProject.Action.Create.code)
    this.loadEmployee()
    this.loadData()
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe()
  }
  loadEmployee() {
    this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((res) => {
      this.dataEmployee = res
    })
  }

  loadData() {
    this.loading = false
    this.listOfData = []
    if (!this.dataObject.userId) return

    this.loading = true
    this.apiService.post(this.apiService.AUTH.LOAD_PERMISSION_EMPLOYEE, this.dataObject).then((res) => {
      this.loading = false
      this.listOfData = res || []
      this.listOfDataFilter = this.listOfData
    })
  }

  saveData() {
    this.notifyService.showloading()
    if (!this.dataObject.userId) {
      this.notifyService.showError('Vui lòng chọn nhân viên trước!')
      return
    }
    if (!this.isCanSave) {
      this.notifyService.showError('Bạn chưa được cấp quyền thực hiện chức năng này!')
      return
    }
    const lstData = this.listOfData
      .filter((c) => c.isView || c.isCreate || c.isUpdate || c.isDelete || c.isPrint || c.isExport || c.isImport || c.isActive)
      .map((c) => {
        return {
          code: c.code,
          isView: c.isView,
          isCreate: c.isCreate,
          isUpdate: c.isUpdate,
          isDelete: c.isDelete,
          isPrint: c.isPrint,
          isExport: c.isExport,
          isImport: c.isImport,
          isActive: c.isActive,
        }
      })

    this.apiService.post(this.apiService.AUTH.SAVE_PERMISSION_EMPLOYEE, { userId: this.dataObject.userId, lstData }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.dataObject.userId = null
    })
  }

  onCheckboxChange(event: boolean, fieldName: string) {
    for (const data of this.listOfData) {
      data[fieldName] = event
    }
  }

  filterFeature() {
    const searchCode = this.dataSearch.code ? this.dataSearch.code.toLowerCase() : ''
    const searchName = this.dataSearch.name ? this.dataSearch.name.toLowerCase() : ''
    this.listOfDataFilter = this.listOfData.filter(
      (item) => item.code.toLowerCase().includes(searchCode) && item.name.toLowerCase().includes(searchName)
    )
  }
}
