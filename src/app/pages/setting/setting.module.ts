import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { SettingRoutingModule } from './setting-routing.module'

// ------------ ANT -------------------
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzDividerModule } from 'ng-zorro-antd/divider'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { CURRENCY_MASK_CONFIG, CurrencyMaskConfig, CurrencyMaskModule } from 'ng2-currency-mask'
import { MaterialModule } from '../../app.module'
export const CustomCurrencyMaskConfig: CurrencyMaskConfig = {
  align: 'left',
  allowNegative: true,
  decimal: '.',
  precision: 0,
  prefix: '',
  suffix: '',
  thousands: ',',
}
// ------------- Create Component ---------------------
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { NgxJsonViewerModule } from 'ngx-json-viewer'
import { AddOrEditAddressComponent } from './config-master-data/address/add-or-edit-address/add-or-edit-address.component'
import { AddressComponent } from './config-master-data/address/address.component'
import { AddOrEditBillLookupComponent } from './config-master-data/bill-lookup/add-or-edit-bill-lookup/add-or-edit-bill-lookup.component'
import { BillLookupComponent } from './config-master-data/bill-lookup/bill-lookup.component'
import { AddOrEditBranchComponent } from './config-master-data/branch/add-or-edit-branch/add-or-edit-branch.component'
import { AddOrEditBranchMemberComponent } from './config-master-data/branch/branch-member/add-or-edit-branch-member/add-or-edit-branch-member.component'
import { BranchMemberComponent } from './config-master-data/branch/branch-member/branch-member.component'
import { BranchComponent } from './config-master-data/branch/branch.component'
import { AddOrEditBannerComponent } from './config-master-data/client/banner/add-or-edit-banner/add-or-edit-banner.component'
import { BannerComponent } from './config-master-data/client/banner/banner.component'
import { ClientComponent } from './config-master-data/client/client.component'
import { AddOrEditFooterComponent } from './config-master-data/client/footer/add-or-edit-footer/add-or-edit-footer.component'
import { FooterComponent } from './config-master-data/client/footer/footer.component'
import { AddOrEditHeaderComponent } from './config-master-data/client/header/add-or-edit-header/add-or-edit-header.component'
import { HeaderComponent } from './config-master-data/client/header/header.component'
import { AddOrEditLinkComponent } from './config-master-data/client/link/add-or-edit-link/add-or-edit-link.component'
import { LinkComponent } from './config-master-data/client/link/link.component'
import { AddOrEditCompanyComponent } from './config-master-data/company/add-or-edit-company/add-or-edit-company.component'
import { CompanyComponent } from './config-master-data/company/company.component'
import { AddOrEditCurrencyComponent } from './config-master-data/currency/add-or-edit-currency/add-or-edit-currency.component'
import { CurrencyComponent } from './config-master-data/currency/currency.component'
import { AddOrEditDepartmentComponent } from './config-master-data/department/add-or-edit-department/add-or-edit-department.component'
import { DepartmentComponent } from './config-master-data/department/department.component'
import { AddOrEditEmailTemplateComponent } from './config-master-data/email-template/add-or-edit-email-template/add-or-edit-email-template.component'
import { EmailTemplateComponent } from './config-master-data/email-template/email-template.component'
import { AddOrEditEmployeeComponent } from './config-master-data/employee/add-or-edit-employee/add-or-edit-employee.component'
import { ChangePasswordEmployeeComponent } from './config-master-data/employee/change-password-employee/change-password-employee.component'
import { EmployeeComponent } from './config-master-data/employee/employee.component'
import { AddOrEditFaqCategoryComponent } from './config-master-data/faq-category/add-or-edit-faq-category/add-or-edit-faq-category.component'
import { FaqCategoryComponent } from './config-master-data/faq-category/faq-category.component'
import { AddOrEditFaqComponent } from './config-master-data/faq/add-or-edit-faq/add-or-edit-faq.component'
import { FaqComponent } from './config-master-data/faq/faq.component'
import { AddOrEditLocationComponent } from './config-master-data/location/add-or-edit-location/add-or-edit-location.component'
import { LocationDetailComponent } from './config-master-data/location/location-detail/location-detail.component'
import { LocationComponent } from './config-master-data/location/location.component'
import { AddOrEditBidFormModelComponent } from './config-master-data/master-bid-form/add-or-edit-bid-form/add-or-edit-bid-form.component'
import { MasterBidFormComponent } from './config-master-data/master-bid-form/master-bid-form.component'
import { AddOrEditBidGuaranteeFormComponent } from './config-master-data/master-bid-guarantee-form/add-or-edit-bid-guarantee-form/add-or-edit-bid-guarantee-form.component'
import { MasterBidGuaranteeFormComponent } from './config-master-data/master-bid-guarantee-form/master-bid-guarantee-form.component'
import { AddOrEditMaterialComponent } from './config-master-data/material/add-or-edit-material/add-or-edit-material.component'
import { MaterialComponent } from './config-master-data/material/material.component'
import { AddOrEditObjectComponent } from './config-master-data/object/add-or-edit-object/add-or-edit-object.component'
import { ObjectComponent } from './config-master-data/object/object.component'
import { AddOrUpdateProductProviderPriceComponent } from './config-master-data/product/add-or-update-product-provider-price/add-or-update-product-provider-price.component'
import { AddOrUpdateProductComponent } from './config-master-data/product/add-or-update-product/add-or-update-product.component'
import { AddOrUpdateProviderComponent } from './config-master-data/product/add-or-update-provider/add-or-update-provider.component'
import { ProductComponent } from './config-master-data/product/product.component'
import { AddOrEditServiceComponent } from './config-master-data/service/add-or-edit-service/add-or-edit-service.component'
import { ServiceComponent } from './config-master-data/service/service.component'
import { AddOrEditUnitComponent } from './config-master-data/unit/add-or-edit-unit/add-or-edit-unit.component'
import { UnitComponent } from './config-master-data/unit/unit.component'
import { AddOrEditWarehouseComponent } from './config-master-data/warehouse/add-or-edit-warehouse/add-or-edit-warehouse.component'
import { WarehouseComponent } from './config-master-data/warehouse/warehouse.component'
import { LanguageKeyComponent } from './language-key/language-key.component'
import { PermissionComponent } from './permission/permission.component'
import { AddOrEditServiceCapacityComponent } from './service-manage/service-capacity/add-or-edit-service-capacity/add-or-edit-service-capacity.component'
import { ServiceCapacityListDetailComponent } from './service-manage/service-capacity/service-capacity-list-detail/service-capacity-list-detail.component'
import { ServiceCapacityComponent } from './service-manage/service-capacity/service-capacity.component'
import { AddOrEditServiceCustomPriceComponent } from './service-manage/service-custom-price/add-or-edit-service-custom-price/add-or-edit-service-custom-price.component'
import { ServiceCustomPriceComponent } from './service-manage/service-custom-price/service-custom-price.component'
import { ServiceManageComponent } from './service-manage/service-manage.component'
import { AddOrEditServicePriceComponent } from './service-manage/service-price/add-or-edit-service-price/add-or-edit-service-price.component'
import { ServicePriceColComponent } from './service-manage/service-price/service-price-col/service-price-col.component'
import { ServicePriceListDetailComponent } from './service-manage/service-price/service-price-list-detail/service-price-list-detail.component'
import { ServicePriceComponent } from './service-manage/service-price/service-price.component'
import { ServiceRateComponent } from './service-manage/service-rate/service-rate.component'
import { AddOrEditServiceTechComponent } from './service-manage/service-tech/add-or-edit-service-tech/add-or-edit-service-tech.component'
import { ServiceTechListDetailComponent } from './service-manage/service-tech/service-tech-list-detail/service-tech-list-detail.component'
import { ServiceTechComponent } from './service-manage/service-tech/service-tech.component'
import { AddOrEditServiceTradeComponent } from './service-manage/service-trade/add-or-edit-service-trade/add-or-edit-service-trade.component'
import { ServiceTradeListDetailComponent } from './service-manage/service-trade/service-trade-list-detail/service-trade-list-detail.component'
import { ServiceTradeComponent } from './service-manage/service-trade/service-trade.component'
import { SupplierManageDeleteComponent } from './supplier-manage-delete/supplier-manage-delete.component'
import { CriteriaComponent } from './criteria/criteria.component'
import { AddOrEditCriteriaModelComponent } from './criteria/add-or-edit-criteria-model/add-or-edit-criteria-model.component'

@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    SettingRoutingModule,
    NzButtonModule,
    NzTableModule,
    NzDividerModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzInputNumberModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDatePickerModule,
    NzPopconfirmModule,
    NzPaginationModule,
    MaterialModule,
    ReactiveFormsModule,
    CurrencyMaskModule,
    NzCollapseModule,
    NzTagModule,
    NzUploadModule,
    NgxJsonViewerModule,
  ],
  declarations: [
    DepartmentComponent,
    AddOrEditDepartmentComponent,
    EmployeeComponent,
    AddOrEditEmployeeComponent,
    PermissionComponent,
    ServiceCapacityComponent,
    MasterBidFormComponent,
    AddOrEditBidFormModelComponent,
    AddressComponent,
    AddOrEditAddressComponent,
    CompanyComponent,
    AddOrEditCompanyComponent,
    MasterBidGuaranteeFormComponent,
    AddOrEditBidGuaranteeFormComponent,
    BannerComponent,
    AddOrEditBannerComponent,
    MaterialComponent,
    AddOrEditMaterialComponent,
    ServiceTechComponent,
    ServiceTradeComponent,
    AddOrEditServiceTechComponent,
    ServicePriceComponent,
    ClientComponent,
    LinkComponent,
    AddOrEditLinkComponent,
    FooterComponent,
    AddOrEditFooterComponent,
    ServiceManageComponent,
    LanguageKeyComponent,
    ChangePasswordEmployeeComponent,
    ServiceCapacityListDetailComponent,
    ServiceTechListDetailComponent,
    ServiceTradeListDetailComponent,
    ServicePriceListDetailComponent,
    AddOrEditServiceTradeComponent,
    AddOrEditServicePriceComponent,
    AddOrEditServiceCapacityComponent,
    UnitComponent,
    AddOrEditUnitComponent,
    HeaderComponent,
    AddOrEditHeaderComponent,
    EmailTemplateComponent,
    AddOrEditEmailTemplateComponent,
    CurrencyComponent,
    AddOrEditCurrencyComponent,
    ServiceCustomPriceComponent,
    AddOrEditServiceCustomPriceComponent,
    ServicePriceColComponent,
    FaqComponent,
    AddOrEditFaqComponent,
    FaqCategoryComponent,
    AddOrEditFaqCategoryComponent,
    ServiceRateComponent,
    SupplierManageDeleteComponent,
    ObjectComponent,
    AddOrEditObjectComponent,
    WarehouseComponent,
    AddOrEditWarehouseComponent,
    BranchComponent,
    BranchMemberComponent,
    AddOrEditBranchComponent,
    AddOrEditBranchMemberComponent,
    AddOrEditLocationComponent,
    LocationDetailComponent,
    LocationComponent,
    ServiceComponent,
    AddOrEditServiceComponent,
    ProductComponent,
    AddOrUpdateProductProviderPriceComponent,
    AddOrUpdateProviderComponent,
    AddOrUpdateProductComponent,
    BillLookupComponent,
    AddOrEditBillLookupComponent,
    CriteriaComponent,
    AddOrEditCriteriaModelComponent,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  exports: [],
})
export class SettingModule {}
