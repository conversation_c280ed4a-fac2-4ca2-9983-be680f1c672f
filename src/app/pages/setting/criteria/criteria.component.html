<div nz-row nzGutter="8">
  <button nz-col nzSpan="3" nzShape="round" nz-button nzType="primary" (click)="onShowAdd()" class="mr-2">
    <i nz-icon nzType="plus" nzTheme="outline"></i>Thêm mới
  </button>
  <button nzShape="round" class="mr-2" nz-button (click)="onDownloadTemplateExcel()">
    <span nz-icon nzType="download"></span>Tải Template Excel
  </button>
  <input
    class="hidden"
    type="file"
    id="file"
    (change)="clickImportExcel($event)"
    placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
  />
  <label nz-button for="file" class="ant-btn lable-custom-file-custom mr-2"> <span nz-icon nzType="upload"></span> Nhập Excel </label>
  <button class="mr-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
    <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
  </button>
</div>
<nz-row>
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã tiêu chí </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.code" placeholder="Lọc mã" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên tiêu chí </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input class="mr-2" nz-input [(ngModel)]="dataSearch.name" placeholder="Lọc tên" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng thái </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.isDeleted" name="isDeleted" nzPlaceHolder="Trạng thái">
                <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</nz-row>

<div nz-row nzGutter="8" class="mt-3">
  <div>
    <nz-table
      class="mb-3"
      nz-col
      nzSpan="24"
      #ajaxTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
      nzTableLayout="fixed"
    >
      <thead>
        <tr>
          <th>Mã tiêu chí</th>
          <th>Tên tiêu chí</th>
          <th nzWidth="500px">Nội dung</th>
          <th>Tác Vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td>{{ data.code }}</td>
          <td>{{ data.name }}</td>
          <td>{{ data.description }}</td>
          <td>
            <button nz-button nzShape="circle" class="mr-2" nzTooltipTitle="Chỉnh sửa" nzTooltipPlacement="top" nz-tooltip (click)="onShowEdit(data)">
              <i nz-icon nzType="edit"></i>
            </button>
            <button
              *ngIf="data.isDeleted"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="setActiveItem(data)"
              nz-tooltip
              nzTooltipTitle="Kích hoạt"
              nz-button
              nzType="primary"
              nzShape="circle"
            >
              <i nz-icon nzType="play-circle" nzTheme="outline"></i>
            </button>
            <button
              *ngIf="!data.isDeleted"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="setActiveItem(data)"
              nz-tooltip
              nzTooltipTitle="Ngưng hoạt động"
              nz-button
              nzType="primary"
              nzDanger
              nzShape="circle"
            >
              <i nz-icon nzType="stop"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>

  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
