import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditCriteriaModelComponent } from './add-or-edit-criteria-model/add-or-edit-criteria-model.component'
import * as XLSX from 'xlsx'
import { enumData } from 'src/app/core/enumData'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './criteria.component.html' })
export class CriteriaComponent implements OnInit {
  currentUser: User | any
  enumData: any
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  dataFilterStatus: any

  listOfData: any = []
  dataSearch: any = {}
  loading = true

  constructor(
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit(): void {
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_CRITERIA.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditCriteriaModelComponent, { disableClose: false })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditCriteriaModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CRITERIA.DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.enumData.Page.pageSizeMax,
    }
    this.apiScService.postRepair(this.apiScService.REPAIR_CRITERIA.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách tiêu chí')
        //#region Body Table
        const header = ['Mã tiêu chí', 'Tên tiêu chí', 'Mô tả', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
              worksheet.getColumn(colNumber).width = 40
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [data.code || '', data.name || '', data.description || '', data.isDeleted ? 'Ngưng hoạt động' : 'Đang hoạt động']

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_TIEU_CHI_LAM_MOI_PHONG_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách tiêu chí')

    //#region Body Table
    const header = ['Tên tiêu chí * (name)', 'Mô tả (description)']
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 40
          break
        case 2:
          worksheet.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet.getColumn(colNumber).width = 60
          break
        case 4:
          worksheet.getColumn(colNumber).width = 30
          break
        case 5:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `MAU_TIEU_CHI_LAM_MOI_PHONG_${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['name', 'description'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 1
        if (row.name) row.name = row.name.toString()
        if (!row.name) {
          strErr += `[ Dòng ${idx + 1} - Tên tiêu chí tính không được để trống ] <br>`
        }
        if (row.name && row.name?.length > 250) {
          strErr += 'Dòng ' + idx + ' - Tên tiêu chí tính không được lớn hơn 250 ký tự <br>'
        }
        if (strErr.length > 0) {
          this.notifyService.showError(strErr)
          return
        }
      }
      this.apiScService.postRepair(this.apiScService.REPAIR_CRITERIA.CREATE_DATA_BY_EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        if (result) {
          this.notifyService.showSuccess('Thêm file excel thành công')
          this.searchData()
        }
      })
    }
  }
}
