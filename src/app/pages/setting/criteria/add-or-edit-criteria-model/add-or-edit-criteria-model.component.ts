import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { ApiScService } from 'src/app/services/apiSc.service'

@Component({ templateUrl: './add-or-edit-criteria-model.component.html' })
export class AddOrEditCriteriaModelComponent implements OnInit {
  enumData: any
  modelTitle: any
  dataObject: any
  isEditItem = false
  formatter = (value: number): string => `${value ? value : 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  parser = (value: string): string => value!.replace(/[^\d]/g, '')

  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    private notifyService: NotifyService,
    private apiScService: ApiScService,
    private dialogRef: MatDialogRef<AddOrEditCriteriaModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    if (this.data && this.data !== null) {
      this.dataObject = this.data

      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT TIÊU CHÍ'
    } else {
      this.modelTitle = 'THÊM MỚI TIÊU CHÍ'
      this.dataObject = new Object()
    }
  }

  onSave() {
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CRITERIA.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.apiScService.postRepair(this.apiScService.REPAIR_CRITERIA.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
