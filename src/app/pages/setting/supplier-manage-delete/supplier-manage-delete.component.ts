import { Component, OnInit } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../services'
import { enumData } from '../../../core'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './supplier-manage-delete.component.html' })
export class SupplierManageDeleteComponent implements OnInit {
  dataObject: any = {}
  result: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
  }

  onConfirm() {
    this.notifyService.showloading()
    this.result = []
    const lstSupplierId = this.dataObject.suppliers.split('\n')
    this.apiService
      .post(this.apiService.SUPPLIER.DELETE, {
        lstSupplierId: lstSupplierId,
        confirmCode: this.dataObject.confirmCode,
      })
      .then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
        this.result = res
      })
  }
}
