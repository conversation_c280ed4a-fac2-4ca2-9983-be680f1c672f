<form nz-form #frmAdd="ngForm">

  <nz-row nzGutter="8">
    <nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">
          Nhà cung cấp cần xóa
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập nhà cung cấp cần xoá!">
          <textarea nz-input required name="suppliers" [(ngModel)]="dataObject.suppliers" rows="5" auto></textarea>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzRequired class="text-left">Mã xác nhận
        </nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mã xác nhận!">
          <input type="password" required nz-input name="confirmCode" [(ngModel)]="dataObject.confirmCode" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-row>
    <nz-col nzSpan="24" class="text-center">
      <button nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn xoá nhà cung cấp?" nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="onConfirm()" nz-button [disabled]="!frmAdd.form.valid" nzDanger>Xác nhận xoá</button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3" *ngIf="result && result.length > 0">
    Kết quả xoá:<br>
    <nz-col nzSpan="24" *ngFor="let res of result">- {{res}}</nz-col>
  </nz-row>
</form>