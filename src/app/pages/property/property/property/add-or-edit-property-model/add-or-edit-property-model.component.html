<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
  </div>

  <form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
    <nz-row class="mt-2" nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mã (1-36 kí tự)!">
            <input
              [disabled]="isEditItem"
              nz-input
              placeholder="Nhập mã 1-36 kí tự"
              [(ngModel)]="dataObject.code"
              name="code"
              required
              pattern=".{1,36}"
            />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên (1-50 kí tự)!">
            <input nz-input placeholder="Nhập tên 1-50 kí tự" [(ngModel)]="dataObject.name" name="name" required pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Loại tài sản </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn loại tài sản!">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn loại tài sản"
              [(ngModel)]="dataObject.propertyTypeId"
              name="propertyTypeId"
              required
            >
              <nz-option *ngFor="let item of lstPropertyType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Số ngày bảo trì định kì</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số ngày bảo trì định kì!">
            <input
              nz-input
              placeholder="0"
              [(ngModel)]="dataObject.numDayToMaintenance"
              name="numDayToMaintenance"
              (ngModelChange)="onChangeNumDayToMaintenance()"
              currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false }"
            />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8" *ngIf="dataObject.numDayToMaintenance > 0">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Tên Công Việc</nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24" nzErrorTip="Vui lòng chọn Loại Công Việc">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.jobCategoryId"
              name="jobCategoryId"
              nzPlaceHolder="Loại Công Việc"
              (ngModelChange)="onCheck()"
            >
              <nz-option *ngFor="let item of lstJobCategory" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" *ngIf="messageCheckJobTypeExistInApartment">
        <span nzSpan="24" class="text-left" style="color: red">Cảnh báo : {{ messageCheckJobTypeExistInApartment }} </span>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mô tả không quá 250 kí tự!">
            <textarea rows="2" nz-input placeholder="Nhập Mô tả" [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
    <nz-row>
      <nz-col nzSpan="24" class="text-center">
        <button nz-button [disabled]="!frmAdd.form.valid" nzShape="round" nzType="primary" class="mr-3" (click)="onSave()">
          <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
        </button>
      </nz-col>
    </nz-row>
  </form>
</div>
