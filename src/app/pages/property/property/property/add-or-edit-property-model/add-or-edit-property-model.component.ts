import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../../services/authentication.service'
import { NotifyService } from '../../../../../services/notify.service'
import { enumData } from 'src/app/core/enumData'

/** Popup tạo/sửa tài sản */
@Component({ templateUrl: './add-or-edit-property-model.component.html' })
export class AddOrEditPropertyModelComponent implements OnInit {
  enumData: any
  modelTitle = 'THÊM MỚI TÀI SẢN'
  dataObject: any = {}
  isEditItem = false
  lstPropertyType: any[] = []
  lstJobCategory: any[] = []
  messageCheckJobTypeExistInApartment = ''
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditPropertyModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    if (this.data && this.data !== null) {
      this.dataObject = this.data
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT TÀI SẢN'
    }
    this.loadAllData()
  }

  async loadAllData() {
    const rs = await Promise.all([
      this.appService.post(this.appService.PROPERTY_TYPE.LOAD_DATA, {}),
      this.appService.postRepair(this.appService.REPAIR_JOB_CATEGORY.LOAD_DATA, { isMaintain: true }),
    ])

    if (rs) {
      this.lstPropertyType = rs[0]
      this.lstJobCategory = rs[1]
    }

    this.onCheck()
  }

  onChangeNumDayToMaintenance() {
    if (this.dataObject.numDayToMaintenance > 0 && this.lstJobCategory.length == 0) {
      this.notifyService.showError(`Vui lòng khai báo công việc với loại sửa chữa "Bảo trì phòng" trước`)
    }
  }

  /** Kiểm tra công việc thuộc loại SC đã được phân khu có tài sản này hết chưa */
  onCheck() {
    if (!this.dataObject.jobCategoryId || !this.dataObject.id) return
    this.notifyService.showloading()
    this.appService
      .postRepair(this.appService.REPAIR_JOB_TYPE_APARTMENT_EMPLOYEE.CHECK_JOB_TYPE_EXIST_IN_APARTMENT, {
        jobCategoryId: this.dataObject.jobCategoryId,
        propertyId: this.dataObject.id,
      })
      .then((result: any) => {
        this.notifyService.hideloading()
        this.messageCheckJobTypeExistInApartment = result?.message
      })
  }

  onSave() {
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
