import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import * as XLSX from 'xlsx'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { AddOrEditPropertyModelComponent } from './add-or-edit-property-model/add-or-edit-property-model.component'
@Component({ templateUrl: './property.component.html' })
export class PropertyComponent implements OnInit {
  role: any
  currentUser: User | any
  enumData: any
  modalTitle: string = ''
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstPropertyType: any = []

  constructor(
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private router: Router,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      const key = 'name'
      where[key] = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      const key = 'code'
      where[key] = this.dataSearch.code
    }
    if (this.dataSearch.propertyTypeId && this.dataSearch.propertyTypeId !== '') {
      const key = 'propertyTypeId'
      where[key] = this.dataSearch.propertyTypeId
    }

    where.isDeleted = this.dataSearch.isDeleted
    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.PROPERTY.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit(): void {
    this.modalTitle = enumData.Constants.Model_Add
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = false
    this.searchData()
    this.loadAllData()
  }

  async loadAllData() {
    this.lstPropertyType = await this.appService.post(this.appService.PROPERTY_TYPE.LOAD_DATA, {})
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditPropertyModelComponent, { disableClose: false })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditPropertyModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY.ACTIVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách tài sản')

    //#region Body Table
    const header = ['Mã tài sản * (code)', 'Tên tài sản * (name)', 'Mã loại tài sản * (propertyTypeCode)', 'Mô tả (description)']
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet.getColumn(colNumber).width = 30
          break
        case 4:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    const worksheet2 = workbook.addWorksheet('Danh sách loại tài sản')

    //#region Body Table
    const header2 = ['Mã loại tài sản * (code)', 'Tên loại tài sản * (name)']
    const headerRow2 = worksheet2.addRow(header2)

    // Add Data and Conditional Formatting
    for (let data of this.lstPropertyType) {
      const rowData = [data.code || '', data.name || '']
      worksheet2.addRow(rowData)
    }

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_TAI_SAN${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  async clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['code', 'name', 'propertyTypeCode', 'description'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.code == null || (typeof row.code === 'string' && row.code.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã tài sản được để trống  <br>'
        }
        if (row.code && row.code?.length > 50) {
          strErr += 'Dòng ' + idx + ' - Mã tài sản không được lớn hơn 50 ký tự <br>'
        }
        if (row.name == null || (typeof row.name === 'string' && row.name.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Tên tài sản được để trống  <br>'
        }
        if (row.name && row.name?.length > 50) {
          strErr += 'Dòng ' + idx + ' - Tên tài sản không được lớn hơn 50 ký tự <br>'
        }
        if (row.propertyTypeCode == null || (typeof row.propertyTypeCode === 'string' && row.propertyTypeCode.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Loại tài sản không được để trống  <br>'
        }
        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }
      this.appService.post(this.appService.PROPERTY.CREATE_DATA_BY_EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        if (result) {
          this.notifyService.showSuccess('Thêm file excel thành công')
          this.searchData()
        }
      })
    }
  }

  async onDownloadExcel() {
    this.notifyService.showloading()

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      const key = 'name'
      where[key] = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      const key = 'code'
      where[key] = this.dataSearch.code
    }
    if (this.dataSearch.propertyTypeId && this.dataSearch.propertyTypeId !== '') {
      const key = 'propertyTypeId'
      where[key] = this.dataSearch.propertyTypeId
    }

    where.isDeleted = this.dataSearch.isDeleted

    where.isGetAll = true

    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.PROPERTY.PAGINATION, dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách tài sản')
        //#region Body Table
        const header = ['Mã', 'Tên', 'Loại tài sản', 'Mô Tả']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet.getColumn(colNumber).width = 30
              break
            case 2:
              worksheet.getColumn(colNumber).width = 30
              break
            case 3:
              worksheet.getColumn(colNumber).width = 30
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [data.code || '', data.name || '', data.propertyTypeName || '', data.description || '']

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_TAI_SAN_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
