<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
  </div>

  <div nz-row class="mt-4">
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã tài sản</nz-form-label>
        <nz-form-control>
          <b>{{ dataObject.code }}</b>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên tài sản</nz-form-label>
        <nz-form-control>
          <b>{{ dataObject.name }}</b>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">UnitId</nz-form-label>
        <nz-form-control>
          <b>{{ dataObject.unitId }}</b>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <nz-row>
    <div nz-col nzSpan="24" *ngIf="dataObject">
      <nz-table
        class="mb-3"
        nz-col
        nzSpan="24"
        #ajaxTable
        [nzData]="listOfData"
        [(nzPageSize)]="pageSize"
        [nzLoading]="loading"
        [nzShowPagination]="false"
        nzBordered
        nzTableLayout="fixed"
        [nzScroll]="{ y: '600px' }"
      >
        <thead>
          <tr>
            <th>Ngày thao tác</th>
            <th>Người thao tác</th>
            <th>Nội dung</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of listOfData; let i = index">
            <tr>
              <td class="text-center">{{ item.createdAt | date : 'dd/MM/yyyy HH:mm' }}</td>
              <td class="text-center">{{ item.createdBy }}</td>
              <td class="text-center">{{ item.description }}</td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>

    <div nz-col nzSpan="24" class="text-right">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()"
        (nzPageSizeChange)="searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
    </div>
  </nz-row>
</div>
