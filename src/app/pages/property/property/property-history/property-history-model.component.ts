import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './property-history-model.component.html' })
export class PropertyHistoryModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'LỊCH SỬ THAY ĐỔI'
  dataObject: any
  listOfData: any = []
  modalTitle: any
  pageIndex: any
  pageSize: any
  total: any
  dataFilterStatus: any
  dataSearch: any = {}
  loading = true
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private appService: ApiNtssService,
    private dialog: MatDialog,
    private dialogRef: MatDialogRef<PropertyHistoryModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    this.dataObject = this.data
    this.modalTitle = enumData.Constants.Model_Add
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true
    const where: any = {}

    where.propertyId = this.data.id

    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.PROPERTY_DETAIL.HISTORY_PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.listOfData = data[0]
        this.total = data[1]
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  setActiveItem(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY.ACTIVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }
}
