<nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="T<PERSON><PERSON>" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã tài sản </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.code" name="dataSearch.code" placeholder="Mã tài sản" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại tài sản </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.propertyTypeId" name="propertyTypeId" nzPlaceHolder="Chọn loại tài sản">
                <nz-option *ngFor="let item of lstPropertyType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên tài sản </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.propertyId" name="propertyId" nzPlaceHolder="Chọn loại tài sản">
                <nz-option *ngFor="let item of lstProperty" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu trọ </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataSearch.apartmentId"
                name="apartmentId"
                nzPlaceHolder="Chọn khu trọ"
                (ngModelChange)="onChangeApartment($event)"
              >
                <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Phòng </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.roomId" name="roomId" nzPlaceHolder="Phòng">
                <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Kho </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.warehouseId" name="warehouseId" nzPlaceHolder="Kho">
                <nz-option *ngFor="let item of lstWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhà cung cấp </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.supplierId" name="supplierId" nzPlaceHolder="Nhà cung cấp">
                <nz-option *ngFor="let item of lstSupplier" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tài sản chủ nhà </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.propHomeId" name="propHomeId" nzPlaceHolder="Tài sản chủ nhà">
                <nz-option *ngFor="let item of lstPropHomeOwner" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày đưa vào </nz-form-label>
            <nz-form-control [nzSm]="24" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.putInDate" name="putInDate"> </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày bảo hành </nz-form-label>
            <nz-form-control [nzSm]="24" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.warrantyDate" name="warrantyDate"> </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày hết hạn bảo hành </nz-form-label>
            <nz-form-control [nzSm]="24" [nzXs]="24">
              <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.warrantyExpirationDate" name="warrantyExpirationDate"> </nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
          <button class="ml-2" nz-button nzType="default" (click)="onDownload()" nzShape="round">
            <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</nz-row>
<nz-row class="mt-2">
  <nz-col nzSpan="24">
    <h3 class="text-right" style="color: black">Tổng số lượng: {{ totalQuantity | number }} - Tổng tiền: {{ totalAmount | number }} VNĐ</h3>
  </nz-col>
</nz-row>
<nz-row nzGutter="8">
  <nz-table class="mb-3 mt-2" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="table-thead">
        <th class="text-center" nzWidth="150px">Mã tài sản</th>
        <th class="text-center" nzWidth="200px">Tên tài sản</th>
        <th class="text-center" nzWidth="250px">Loại tài sản</th>
        <th class="text-center" nzWidth="150px">UnitId</th>
        <th class="text-center" nzWidth="120px">Đơn giá</th>
        <th class="text-center" nzWidth="250px">Nhà cung cấp</th>
        <th class="text-center" nzWidth="150px">Phòng/Kho</th>
        <th class="text-center" nzWidth="200px">Khu trọ</th>
        <th class="text-center" nzWidth="120px">Tài sản của chủ nhà</th>
        <th class="text-center" nzWidth="120px">Ngày đưa vào</th>
        <th class="text-center" nzWidth="120px">Ngày bảo trì lần cuối</th>
        <th class="text-center" nzWidth="120px">Ngày bảo trì kế tiếp</th>
        <th class="text-center" nzWidth="120px">Trạng thái bảo trì</th>
        <th class="text-center" nzWidth="120px">Ngày bảo hành</th>
        <th class="text-center" nzWidth="120px">Ngày hết hạn bảo hành</th>
      </tr>
    </thead>
    <tbody>
      <tr class="table-tbody" *ngFor="let item of listOfData">
        <td>
          <b>{{ item.propertyCode }}</b>
        </td>
        <td>
          <span>{{ item.propertyName }}</span>
        </td>
        <td>
          <span>{{ item.propertyTypeName }}</span>
        </td>
        <td>
          <span>{{ item.unitId }}</span>
        </td>
        <td class="text-right">
          <span>{{ item.price | number }}</span>
        </td>
        <td>
          <span>{{ item.supplierName }}</span>
        </td>
        <td>
          <span>{{ item.roomName ? item.roomName : item.warehouseName }}</span>
        </td>
        <td>
          <span>{{ item.apartmentName ? item.apartmentName : '' }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.propertyOfHomeowner ? 'Có' : 'Không' }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.putInDate | date : 'dd/MM/yyyy' }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.maintenanceDate | date : 'dd/MM/yyyy' }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.maintenanceNextDate | date : 'dd/MM/yyyy' }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.maintenanceStatusName }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.warrantyDate | date : 'dd/MM/yyyy' }}</span>
        </td>
        <td class="text-center">
          <span>{{ item.warrantyExpirationDate | date : 'dd/MM/yyyy' }}</span>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</nz-row>
