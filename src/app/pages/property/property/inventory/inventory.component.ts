import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import * as moment from 'moment'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './inventory.component.html' })
export class InventoryComponent implements OnInit {
  loading = true
  currentUser: User | any
  enumData = enumData
  modalTitle: any
  listOfData: any = []
  dataSearch: any = {}
  dataFinanceType: any
  totalOpeningStock: any
  totalDeposit: any
  totalPayDeposit: any
  totalClosingStock: any
  lstApartment: any = []
  lstPropertyDetail: any = []
  pageIndex: any
  pageSize: any
  total: any
  lstWarehouse: any = []
  lstSupplier: any = []
  lstRoom: any = []
  lstPropertyType: any = []
  lstProperty: any = []
  totalQuantity: number = 0
  totalAmount: number = 0
  lstPropHomeOwner: any = [
    { name: 'Có', id: 1 },
    { name: 'Không', id: 0 },
  ]
  constructor(
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    public coreService: CoreService,
    public dialog: MatDialog,
    private authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
  }

  async ngOnInit() {
    this.pageIndex = enumData.Page.pageIndex
    this.pageSize = enumData.Page.pageSize
    this.total = enumData.Page.total
    this.modalTitle = enumData.Constants.Model_Add
    this.dataSearch.month = new Date()
    await this.searchData()
    await this.loadAllData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      const key = 'name'
      where[key] = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      const key = 'code'
      where[key] = this.dataSearch.code
    }
    if (this.dataSearch.propertyTypeId && this.dataSearch.propertyTypeId !== '') {
      const key = 'propertyTypeId'
      where[key] = this.dataSearch.propertyTypeId
    }
    if (this.dataSearch.propertyId && this.dataSearch.propertyId !== '') {
      const key = 'propertyId'
      where[key] = this.dataSearch.propertyId
    }
    if (this.dataSearch.supplierId && this.dataSearch.supplierId !== '') {
      const key = 'supplierId'
      where[key] = this.dataSearch.supplierId
    }

    if (this.dataSearch.propHomeId != undefined) {
      const key = 'propHomeId'
      where[key] = this.dataSearch.propHomeId
    }

    if (this.dataSearch.apartmentId && this.dataSearch.apartmentId !== '') {
      const key = 'apartmentId'
      where[key] = this.dataSearch.apartmentId
    }
    if (this.dataSearch.roomId && this.dataSearch.roomId !== '') {
      const key = 'roomId'
      where[key] = this.dataSearch.roomId
    }

    if (this.dataSearch.warehouseId && this.dataSearch.warehouseId !== '') {
      const key = 'warehouseId'
      where[key] = this.dataSearch.warehouseId
    }

    if (this.dataSearch.putInDate && this.dataSearch.putInDate?.length > 0) {
      const key = 'putInDate'
      where[key] = this.dataSearch.putInDate
    }

    if (this.dataSearch.warrantyDate && this.dataSearch.warrantyDate?.length > 0) {
      const key = 'warrantyDate'
      where[key] = this.dataSearch.warrantyDate
    }

    if (this.dataSearch.warrantyExpirationDate && this.dataSearch.warrantyExpirationDate?.length > 0) {
      const key = 'warrantyExpirationDate'
      where[key] = this.dataSearch.warrantyExpirationDate
    }
    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.PROPERTY_DETAIL.INVENTORY, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        this.totalAmount = data[2]
        this.totalQuantity = data[3]
      }
    })
  }
  async loadAllData() {
    Promise.all([
      this.appService.post(this.appService.APARTMENT.LOAD_DATA, {}),
      this.appService.post(this.appService.PROPERTY_TYPE.LOAD_DATA, {}),
      this.appService.post(this.appService.PROPERTY.LOAD_DATA, {}),
      this.appService.post(this.appService.SUPPLIER.LOAD_DATA, {}),
    ]).then((res) => {
      this.lstApartment = res[0]
      this.lstPropertyType = res[1]
      this.lstProperty = res[2]
      this.lstSupplier = res[3]
    })
  }

  async onChangeApartment(event: any) {
    this.dataSearch.roomId = this.dataSearch.warehouseId = ''
    if (event) {
      this.lstRoom = await this.appService.post(this.appService.ROOM.FIND, { apartmentId: event })
      this.lstWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: event })
    } else {
      this.lstRoom = []
      this.lstWarehouse = []
    }
  }

  public groupByArrayByManyKey(arr: any, lstKey: any[]) {
    const arrR: any[] = []
    const dic: any = {}
    for (const item of arr) {
      let key: any = ''
      for (let i = 0; i < lstKey.length; i++) {
        if (i < lstKey.length - 1) {
          key += `${item[lstKey[i]]}-`
        } else {
          key += `${item[lstKey[i]]}`
        }
      }
      let temp = dic[key]
      if (temp) {
        temp.list.push(item)
      } else {
        temp = {
          roomId: item.roomId,
          roomName: item.roomName,
          list: [item],
        }
        dic[key] = temp
        arrR.push(temp)
      }
    }

    return Object.values(dic)
  }

  public groupByArrayByManyKeyWarehouse(arr: any, lstKey: any[]) {
    const arrR: any[] = []
    const dic: any = {}
    for (const item of arr) {
      let key: any = ''
      for (let i = 0; i < lstKey.length; i++) {
        if (i < lstKey.length - 1) {
          key += `${item[lstKey[i]]}-`
        } else {
          key += `${item[lstKey[i]]}`
        }
      }
      let temp = dic[key]
      if (temp) {
        temp.list.push(item)
      } else {
        temp = {
          warehouseId: item.warehouseId,
          warehouseName: item.warehouseName,
          list: [item],
        }
        dic[key] = temp
        arrR.push(temp)
      }
    }

    return Object.values(dic)
  }

  async onDownload() {
    this.notifyService.showloading()

    this.lstPropertyDetail = await this.appService.post(this.appService.PROPERTY_DETAIL.DOWNLOAD_EXCEL_PROPERTY_REPORT, this.dataSearch)
    const mergeLstProDetailByApartment = this.coreService.groupByArray(this.lstPropertyDetail, 'apartmentId')
    this.notifyService.hideloading()
    const workbook = new Workbook()

    //#region Body Table

    const worksheet2 = workbook.addWorksheet('Chi tiết tài sản')
    //#region Body Table
    const header2 = [
      'Tên tài sản',
      'UnitId',
      'Đơn giá',
      'Nhà cung cấp',
      'Phòng',
      'Kho',
      'Khu',
      'Tài sản chủ nhà',
      'Ngày đưa vào',
      'Ngày bảo hành',
      'Ngày hết hạn bảo hành',
    ]
    const headerRow2 = worksheet2.addRow(header2)
    const worksheet3 = workbook.addWorksheet('Chi tiết tài sản trong khu')

    const lstProperty = await this.appService.post(this.appService.PROPERTY.GET_NUMBER_OF_PROPERTY_DETAIL_BY_PROPERTY, {})

    for (let apartment of mergeLstProDetailByApartment) {
      const header3 = [`Khu: ${apartment.list[0]?.apartmentName ? apartment.list[0].apartmentName : ''}`]

      const headerRow3 = worksheet3.addRow(header3)

      const mainHeader3 = []

      // Thêm danh sách tài sản vào header
      mainHeader3.push('Phòng', 'Kho')
      for (let data of lstProperty) {
        mainHeader3.push(data.name)
      }
      if (mainHeader3.length > 0) {
        const mainHeader3Row = worksheet3.addRow(mainHeader3)
        // Cell Style : Fill and Border
        mainHeader3Row.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet3.getColumn(colNumber).width = 25
              break
            case 3:
              worksheet3.getColumn(colNumber).width = 25
              worksheet3.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
            default:
              worksheet3.getColumn(colNumber).width = 15
              worksheet3.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
          }
        })
      } else {
        // Cell Style : Fill and Border
        headerRow3.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet3.getColumn(colNumber).width = 25
              break
            case 3:
              worksheet3.getColumn(colNumber).width = 25
              worksheet3.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
            default:
              worksheet3.getColumn(colNumber).width = 15
              worksheet3.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
          }
        })
      }

      // Add Data and Conditional Formatting
      const mergeLstProDetail: any = this.groupByArrayByManyKey(apartment.list, ['roomId'])

      const mergeLstProDetailWarehouse: any = this.groupByArrayByManyKeyWarehouse(apartment.list, ['warehouseId'])

      const mergeLstProDetailCode: any = this.coreService.groupByArray(apartment.list, 'code')

      for (let data of mergeLstProDetail) {
        let arr = data.roomName?.split(', ') ? data.roomName?.split(', ') : []
        data.roomName = arr.join(' ')
        const rowData = [data.roomName || '', '']
        const test = this.coreService.groupByArray(data.list, 'code')
        mergeLstProDetailCode.forEach((e: any, index: number) => {
          const find = test.find((f: any) => f.heading == e.heading)
          if (!find) rowData.push(0)
          else rowData.push(find.list.length)
        })

        const row = worksheet3.addRow(rowData)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet3.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }

      for (let data of mergeLstProDetailWarehouse) {
        let arr = data.warehouseName?.split(', ') ? data.warehouseName?.split(', ') : []
        data.warehouseName = arr.join(' ')

        const rowData = ['', data.warehouseName || '']
        const test = this.coreService.groupByArray(data.list, 'code')
        mergeLstProDetailCode.forEach((e: any, index: number) => {
          const find = test.find((f: any) => f.heading == e.heading)
          if (!find) rowData.push(0)
          else rowData.push(find.list.length)
        })

        const row = worksheet3.addRow(rowData)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet3.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }
    }

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet2.getColumn(colNumber).width = 25
          break
        case 3:
          worksheet2.getColumn(colNumber).width = 25
          worksheet2.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
          break
        default:
          worksheet2.getColumn(colNumber).width = 25
          worksheet2.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
          break
      }
    })

    // Add Data and Conditional Formatting
    for (let data of this.lstPropertyDetail) {
      const rowData = [
        data.name || '',
        data.unitId || '',
        data.price ? data.price : 0,
        data.supplierName || '',
        data.roomName || '',
        data.warehouseName || '',
        data.apartmentName || '',
        data.propertyOfHomeowner ? 'Có' : 'Không',
        data.putInDate ? moment(data.putInDate).format('DD/MM/YYYY') : '',
        data.warrantyDate ? moment(data.warrantyDate).format('DD/MM/YYYY') : '',
        data.warrantyExpirationDate ? moment(data.warrantyExpirationDate).format('DD/MM/YYYY') : '',
      ]
      const row = worksheet2.addRow(rowData)
      row.eachCell((cell, colNumber) => {
        cell.font = { bold: true }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }
      })
      worksheet2.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
    }

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `BAO_CAO_TAI_SAN _${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  onViewDetail(data: any) {
    // if (this.dataSearch.apartmentId) data.apartmentId = this.dataSearch.apartmentId
    // this.dialog
    //   .open(PropertyReportDetailModelComponent, { disableClose: false, data: data })
    //   .afterClosed()
    //   .subscribe(() => {
    //     this.searchData()
    //   })
  }
}
