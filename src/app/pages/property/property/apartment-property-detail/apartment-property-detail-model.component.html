<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
  </div>

  <div nz-row class="mt-4" *ngIf="dataObject.id">
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã khu</nz-form-label>
        <nz-form-control>
          <b>{{ dataObject.code }}</b>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên khu</nz-form-label>
        <nz-form-control>
          <b>{{ dataObject.name }}</b>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="8" *ngIf="dataObject.description">
      <nz-form-item nzFlex>
        <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mô Tả </nz-form-label>
        <nz-form-control>
          <b>{{ dataObject.description }}</b>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <nz-row>
    <div nz-col nzSpan="24" *ngIf="dataObject">
      <nz-table
        class="mb-3"
        nz-col
        nzSpan="24"
        #ajaxTable
        [nzData]="listOfData"
        [(nzPageSize)]="pageSize"
        [nzLoading]="loading"
        [nzShowPagination]="false"
        nzBordered
        nzTableLayout="fixed"
        [nzScroll]="{ y: '600px' }"
      >
        <thead>
          <tr>
            <th>Mã tài sản</th>
            <th>Tên tài sản</th>
            <th>Loại tài sản</th>
            <th>Phòng</th>
            <th>UnitId</th>
            <th>Giá</th>
            <th>Tác vụ</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of listOfData; let i = index">
            <tr>
              <td class="text-center">{{ item.code }}</td>
              <td class="text-center">{{ item.name }}</td>
              <td class="text-center">{{ item.propertyTypeName }}</td>
              <td class="text-center">{{ item?.roomName }}</td>
              <td class="text-center">{{ item.unitId }}</td>
              <td class="text-center">{{ item.price | number }}</td>
              <td class="text-center">
                <button
                  class="mr-1"
                  *ngIf="item.isDeleted"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn hoạt động lại?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(item)"
                  nz-tooltip
                  nzTooltipTitle="Kích hoạt"
                  nz-button
                  nzType="primary"
                  nzShape="circle"
                >
                  <i nz-icon nzType="play-circle" nzTheme="outline"></i>
                </button>
                <button
                  class="mr-1"
                  *ngIf="!item.isDeleted"
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="setActiveItem(item)"
                  nz-tooltip
                  nzTooltipTitle="Ngưng hoạt động"
                  nz-button
                  nzType="primary"
                  nzDanger
                  nzShape="circle"
                >
                  <i nz-icon nzType="stop"></i>
                </button>
                <button
                  nz-tooltip
                  nzTooltipTitle="Xem lịch sử"
                  nz-button
                  nzShape="circle"
                  class="mr-2"
                  nzType="primary"
                  nzGhost
                  (click)="onDetailHistory(item)"
                >
                  <span nz-icon nzType="history" nzTheme="outline"></span>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>

    <div nz-col nzSpan="24" class="text-right">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()"
        (nzPageSizeChange)="searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
    </div>
  </nz-row>
</div>
