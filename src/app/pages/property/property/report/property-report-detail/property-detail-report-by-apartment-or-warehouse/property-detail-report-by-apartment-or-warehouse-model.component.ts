import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../../../services/authentication.service'
import { CoreService } from '../../../../../../services/core.service'

@Component({ templateUrl: './property-detail-report-by-apartment-or-warehouse-model.component.html' })
export class PropertyDetailReportByApartmentOrWarehouseModelComponent implements OnInit {
  enumData = enumData
  modelTitle: string = 'CHI TIẾT TÀI SẢN'
  dataObject: any
  listOfData: any = []
  pageIndex: any
  pageSize: any
  total: any
  dataFilterStatus: any
  dataSearch: any = {}
  loading = true
  lstWarehouse: any = []
  lstRoom: any = []
  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<PropertyDetailReportByApartmentOrWarehouseModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.modelTitle += ` của ${this.data.name}`
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    await this.loadAllData()
    await this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true
    const where: any = {}
    if (this.dataSearch.roomId && this.dataSearch.roomId !== '') {
      where.roomId = this.dataSearch.roomId
    }
    if (this.dataSearch.unitId && this.dataSearch.unitId !== '') {
      where.unitId = this.dataSearch.unitId
    }

    if (this.dataSearch.warehouseId && this.dataSearch.warehouseId !== '') {
      where.warehouseId = this.dataSearch.warehouseId
    }

    where.apartmentId = this.data.id

    where.propertyTypeId = this.data.propertyTypeId

    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    await this.appService.post(this.appService.PROPERTY_DETAIL.PAGINATION_CUSTOM_FOR_PROPERTY_REPORT, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.listOfData = data[0]
        this.total = data[1]
      }
    })
  }

  async loadAllData() {
    this.lstWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: this.data.id })
    this.lstRoom = await this.appService.post(this.appService.ROOM.FIND, { apartmentId: this.data.id })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
