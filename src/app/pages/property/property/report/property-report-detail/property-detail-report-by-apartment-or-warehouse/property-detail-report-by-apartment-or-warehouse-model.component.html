<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
  </div>

  <nz-row nzAlign="middle" nzGutter="8" nzJustify="start">
    <div nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.roomId" name="roomId" nzPlaceHolder="Phòng trọ" (ngModelChange)="searchData()">
            <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-select
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataSearch.warehouseId"
            name="warehouseId"
            nzPlaceHolder="Kho"
            (ngModelChange)="searchData()"
          >
            <nz-option *ngFor="let item of lstWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <input [(ngModel)]="dataSearch.unitId" name="unitId" nz-input placeholder="UnitId" />
        </nz-form-control>
      </nz-form-item>
    </div>
    <div nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <button nz-button nzType="default" (click)="searchData()" nzShape="round"><i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm</button>
        </nz-form-control>
      </nz-form-item>
    </div>
  </nz-row>

  <nz-row>
    <div nz-col nzSpan="24">
      <nz-table
        class="mb-3"
        nz-col
        nzSpan="24"
        #ajaxTable
        [nzData]="['']"
        [(nzPageSize)]="pageSize"
        [nzLoading]="loading"
        [nzShowPagination]="false"
        nzBordered
        nzTableLayout="fixed"
        [nzScroll]="{ y: '600px' }"
      >
        <thead>
          <tr>
            <th class="text-center">Mã</th>
            <th class="text-center">Tên tài sản</th>
            <th class="text-center">Kho/Phòng</th>
            <th class="text-center">UnitId</th>
            <th class="text-center">Nguyên giá</th>
            <th class="text-center">Ngày đưa vào sử dụng</th>
            <th class="text-center">Ngày bảo hành</th>
            <th class="text-center">Ngày hết hạn bảo hành</th>
            <th class="text-center">Tài sản chủ nhà</th>
            <!-- <th class="text-center">Lịch sử</th> -->
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of listOfData; let i = index">
            <tr>
              <td class="text-center">{{ item.code }}</td>
              <td class="text-center">{{ item.name }}</td>
              <td class="text-center">{{ item.roomName ? item.roomName : item.warehouseName }}</td>
              <td class="text-center">{{ item.unitId }}</td>
              <td class="text-center">{{ item.price | number }}</td>
              <td class="text-center">{{ item.putInDate | date : 'dd/MM/yyyy HH:mm' }}</td>
              <td class="text-center">{{ item.warrantyDate | date : 'dd/MM/yyyy HH:mm' }}</td>
              <td class="text-center">{{ item.warrantyExpirationDate | date : 'dd/MM/yyyy HH:mm' }}</td>
              <td class="text-center">{{ item.propertyOfHomeowner ? 'Có' : 'Không' }}</td>
              <!-- <td class="text-center"></td> -->
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>

    <div nz-col nzSpan="24" class="text-right">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()"
        (nzPageSizeChange)="searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
    </div>
  </nz-row>
</div>
