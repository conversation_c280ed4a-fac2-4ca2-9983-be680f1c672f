<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
  </div>

  <nz-row nzAlign="middle" nzGutter="8" nzJustify="start">
    <div nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <nz-select
            style="margin-top: 24px"
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataSearch.apartmentId"
            name="apartmentId"
            nzPlaceHolder="Khu Trọ"
            (ngModelChange)="searchData()"
          >
            <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>
  </nz-row>

  <div nz-row nzGutter="8">
    <nz-col nzSpan="4">
      <span style="font-size: medium" class="text-left"
        >Tổng số lượng:
        <b
          ><i>{{ totalQuantity | number }}</i></b
        >
      </span>
    </nz-col>
    <nz-col nzSpan="4">
      <span style="font-size: medium" class="text-left"
        >Tổng tiền:
        <b style="color: blue"
          ><i>{{ totalAmount | number }} VNĐ</i></b
        >
      </span>
    </nz-col>
  </div>

  <nz-row>
    <div nz-col nzSpan="24">
      <nz-table
        class="mb-3"
        nz-col
        nzSpan="24"
        #ajaxTable
        [nzData]="listOfData"
        [(nzPageSize)]="pageSize"
        [nzLoading]="loading"
        [nzShowPagination]="false"
        nzBordered
        nzTableLayout="fixed"
        [nzScroll]="{ y: '600px' }"
      >
        <thead>
          <tr>
            <th class="text-center">Khu</th>
            <th class="text-center">Tổng số lượng</th>
            <th class="text-center">Tổng giá trị</th>
            <th class="text-center">Tác vụ</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of listOfData; let i = index">
            <tr>
              <td class="text-left">{{ item.name }}</td>
              <td class="text-right">{{ item.totalPropertyDetail | number }}</td>
              <td class="text-right">{{ item.totalPropertyDetailPrice | number }}</td>
              <td class="text-center">
                <button
                  class="mr-2 mb-1"
                  (click)="onViewDetail(item)"
                  nz-tooltip
                  nzTooltipTitle="Xem chi tiết"
                  nz-button
                  nzType="primary"
                  nzGhost
                  nzShape="circle"
                >
                  <i nz-icon nzType="eye" nzTheme="outline"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>

    <div nz-col nzSpan="24" class="text-right">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()"
        (nzPageSizeChange)="searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
    </div>
  </nz-row>
</div>
