import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../../services/authentication.service'
import { CoreService } from '../../../../../services/core.service'
import { PropertyDetailReportByApartmentOrWarehouseModelComponent } from './property-detail-report-by-apartment-or-warehouse/property-detail-report-by-apartment-or-warehouse-model.component'

@Component({ templateUrl: './property-report-detail-model.component.html' })
export class PropertyReportDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT LOẠI TÀI SẢN'
  dataObject: any
  listOfData: any = []
  pageIndex: any
  pageSize: any
  total: any
  dataFilterStatus: any
  dataSearch: any = {}
  loading = true
  lstApartment: any = []
  totalAmount = 0
  totalQuantity = 0
  constructor(
    private authenticationService: AuthenticationService,
    public coreService: CoreService,
    private appService: ApiNtssService,
    public dialog: MatDialog,
    private dialogRef: MatDialogRef<PropertyReportDetailModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.modelTitle += ` ${this.data.name}`
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total

    await this.searchData()
    await this.loadAllData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true
    const where: any = {}
    if (this.data.apartmentId && !this.dataSearch.apartmentId) {
      this.dataSearch.apartmentId = where.apartmentId = this.data.apartmentId
    } else if (this.dataSearch.apartmentId && this.dataSearch.apartmentId !== '') {
      where.apartmentId = this.dataSearch.apartmentId
    }
    where.propertyTypeId = this.data.id

    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.APARTMENT.PAGINATION_CUSTOM, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.listOfData = data[0]
        this.total = data[1]
        this.totalAmount = data[2]
        this.totalQuantity = data[3]
      }
    })
  }

  async loadAllData() {
    this.lstApartment = await this.appService.post(this.appService.APARTMENT.LOAD_DATA, {})
  }

  onViewDetail(data: any) {
    data.propertyTypeId = this.data.propertyTypeId
    this.dialog
      .open(PropertyDetailReportByApartmentOrWarehouseModelComponent, { disableClose: false, data: data })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
