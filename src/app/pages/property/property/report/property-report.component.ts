import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import * as moment from 'moment'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { User } from '../../../../models/user.model'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'
import { PropertyDetailReportByApartmentOrWarehouseModelComponent } from './property-report-detail/property-detail-report-by-apartment-or-warehouse/property-detail-report-by-apartment-or-warehouse-model.component'
import { PropertyReportDetailModelComponent } from './property-report-detail/property-report-detail-model.component'

@Component({ templateUrl: './property-report.component.html' })
export class PropertyReportComponent implements OnInit {
  loading = true
  role: any
  currentUser: User | any
  enumData: any
  modalTitle: any
  listOfData: any = []
  dataSearch: any = {}
  lstPropertyType: any = []
  lstApartment: any = []
  lstPropertyDetail: any = []
  pageIndex: any
  pageSize: any
  total: any
  totalQuantity = 0
  totalAmount = 0
  constructor(
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    public coreService: CoreService,
    public dialog: MatDialog,
    private authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  async ngOnInit() {
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.modalTitle = enumData.Constants.Model_Add
    await this.loadAllData()
    await this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) this.pageIndex = 1
    this.notifyService.showloading()

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.loading = true
    this.notifyService.showloading()
    await this.appService.post(this.appService.PROPERTY_TYPE.REPORT, dataSearch).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.listOfData = res[0]
        this.total = res[1]
        this.totalQuantity = res[2]
        this.totalAmount = res[3]
        this.loading = false
      }
    })
  }
  async loadAllData() {
    await Promise.all([
      this.appService.post(this.appService.PROPERTY_TYPE.LOAD_DATA, {}),
      this.appService.post(this.appService.APARTMENT.LOAD_DATA, {}),
    ]).then((res: any) => {
      this.lstPropertyType = res[0]
      this.lstApartment = res[1]
      if (this.lstApartment.length > 0) this.dataSearch.apartmentId = this.lstApartment[0].id
    })
  }

  public groupByArrayByManyKey(arr: any, lstKey: any[]) {
    const arrR: any[] = []
    const dic: any = {}
    for (const item of arr) {
      let key: any = ''
      for (let i = 0; i < lstKey.length; i++) {
        if (i < lstKey.length - 1) {
          key += `${item[lstKey[i]]}-`
        } else {
          key += `${item[lstKey[i]]}`
        }
      }
      let temp = dic[key]
      if (temp) {
        temp.list.push(item)
      } else {
        temp = {
          roomId: item.roomId,
          roomName: item.roomName,
          list: [item],
        }
        dic[key] = temp
        arrR.push(temp)
      }
    }

    return Object.values(dic)
  }

  async onDownload() {
    this.notifyService.showloading()
    const workbook = new Workbook()

    if (!this.dataSearch.apartmentId) {
      const dataSearch: any = {
        where: this.dataSearch,
        order: { createdAt: 'DESC' },
        skip: (this.pageIndex - 1) * this.pageSize,
        take: this.pageSize,
      }
      dataSearch.isGetAll = true

      let lstAll: any = []

      this.loading = true
      await this.appService.post(this.appService.PROPERTY_TYPE.REPORT, dataSearch).then((res: any) => {
        if (res) {
          this.notifyService.hideloading()
          this.loading = false
          lstAll = res[0]
        }
      })

      const worksheet = workbook.addWorksheet('Báo cáo tài sản')
      //#region Body Table
      const header = ['Loại tài sản', 'Tổng số lượng', 'Tổng giá trị']
      const headerRow = worksheet.addRow(header)
      // Cell Style : Fill and Border
      headerRow.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '203751' },
          bgColor: { argb: '203751' },
        }
        cell.alignment = { horizontal: 'center' }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }

        switch (colNumber) {
          case 1:
            worksheet.getColumn(colNumber).width = 25
            break
          case 3:
            worksheet.getColumn(colNumber).width = 15
            worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
          default:
            worksheet.getColumn(colNumber).width = 15
            worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
        }
      })

      // Add Data and Conditional Formatting
      for (let data of lstAll) {
        const rowData = [data.name || '', data.totalPropertyDetail || 0, data.totalPropertyDetailPrice || 0]
        const row = worksheet.addRow(rowData)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }
    } else if (this.dataSearch.apartmentId) {
      const dataSearch: any = {
        where: this.dataSearch,
        order: { createdAt: 'DESC' },
        skip: (this.pageIndex - 1) * this.pageSize,
        take: this.pageSize,
      }
      dataSearch.isGetAll = true

      let lstAll: any = []

      await this.appService.post(this.appService.PROPERTY_TYPE.REPORT, dataSearch).then((res: any) => {
        if (res) {
          lstAll = res[0]
        }
      })

      const worksheet = workbook.addWorksheet('Tổng thể')
      //#region Body Table
      const header = ['Loại tài sản', 'Tổng số lượng', 'Tổng giá trị']
      const headerRow = worksheet.addRow(header)
      // Cell Style : Fill and Border
      headerRow.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '203751' },
          bgColor: { argb: '203751' },
        }
        cell.alignment = { horizontal: 'center' }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }

        switch (colNumber) {
          case 1:
            worksheet.getColumn(colNumber).width = 25
            break
          case 3:
            worksheet.getColumn(colNumber).width = 15
            worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
          default:
            worksheet.getColumn(colNumber).width = 15
            worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
        }
      })

      // Add Data and Conditional Formatting
      for (let data of lstAll) {
        const rowData = [data.name || '', data.totalPropertyDetail || 0, data.totalPropertyDetailPrice || 0]
        const row = worksheet.addRow(rowData)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }

      const dataSearchDetail = {
        apartmentId: this.dataSearch.apartmentId,
        propertyTypeId: this.dataSearch.propertyTypeId,
      }

      // #region Chi tiết
      let lstDetail: any = []
      await this.appService.post(this.appService.PROPERTY_DETAIL.LOAD_DATA_BY_APARTMENT_AND_PROPERTY_TYPE, dataSearchDetail).then((res: any) => {
        if (res) {
          lstDetail = res
        }
      })

      const worksheet2 = workbook.addWorksheet('Chi tiết')
      //#region Body Table
      const header2 = [
        'Loại tài sản',
        'Tên tài sản',
        'Phòng/Kho',
        'Trị giá',
        'Ngày đưa vào sử dụng',
        'Ngày bảo hành',
        'Ngày hết hạn bảo hành',
        'Tài sản chủ nhà',
      ]
      const headerRow2 = worksheet2.addRow(header2)
      // Cell Style : Fill and Border
      headerRow2.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '203751' },
          bgColor: { argb: '203751' },
        }
        cell.alignment = { horizontal: 'center' }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }

        switch (colNumber) {
          case 1:
            worksheet2.getColumn(colNumber).width = 25
            break
          case 2:
            worksheet2.getColumn(colNumber).width = 25
            break
          case 3:
            worksheet2.getColumn(colNumber).width = 25
            break
          case 4:
            worksheet2.getColumn(colNumber).width = 25
            break
          case 5:
            worksheet2.getColumn(colNumber).width = 25
            break
          case 6:
            worksheet2.getColumn(colNumber).width = 25
            break
          case 7:
            worksheet2.getColumn(colNumber).width = 25
            break
          default:
            worksheet2.getColumn(colNumber).width = 10
            worksheet2.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
        }
      })
      //#endregion
      // Add Data and Conditional Formatting
      for (let data of lstDetail) {
        const rowData2 = [
          data.propertyTypeName || '',
          data.propertyName || '',
          data.objectName || '',
          data.price || 0,
          data.putInDate ? moment(data.putInDate).format('DD/MM/YYYY') : '',
          data.warrantyDate ? moment(data.warrantyDate).format('DD/MM/YYYY') : '',
          data.warrantyExpirationDate ? moment(data.warrantyExpirationDate).format('DD/MM/YYYY') : '',
          data.propertyOfHomeowner ? 'X' : '',
        ]
        const row = worksheet2.addRow(rowData2)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet2.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }
      //#endregion

      // #region Theo phòng

      const whereRoom: any = {
        apartmentId: this.dataSearch.apartmentId,
        propertyTypeId: this.dataSearch.propertyTypeId,
        isGetAll: true,
      }

      const dataSearchRoom = {
        where: whereRoom,
        skip: (this.pageIndex - 1) * this.pageSize,
        take: this.pageSize,
      }

      let lstPropByRoom: any = []
      await this.appService.post(this.appService.PROPERTY_DETAIL.PAGINATION_BY_ROOM, dataSearchRoom).then((res: any) => {
        lstPropByRoom = res[0]
      })

      const worksheet3 = workbook.addWorksheet('Theo phòng')
      //#region Body Table
      const header3 = ['Phòng', 'Tổng số lượng', 'Trị giá']
      const headerRow3 = worksheet3.addRow(header3)
      // Cell Style : Fill and Border
      headerRow3.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '203751' },
          bgColor: { argb: '203751' },
        }
        cell.alignment = { horizontal: 'center' }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }

        switch (colNumber) {
          case 1:
            worksheet3.getColumn(colNumber).width = 20
            break
          case 2:
            worksheet3.getColumn(colNumber).width = 20
            break
          case 3:
            worksheet3.getColumn(colNumber).width = 25
            break
          default:
            worksheet3.getColumn(colNumber).width = 15
            worksheet3.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
        }
      })
      //#endregion
      // Add Data and Conditional Formatting
      for (let data of lstPropByRoom) {
        const rowData3 = [data.name || '', data.propQuantity || 0, data.totalPrice || 0]
        const row = worksheet3.addRow(rowData3)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet3.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }
      //#endregion

      // #region Chi tiết theo phòng
      const worksheet4 = workbook.addWorksheet('Chi tiết theo phòng')
      //#region Body Table
      const header4 = [
        'Phòng/Kho',
        'Loại tài sản',
        'Tên tài sản',
        'Trị giá',
        'Ngày đưa vào sử dụng',
        'Ngày bảo hành',
        'Ngày hết hạn bảo hành',
        'Tài sản chủ nhà',
      ]
      const headerRow4 = worksheet4.addRow(header4)
      // Cell Style : Fill and Border
      headerRow4.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '203751' },
          bgColor: { argb: '203751' },
        }
        cell.alignment = { horizontal: 'center' }
        cell.font = { bold: true, color: { argb: 'FFFFFF' } }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        }

        switch (colNumber) {
          case 1:
            worksheet4.getColumn(colNumber).width = 25
            break
          case 2:
            worksheet4.getColumn(colNumber).width = 25
            break
          case 3:
            worksheet4.getColumn(colNumber).width = 25
            break
          case 4:
            worksheet4.getColumn(colNumber).width = 25
            break
          case 5:
            worksheet4.getColumn(colNumber).width = 25
            break
          case 6:
            worksheet4.getColumn(colNumber).width = 25
            break
          case 7:
            worksheet4.getColumn(colNumber).width = 25
            break
          default:
            worksheet4.getColumn(colNumber).width = 10
            worksheet4.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
            break
        }
      })
      //#endregion
      // Add Data and Conditional Formatting
      for (let data of lstDetail) {
        const rowData2 = [
          data.objectName || '',
          data.propertyTypeName || '',
          data.propertyName || '',
          data.price || 0,
          data.putInDate ? moment(data.putInDate).format('DD/MM/YYYY') : '',
          data.warrantyDate ? moment(data.warrantyDate).format('DD/MM/YYYY') : '',
          data.warrantyExpirationDate ? moment(data.warrantyExpirationDate).format('DD/MM/YYYY') : '',
          data.propertyOfHomeowner ? 'X' : '',
        ]
        const row = worksheet4.addRow(rowData2)
        row.eachCell((cell, colNumber) => {
          cell.font = { bold: true }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }
        })
        worksheet4.getCell(`A${row.number}`).alignment = { horizontal: 'left' }
      }
      //#endregion
    }

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `BAO_CAO_TAI_SAN _${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  onViewDetail(data: any) {
    if (this.dataSearch.apartmentId) {
      const dt: any = {}
      dt.name = data.name
      dt.id = this.dataSearch.apartmentId
      dt.propertyTypeId = data.id

      this.dialog
        .open(PropertyDetailReportByApartmentOrWarehouseModelComponent, { disableClose: false, data: dt })
        .afterClosed()
        .subscribe(() => {
          this.searchData()
        })
    } else {
      data.propertyTypeId = data.id
      this.dialog
        .open(PropertyReportDetailModelComponent, { disableClose: false, data: data })
        .afterClosed()
        .subscribe(() => {
          this.searchData()
        })
    }
  }
}
