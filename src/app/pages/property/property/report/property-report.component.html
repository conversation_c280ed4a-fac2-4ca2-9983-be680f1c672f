<div nz-row nzGutter="8" nzAlign="middle">
  <div nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu </nz-form-label>
      <nz-form-control [nzSm]="24" [nzXs]="24">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.apartmentId"
          name="dataSearch.apartmentId"
          nzPlaceHolder="Chọn khu"
          (ngModelChange)="searchData()"
        >
          <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div nz-col nzSpan="8">
    <nz-form-item nzFlex>
      <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại tài sản </nz-form-label>
      <nz-form-control [nzSm]="24" [nzXs]="24">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.propertyTypeId"
          name="dataSearch.propertyTypeId"
          nzPlaceHolder="Chọn loại tài sản"
          (ngModelChange)="searchData()"
        >
          <nz-option *ngFor="let item of lstPropertyType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div nz-col nzSpan="8" class="mt-2">
    <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData()">
      <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
    </button>
    <button class="ml-2" nz-button nzType="default" (click)="onDownload()" nzShape="round">
      <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
    </button>
  </div>
</div>

<div nz-row class="mt-2">
  <nz-col nzSpan="12">
    <span style="font-size: medium" class="text-left"
      >Tổng số lượng:
      <b
        ><i>{{ totalQuantity | number }}</i></b
      >
    </span>
  </nz-col>
  <nz-col nzSpan="12" class="text-right">
    <span style="font-size: medium" class="text-left"
      >Tổng tiền:
      <b style="color: blue"
        ><i>{{ totalAmount | number }} VNĐ</i></b
      >
    </span>
  </nz-col>

  <nz-col nzSpan="24" class="mt-2">
    <nz-table class="mb-3" [nzData]="listOfData" [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
      <thead>
        <tr class="table-thead">
          <th class="text-center" nzWidth="260px">Loại tài sản</th>
          <th class="text-center" nzWidth="200px">Tổng số lượng</th>
          <th class="text-center" nzWidth="200px">Tổng giá trị</th>
          <th class="text-center" nzWidth="110px">Tác vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr class="table-tbody" *ngFor="let item of listOfData">
          <td nzLeft class="text-left">
            <b>{{ item.name }}</b>
          </td>
          <td class="text-right">
            <span>{{ item.totalPropertyDetail | number }}</span>
          </td>
          <td class="text-right">
            <span>{{ item.totalPropertyDetailPrice | number }}</span>
          </td>
          <td class="text-center">
            <button
              class="mr-2 mb-1"
              (click)="onViewDetail(item)"
              nz-tooltip
              nzTooltipTitle="Xem chi tiết"
              nz-button
              nzType="primary"
              nzGhost
              nzShape="circle"
            >
              <i nz-icon nzType="eye" nzTheme="outline"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <div class="text-left">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()"
        (nzPageSizeChange)="searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
    </div>
  </nz-col>
</div>
