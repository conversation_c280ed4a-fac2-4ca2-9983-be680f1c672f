import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './warehouse-detail-model.component.html' })
export class WarehouseDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT KHO'
  dataObject: any

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private appService: ApiNtssService,
    private dialog: MatDialog,
    private dialogRef: MatDialogRef<WarehouseDetailModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    this.dataObject = this.data
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
