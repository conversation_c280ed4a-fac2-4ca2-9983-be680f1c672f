import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'

declare var Object: any
@Component({ templateUrl: './add-or-edit-warehouse-model.component.html' })
export class AddOrEditWarehouseModelComponent implements OnInit {
  enumData: any
  modelTitle: any
  dataObject: any
  isEditItem = false
  lstApartment: any = []
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditWarehouseModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    if (this.data && this.data !== null) {
      this.dataObject = this.data
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT KHO'
    } else {
      this.modelTitle = 'THÊM MỚI KHO'
      this.dataObject = new Object()
    }
    this.loadAllData()
  }

  async onChangeApartment(event: any) {
    const findA = this.lstApartment.find((a: any) => a.id === event)
    this.dataObject.name = findA.name
  }

  async loadAllData() {
    this.lstApartment = await this.appService.post(this.appService.APARTMENT.LOAD_DATA, {})
  }

  onSave() {
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.WAREHOUSE.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.WAREHOUSE.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
