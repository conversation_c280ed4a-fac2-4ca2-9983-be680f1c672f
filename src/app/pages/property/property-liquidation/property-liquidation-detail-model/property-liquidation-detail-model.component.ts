import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './property-liquidation-detail-model.component.html' })
export class PropertyLiquidationDetailModelComponent implements OnInit {
  enumData: any
  modelTitle = 'THÔNG TIN PHIẾU THANH LÝ TÀI SẢN'
  modelTitleSub = 'DANH SÁCH TÀI SẢN'
  dataObject: any
  lstDetails: any = []
  lstHistories: any = []

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<PropertyLiquidationDetailModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    if (this.data && this.data !== null) {
      this.searchData()
    }
  }

  async searchData() {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_LIQUIDATION.DETAIL, { id: this.data.id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res
        this.lstHistories = res.lstHistories
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
