import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './add-property-liquidation-model.component.html' })
export class AddPropertyLiquidationModelComponent implements OnInit {
  enumData: any
  modelTitle: any
  dataObject: any
  isEditItem = false
  lstPropertyDetail: any = []
  lstApartment: any = []
  lstRoom: any = []
  lstRoomSrc: any = []
  lstWarehouse: any = []
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<AddPropertyLiquidationModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.dataObject = new Object()
    this.modelTitle = 'THANH LÝ TÀI SẢN'
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    await this.loadAllData()
  }

  async loadAllData() {
    this.notifyService.showloading()
    await Promise.all([this.appService.post(this.appService.APARTMENT.LOAD_DATA, {})]).then(async (res) => {
      this.notifyService.hideloading()
      this.lstApartment = res[0]
    })
  }

  async loadPropertyDetail() {
    this.notifyService.showloading()

    const where: any = {}
    if (this.dataObject.apartmentId && this.dataObject.apartmentId !== null) {
      where.apartmentId = this.dataObject.apartmentId
    }

    if (this.dataObject.roomId && this.dataObject.roomId !== null) {
      where.roomId = this.dataObject.roomId
    }

    if (this.dataObject.warehouseId && this.dataObject.warehouseId !== null) {
      where.warehouseId = this.dataObject.warehouseId
    }

    this.appService.post(this.appService.PROPERTY_DETAIL.LOAD_DATA, where).then((data: any) => {
      this.notifyService.hideloading()
      this.lstPropertyDetail = data || []
    })
  }

  async onChangeApartment(event: any) {
    this.dataObject.roomId =
      this.dataObject.warehouseId =
      this.dataObject.money =
      this.dataObject.liquidationDate =
      this.dataObject.propertyDetailId =
        ''

    this.lstRoom = []
    this.lstWarehouse = []
    if (event) {
      this.lstRoom = await this.appService.post(this.appService.ROOM.FIND, { apartmentId: event })
      this.lstWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: event })
    } else {
      this.lstWarehouse = []
    }
    await this.loadPropertyDetail()
  }

  async onChangeWarehouse() {
    this.dataObject.roomId = this.dataObject.money = this.dataObject.liquidationDate = this.dataObject.propertyDetailId = ''
    await this.loadPropertyDetail()
  }

  async onChangeRoom() {
    this.dataObject.money = this.dataObject.liquidationDate = this.dataObject.propertyDetailId = this.dataObject.warehouseId = ''
    await this.loadPropertyDetail()
  }

  onSave() {
    this.notifyService.showloading()
    if (!this.dataObject.apartmentId && !this.dataObject.warehouseId) {
      this.notifyService.showError(`Vui lòng chọn kho hoặc khu`)
      return
    }
    this.appService.post(this.appService.PROPERTY_LIQUIDATION.CREATE, this.dataObject).then((result) => {
      this.notifyService.hideloading()
      this.notifyService.showSuccess(result.message)
      this.closeDialog()
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
