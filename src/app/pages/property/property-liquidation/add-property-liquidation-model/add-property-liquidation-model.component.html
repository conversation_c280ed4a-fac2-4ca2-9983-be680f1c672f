<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
    <br />
  </div>

  <form nz-form class="p-4" #frmAdd="ngForm">
    <div nz-row class="mt-4" nzGutter="8">
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng chọn khu!">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Khu"
              [(ngModel)]="dataObject.apartmentId"
              name="apartmentId"
              (ngModelChange)="onChangeApartment($event)"
            >
              <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Phòng </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng chọn phòng!">
            <nz-select
              [disabled]="!dataObject.apartmentId"
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn phòng"
              [(ngModel)]="dataObject.roomId"
              name="roomId"
              (ngModelChange)="onChangeRoom()"
            >
              <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Kho </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn kho"
              [(ngModel)]="dataObject.warehouseId"
              name="warehouseId"
              (ngModelChange)="onChangeWarehouse()"
            >
              <nz-option *ngFor="let item of lstWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Tài sản cần thanh lý </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng chọn tài sản cần thanh lý!">
            <nz-select nzShowSearch nzPlaceHolder="Tài sản cần thanh lý" [(ngModel)]="dataObject.propertyDetailId" name="propertyDetailId" required>
              <nz-option *ngFor="let item of lstPropertyDetail" [nzLabel]="'(' + item.unitId + ') ' + item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Giá </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" class="text-center">
            <input
              style="font-weight: bold; border-radius: 25px"
              nz-input
              placeholder="Vui lòng nhập giá"
              [(ngModel)]="dataObject.money"
              name="money"
              required
              currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false }"
            />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Ngày thanh lý </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập ngày thanh lý">
            <nz-date-picker
              nzFormat="dd/MM/yyyy"
              [(ngModel)]="dataObject.liquidationDate"
              nzPlaceHolder="Nhập ngày thanh lý"
              name="liquidationDate"
              required
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ghi chú</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <textarea rows="2" nz-input placeholder="Nhập ghi chú" [(ngModel)]="dataObject.note" name="note"></textarea>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
    <div nz-row>
      <div nz-col nzSpan="24" class="text-center">
        <button nz-button [disabled]="!frmAdd.form.valid" nzShape="round" nzType="primary" class="mr-3" (click)="onSave()">
          <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
        </button>
      </div>
    </div>
  </form>
</div>
