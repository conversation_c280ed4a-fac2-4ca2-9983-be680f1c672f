<div nz-row nzGutter="8">
  <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  <!-- <button nzShape="round" *ngIf="coreService.checkPermission(role.Download.code)" class="mr-2" nz-button
    (click)="onDownloadTemplateExcel()"><span nz-icon nzType="download"></span>Tải
    Template Excel</button> -->
  <!-- <input class="hidden" type="file" id="file" (change)="onImportExcel($event)" placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
  <label *ngIf="coreService.checkPermission(role.Upload.code)" nz-button for="file"
    class="ant-btn lable-custom-file-custom">
    <span nz-icon nzType="upload"></span> Nhập Excel
  </label> -->
</div>
<nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
  <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
    <div nz-row nzGutter="8">
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã lệnh </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="24">
            <input [(ngModel)]="dataSearch.code" name="code" nz-input placeholder="Mã lệnh" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tên lệnh </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="24">
            <input [(ngModel)]="dataSearch.name" name="name" nz-input placeholder="Tên lệnh" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu trọ </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.apartmentId"
              name="apartmentId"
              nzPlaceHolder="Khu trọ"
              (ngModelChange)="onChangeApartment($event)"
            >
              <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Phòng trọ </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="24">
            <nz-select
              [disable]="!dataSearch.apartmentId"
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.roomId"
              name="roomId"
              nzPlaceHolder="Phòng trọ"
            >
              <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tài sản thanh lý </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="24">
            <nz-select
              [disable]="!dataSearch.apartmentId"
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.propertyId"
              name="propertyId"
              nzPlaceHolder="Tài sản thanh lý"
            >
              <nz-option *ngFor="let item of lstProperty" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày thanh lý </nz-form-label>
          <nz-form-control [nzSm]="20" [nzXs]="24">
            <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.liquidationDate" name="liquidationDate"> </nz-range-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng thái </nz-form-label>
          <nz-form-control [nzSm]="22" [nzXs]="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.isDeleted" name="isDeleted" nzPlaceHolder="Trạng thái">
              <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24" class="text-center">
        <button class="mr-2 ml-2" nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
          <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
        </button>
        <button
          [disabled]="setOfCheckedId.size <= 0"
          nz-button
          nzType="default"
          nzShape="round"
          class="mr-2 ant-btn-success"
          (click)="onConfirmList()"
        >
          <i nz-icon nzType="check-circle" nzTheme="outline"></i>Duyệt phiếu
        </button>
        <button [disabled]="setOfCheckedId.size <= 0" nz-button nzType="default" nzShape="round" class="mr-2 ant-btn-danger" (click)="onCancelList()">
          <i nz-icon nzType="close-circle" nzTheme="outline"></i>Hủy phiếu
        </button>
        <button nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
          <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
        </button>
      </div>
    </div>
  </nz-collapse-panel>
</nz-collapse>

<div nz-row class="mt-3">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
    [nzScroll]="{ x: '2000px' }"
  >
    <thead>
      <tr>
        <th nzWidth="50px" nzLeft></th>
        <th class="text-center" nzWidth="100px" nzLeft>Mã</th>
        <th class="text-center" nzWidth="200px">Tên phiếu</th>
        <th class="text-center" nzWidth="270px">Tên tài sản</th>
        <th class="text-center" nzWidth="270px">Loại tài sản</th>
        <th class="text-center" nzWidth="270px">UnitId</th>
        <th class="text-center" nzWidth="200px">Giá trị thanh lý</th>
        <th class="text-center" nzWidth="135px">Ngày thanh lý</th>
        <th class="text-center" nzWidth="165px">Trạng thái</th>
        <th class="text-center" nzWidth="180px" nzRight>Tác Vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td
          class="column-check"
          [nzChecked]="setOfCheckedId.has(data)"
          (nzCheckedChange)="onItemChecked(data, $event)"
          [nzDisabled]="!(data.status === enumData.ReviewStatus.New.code)"
          nzLeft
        ></td>
        <td class="text-center" nzLeft>{{ data.code }}</td>
        <td>{{ data.name }}</td>
        <td>{{ data.propertyName }}</td>
        <td>{{ data.propertyTypeName }}</td>
        <td>{{ data.unitId }}</td>
        <td class="text-right">{{ data.money | number }}</td>
        <td>{{ data.liquidationDate | date : 'dd/MM/yyyy' }}</td>
        <td class="text-center">
          <nz-tag class="tag-status-payment" [nzColor]="coreService.getEnumElementColor(enumData.ReviewStatus, data.status)">
            {{ coreService.getEnumElementName(enumData.ReviewStatus, data.status) }}
          </nz-tag>
        </td>
        <td class="text-center text-nowrap" nzRight>
          <button class="mr-1" (click)="onDetail(data)" nz-tooltip nzTooltipTitle="Xem chi tiết" nz-button nzType="primary" nzGhost nzShape="circle">
            <i nz-icon nzType="eye" nzTheme="outline"></i>
          </button>
          <button
            class="mr-1"
            *ngIf="data.status === enumData.ReviewStatus.New.code"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn duyệt lệnh?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="onConfirm(data)"
            nz-tooltip
            nzTooltipTitle="Duyệt lệnh"
            nz-button
            nzType="primary"
            nzShape="circle"
          >
            <i nz-icon nzType="check" nzTheme="outline"></i>
          </button>
          <button
            class="mr-1"
            *ngIf="data.status === enumData.ReviewStatus.New.code"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn hủy lệnh?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="onCancel(data)"
            nz-tooltip
            nzTooltipTitle="Hủy lệnh"
            nz-button
            nzType="primary"
            nzDanger
            nzShape="circle"
          >
            <i nz-icon nzType="close"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</div>
<div nz-col nzSpan="24" class="text-right">
  <nz-pagination
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
</div>
