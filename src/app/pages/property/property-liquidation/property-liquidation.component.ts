import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import * as moment from 'moment'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import * as XLSX from 'xlsx'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddPropertyLiquidationModelComponent } from './add-property-liquidation-model/add-property-liquidation-model.component'
import { PropertyLiquidationDetailModelComponent } from './property-liquidation-detail-model/property-liquidation-detail-model.component'

@Component({ templateUrl: './property-liquidation.component.html' })
export class PropertyLiquidationComponent implements OnInit {
  role: any
  currentUser: User | any
  enumData = enumData
  modalTitle: string = ''
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  dataStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstApartment: any = []
  lstRoomSrc: any = []
  lstRoom: any = []
  lstProperty: any = []
  dataUploadExcel: any = []

  setOfCheckedId = new Set<any>()
  checked = false
  indeterminate = false

  constructor(
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    // this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.modalTitle = enumData.Constants.Model_Add
    this.pageIndex = enumData.Page.pageIndex
    this.pageSize = enumData.Page.pageSize
    this.total = enumData.Page.total
    this.dataStatus = this.coreService.convertObjToArray(enumData.ReviewStatus)

    await this.loadAllData()
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.PROPERTY_LIQUIDATION.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  onShowAdd() {
    this.dialog
      .open(AddPropertyLiquidationModelComponent, { disableClose: false })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onDetail(object: any) {
    this.dialog
      .open(PropertyLiquidationDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onConfirm(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_LIQUIDATION.CONFIRM, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onCancel(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_LIQUIDATION.CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onConfirmList() {
    let data: any = []
    if (this.setOfCheckedId.size > 0) {
      this.setOfCheckedId.forEach((x) => {
        if (x.status === enumData.PayslipStatus.New.code) {
          data.push({ id: x.id })
        }
      })
      this.notifyService.showloading()
      this.appService.post(this.appService.PROPERTY_LIQUIDATION.CONFIRM_LIST, data).then((res) => {
        this.notifyService.showSuccess(res.message)
        this.notifyService.hideloading()
        this.searchData()
      })
    } else {
      this.notifyService.showError('Vui lòng chọn ít nhất một phiếu lệnh !')
    }
  }

  onCancelList() {
    let data: any = []
    if (this.setOfCheckedId.size > 0) {
      this.setOfCheckedId.forEach((x) => {
        if (x.status === enumData.PayslipStatus.New.code) {
          data.push({ id: x.id })
        }
      })
      this.notifyService.showloading()
      this.appService.post(this.appService.PROPERTY_LIQUIDATION.CANCEL_LIST, data).then((res) => {
        this.notifyService.showSuccess(res.message)
        this.notifyService.hideloading()
        this.searchData()
      })
    } else {
      this.notifyService.showError('Vui lòng chọn ít nhất một phiếu lệnh !')
    }
  }

  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id)
    } else {
      this.setOfCheckedId.delete(id)
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked)
    this.refreshCheckedStatus()
  }

  onAllChecked(value: boolean): void {
    this.setOfCheckedId = new Set<any>()
    this.listOfData.filter((x: { status: string }) => x.status !== 'CANCEL').forEach((item: number) => this.updateCheckedSet(item, value))
    this.refreshCheckedStatus()
  }

  onCurrentPageDataChange($event: any): void {
    this.listOfData = $event
    this.refreshCheckedStatus()
  }

  refreshCheckedStatus(): void {
    this.checked = this.listOfData.every((item: any) => this.setOfCheckedId.has(item))
    this.indeterminate = this.listOfData.some((item: any) => this.setOfCheckedId.has(item)) && !this.checked
  }

  async loadAllData() {
    this.notifyService.showloading()
    await Promise.all([
      this.appService.post(this.appService.APARTMENT.LOAD_DATA, {}),
      this.appService.post(this.appService.ROOM.LOAD_DATA, {}),
      this.appService.post(this.appService.PROPERTY.LOAD_DATA, {}),
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.lstApartment = res[0]
      this.lstRoomSrc = res[1]
      this.lstRoom = res[1]
      this.lstProperty = res[2]
    })
  }

  async onChangeApartment(event: any) {
    delete this.dataSearch.roomId
    this.lstRoom = []
    if (event) this.lstRoom = this.lstRoomSrc.filter((e: any) => e.apartmentId == event)
    await this.searchData()
  }

  onDownloadTemplateExcel() {
    let date = new Date().toISOString()
    let dataExcel: any = []
    let fileName = 'Danh_Sach_Lenh_Thanh_Ly_' + date + '.xlsx'
    dataExcel.push({
      'Mã khu trọ (Apartment Code)*': '',
      'Mã phòng (Room Code)': '',
      'UnitId tài sản (UnitId Code)*': '',
      'Giá Thanh Lý (money)*': '',
      'Ngày thanh lý (Liquidation Date)*': '',
      'Ghi chú (Note)*': '',
    })

    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
    const wb: XLSX.WorkBook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Danh Sách Lệnh Thanh Lý')
    XLSX.writeFile(wb, fileName)
  }

  onImportExcel(ev: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    this.dataUploadExcel = []
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['apartmentCode', 'roomCode', 'propertyUnitId', 'money', 'liquidationDate', 'note'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      let strErr = ''
      for (const row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.apartmentCode == null || (typeof row.apartmentCode === 'string' && row.apartmentCode.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã khu không được để trống  <br>'
        }
        if (row.propertyUnitId == null || (typeof row.propertyUnitId === 'string' && row.propertyUnitId.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Mã unitId của tài sản không được để trống  <br>'
        }
        if (row.money == null || (typeof row.money === 'string' && row.money.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Giá thanh lý không được để trống  <br>'
        }
        if (row.liquidationDate == null || (typeof row.liquidationDate === 'string' && row.liquidationDate.trim().length == 0)) {
          strErr += 'Dòng ' + idx + ' - Ngày thanh lý không được để trống <br>'
        } else {
          row.liquidationDate = this.coreService.convertToDate(row.liquidationDate)
          if (!row.liquidationDate) {
            strErr += 'Dòng ' + idx + ' - Ngày thanh lý không đúng định dạng <br>'
          }
        }
      }
      if (strErr.length > 0) {
        this.notifyService.hideloading()
        this.notifyService.showError(strErr)
        return
      }
      this.appService.post(this.appService.PROPERTY_LIQUIDATION.EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.searchData()
        this.dataUploadExcel = []
      })
    }
  }

  async onDownloadExcel() {
    this.loading = true

    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 10000,
    }
    this.loading = true
    this.appService.post(this.appService.PROPERTY_LIQUIDATION.PAGINATION, dataSearch).then((res) => {
      if (res) {
        this.loading = false
        this.notifyService.hideloading()
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách lệnh thanh lý tài sản')
        //#region Body Table
        const header = ['Mã', 'Tên phiếu', 'Tên tài sản', 'Giá trị thanh lý', 'Ngày thanh lý', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet.getColumn(colNumber).width = 15
              break
            case 2:
              worksheet.getColumn(colNumber).width = 100
              break
            case 3:
              worksheet.getColumn(colNumber).width = 35
              break
            case 4:
              worksheet.getColumn(colNumber).width = 25
              worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.name || '',
            data.propertyName || '',
            data.money || 0,
            data.liquidationDate ? moment(new Date(data.liquidationDate)).format('DD/MM/YYYY)') : '',
            data.status ? this.coreService.getEnumElementName(this.enumData.ReviewStatus, data.status) : '',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_LENH_THANH_LY_TAI_SAN_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
