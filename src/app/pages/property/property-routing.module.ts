import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { PropertyLiquidationComponent } from './property-liquidation/property-liquidation.component'
import { PropertyTransferComponent } from './property-transfer/property-transfer.component'
import { PropertyTypeComponent } from './property-type/property-type.component'
import { PropertyReportComponent } from './property/report/property-report.component'
import { WarehouseComponent } from './warehouse/warehouse.component'
import { PropertyComponent } from './property/property/property.component'
import { InboundComponent } from './inbound/inbound.component'
import { InventoryComponent } from './property/inventory/inventory.component'

const routes: Routes = [
  { path: 'property', component: PropertyComponent },
  { path: 'property-type', component: PropertyTypeComponent },
  { path: 'property-transfer', component: PropertyTransferComponent },
  { path: 'property-liquidation', component: PropertyLiquidationComponent },
  { path: 'property-report', component: PropertyReportComponent },
  { path: 'warehouse', component: WarehouseComponent },
  { path: 'inbound', component: InboundComponent },
  { path: 'inventory', component: InventoryComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PropertyRoutingModule { }
