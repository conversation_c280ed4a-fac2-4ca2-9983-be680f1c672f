import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import * as moment from 'moment'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { User } from '../../../models/user.model'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddPropertyTransferModelComponent } from './add-property-transfer-model/add-property-transfer-model.component'
import { PropertyTransferDetailModelComponent } from './property-transfer-detail-model/property-transfer-detail-model.component'

@Component({ templateUrl: './property-transfer.component.html' })
export class PropertyTransferComponent implements OnInit {
  role: any
  currentUser: User | any
  enumData = enumData
  modalTitle: string = ''
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  dataResident: any = []
  dataService: any = []
  dataArea: any = []
  dataApartmentSource: any = []
  dataApartment: any = []
  dataRoomSource: any = []
  dataCurRoom: any = []
  lstCurRoom: any = []
  dataNewRoom: any = []
  lstNewRoom: any = []
  dataStatus: any = []
  lstCurWarehouse: any = []
  lstNewWarehouse: any = []
  constructor(
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    public coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    // this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit(): void {
    this.loadAllDataSelect()
    this.modalTitle = enumData.Constants.Model_Add
    this.pageIndex = enumData.Page.pageIndex
    this.pageSize = enumData.Page.pageSize
    this.total = enumData.Page.total
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataStatus = this.coreService.convertObjToArray(enumData.PropertyTransferStatus)
    this.dataSearch.isDeleted = false
    this.searchData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.PROPERTY_TRANSFER.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([this.appService.post(this.appService.APARTMENT.LOAD_DATA, {})]).then(async (res) => {
      this.notifyService.hideloading()
      this.dataApartment = res[0]
    })
  }

  async onChangeCurrentApartment(obj: any) {
    this.dataSearch.currentRoomId = this.dataSearch.currentWarehouseId = ''
    if (obj) {
      this.lstCurRoom = await this.appService.post(this.appService.ROOM.LOAD_DATA_BY_APARTMENT, { apartmentId: obj })
      this.lstCurWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: obj })
    } else {
      this.lstCurRoom = []
      this.lstCurWarehouse = []
    }
  }

  onChangeCurrentRoom() {
    this.dataSearch.currentWarehouseId = ''
  }

  onChangeCurrentWarehouse() {
    this.dataSearch.currentRoomId = ''
  }

  async onChangeNewApartment(obj: any) {
    this.dataSearch.newRoomId = this.dataSearch.newWarehouseId = ''
    if (obj) {
      this.lstNewRoom = await this.appService.post(this.appService.ROOM.LOAD_DATA_BY_APARTMENT, { apartmentId: obj })
      this.lstNewWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: obj })
    } else {
      this.lstNewRoom = []
      this.lstNewWarehouse = []
    }
  }

  onChangeNewRoom() {
    this.dataSearch.newWarehouseId = ''
  }

  onChangeNewWarehouse() {
    this.dataSearch.newRoomId = ''
  }

  onShowAdd() {
    this.dialog
      .open(AddPropertyTransferModelComponent, { disableClose: false })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onDetail(object: any) {
    this.dialog
      .open(PropertyTransferDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onConfirm(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_TRANSFER.CONFIRM, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onSend(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_TRANSFER.SEND, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onCancel(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_TRANSFER.CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async onDownloadExcel() {
    this.loading = true
    this.notifyService.showloading()
    const dataSearch = {
      where: this.dataSearch,
      order: { createdAt: 'DESC' },
      skip: 0,
      take: 10000,
    }
    this.loading = true
    this.appService.post(this.appService.PROPERTY_TRANSFER.PAGINATION, dataSearch).then((res) => {
      if (res) {
        this.loading = false
        this.notifyService.hideloading()
        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách điều chuyển tài sản')
        //#region Body Table
        const header = ['Mã', 'Tên phiếu', 'Khu chuyển', 'Phòng chuyển', 'Khu đi', 'Phòng đi', 'Ngày tạo', 'Trạng thái']

        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '203751' },
            bgColor: { argb: '203751' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet.getColumn(colNumber).width = 15
              break
            case 2:
              worksheet.getColumn(colNumber).width = 100
              break
            case 3:
              worksheet.getColumn(colNumber).width = 30
              break
            case 4:
              worksheet.getColumn(colNumber).width = 20
              break
            case 5:
              worksheet.getColumn(colNumber).width = 30
              break
            case 6:
              worksheet.getColumn(colNumber).width = 20
              break
            default:
              worksheet.getColumn(colNumber).width = 15
              break
          }
        })

        for (let data of res[0]) {
          const rowData = [
            data.code || '',
            data.name || '',
            data.currentApartmentName || '',
            data.currentRoomName || '',
            data.newApartmentName || '',
            data.newRoomName || '',
            data.createdAt ? moment(new Date(data.createdAt)).format('DD/MM/YYYY)') : '',
            data.status ? this.coreService.getEnumElementName(this.enumData.PropertyTransferStatus, data.status) : '',
          ]

          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }

        //#region Save File
        workbook.xlsx.writeBuffer().then((data) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `DANH_SACH_LENH_DIEU_CHUYEN_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }
}
