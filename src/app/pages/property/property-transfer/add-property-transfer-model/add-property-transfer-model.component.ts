import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './add-property-transfer-model.component.html' })
export class AddPropertyTransferModelComponent implements OnInit {
  enumData: any
  modelTitle: any
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  loading = false
  dataObject: any
  isEditItem = false
  lstPropertySrc: any[] = []
  lstProperty: any[] = []
  lstPropertyType: any = []
  lstApartment: any = []
  lstNewApartment: any = []
  lstRoom: any = []
  lstNewRoom: any = []
  lstRoomSrc: any = []
  dataSearch: any = ({} = [])
  setOfCheckedId = new Set<any>()
  checked = false
  indeterminate = false
  dataChoose: any = []
  modelTitleSub: any
  editCache: { [key: string]: { edit: boolean; data: any } } = {}
  dataChooseSrc: any = []
  lstWarehouse: any = []
  lstNewWarehouse: any = []

  totalQuantitySelect: number = 0
  totalPriceSelect: number = 0

  totalQuantitySave: number = 0
  totalPriceSave: number = 0

  currentDay = new Date()

  setOfCheckedId1 = new Set<any>()
  checked1 = false
  indeterminate1 = false

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<AddPropertyTransferModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataObject = new Object()
    this.isEditItem = true
    this.modelTitle = 'TẠO LỆNH ĐIỀU CHUYỂN TÀI SẢN'
    this.modelTitleSub = 'DANH SÁCH TÀI SẢN'
    await this.loadAllData()
  }

  async loadAllData() {
    this.notifyService.showloading()
    await Promise.all([
      this.appService.post(this.appService.PROPERTY_TYPE.LOAD_DATA, {}),
      this.appService.post(this.appService.APARTMENT.LOAD_DATA, {}),
      this.appService.post(this.appService.PROPERTY.LOAD_DATA, {}),
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.lstPropertyType = res[0]
      this.lstApartment = res[1]
      this.lstPropertySrc = res[2]
      this.lstProperty = this.lstPropertySrc
    })
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true
    let where: any = {}

    if (this.dataObject.currentApartmentId && this.dataObject.currentApartmentId !== null) {
      where.apartmentId = this.dataObject.currentApartmentId
    }

    if (this.dataObject.currentRoomId && this.dataObject.currentRoomId !== null) {
      where.roomId = this.dataObject.currentRoomId
    }

    if (this.dataObject.currentWarehouseId && this.dataObject.currentWarehouseId !== null) {
      where.warehouseId = this.dataObject.currentWarehouseId
    }

    if (this.dataSearch.propertyTypeId && this.dataSearch.propertyTypeId !== null) {
      where.propertyTypeId = this.dataSearch.propertyTypeId
    }

    if (this.dataSearch.propertyId && this.dataSearch.propertyId !== null) {
      where.propertyId = this.dataSearch.propertyId
    }

    const dataSearch = {
      where,
      skip: 0,
      take: 100000,
    }

    this.appService.post(this.appService.PROPERTY_DETAIL.PAGINATION_CUSTOM_FOR_PROPERTY_TRANSFER, dataSearch).then((data: any) => {
      if (data) {
        this.listOfData = data[0]
        this.total = data[1]
        this.loading = false
      }
    })
  }

  onChangeProperty() {
    if (this.dataSearch.propertyId && !this.dataSearch.propertyTypeId) {
      const property = this.lstProperty.find((c) => c.id == this.dataSearch.propertyId)
      this.dataSearch.propertyTypeId = property?.propertyTypeId
    }

    this.searchData()
  }

  onChangePropertyType() {
    this.dataSearch.propertyId = null
    this.lstProperty = this.lstPropertySrc
    if (this.dataSearch.propertyTypeId) {
      this.lstProperty = this.lstPropertySrc.filter((c) => c.propertyTypeId == this.dataSearch.propertyTypeId)
    }
    this.searchData()
  }

  onChangeNumPropertyChoose() {
    if (!this.dataObject.currentRoomId && !this.dataObject.currentWarehouseId) {
      this.notifyService.showError(`Vui lòng chọn phòng hoặc kho trước!`)
      delete this.dataSearch.numPropertyChoose
      return
    }
    if (!this.dataSearch.propertyId) {
      this.notifyService.showError(`Vui lòng chọn tài sản trước!`)
      delete this.dataSearch.numPropertyChoose
      return
    }

    if (!(this.dataSearch.numPropertyChoose > 0)) {
      this.notifyService.showError(`Vui lòng nhập SL hợp lệ!`)
      delete this.dataSearch.numPropertyChoose
      return
    }

    // bỏ check các dòng cũ
    this.setOfCheckedId.clear()
    this.totalPriceSelect = 0
    this.totalQuantitySelect = 0
    if (this.listOfData.length < this.dataSearch.numPropertyChoose) {
      this.notifyService.showInfo(`"Số tài sản tìm được" < "SL Tài sản chọn" => hệ thống tự động chọn hết!`)
    }

    // check lại dòng mới
    for (let i = 0; i < this.listOfData.length; i++) {
      if (i < this.dataSearch.numPropertyChoose) {
        this.updateCheckedSet(this.listOfData[i], true)
      } else break
    }
    this.refreshCheckedStatus()
  }

  onSave() {
    this.dataObject.lstPropertyDetail = this.dataChoose
    const data = this.dataObject
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_TRANSFER.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(result.message)
        this.closeDialog()
      }
    })
  }

  async onChangeApartment(event: any) {
    this.setOfCheckedId = new Set<any>()
    this.dataChoose = []
    this.dataObject.currentRoomId = this.dataObject.currentWarehouseId = ''
    if (event) {
      this.lstRoom = await this.appService.post(this.appService.ROOM.FIND, { apartmentId: event })
      this.lstWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: event })
    } else {
      this.lstRoom = []
      this.lstWarehouse = []
    }
    if (event) await this.searchData()
    else this.listOfData = []
  }

  async onChangeCurrentRoom() {
    this.setOfCheckedId = new Set<any>()
    this.dataObject.currentWarehouseId = ''
    await this.searchData()
  }

  async onChangeCurrentWarehouse() {
    this.setOfCheckedId = new Set<any>()
    this.dataObject.currentRoomId = ''
    await this.searchData()
  }

  onChangeNewRoom() {
    this.dataObject.newWarehouseId = ''
  }

  onChangeNewWarehouse() {
    this.dataObject.newRoomId = ''
  }

  async onChangeNewApartment(event: any) {
    this.dataObject.newRoomId = this.dataObject.newWarehouseId = ''
    if (event) {
      this.lstNewRoom = await this.appService.post(this.appService.ROOM.FIND, { apartmentId: event, isDeleted: false })
      this.lstNewWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, { apartmentId: event })
    } else {
      this.lstNewRoom = []
      this.lstNewWarehouse = []
    }
  }

  async onChangeRoom() {
    this.dataSearch.roomId = this.dataObject.currentRoomId
    await this.searchData()
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  updateCheckedSet(object: any, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(object)
      this.totalQuantitySelect++
      this.totalPriceSelect += +object.price
    } else {
      this.setOfCheckedId.delete(object)
      this.totalQuantitySelect--
      this.totalPriceSelect -= +object.price
    }
  }

  onItemChecked(item: any, checked: boolean): void {
    this.updateCheckedSet(item, checked)
    this.refreshCheckedStatus()
  }

  onAllChecked(value: boolean): void {
    this.setOfCheckedId = new Set<any>()
    for (let item of this.listOfData) this.updateCheckedSet(item, value)
    this.refreshCheckedStatus()
  }

  onCurrentPageDataChange($event: any): void {
    this.listOfData = $event
    this.refreshCheckedStatus()
  }

  refreshCheckedStatus(): void {
    this.checked = this.listOfData.every((item: any) => this.setOfCheckedId.has(item))
    this.indeterminate = this.listOfData.some((item: any) => this.setOfCheckedId.has(item)) && !this.checked
  }

  onAddProperty() {
    this.totalPriceSave = 0
    if (this.setOfCheckedId.size > 0) {
      const lstId = this.dataChoose.map((e: any) => e.id)

      const filterDubId = [...this.setOfCheckedId].filter((e: any) => !lstId.includes(e.id))

      this.dataChoose = [...filterDubId, ...this.dataChoose]

      let indexTemp = 0
      this.dataChoose.forEach((element: any) => {
        element.i = indexTemp
        this.editCache[indexTemp] = {
          edit: false,
          data: { index: indexTemp, ...element },
        }
        indexTemp++

        this.totalPriceSave += +element.price
      })

      this.setOfCheckedId.clear()
      this.checked = false
    }

    delete this.dataSearch.numPropertyChoose
    this.totalQuantitySelect = this.totalPriceSelect = 0
  }

  onDelete() {
    this.totalPriceSave = 0
    const lstId = [...this.setOfCheckedId1].map((e: any) => e.id)
    this.dataChoose = this.dataChoose.filter((e: any) => !lstId.includes(e.id))
    for (let e of this.dataChoose) this.totalPriceSave += +e.price

    const lst = [...this.setOfCheckedId1]
    for (let item of lst) this.updateCheckedSet1(item, false)
    this.refreshCheckedStatus1()
    this.setOfCheckedId1.clear()
    this.checked1 = false
  }

  updateCheckedSet1(object: any, checked: boolean): void {
    if (checked) this.setOfCheckedId1.add(object)
    else this.setOfCheckedId1.delete(object)
  }

  onItemChecked1(id: number, checked: boolean): void {
    this.updateCheckedSet1(id, checked)
    this.refreshCheckedStatus1()
  }

  onAllChecked1(value: boolean): void {
    this.setOfCheckedId1 = new Set<any>()
    for (let item of this.dataChoose) this.updateCheckedSet1(item, value)
    this.refreshCheckedStatus1()
  }

  refreshCheckedStatus1(): void {
    this.checked1 = this.dataChoose.every((item: any) => this.setOfCheckedId1.has(item))
    this.indeterminate1 = this.dataChoose.some((item: any) => this.setOfCheckedId1.has(item)) && !this.checked1
  }
}
