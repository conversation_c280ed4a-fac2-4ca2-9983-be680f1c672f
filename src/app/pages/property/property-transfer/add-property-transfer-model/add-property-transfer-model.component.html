<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
    <br />
  </div>

  <form nz-form class="ant-advanced-search-form" #frmAdd="ngForm" class="p-4">
    <div nz-row class="mt-4" nzGutter="8">
      <div nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Ngày đi </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.moveOutDate"
              name="dataObject.moveOutDate"
              nzFormat="dd/MM/yyyy"
            ></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Khu nhận </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Khu"
              [(ngModel)]="dataObject.newApartmentId"
              name="newApartmentId"
              (ngModelChange)="onChangeNewApartment($event)"
            >
              <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Phòng nhận </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Phòng nhận"
              [(ngModel)]="dataObject.newRoomId"
              name="newRoomId"
              (ngModelChange)="onChangeNewRoom()"
            >
              <nz-option *ngFor="let item of lstNewRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left">Kho nhận </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Kho nhận"
              [(ngModel)]="dataObject.newWarehouseId"
              name="newWarehouseId"
              (ngModelChange)="onChangeNewWarehouse()"
            >
              <nz-option *ngFor="let item of lstNewWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <nz-row>
      <div nz-col nzSpan="24" class="text-center">
        <span class="text-title" style="font-size: 20px">{{ modelTitleSub }}</span>
        <br />
      </div>
      <nz-collapse nz-col nzSpan="24" [nzBordered]="false">
        <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
          <div nz-row nzGutter="8">
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">Khu đi </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn khu!">
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Khu"
                    [(ngModel)]="dataObject.currentApartmentId"
                    name="currentApartmentId"
                    (ngModelChange)="onChangeApartment($event)"
                  >
                    <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">Phòng đi </nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Phòng"
                    [(ngModel)]="dataObject.currentRoomId"
                    name="currentRoomId"
                    (ngModelChange)="onChangeCurrentRoom()"
                  >
                    <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">Kho đi </nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Kho"
                    [(ngModel)]="dataObject.currentWarehouseId"
                    name="currentWarehouseId"
                    (ngModelChange)="onChangeCurrentWarehouse()"
                  >
                    <nz-option *ngFor="let item of lstWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">Loại tài sản </nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Loại tài sản"
                    [(ngModel)]="dataSearch.propertyTypeId"
                    name="propertyTypeId"
                    (ngModelChange)="onChangePropertyType()"
                  >
                    <nz-option *ngFor="let item of lstPropertyType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">Tài sản </nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Tài sản"
                    [(ngModel)]="dataSearch.propertyId"
                    name="propertyId"
                    (ngModelChange)="onChangeProperty()"
                  >
                    <nz-option *ngFor="let item of lstPropertyType" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>

            <div nz-col nzSpan="8" *ngIf="dataSearch.propertyId && (dataObject.currentRoomId || dataObject.currentWarehouseId)">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nz-tooltip nzTooltipTitle="Sau khi ấn tìm kiếm sẽ chọn nhanh"
                  >SL Tài sản chọn
                </nz-form-label>
                <nz-form-control nzSpan="24">
                  <input
                    nz-input
                    placeholder="0"
                    [(ngModel)]="dataSearch.numPropertyChoose"
                    name="numPropertyChoose"
                    (ngModelChange)="onChangeNumPropertyChoose()"
                    currencyMask
                    [options]="{ prefix: '', precision: 0, allowNegative: false }"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>

            <div nz-col nzSpan="24" class="text-center">
              <button nzShape="round" class="mr-2" nz-button nzType="primary" nzGhost (click)="searchData()">
                <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
              </button>
            </div>
          </div>
        </nz-collapse-panel>
      </nz-collapse>

      <div nz-col nzSpan="24" class="table-scroll-item">
        <h3>Tổng số lượng: {{ totalQuantitySelect }} - Tổng giá trị {{ totalPriceSelect }} VNĐ</h3>
      </div>

      <div nz-col nzSpan="24" class="table-scroll-item">
        <nz-table
          class="mb-3"
          nz-col
          nzSpan="24"
          #ajaxTable
          [nzData]="['']"
          [(nzPageSize)]="pageSize"
          [nzShowPagination]="false"
          nzBordered
          nzTableLayout="fixed"
          [nzScroll]="{ x: '1000px', y: '600px' }"
          [nzLoading]="loading"
        >
          <thead>
            <tr>
              <th
                column-check
                [nzChecked]="checked"
                [nzIndeterminate]="indeterminate"
                (nzCheckedChange)="onAllChecked($event)"
                nzWidth="50px"
                nzLeft
              ></th>
              <th nzWidth="150px">Khu</th>
              <th nzWidth="100px">Phòng</th>
              <th nzWidth="150px">Kho</th>
              <th nzWidth="150px">Mã tài sản</th>
              <th nzWidth="150px">Tên tài sản</th>
              <th nzWidth="150px">Loại tài sản</th>
              <th nzWidth="150px">UnitId</th>
              <th nzWidth="150px">Ngày đưa vào</th>
              <th nzWidth="150px">Ngày bảo hành</th>
              <th nzWidth="150px">Ngày hết hạn bảo hành</th>
              <th nzWidth="150px" nzRight>Giá</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let item of listOfData; let i = index">
              <tr>
                <td
                  class="column-check"
                  [nzChecked]="setOfCheckedId.has(item)"
                  (nzCheckedChange)="onItemChecked(item, $event)"
                  nzWidth="50px"
                  nzLeft
                ></td>
                <td class="text-center">{{ item.apartmentName }}</td>
                <td class="text-center">{{ item.roomName }}</td>
                <td class="text-center">{{ item.warehouseName }}</td>
                <td class="text-center">{{ item.code }}</td>
                <td class="text-center">{{ item.name }}</td>
                <td class="text-center">{{ item.propertyTypeName }}</td>
                <td class="text-center">{{ item.unitId }}</td>
                <td class="text-center">{{ item.putInDate | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center">{{ item.warrantyDate | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center">{{ item.warrantyExpirationDate | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center" nzWidth="150px" nzRight>{{ item.price | number }}</td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </div>
      <div nz-col nzSpan="24" class="text-center mb-3" *ngIf="listOfData && listOfData.length > 0">
        <button nz-button [disabled]="setOfCheckedId.size === 0" nzShape="round" nzType="primary" class="mr-3" nzDanger (click)="onAddProperty()">
          <i nz-icon nzType="plus" nzTheme="outline"></i>Xem trước
        </button>
      </div>
    </nz-row>

    <nz-row *ngIf="dataChoose?.length > 0">
      <div nz-col nzSpan="24" class="table-scroll-item">
        <h3>Tổng số lượng: {{ dataChoose?.length }} - Tổng giá trị {{ totalPriceSave }} VNĐ</h3>
      </div>
      <div nz-col nzSpan="24" class="text-center">
        <span class="text-title" style="font-size: 20px">{{ modelTitleSub }}</span>
        <br />
      </div>

      <button
        [disabled]="setOfCheckedId1.size < 1"
        nz-tooltip
        nzTooltipTitle=" Xóa"
        class="mr-1"
        nz-button
        nzType="primary"
        nzDanger
        nzShape="circle"
        (click)="onDelete()"
      >
        <i nz-icon nzType="delete" nzTheme="outline"></i>
      </button>

      <div nz-col nzSpan="24">
        <nz-table
          class="mb-3"
          nz-col
          nzSpan="24"
          #ajaxTable
          [nzData]="['']"
          [(nzPageSize)]="pageSize"
          [nzShowPagination]="false"
          nzBordered
          nzTableLayout="fixed"
          [nzScroll]="{ y: '300px' }"
        >
          <thead>
            <tr>
              <th
                class="text-center"
                column-check
                [nzChecked]="checked1"
                [nzIndeterminate]="indeterminate1"
                (nzCheckedChange)="onAllChecked1($event)"
                nzWidth="50px"
                nzLeft
              ></th>
              <th>Khu</th>
              <th>Phòng</th>
              <th>Kho</th>
              <th>Mã tài sản</th>
              <th>Tên tài sản</th>
              <th>Loại tài sản</th>
              <th>Ngày đưa vào</th>
              <th>Ngày bảo hành</th>
              <th>Ngày hết hạn bảo hành</th>
              <th>Giá</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let item of dataChoose; let i = index">
              <tr>
                <td
                  class="column-check"
                  [nzChecked]="setOfCheckedId1.has(item)"
                  (nzCheckedChange)="onItemChecked1(item, $event)"
                  nzWidth="50px"
                  nzLeft
                ></td>
                <td class="text-center">{{ item.apartmentName }}</td>
                <td class="text-center">{{ item.roomName }}</td>
                <td class="text-center">{{ item.warehouseName }}</td>
                <td class="text-center">{{ item.code }}</td>
                <td class="text-center">{{ item.name }}</td>
                <td class="text-center">{{ item.propertyTypeName }}</td>
                <td class="text-center">{{ item.putInDate | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center">{{ item.warrantyDate | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center">{{ item.warrantyExpirationDate | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td class="text-center">{{ item.price | number }}</td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </div>
    </nz-row>

    <div nz-row>
      <div nz-col nzSpan="24" class="text-center">
        <button
          nz-button
          [disabled]="
            !frmAdd.form.valid || dataChoose?.length < 1 || (!dataObject.newApartmentId && !dataObject.newRoomId && !dataObject.newWarehouseId)
          "
          nzShape="round"
          nzType="primary"
          class="mr-3"
          (click)="onSave()"
        >
          <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
        </button>
      </div>
    </div>
  </form>
</div>
