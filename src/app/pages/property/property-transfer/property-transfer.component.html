<nz-collapse [nzBordered]="false">
  <nz-collapse-panel nzHeader="<PERSON><PERSON><PERSON>" nzActive="true" class="ant-bg-antiquewhite">
    <div nz-row nzGutter="8">
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON> chuy<PERSON>n </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.currentApartmentId"
              name="currentApartmentId"
              nzPlaceHolder="Khu chuyển"
              (ngModelChange)="onChangeCurrentApartment($event)"
            >
              <nz-option *ngFor="let item of dataApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Phòng chuyển </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.currentRoomId"
              name="currentRoomId"
              nzPlaceHolder="Phòng chuyển"
              (ngModelChange)="onChangeCurrentRoom()"
            >
              <nz-option *ngFor="let item of lstCurRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Kho chuyển </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.currentWarehouseId"
              name="currentWarehouseId"
              nzPlaceHolder="Kho chuyển"
              (ngModelChange)="onChangeCurrentWarehouse()"
            >
              <nz-option *ngFor="let item of lstCurWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Khu nhận </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.newApartmentId"
              name="newApartmentId"
              nzPlaceHolder="Khu nhận"
              (ngModelChange)="onChangeNewApartment($event)"
            >
              <nz-option *ngFor="let item of dataApartment" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Phòng nhận </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.newRoomId"
              name="newRoomId"
              nzPlaceHolder="Phòng nhận"
              (ngModelChange)="onChangeNewRoom()"
            >
              <nz-option *ngFor="let item of lstNewRoom" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Kho nhận </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataSearch.newWarehouseId"
              name="newWarehouseId"
              nzPlaceHolder="Kho nhận"
              (ngModelChange)="onChangeNewWarehouse()"
            >
              <nz-option *ngFor="let item of lstNewWarehouse" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mã phiếu </nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input [(ngModel)]="dataSearch.code" placeholder="Mã phiếu" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Trạng thái </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Trạng thái">
              <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Ngày tạo </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-range-picker [nzShowTime]="false" [(ngModel)]="dataSearch.createdAt" name="createdAt"> </nz-range-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24" class="text-center">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
          <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
        </button>
        <button class="ml-2" nz-button nzType="default" (click)="onDownloadExcel()" nzShape="round">
          <i nz-icon nzType="download" nzTheme="outline"></i> Tải Excel
        </button>
      </div>
    </div>
  </nz-collapse-panel>
</nz-collapse>

<div nz-row nzGutter="8" class="mt-2">
  <div nz-col nzSpan="24">
    <button nzShape="round" nz-button nzType="primary" (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  </div>
</div>
<div class="table-scroll-item mt-2">
  <nz-row nzGutter="8">
    <nz-table
      class="mb-3"
      nz-col
      nzSpan="24"
      #ajaxTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
      nzTableLayout="fixed"
      [nzScroll]="{ x: '2000px', y: '600px' }"
    >
      <thead>
        <tr>
          <th class="text-center" nzLeft nzWidth="120px">Mã</th>
          <th class="text-center" nzWidth="120px">Ngày điều chuyển</th>
          <th class="text-center" nzWidth="120px">Ngày duyệt</th>
          <th class="text-center" nzWidth="120px">Người duyệt</th>
          <th class="text-center" nzWidth="120px">Khu đi</th>
          <th class="text-center" nzWidth="120px">Khu nhận</th>
          <th class="text-center" nzWidth="120px">Phòng nhận</th>
          <th class="text-center" nzWidth="120px">Kho nhận</th>
          <th class="text-center" nzRight nzWidth="160px">Trạng thái</th>
          <th class="text-center" nzRight nzWidth="200px">Tác Vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td class="text-center" nzLeft>{{ data.code }}</td>
          <td class="text-center">{{ data.createdAt | date : 'HH:mm dd/MM/yyyy' }}</td>
          <td class="text-center">{{ data.moveOutDate | date : 'HH:mm dd/MM/yyyy' }}</td>
          <td class="text-center">{{ data.updatedByName }}</td>
          <td class="text-center">{{ data.currentApartmentName }}</td>
          <td class="text-center">{{ data.newApartmentName }}</td>
          <td class="text-center">{{ data.newRoomName }}</td>
          <td class="text-center">{{ data.newWarehouseName }}</td>
          <td class="text-center" nzRight>
            <nz-tag class="tag-status-payment" [nzColor]="coreService.getEnumElementColor(enumData.PropertyTransferStatus, data.status)">
              {{ coreService.getEnumElementName(enumData.PropertyTransferStatus, data.status) }}
            </nz-tag>
          </td>
          <td class="text-center text-nowrap" nzRight>
            <button
              class="mr-1"
              (click)="onDetail(data)"
              nz-tooltip
              nzTooltipTitle="Xem chi tiết"
              nz-button
              nzType="primary"
              nzGhost
              nzShape="circle"
            >
              <i nz-icon nzType="eye" nzTheme="outline"></i>
            </button>
            <button
              class="mr-1"
              *ngIf="data.status === enumData.PropertyTransferStatus.NEW.code"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn gửi phiếu?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="onSend(data)"
              nz-tooltip
              nzTooltipTitle="Gửi phiếu"
              nz-button
              nzType="primary"
              nzShape="circle"
            >
              <i nz-icon nzType="arrow-right" nzTheme="outline"></i>
            </button>
            <button
              class="mr-1"
              *ngIf="dataSearch.status === enumData.PropertyTransferStatus.WAIT.code"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn duyệt phiếu?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="onConfirm(data)"
              nz-tooltip
              nzTooltipTitle="Duyệt phiếu"
              nz-button
              nzType="primary"
              nzShape="circle"
            >
              <i nz-icon nzType="check" nzTheme="outline"></i>
            </button>
            <button
              class="mr-1"
              *ngIf="dataSearch.status == enumData.PropertyTransferStatus.NEW.code || dataSearch.status == enumData.PropertyTransferStatus.WAIT.code"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn hủy phiếu?"
              nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="onCancel(data)"
              nz-tooltip
              nzTooltipTitle="Hủy phiếu"
              nz-button
              nzType="primary"
              nzDanger
              nzShape="circle"
            >
              <i nz-icon nzType="close"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <div nz-col nzSpan="24" class="text-right">
      <nz-pagination
        [nzTotal]="total"
        [(nzPageIndex)]="pageIndex"
        [(nzPageSize)]="pageSize"
        (nzPageIndexChange)="searchData()"
        (nzPageSizeChange)="searchData(true)"
        [nzShowTotal]="rangeTemplate"
        nzShowSizeChanger
      >
      </nz-pagination>
      <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
    </div>
  </nz-row>
</div>
