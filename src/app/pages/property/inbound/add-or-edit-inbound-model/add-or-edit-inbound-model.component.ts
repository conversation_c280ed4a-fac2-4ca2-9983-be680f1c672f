import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'

declare var Object: any
@Component({ templateUrl: './add-or-edit-inbound-model.component.html' })
export class AddOrEditInboundModelComponent implements OnInit {
  enumData: any
  modelTitle: any
  dataObject: any = {}
  isEditItem = false
  lstApartment: any = []
  lstRoom: any = []
  lstWarehouse: any = []
  lstSupplier: any = []
  lstProperty: any = []
  today = new Date()
  inboundDetail: any = {}
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditInboundModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    await this.loadAllData()
    if (this.data && this.data !== null) {
      this.dataObject = await this.appService.post(this.appService.INBOUND.FIND_FOR_EDIT, { id: this.data.id })
      for (let ibDetail of this.dataObject.lstInboundDetail) {
        await this.onChangeProperty(ibDetail)
        if (ibDetail.apartmentId) this.onChangeApartmentEdit(ibDetail)
        // Kho công ty
        else ibDetail.lstWarehouse = []
      }
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT PHIẾU NHẬP KHO'
    } else {
      this.modelTitle = 'THÊM MỚI PHIẾU NHẬP KHO'
      this.dataObject = new Object()
      this.dataObject.lstInboundDetail = []
    }
    this.notifyService.hideloading()
  }

  async loadAllData() {
    await Promise.all([
      this.appService.post(this.appService.APARTMENT.LOAD_DATA, {}),
      this.appService.post(this.appService.SUPPLIER.LOAD_DATA, {}),
      this.appService.post(this.appService.PROPERTY.LOAD_DATA, {}),
    ]).then((res) => {
      this.lstApartment = res[0]
      this.lstSupplier = res[1]
      this.lstProperty = res[2]
    })
  }

  onSave() {
    const data = this.dataObject
    let i = 0
    for (let ibDetail of data.lstInboundDetail) {
      i++
      if (!ibDetail.propertyId) {
        this.notifyService.showError(`Dòng [${i}] Tài sản không được để trống!`)
        return
      }
      // Nếu không chọn apartmentId thì phải chọn Kho công ty
      if (!ibDetail.apartmentId && !ibDetail.warehouseId) {
        this.notifyService.showError(`Dòng [${i}] Chọn khu hoặc kho công ty!`)
        return
      }
      if (ibDetail.roomId && ibDetail.warehouseId) {
        this.notifyService.showError(`Dòng [${i}] Chỉ chọn phòng hoặc kho!`)
        return
      }
      if (!ibDetail?.roomId && !ibDetail?.warehouseId) {
        this.notifyService.showError(`Dòng [${i}] Phòng hoặc kho không được để trống!`)
        return
      }
      if (ibDetail.quantity < 1) {
        this.notifyService.showError(`Dòng [${i}] Số lượng nhập phải lớn hơn hoặc bằng 1!`)
        return
      }
      // if (ibDetail?.dateOfEntry && ibDetail?.dateOfWarranty)
      //   if (new Date(ibDetail?.dateOfEntry).getTime() < new Date(ibDetail?.dateOfWarranty).getTime()) {
      //     this.notifyService.showError(`Dòng [${i}] Ngày đưa vào không được bé hơn ngày bảo hành!`)
      //     return
      //   }
      // if (ibDetail?.dateOfEntry && ibDetail?.dateOfExpiryWarranty)
      //   if (new Date(ibDetail?.dateOfEntry).getTime() > new Date(ibDetail?.dateOfExpiryWarranty).getTime()) {
      //     this.notifyService.showError(`Dòng [${i}] Ngày đưa vào không được lớn hơn ngày hết hạn bảo hành!`)
      //     return
      //   }
      if (ibDetail?.dateOfWarranty && ibDetail?.dateOfExpiryWarranty)
        if (new Date(ibDetail?.dateOfWarranty).getTime() > new Date(ibDetail?.dateOfExpiryWarranty).getTime()) {
          this.notifyService.showError(`Dòng [${i}] Ngày bảo hành không được lớn hơn ngày hết hạn bảo hành!`)
          return
        }
    }
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  async onAddInboundDetail() {
    // Nếu không chọn tài sản chủ nhà thì đơn giá phải lớn hơn 0
    if (!this.inboundDetail.propertyOfHomeowner && !this.inboundDetail.price) {
      this.notifyService.showError(`Vui lòng nhập đơn giá!`)
      return
    }
    if (this.inboundDetail.propertyId) {
      this.dataObject.lstInboundDetail.push({
        inboundId: '',
        propertyTypeId: this.inboundDetail.propertyTypeId,
        propertyTypeName: this.inboundDetail.propertyTypeName,
        propertyId: this.inboundDetail.propertyId,
        lstRoom: this.inboundDetail?.apartmentId ? this.lstRoom : [],
        lstWarehouse: this.inboundDetail?.apartmentId ? this.lstWarehouse : [],
        apartmentId: this.inboundDetail?.apartmentId ? this.inboundDetail.apartmentId : '',
        apartmentName: '',
        roomId: this.inboundDetail?.roomId ? this.inboundDetail.roomId : '',
        warehouseId: this.inboundDetail?.warehouseId ? this.inboundDetail.warehouseId : '',
        propertyOfHomeowner: this.inboundDetail?.propertyOfHomeowner ? this.inboundDetail.propertyOfHomeowner : false,
        quantity: this.inboundDetail?.quantity ? this.inboundDetail.quantity : 0,
        realQuantity: this.inboundDetail?.realQuantity ? this.inboundDetail.realQuantity : 0,
        price: this.inboundDetail?.price ? this.inboundDetail.price : 0,
        intoMoney: (this.inboundDetail.quantity ? this.inboundDetail.quantity : 0) * (this.inboundDetail.price ? this.inboundDetail.price : 0),
        dateOfEntry: this.inboundDetail?.dateOfEntry ? this.inboundDetail.dateOfEntry : '',
        dateOfWarranty: this.inboundDetail?.dateOfWarranty ? this.inboundDetail.dateOfWarranty : '',
        dateOfExpiryWarranty: this.inboundDetail?.dateOfExpiryWarranty ? this.inboundDetail.dateOfExpiryWarranty : '',
        description: this.inboundDetail?.description ? this.inboundDetail.description : '',
      })
    } else
      this.dataObject.lstInboundDetail.push({
        inboundId: '',
        propertyTypeId: '',
        propertyTypeName: '',
        propertyId: '',
        lstRoom: [],
        lstWarehouse: [],
        apartmentId: '',
        apartmentName: '',
        roomId: '',
        warehouseId: '',
        propertyOfHomeowner: false,
        quantity: 0,
        realQuantity: 0,
        price: 0,
        intoMoney: 0,
        dateOfEntry: '',
        dateOfWarranty: '',
        dateOfExpiryWarranty: '',
        description: '',
      })

    this.inboundDetail = {}
  }

  async onChangeProperty(item: any) {
    const findProp = this.lstProperty.find((e: any) => e.id == item.propertyId)
    item.propertyTypeName = findProp.propertyTypeName
    item.propertyTypeId = findProp.propertyTypeId
  }

  async onChangeRoom(item: any) {
    item.warehouseId = ''
  }

  async onChangeWarehouse(item: any) {
    item.roomId = ''
  }

  async onChangeRoomEdit(item: any) {
    // const findApartment = this.lstApartment.find((e: any) => e.id == item.apartmentId)
    // item.apartmentName = findApartment.name
    // item.apartmentId = findApartment.id
  }

  async onChangeWarehouseEdit(item: any) {
    // const findApartment = this.lstApartment.find((e: any) => e.id == item.apartmentId)
    // item.apartmentName = findApartment.name
    // item.apartmentId = findApartment.id
  }

  onChangePropertyOfHomeowner(item: any) {
    item.price = 0
  }

  async onChangeApartment(item: any) {
    this.lstRoom = []
    this.lstWarehouse = item.lstWarehouse = []
    if (item.apartmentId) {
      item.roomId = item.warehouseId = ''
      const findApartment = this.lstApartment.find((e: any) => e.id == item.apartmentId)
      item.apartmentName = findApartment.name
      this.lstRoom = item.lstRoom = await this.appService.post(this.appService.ROOM.FIND, {
        apartmentId: item.apartmentId,
        isDeleted: false,
      })

      this.lstWarehouse = item.lstWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, {
        apartmentId: item.apartmentId,
      })
    }
  }

  async onChangeApartmentEdit(item: any) {
    item.lstRoom = await this.appService.post(this.appService.ROOM.FIND, {
      apartmentId: item.apartmentId,
      isDeleted: false,
    })
    this.lstWarehouse = item.lstWarehouse = await this.appService.post(this.appService.WAREHOUSE.FIND, {
      apartmentId: item.apartmentId,
    })
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.INBOUND.CREATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.INBOUND.UPDATE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  onDeleteInboundDetail(index: any) {
    this.dataObject.lstInboundDetail.splice(index, 1)
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
