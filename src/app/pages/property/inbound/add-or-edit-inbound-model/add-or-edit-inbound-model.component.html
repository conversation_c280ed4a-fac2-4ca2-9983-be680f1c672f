<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
    <br />
  </div>

  <form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
    <div nz-row nzGutter="8" class="mt-4" nzAlign="middle" nzJustify="center">
      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Số hoá đơn</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập số hoá đơn (1-100 ký tự)!">
            <input nz-input placeholder="Nhập số hoá đơn" [(ngModel)]="dataObject.billNumber" name="billNumber" pattern=".{1,100}" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhà cung cấp </nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng chọn nhà cung cấp!">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn nhà cung cấp" [(ngModel)]="dataObject.supplierId" name="supplierId">
              <nz-option *ngFor="let item of lstSupplier" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mô tả</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập mô tả không quá 250 kí tự!">
            <textarea rows="1" nz-input placeholder="Nhập mô tả" [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <nz-tabset class="mt-3 mb-2">
      <nz-tab nzTitle="Thông tin tài sản">
        <div nz-row nzGutter="8" class="mt-4" nzAlign="middle" nzJustify="center">
          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Tên tài sản </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng tên tài sản!">
                <nz-select
                  nzShowSearch
                  [(ngModel)]="inboundDetail.propertyId"
                  name="propertyName"
                  nzPlaceHolder="Chọn tên tài sản"
                  (ngModelChange)="onChangeProperty(inboundDetail)"
                >
                  <nz-option *ngFor="let item of lstProperty" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Mã tài sản </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng mã tài sản!">
                <nz-select
                  nzShowSearch
                  [(ngModel)]="inboundDetail.propertyId"
                  name="propertyCode"
                  nzPlaceHolder="Chọn mã tài sản"
                  (ngModelChange)="onChangeProperty(inboundDetail)"
                >
                  <nz-option *ngFor="let item of lstProperty" [nzLabel]="item.code" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Loại tài sản</nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24">
                <span>{{ inboundDetail.propertyTypeName }}</span>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày bảo hành </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24">
                <nz-date-picker
                  nzShowSearch
                  nzAllowClear
                  [(ngModel)]="inboundDetail.dateOfWarranty"
                  name="inboundDetail.dateOfWarranty"
                  nzFormat="dd/MM/yyyy"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày hết hạn </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24">
                <nz-date-picker
                  nzShowSearch
                  nzAllowClear
                  [(ngModel)]="inboundDetail.dateOfExpiryWarranty"
                  name="inboundDetail.dateOfExpiryWarranty"
                  nzFormat="dd/MM/yyyy"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày đưa vào sử dụng </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng ngày đưa vào sử dụng!">
                <nz-date-picker
                  nzShowSearch
                  nzAllowClear
                  [(ngModel)]="inboundDetail.dateOfEntry"
                  name="inboundDetail.dateOfEntry"
                  nzFormat="dd/MM/yyyy"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Tài sản chủ nhà </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24">
                <label
                  nz-checkbox
                  [(ngModel)]="inboundDetail.propertyOfHomeowner"
                  name="inboundDetail.propertyOfHomeowner"
                  (ngModelChange)="onChangePropertyOfHomeowner(inboundDetail)"
                ></label>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Số lượng </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng số nhập lượng!">
                <input
                  style="font-weight: bold; border-radius: 25px"
                  nz-input
                  placeholder="Nhập số lượng nhập"
                  [(ngModel)]="inboundDetail.quantity"
                  name="inboundDetail.quantity"
                  currencyMask
                  [options]="{ prefix: '', precision: 0, allowNegative: false }"
                />
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Đơn giá </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng đơn giá!">
                <input
                  [disabled]="inboundDetail.propertyOfHomeowner"
                  style="font-weight: bold; border-radius: 25px"
                  nz-input
                  placeholder="Nhập đơn giá"
                  [(ngModel)]="inboundDetail.price"
                  name="inboundDetail.price"
                  currencyMask
                  [options]="{ prefix: '', precision: 0, allowNegative: false }"
                />
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Thành tiền </nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24">
                <span>{{
                  (inboundDetail?.quantity ? +inboundDetail.quantity : 0) * (inboundDetail?.price ? +inboundDetail.price : 0) | number
                }}</span>
              </nz-form-control>
            </nz-form-item>
          </div>

          <nz-col nzSpan="24">
            <nz-row nzGutter="8">
              <div nz-col nzSpan="8">
                <nz-form-item>
                  <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Khu </nz-form-label>
                  <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng khu!">
                    <nz-select
                      nzShowSearch
                      nzAllowClear
                      [(ngModel)]="inboundDetail.apartmentId"
                      name="inboundDetail.apartmentId"
                      nzPlaceHolder="Chọn khu"
                      (ngModelChange)="onChangeApartment(inboundDetail)"
                    >
                      <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>

              <div nz-col nzSpan="8">
                <nz-form-item>
                  <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Phòng </nz-form-label>
                  <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng phòng!">
                    <nz-select
                      nzShowSearch
                      [(ngModel)]="inboundDetail.roomId"
                      name="inboundDetail.roomId"
                      nzPlaceHolder="Chọn phòng"
                      (ngModelChange)="onChangeRoom(inboundDetail)"
                    >
                      <nz-option *ngFor="let item of lstRoom" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>

              <div nz-col nzSpan="8">
                <nz-form-item>
                  <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Kho </nz-form-label>
                  <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng kho!">
                    <nz-select
                      nzShowSearch
                      [(ngModel)]="inboundDetail.warehouseId"
                      name="inboundDetail.warehouseId"
                      nzPlaceHolder="Chọn kho"
                      (ngModelChange)="onChangeWarehouse(inboundDetail)"
                    >
                      <nz-option *ngFor="let item of lstWarehouse" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </nz-row>
          </nz-col>

          <div nz-col nzSpan="24">
            <nz-form-item nzFlex>
              <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ghi chú</nz-form-label>
              <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập ghi chú không quá 250 kí tự!">
                <textarea rows="1" nz-input placeholder="Nhập ghi chú" [(ngModel)]="inboundDetail.description" name="description"></textarea>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <nz-row class="mt-2" nzJustify="center">
          <button [disabled]="!inboundDetail.propertyId" class="mr-2" nz-button nzType="primary" (click)="onAddInboundDetail()">Thêm tài sản</button>
        </nz-row>

        <nz-row class="mt-2">
          <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" nzBordered nzTemplateMode [nzScroll]="{ x: '1200px' }">
            <thead>
              <tr>
                <th nzWidth="80px" nzLeft>STT</th>
                <th nzWidth="220px" nzLeft><span class="text-danger">* </span>Tên tài sản</th>
                <th nzWidth="220px"><span class="text-danger">* </span>Mã tài sản</th>
                <th nzWidth="150px"><span class="text-danger">* </span>Loại tài sản</th>
                <th nzWidth="250px">Khu</th>
                <th nzWidth="200px">Phòng</th>
                <th nzWidth="200px">Kho</th>
                <th nzWidth="120px">Tài sản chủ nhà</th>
                <th nzWidth="200px">Số lượng nhập</th>
                <th nzWidth="200px">Đơn giá</th>
                <th nzWidth="200px">Thành tiền</th>
                <th nzWidth="200px">Ngày đưa vào</th>
                <th nzWidth="200px">Ngày bảo hành</th>
                <th nzWidth="200px">Ngày hết hạn</th>
                <th nzWidth="200px">Ghi chú</th>
                <th nzWidth="150px" nzRight>Tác vụ</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of dataObject.lstInboundDetail; let i = index">
                <td nzLeft class="text-center">
                  {{ i + 1 }}
                </td>
                <td nzLeft class="text-center">
                  <nz-select
                    nzShowSearch
                    [(ngModel)]="item.propertyId"
                    [name]="'propertyId' + i"
                    nzPlaceHolder="Chọn tên tài sản"
                    (ngModelChange)="onChangeProperty(item)"
                  >
                    <nz-option *ngFor="let item of lstProperty" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </td>
                <td class="text-center">
                  <nz-select
                    nzShowSearch
                    [(ngModel)]="item.propertyId"
                    [name]="'propertyId' + i"
                    nzPlaceHolder="Chọn mã tài sản"
                    (ngModelChange)="onChangeProperty(item)"
                  >
                    <nz-option *ngFor="let item of lstProperty" [nzLabel]="item.code" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </td>
                <td class="text-center">
                  {{ item.propertyTypeName }}
                </td>
                <td class="text-center">
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    [(ngModel)]="item.apartmentId"
                    [name]="'apartmentId' + i"
                    nzPlaceHolder="Chọn khu"
                    (ngModelChange)="onChangeApartment(item)"
                  >
                    <nz-option *ngFor="let item of lstApartment" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </td>
                <td class="text-center">
                  <nz-select
                    nzShowSearch
                    [(ngModel)]="item.roomId"
                    [name]="'roomId' + i"
                    nzPlaceHolder="Chọn phòng"
                    (ngModelChange)="onChangeRoom(item)"
                  >
                    <nz-option *ngFor="let item of item.lstRoom" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </td>
                <td class="text-center">
                  <nz-select
                    nzShowSearch
                    [(ngModel)]="item.warehouseId"
                    [name]="'warehouseId' + i"
                    nzPlaceHolder="Chọn kho"
                    (ngModelChange)="onChangeWarehouse(item)"
                  >
                    <nz-option *ngFor="let item of item.lstWarehouse" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </td>
                <!-- Tài sản chủ nhà? -->
                <td class="text-center">
                  <label
                    nz-checkbox
                    [(ngModel)]="item.propertyOfHomeowner"
                    [name]="'propertyOfHomeowner' + i"
                    (ngModelChange)="onChangePropertyOfHomeowner(item)"
                  ></label>
                </td>
                <td class="text-center">
                  <input
                    style="font-weight: bold; border-radius: 25px"
                    nz-input
                    placeholder="Nhập số lượng nhập"
                    [(ngModel)]="item.quantity"
                    [name]="'quantity' + i"
                    required
                    currencyMask
                    [options]="{ prefix: '', precision: 0, allowNegative: false }"
                  />
                </td>
                <td class="text-center">
                  <input
                    [disabled]="item.propertyOfHomeowner"
                    style="font-weight: bold; border-radius: 25px"
                    nz-input
                    placeholder="Nhập đơn giá"
                    [(ngModel)]="item.price"
                    [name]="'price' + i"
                    required
                    currencyMask
                    [options]="{ prefix: '', precision: 0, allowNegative: false }"
                  />
                </td>
                <td class="text-center">
                  <span>{{ (item?.quantity ? +item.quantity : 0) * (item?.price ? +item.price : 0) | number }}</span>
                </td>
                <!-- Ngày sản xuất -->
                <td class="text-center">
                  <nz-date-picker
                    nzShowSearch
                    nzAllowClear
                    [(ngModel)]="item.dateOfEntry"
                    [name]="'dateOfEntry' + i"
                    nzFormat="dd/MM/yyyy"
                  ></nz-date-picker>
                </td>
                <td class="text-center">
                  <nz-date-picker
                    nzShowSearch
                    nzAllowClear
                    [(ngModel)]="item.dateOfWarranty"
                    [name]="'dateOfWarranty' + i"
                    nzFormat="dd/MM/yyyy"
                  ></nz-date-picker>
                </td>
                <!-- Ngày hết hạn -->
                <td class="text-center">
                  <nz-date-picker
                    nzShowSearch
                    nzAllowClear
                    [(ngModel)]="item.dateOfExpiryWarranty"
                    [name]="'dateOfExpiryWarranty' + i"
                    nzFormat="dd/MM/yyyy"
                  ></nz-date-picker>
                </td>
                <td class="text-center">
                  <input placeholder="Nhập ghi chú" nz-input [name]="'description' + i" [(ngModel)]="item.description" />
                </td>
                <th nzWidth="150px" nzRight class="text-center">
                  <button
                    nz-button
                    nzDanger
                    nz-tooltip
                    nzTooltipTitle="Xóa"
                    nz-popconfirm
                    nzPopconfirmTitle="Bạn có chắc muốn [XÓA]?"
                    (nzOnConfirm)="onDeleteInboundDetail(i)"
                    class="mr-2"
                  >
                    <span nz-icon nzType="delete"></span>
                  </button>
                </th>
              </tr>
            </tbody>
          </nz-table>
        </nz-row>
      </nz-tab>
    </nz-tabset>
    <div nz-row>
      <div nz-col nzSpan="24" class="text-center">
        <button
          nz-button
          [disabled]="!frmAdd.form.valid || dataObject.lstInboundDetail?.length < 1"
          nzShape="round"
          nzType="primary"
          class="mr-3"
          (click)="onSave()"
        >
          <i nz-icon nzType="save" nzTheme="outline"></i> Lưu
        </button>
      </div>
    </div>
  </form>
</div>
