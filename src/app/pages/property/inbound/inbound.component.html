<div nz-row nzGutter="8" nzAlign="middle" nzJustify="start">
  <button nzShape="round" class="mr-2" nz-button nzType="primary" (click)="onShowAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  <button nzShape="round" class="mr-2" nz-button (click)="onDownloadTemplateExcel()">
    <span nz-icon nzType="download"></span>Tải Template Excel
  </button>
  <input
    class="hidden"
    type="file"
    id="file"
    (change)="clickImportExcel($event)"
    placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
  />
  <label nz-button for="file" class="ant-btn lable-custom-file-custom"> <span nz-icon nzType="upload"></span> Nhập Excel </label>
  <nz-collapse nz-col nzSpan="24" [nzBordered]="false" class="mt-2">
    <nz-collapse-panel nzHeader="Tìm Kiếm" nzActive="true" class="ant-bg-antiquewhite">
      <div nz-row nzGutter="8">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mã phiếu nhập kho </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.code" name="dataSearch.code" placeholder="Nhập mã phiếu nhập kho" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Số hoá đơn </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <input nz-input [(ngModel)]="dataSearch.billNumber" name="dataSearch.billNumber" placeholder="Nhập số hoá đơn" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhà cung cấp </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.supplierId" name="supplierId" nzPlaceHolder="Chọn nhà cung cấp">
                <nz-option *ngFor="let item of lstSupplier" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng thái </nz-form-label>
            <nz-form-control [nzSm]="20" [nzXs]="24">
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Chọn trạng thái">
                <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="24" class="text-center">
          <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true)">
            <i nz-icon nzType="search" nzTheme="outline"></i>Tìm kiếm
          </button>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<div nz-row nzGutter="8" class="mt-2">
  <nz-table
    class="mb-2"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th class="text-center">Mã phiếu</th>
        <th class="text-center">Số hoá đơn</th>
        <th class="text-center">Ngày tạo</th>
        <th class="text-center">Ngày duyệt</th>
        <th class="text-center">Nhà cung cấp</th>
        <th class="text-center">Trạng thái</th>
        <th class="text-center">Mô Tả</th>
        <th class="text-center text-nowrap">Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">
          {{ data.code }}
        </td>
        <td class="text-center">
          {{ data.billNumber }}
        </td>
        <td class="text-center">
          {{ data.createdAt | date : 'dd/MM/yyyy HH:mm' }}
        </td>
        <td class="text-center">
          {{ data.approveDate | date : 'dd/MM/yyyy HH:mm' }}
        </td>
        <td class="text-center">
          {{ data.supplierName }}
        </td>
        <td class="text-center">
          {{ data.statusName }}
        </td>
        <td class="text-center">
          {{ data.description }}
        </td>
        <td class="text-center">
          <button
            class="mr-2 mt-2"
            *ngIf="data.isNew"
            (click)="onShowDetail(data)"
            nz-tooltip
            nzTooltipTitle="Duyệt phiếu nhập kho"
            nz-button
            nzType="primary"
            nzShape="circle"
          >
            <i nz-icon nzType="check" nzTheme="outline"></i>
          </button>
          <button
            class="mr-2 mt-2"
            *ngIf="data.isNew"
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn huỷ phiếu nhập kho?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="cancel(data)"
            nz-tooltip
            nzTooltipTitle="Huỷ phiếu nhập kho"
            nz-button
            nzType="primary"
            nzDanger
            nzShape="circle"
          >
            <i nz-icon nzType="close"></i>
          </button>
          <br *ngIf="data.isNew" />
          <button
            class="mr-2 mt-2"
            *ngIf="data.isNew"
            (click)="onShowEdit(data)"
            nz-tooltip
            nzTooltipTitle="Chỉnh Sửa"
            nz-button
            nzType="primary"
            nzGhost
            nzShape="circle"
          >
            <i nz-icon nzType="edit"></i>
          </button>
          <button
            class="mr-2 mt-2"
            (click)="onShowDetail(data)"
            nz-tooltip
            nzGhost
            nzTooltipTitle="Xem chi tiết phiếu nhập kho"
            nz-button
            nzType="primary"
            nzShape="circle"
          >
            <i nz-icon nzType="eye"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
