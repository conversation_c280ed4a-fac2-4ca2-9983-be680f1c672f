<div class="p-4">
  <div class="text-center">
    <span class="text-title">{{ modelTitle }}</span>
  </div>

  <nz-tabset class="mt-4">
    <nz-tab nzTitle="Thông Tin Chung">
      <div nz-row class="mt-4">
        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">M<PERSON> phiếu nhập kho</nz-form-label>
            <nz-form-control>
              <b>{{ dataObject.code }}</b>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Số hoá đơn</nz-form-label>
            <nz-form-control>
              <b *ngIf="dataObject.billNumber">{{ dataObject.billNumber }}</b>
              <i class="text-danger" *ngIf="!dataObject.billNumber">Không có thông tin</i>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Ngày tạo</nz-form-label>
            <nz-form-control>
              <b>{{ dataObject.createdAt | date : 'dd/MM/yyyy HH:mm' }}</b>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhà cung cấp</nz-form-label>
            <nz-form-control>
              <b *ngIf="dataObject.supplierName">{{ dataObject.supplierName }}</b>
              <i class="text-danger" *ngIf="!dataObject.supplierName">Không có thông tin</i>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Trạng thái</nz-form-label>
            <nz-form-control>
              <b>{{ coreService.getEnumElementName(enumData.InboundStatus, dataObject.status) }}</b>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col nzSpan="6" *ngIf="dataObject.description">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Mô Tả </nz-form-label>
            <nz-form-control>
              <b>{{ dataObject.description }}</b>
            </nz-form-control>
          </nz-form-item>
        </div>

        <nz-col nzSpan="24">
          <nz-row class="mt-2">
            <nz-col nzSpan="12">
              <h2>Danh sách tài sản</h2>
            </nz-col>
            <nz-col nzSpan="12">
              <h2 class="text-right">Tổng số lượng: {{ totalQuantity | number }} - Tổng tiền: {{ totalAmount | number }} VNĐ</h2>
            </nz-col>
          </nz-row>
          <nz-row>
            <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" nzBordered nzTemplateMode [nzScroll]="{ x: '1200px', y: '600px' }">
              <thead>
                <tr>
                  <th nzWidth="80px" nzLeft>STT</th>
                  <th nzWidth="220px" nzLeft><span class="text-danger">* </span>Tên tài sản</th>
                  <th nzWidth="220px"><span class="text-danger">* </span>Mã tài sản</th>
                  <th nzWidth="150px"><span class="text-danger">* </span>Loại tài sản</th>
                  <th nzWidth="200px">Phòng</th>
                  <th nzWidth="200px">Kho</th>
                  <th nzWidth="250px">Khu</th>
                  <th nzWidth="120px">Tài sản chủ nhà</th>
                  <th nzWidth="200px">Số lượng nhập</th>
                  <th nzWidth="200px">Đơn giá</th>
                  <th nzWidth="200px">Thành tiền</th>
                  <th nzWidth="200px">Ngày đưa vào</th>
                  <th nzWidth="200px">Ngày bảo hành</th>
                  <th nzWidth="200px">Ngày hết hạn bảo hành</th>
                  <th nzWidth="200px" nzRight>Ghi chú</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of dataObject.lstInboundDetail; let i = index">
                  <td nzLeft class="text-center">
                    {{ i + 1 }}
                  </td>
                  <td nzLeft class="text-center">
                    {{ item.propertyName }}
                  </td>
                  <td class="text-center">
                    {{ item.propertyCode }}
                  </td>
                  <td class="text-center">
                    {{ item.propertyTypeName }}
                  </td>
                  <td class="text-center">
                    {{ item?.roomName }}
                  </td>
                  <td class="text-center">
                    {{ item?.warehouseName }}
                  </td>
                  <td class="text-center">
                    {{ item?.apartmentName }}
                  </td>
                  <!-- Tài sản chủ nhà? -->
                  <td class="text-center">
                    {{ item.propertyOfHomeowner ? 'Có' : 'Không' }}
                  </td>
                  <td class="text-center">
                    <span>{{ (item?.quantity ? +item.quantity : 0) | number }}</span>
                  </td>
                  <td class="text-center">
                    <span>{{ (item?.price ? +item.price : 0) | number }}</span>
                  </td>
                  <td class="text-center">
                    <span>{{ (item?.quantity ? +item.quantity : 0) * (item?.price ? +item.price : 0) | number }}</span>
                  </td>
                  <!-- Ngày sản xuất -->
                  <td class="text-center">
                    {{ item.dateOfEntry | date : 'dd/MM/yyyy' }}
                  </td>
                  <td class="text-center">
                    {{ item.dateOfWarranty | date : 'dd/MM/yyyy' }}
                  </td>
                  <!-- Ngày hết hạn -->
                  <td class="text-center">
                    {{ item.dateOfExpiryWarranty | date : 'dd/MM/yyyy' }}
                  </td>
                  <td class="text-center" nzRight>
                    <span>{{ item.description }}</span>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-col>
      </div>
    </nz-tab>
    <nz-tab nzTitle="Lịch sử chỉnh sửa">
      <nz-row class="mt-2">
        <nz-table nz-col nzSpan="24" [nzFrontPagination]="false" nzBordered nzTemplateMode>
          <thead>
            <tr>
              <th nzWidth="80px" nzLeft>STT</th>
              <th>Ghi chú</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataObject.lstInboundHistories; let i = index">
              <td nzLeft class="text-center">
                {{ i + 1 }}
              </td>
              <td class="text-center" nzRight>
                <span>{{ item.description }}</span>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-tab>
  </nz-tabset>

  <div nz-row class="mt-3">
    <div nz-col nzSpan="24" class="text-center">
      <button
        class="mr-2 mt-2"
        *ngIf="dataObject.status == enumData.InboundStatus.NEW.code"
        style="background-color: green"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc muốn duyệt phiếu nhập kho?"
        nzPopconfirmPlacement="bottom"
        (nzOnConfirm)="approve(dataObject)"
        nz-tooltip
        nzTooltipTitle="Duyệt phiếu nhập kho"
        nz-button
        nzType="primary"
        nzShape="round"
      >
        Duyệt
        <i nz-icon nzType="check" nzTheme="outline"></i>
      </button>
      <button nz-button nzShape="round" nzType="default" class="mr-2" (click)="closeDialog()">
        <i nz-icon nzType="lock" nzTheme="outline"></i> Huỷ
      </button>
    </div>
  </div>
</div>
