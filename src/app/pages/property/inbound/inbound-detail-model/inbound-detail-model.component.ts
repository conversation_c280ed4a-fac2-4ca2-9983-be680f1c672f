import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services'
import { AuthenticationService } from '../../../../services/authentication.service'
import { CoreService } from '../../../../services/core.service'
import { NotifyService } from '../../../../services/notify.service'

@Component({ templateUrl: './inbound-detail-model.component.html' })
export class InboundDetailModelComponent implements OnInit {
  enumData: any
  modelTitle: string = 'CHI TIẾT PHIẾU NHẬP KHO'
  dataObject: any
  totalQuantity: number = 0
  totalAmount: number = 0
  role: any
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<InboundDetailModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async ngOnInit() {
    this.dataObject = this.data
    this.dataObject = await this.appService.post(this.appService.INBOUND.FIND_DETAIL, { id: this.data.id })
    this.totalQuantity = this.coreService.sumArray(this.dataObject.lstInboundDetail, 'quantity')

    for (let i = 0; i < this.dataObject.lstInboundDetail.length; i++) {
      this.totalAmount += +this.dataObject.lstInboundDetail[i].quantity * +this.dataObject.lstInboundDetail[i].price
    }
  }

  closeDialog() {
    this.dialogRef.close(1)
  }

  approve(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.INBOUND.UPDATE_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.hideloading()
      this.notifyService.showSuccess(res.message)
      this.closeDialog()
    })
  }
}
