import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Workbook } from 'exceljs'
import * as fs from 'file-saver'
import * as moment from 'moment'
import { enumData } from 'src/app/core/enumData'
import { ApiNtssService } from 'src/app/services'
import * as XLSX from 'xlsx'
import { AuthenticationService } from '../../../services/authentication.service'
import { CoreService } from '../../../services/core.service'
import { NotifyService } from '../../../services/notify.service'
import { AddOrEditInboundModelComponent } from './add-or-edit-inbound-model/add-or-edit-inbound-model.component'
import { InboundDetailModelComponent } from './inbound-detail-model/inbound-detail-model.component'

@Component({ templateUrl: './inbound.component.html' })
export class InboundComponent implements OnInit {
  enumData: any
  modalTitle: string = ''
  pageIndex: number = 0
  pageSize: number = 0
  total: number = 0
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  lstSupplier: any = []

  constructor(
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private coreService: CoreService,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit(): void {
    this.modalTitle = enumData.Constants.Model_Add
    this.pageIndex = this.enumData.Page.pageIndex
    this.pageSize = this.enumData.Page.pageSize
    this.total = this.enumData.Page.total
    this.dataFilterStatus = this.coreService.convertObjToArray(this.enumData.InboundStatus)
    this.searchData()
    this.loadAllData()
  }

  async searchData(reset: boolean = false) {
    if (reset) {
      this.pageIndex = 1
    }
    this.loading = true

    const where: any = {}
    if (this.dataSearch.billNumber && this.dataSearch.billNumber !== '') {
      const key = 'billNumber'
      where[key] = this.dataSearch.billNumber
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      const key = 'code'
      where[key] = this.dataSearch.code
    }

    if (this.dataSearch.supplierId && this.dataSearch.supplierId !== '') {
      const key = 'supplierId'
      where[key] = this.dataSearch.supplierId
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      const key = 'status'
      where[key] = this.dataSearch.status
    }

    const dataSearch = {
      where,
      order: { createdAt: 'DESC' },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.appService.post(this.appService.INBOUND.PAGINATION, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  async loadAllData() {
    this.lstSupplier = await this.appService.post(this.appService.SUPPLIER.LOAD_DATA, {})
  }

  onShowAdd() {
    this.dialog
      .open(AddOrEditInboundModelComponent, { disableClose: false })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowEdit(object: any) {
    this.dialog
      .open(AddOrEditInboundModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  onShowDetail(object: any) {
    this.dialog
      .open(InboundDetailModelComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((data) => {
        this.searchData()
      })
  }

  approve(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.INBOUND.UPDATE_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  cancel(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.INBOUND.UPDATE_CANCEL, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  onDownloadTemplateExcel() {
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Danh sách phiếu nhập kho')

    //#region Body Table
    const header = ['STT phiếu * (code)', 'Số hoá đơn (billNumber)', 'MST nhà cung cấp (taxCode)', 'Mô tả (description)']
    const headerRow = worksheet.addRow(header)

    // Cell Style : Fill and Border
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet.getColumn(colNumber).width = 30
          break
        case 4:
          worksheet.getColumn(colNumber).width = 30
          break
        default:
          worksheet.getColumn(colNumber).width = 12
          break
      }
    })

    const worksheet2 = workbook.addWorksheet('Danh sách tài sản')

    //#region Body Table
    const header2 = [
      'STT phiếu * (code)',
      'Mã tài sản * (propertyCode)',
      'Mã khu (apartmentCode)',
      'Tên phòng (roomName)',
      'Mã kho (warehouseCode)',
      'Tài sản chủ nhà (propertyOfHomeowner - x nếu đúng)',
      'Số lượng nhập (quantity)',
      'Đơn giá (price)',
      'Ngày đưa vào (dateOfEntry - dd/MM/yyyy)',
      'Ngày bảo hành (dateOfWarranty - dd/MM/yyyy)',
      'Ngày hết hạn bảo hành (dateOfExpiryWarranty - dd/MM/yyyy)',
      'Ghi chú (description)',
    ]
    const headerRow2 = worksheet2.addRow(header2)

    // Cell Style : Fill and Border
    headerRow2.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '203751' },
        bgColor: { argb: '203751' },
      }
      cell.alignment = { horizontal: 'center' }
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      }

      switch (colNumber) {
        case 1:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 2:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 3:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 4:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 5:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 6:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 7:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 8:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 9:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 10:
          worksheet2.getColumn(colNumber).width = 30
          break
        case 11:
          worksheet2.getColumn(colNumber).width = 30
          break
        default:
          worksheet2.getColumn(colNumber).width = 12
          break
      }
    })

    //#region Save File
    workbook.xlsx.writeBuffer().then((data: any) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      let date = new Date().toISOString()
      const fileName = `DANH_SACH_PHIEU_NHAP_KHO${date}.xlsx`
      fs.saveAs(blob, fileName)
      this.notifyService.hideloading()
    })
    //#endregion
  }

  clickImportExcel(event: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    let jsonData1: any = null
    const reader = new FileReader()
    const file = event.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['code', 'billNumber', 'taxCode', 'description'],
      })
      // fix lỗi k import 2 lần đc
      ;(<HTMLInputElement>document.getElementById('file')).value = ''

      // bỏ dòng merge
      jsonData.shift()

      jsonData1 = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[1]], {
        raw: true,
        defval: null,
        header: [
          'code',
          'propertyCode',
          'apartmentCode',
          'roomName',
          'warehouseCode',
          'propertyOfHomeowner',
          'quantity',
          'price',
          'dateOfEntry',
          'dateOfWarranty',
          'dateOfExpiryWarranty',
          'description',
        ],
      })
      jsonData1.shift()
      // bỏ dòng header
      let isErr = false
      let strErr = ''
      for (let row of jsonData) {
        let idx = jsonData.indexOf(row) + 2
        if (row.code == null || (typeof row.code === 'string' && row.code.trim().length == 0)) {
          strErr += 'Sheet 1 - Dòng ' + idx + ' - Mã phiếu nhập kho không được để trống  <br>'
        }
        if (strErr.length > 0) {
          this.notifyService.showError(strErr)
          return
        }
      }

      for (let row of jsonData1) {
        let idx = jsonData1.indexOf(row) + 2
        if (row.code == null || (typeof row.code === 'string' && row.code.trim().length == 0)) {
          strErr += 'Sheet 2 - Dòng ' + idx + ' - Mã phiếu nhập kho không được để trống <br>'
        }
        if (row.propertyCode == null || (typeof row.propertyCode === 'string' && row.propertyCode.trim().length == 0)) {
          strErr += 'Sheet 2 - Dòng ' + idx + ' - Mã tài sản được để trống <br>'
        }
        if (row.apartmentCode) {
          if (row.roomName && row.warehouseCode) {
            strErr += 'Sheet 2 - Dòng ' + idx + ' - Không thể cùng nhập mã phòng và mã kho <br>'
          }
        }
        // Nếu không nhập mã khu thì mặc định là kho nhà trọ
        else {
          if (row.roomName || row.warehouseCode) {
            strErr += 'Sheet 2 - Dòng ' + idx + ' - Không thể nhập mã phòng hoặc mã kho khi không chọn mã khu <br>'
          }
        }

        row.propertyOfHomeowner = row.propertyOfHomeowner == 'x'

        // Nếu tick chọn tài sản chủ nhà thì số lượng và giá tiền phải = 0
        if (row.propertyOfHomeowner) {
          if (!row.quantity) row.quantity = 0
          if (!row.price) row.price = 0

          if (row.quantity && isNaN(row.quantity)) strErr += 'Sheet 2 - Dòng ' + idx + ' - Số lượng phải là số <br>'

          if (row.price && isNaN(row.price)) strErr += 'Sheet 2 - Dòng ' + idx + ' - Đơn giá phải là số <br>'

          if (row.quantity < 1) strErr += 'Sheet 2 - Dòng ' + idx + ' - Số lượng phải lớn hơn 1 <br>'

          if (row.price != 0) strErr += 'Sheet 2 - Dòng ' + idx + ' - Đơn giá phải bằng 0 <br>'
        } else {
          if (row.quantity && isNaN(row.quantity)) strErr += 'Sheet 2 - Dòng ' + idx + ' - Số lượng phải là số <br>'

          if (row.price && isNaN(row.price)) strErr += 'Sheet 2 - Dòng ' + idx + ' - Đơn giá phải là số <br>'

          if (+row.quantity < 1) strErr += 'Sheet 2 - Dòng ' + idx + ' - Số lượng phải lớn hơn hoặc bằng 1 <br>'

          if (+row.price < 0) strErr += 'Sheet 2 - Dòng ' + idx + ' - Đơn giá phải lớn hơn hoặc bằng 0 <br>'
        }

        if (row.dateOfEntry) {
          let ex = this.coreService.convertToDate(row.dateOfEntry)
          if (!ex) {
            strErr += 'Sheet 2 - Dòng ' + (idx + 1) + ' - Ngày đưa vào không đúng định dạng <br>'
          }
        }

        if (typeof row.dateOfEntry === 'number') {
          let dateOfEntryFm = this.coreService.excelDateToJSDate(row.dateOfEntry)
          row.dateOfEntry = moment(dateOfEntryFm).format('YYYY-MM-DD')
        } else {
          if (row.dateOfEntry) {
            var dateFormat = 'DD/MM/YYYY'
            let check = moment(row.dateOfEntry, dateFormat, true).isValid()
            if (!check) {
              strErr += 'Dòng ' + idx + ' - Ngày đưa vào không tồn tại \n'
            }
            var dateParts = row.dateOfEntry.split('/')
            // month is 0-based, that's why we need dataParts[1] - 1
            var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
            row.dateOfEntry = moment(dateObject).format('YYYY-MM-DD')
          }
        }

        if (row.dateOfWarranty) {
          let ex = this.coreService.convertToDate(row.dateOfWarranty)
          if (!ex) {
            strErr += 'Sheet 2 - Dòng ' + (idx + 1) + ' - Ngày bảo hành không đúng định dạng <br>'
          }
        }

        if (typeof row.dateOfWarranty === 'number') {
          let dateOfWarrantyFm = this.coreService.excelDateToJSDate(row.dateOfWarranty)
          row.dateOfWarranty = moment(dateOfWarrantyFm).format('YYYY-MM-DD')
        } else {
          if (row.dateOfWarranty) {
            var dateFormat = 'DD/MM/YYYY'
            let check = moment(row.dateOfWarranty, dateFormat, true).isValid()
            if (!check) {
              strErr += 'Dòng ' + idx + ' - Ngày bảo hành không tồn tại \n'
            }
            var dateParts = row.dateOfWarranty.split('/')
            // month is 0-based, that's why we need dataParts[1] - 1
            var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
            row.dateOfWarranty = moment(dateObject).format('YYYY-MM-DD')
          }
        }

        if (row.dateOfExpiryWarranty) {
          let ex = this.coreService.convertToDate(row.dateOfExpiryWarranty)
          if (!ex) {
            strErr += 'Sheet 2 - Dòng ' + (idx + 1) + ' - Ngày hết hạn bảo hành không đúng định dạng <br>'
          }
        }

        if (typeof row.dateOfExpiryWarranty === 'number') {
          let dateOfExpiryWarrantyFm = this.coreService.excelDateToJSDate(row.dateOfExpiryWarranty)
          row.dateOfExpiryWarranty = moment(dateOfExpiryWarrantyFm).format('YYYY-MM-DD')
        } else {
          if (row.dateOfExpiryWarranty) {
            var dateFormat = 'DD/MM/YYYY'
            let check = moment(row.dateOfExpiryWarranty, dateFormat, true).isValid()
            if (!check) {
              strErr += 'Dòng ' + idx + ' - Ngày hết hạn bảo hành không tồn tại \n'
            }
            var dateParts = row.dateOfExpiryWarranty.split('/')
            // month is 0-based, that's why we need dataParts[1] - 1
            var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
            row.dateOfExpiryWarranty = moment(dateObject).format('YYYY-MM-DD')
          }
        }

        if (row.dateOfWarranty && row.dateOfExpiryWarranty)
          if (new Date(row?.dateOfWarranty).getTime() > new Date(row?.dateOfExpiryWarranty).getTime()) {
            strErr += `Dòng [${idx}] Ngày bảo hành không được lớn hơn ngày hết hạn bảo hành!`
          }

        if (strErr.length > 0) {
          this.notifyService.hideloading()
          this.notifyService.showError(strErr)
          return
        }
      }
      for (let e of jsonData) {
        const find = jsonData1.filter((c: any) => e.code == c.code)
        // Nếu có một phiếu giao hàng không có hàng hoá thì báo lỗi
        if (find.length < 1) {
          this.notifyService.showError(`Phiếu nhập kho [ ${find.code} không có thông tin tài sản!]`)
          return
        }
        e.lstInboundDetail = find
      }
      this.appService.post(this.appService.INBOUND.CREATE_DATA_BY_EXCEL, jsonData).then((result) => {
        this.notifyService.hideloading()
        if (result) {
          this.notifyService.showSuccess('Thêm file excel thành công')
          this.searchData()
        }
      })
    }
  }
}
