import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiNtssService } from 'src/app/services/apiNtss.service'
import { AuthenticationService } from '../../../../services/authentication.service'
import { NotifyService } from '../../../../services/notify.service'

declare var Object: any
@Component({ templateUrl: './add-or-edit-property-type-model.component.html' })
export class AddOrEditPropertyTypeModelComponent implements OnInit {
  enumData: any
  modelTitle: any
  dataObject: any
  isEditItem = false

  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private appService: ApiNtssService,
    private dialogRef: MatDialogRef<AddOrEditPropertyTypeModelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  ngOnInit() {
    if (this.data && this.data !== null) {
      this.dataObject = this.data
      this.isEditItem = true
      this.modelTitle = 'CẬP NHẬT LOẠI TÀI SẢN'
    } else {
      this.modelTitle = 'THÊM MỚI LOẠI TÀI SẢN'
      this.dataObject = new Object()
    }
  }

  onSave() {
    const data = this.dataObject
    data.isDeleted = false
    if (data.id && data.id !== '') {
      this.updateObject(data)
      return
    }
    this.addObject(data)
  }

  addObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_TYPE.CREATE, data).then((result: any) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject(data: any) {
    this.notifyService.showloading()
    this.appService.post(this.appService.PROPERTY_TYPE.UPDATE, data).then((result: any) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(this.enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  closeDialog() {
    this.dialogRef.close(1)
  }
}
