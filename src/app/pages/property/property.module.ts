import { CommonModule } from '@angular/common'
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'

import { FormsModule } from '@angular/forms'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzSwitchModule } from 'ng-zorro-antd/switch'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { CURRENCY_MASK_CONFIG, CurrencyMaskConfig, CurrencyMaskModule } from 'ng2-currency-mask'
import { AddOrEditInboundModelComponent } from './inbound/add-or-edit-inbound-model/add-or-edit-inbound-model.component'
import { InboundDetailModelComponent } from './inbound/inbound-detail-model/inbound-detail-model.component'
import { InboundComponent } from './inbound/inbound.component'
import { AddPropertyLiquidationModelComponent } from './property-liquidation/add-property-liquidation-model/add-property-liquidation-model.component'
import { PropertyLiquidationDetailModelComponent } from './property-liquidation/property-liquidation-detail-model/property-liquidation-detail-model.component'
import { PropertyLiquidationComponent } from './property-liquidation/property-liquidation.component'
import { PropertyRoutingModule } from './property-routing.module'
import { AddPropertyTransferModelComponent } from './property-transfer/add-property-transfer-model/add-property-transfer-model.component'
import { PropertyTransferDetailModelComponent } from './property-transfer/property-transfer-detail-model/property-transfer-detail-model.component'
import { PropertyTransferComponent } from './property-transfer/property-transfer.component'
import { AddOrEditPropertyTypeModelComponent } from './property-type/add-or-edit-property-type-model/add-or-edit-property-type-model.component'
import { PropertyTypeComponent } from './property-type/property-type.component'
import { ApartmentPropertyDetailModelComponent } from './property/apartment-property-detail/apartment-property-detail-model.component'
import { InventoryComponent } from './property/inventory/inventory.component'
import { PropertyHistoryModelComponent } from './property/property-history/property-history-model.component'
import { AddOrEditPropertyModelComponent } from './property/property/add-or-edit-property-model/add-or-edit-property-model.component'
import { PropertyComponent } from './property/property/property.component'
import { PropertyDetailReportByApartmentOrWarehouseModelComponent } from './property/report/property-report-detail/property-detail-report-by-apartment-or-warehouse/property-detail-report-by-apartment-or-warehouse-model.component'
import { PropertyReportDetailModelComponent } from './property/report/property-report-detail/property-report-detail-model.component'
import { PropertyReportComponent } from './property/report/property-report.component'
import { AddOrEditWarehouseModelComponent } from './warehouse/add-or-edit-warehouse-model/add-or-edit-warehouse-model.component'
import { WarehouseDetailModelComponent } from './warehouse/warehouse-detail-model/warehouse-detail-model.component'
import { WarehouseComponent } from './warehouse/warehouse.component'

export const CustomCurrencyMaskConfig: CurrencyMaskConfig = {
  align: 'right',
  allowNegative: true,
  decimal: '.',
  precision: 0,
  prefix: '',
  suffix: '',
  thousands: ',',
}

@NgModule({
  declarations: [
    AddOrEditPropertyModelComponent,
    PropertyTypeComponent,
    AddOrEditPropertyTypeModelComponent,
    ApartmentPropertyDetailModelComponent,
    PropertyHistoryModelComponent,
    PropertyTransferComponent,
    AddPropertyTransferModelComponent,
    PropertyTransferDetailModelComponent,
    PropertyLiquidationComponent,
    AddPropertyLiquidationModelComponent,
    PropertyLiquidationDetailModelComponent,
    PropertyReportComponent,
    PropertyReportDetailModelComponent,
    AddOrEditWarehouseModelComponent,
    WarehouseDetailModelComponent,
    WarehouseComponent,
    PropertyComponent,
    InboundComponent,
    InboundDetailModelComponent,
    AddOrEditInboundModelComponent,
    InventoryComponent,
    PropertyDetailReportByApartmentOrWarehouseModelComponent,
  ],
  imports: [
    CommonModule,
    PropertyRoutingModule,
    NzInputModule,
    NzButtonModule,
    NzTableModule,
    NzPaginationModule,
    NzSelectModule,
    NzFormModule,
    FormsModule,
    NzSwitchModule,
    NzIconModule,
    NzToolTipModule,
    CurrencyMaskModule,
    NzCollapseModule,
    NzModalModule,
    NzCheckboxModule,
    NzCascaderModule,
    NzPopconfirmModule,
    NzDatePickerModule,
    NzTabsModule,
    NzTagModule,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PropertyModule {}
