import { <PERSON>mponent, NgZone, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core'
// amCharts imports
import * as am4core from '@amcharts/amcharts4/core'
import * as am4charts from '@amcharts/amcharts4/charts'

import am4themes_kelly from '@amcharts/amcharts4/themes/kelly'
import am4themes_animated from '@amcharts/amcharts4/themes/animated'
import { ApiService, CoreService, StorageService } from '../../../services'
import { Subscription } from 'rxjs'

am4core.useTheme(am4themes_kelly)
am4core.useTheme(am4themes_animated)
@Component({
  selector: 'app-analysis',
  templateUrl: './analysis.component.html',
})
export class AnalysisComponent implements OnInit, OnDestroy {
  dNow = new Date()
  private chart!: am4charts.XYChart
  listOfDataPrArise: any[] = []
  listOfStatusAsync: any[] = []
  listOfPRQC: any[] = []
  listOfStatusQC: any[] = []
  @ViewChild('chartPieSeriesPrArise', { static: false }) chartPieSeriesPrArise: any
  @ViewChild('chartPieSeriesPrPlant', { static: false }) chartPieSeriesPrPlant: any
  @ViewChild('chartSupplier', { static: false }) chartSupplier: any
  @ViewChild('chartBid', { static: false }) chartBid: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(private apiService: ApiService, private ngZone: NgZone, public coreService: CoreService, private storageService: StorageService) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
  }

  ngAfterViewInit() {
    this.searchData()
  }

  searchData() {
    this.apiService.post(this.apiService.DASHBOARD.DATA, {}).then((data) => {
      if (data) {
        this.loadChartPrArise(data.lstStatusPrArise)
        this.loadChartPrPlant(data.lstStatusPrPlant)
        this.loadChartBid(data.lstStatusBid)
        this.loadChartSupplier(data.lstStatusSupplier)
      }
    })
  }

  loadChartSupplier(object: any[]) {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chartSupplier.nativeElement, am4charts.PieChart)
      let data: any[] = []
      if (object !== undefined) {
        object.forEach((item) => {
          data.push({
            count: item.count,
            name: item.name,
          })
        })
      }
      chart.data = data

      let pieSeries = chart.series.push(new am4charts.PieSeries())
      pieSeries.dataFields.value = 'count'
      pieSeries.dataFields.category = 'name'
      pieSeries.slices.template.stroke = am4core.color('#fff')
      pieSeries.slices.template.strokeOpacity = 1

      chart.legend = new am4charts.Legend()
      chart.legend.position = 'bottom'
      // This creates initial animation
      pieSeries.hiddenState.properties.opacity = 1
      pieSeries.hiddenState.properties.endAngle = -90
      pieSeries.hiddenState.properties.startAngle = -90

      chart.hiddenState.properties.radius = am4core.percent(0)
    })
  }

  loadChartBid(object: any[]) {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chartBid.nativeElement, am4charts.PieChart)
      let data: any[] = []
      if (object !== undefined) {
        object.forEach((item) => {
          data.push({
            count: item.count,
            name: item.name,
          })
        })
      }
      chart.data = data

      let pieSeries = chart.series.push(new am4charts.PieSeries())
      pieSeries.dataFields.value = 'count'
      pieSeries.dataFields.category = 'name'
      pieSeries.slices.template.stroke = am4core.color('#fff')
      pieSeries.slices.template.strokeOpacity = 1

      chart.legend = new am4charts.Legend()
      chart.legend.position = 'bottom'
      // This creates initial animation
      pieSeries.hiddenState.properties.opacity = 1
      pieSeries.hiddenState.properties.endAngle = -90
      pieSeries.hiddenState.properties.startAngle = -90

      chart.hiddenState.properties.radius = am4core.percent(0)
    })
  }

  loadChartPrPlant(object: any[]) {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chartPieSeriesPrPlant.nativeElement, am4charts.PieChart)
      let data: any[] = []
      if (object !== undefined) {
        object.forEach((item) => {
          data.push({
            count: item.count,
            name: item.name,
          })
        })
      }
      chart.data = data

      let pieSeries = chart.series.push(new am4charts.PieSeries())
      pieSeries.dataFields.value = 'count'
      pieSeries.dataFields.category = 'name'
      pieSeries.slices.template.stroke = am4core.color('#fff')
      pieSeries.slices.template.strokeOpacity = 1

      chart.legend = new am4charts.Legend()
      chart.legend.position = 'bottom'
      // This creates initial animation
      pieSeries.hiddenState.properties.opacity = 1
      pieSeries.hiddenState.properties.endAngle = -90
      pieSeries.hiddenState.properties.startAngle = -90

      chart.hiddenState.properties.radius = am4core.percent(0)
    })
  }

  loadChartPrArise(object: any[]) {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chartPieSeriesPrArise.nativeElement, am4charts.PieChart)
      let data: any[] = []
      if (object !== undefined) {
        object.forEach((item) => {
          data.push({
            count: item.count,
            name: item.name,
          })
        })
      }
      chart.data = data

      let pieSeries = chart.series.push(new am4charts.PieSeries())
      pieSeries.dataFields.value = 'count'
      pieSeries.dataFields.category = 'name'
      pieSeries.slices.template.stroke = am4core.color('#fff')
      pieSeries.slices.template.strokeOpacity = 1

      chart.legend = new am4charts.Legend()
      chart.legend.position = 'bottom'
      // This creates initial animation
      pieSeries.hiddenState.properties.opacity = 1
      pieSeries.hiddenState.properties.endAngle = -90
      pieSeries.hiddenState.properties.startAngle = -90

      chart.hiddenState.properties.radius = am4core.percent(0)
    })
  }
  // Create series
  createSeries(field: any, name: any, chart: { series: any[]; zoomOutButton: { disabled: boolean } }) {
    let series: any = chart.series.push(new am4charts.ColumnSeries())
    series.dataFields.valueY = field
    series.dataFields.categoryX = 'name'
    series.name = name
    series.columns.template.tooltipText = '{name}: [bold]{valueY}[/]'
    series.columns.pixelHeight = 100
    let labelBullet = series.bullets.push(new am4charts.LabelBullet())
    labelBullet.label.verticalCenter = 'bottom'
    labelBullet.label.dy = -3
    labelBullet.label.text = '[font-size:14px]{valueY}'
    // chart.legend = new am4charts.Legend();
    // chart.legend.position = "bottom";
    chart.zoomOutButton.disabled = false
    return series
  }

  ngOnDestroy() {
    // Clean up chart when the component is removed
    this.ngZone.runOutsideAngular(() => {
      if (this.chart) {
        this.chart.dispose()
      }
    })
  }
}
