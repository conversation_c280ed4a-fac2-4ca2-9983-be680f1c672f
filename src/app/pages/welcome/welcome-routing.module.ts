import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { WelcomeComponent } from './welcome.component'
import { AnalysisComponent } from './analysis/analysis.component'
import { SupplierTop10Component } from './supplier-top10/supplier-top10.component'
const routes: Routes = [
  { path: '', component: WelcomeComponent },
  { path: 'analysis', component: AnalysisComponent },
  { path: 'supplier-top10', component: SupplierTop10Component },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class WelcomeRoutingModule {}
