import { Component, NgZone, OnInit, ViewChild } from '@angular/core'
// amCharts imports
import * as am4core from '@amcharts/amcharts4/core'
import * as am4charts from '@amcharts/amcharts4/charts'
import am4themes_kelly from '@amcharts/amcharts4/themes/kelly'
import am4themes_animated from '@amcharts/amcharts4/themes/animated'
import * as XLSX from 'xlsx'
import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { Subscription } from 'rxjs'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialog } from '@angular/material/dialog'
import { SupplierTop10DetailComponent } from './supplier-top10-detail/supplier-top10-detail.component'

am4core.useTheme(am4themes_kelly)
am4core.useTheme(am4themes_animated)

@Component({
  selector: 'app-supplier-top10',
  templateUrl: './supplier-top10.component.html',
})
export class SupplierTop10Component implements OnInit {
  @ViewChild('chart1', { static: false }) chart1: any
  @ViewChild('chart2', { static: false }) chart2: any
  @ViewChild('chart3', { static: false }) chart3: any
  @ViewChild('chart4', { static: false }) chart4: any
  @ViewChild('chart5', { static: false }) chart5: any
  @ViewChild('chart6', { static: false }) chart6: any
  dataSearch: any = {}
  lstChart: any[] = [
    { id: 1, name: 'Chart 1', description: 'Doanh số cao nhất' },
    { id: 2, name: 'Chart 2', description: 'Doanh số thấp nhất' },
    { id: 3, name: 'Chart 3', description: 'Đơn giá cao nhất' },
    { id: 4, name: 'Chart 4', description: 'Đơn giá thấp nhất' },
    { id: 5, name: 'Chart 5', description: 'Điểm năng lực cao nhất' },
    { id: 6, name: 'Chart 6', description: 'Điểm năng lực thấp nhất' },
  ]
  isVisibleTotalPrice = false
  isVisibleScore = false
  dataChart: any
  isVisibleTotalPriceDetail = false
  isVisibleScoreDetail = false
  serviceChoose: any
  titleModal: string = ''
  lstDataService: any[] = []
  isVisibleUnitPrice = false
  isVisibleUnitPriceDetail = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  listOfData: any[] = []
  loading = false
  pageIndex: any
  pageSize: any
  total: number = 0
  dataStatus: any[] = []

  constructor(
    private apiService: ApiService,
    public notifyService: NotifyService,
    private ngZone: NgZone,
    public coreService: CoreService,
    private storageService: StorageService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.dataSearch.listChart = []
    this.apiService.post(this.apiService.DASHBOARD.SUPPLIER_TOP10, {}).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        this.listOfData = res
      }
    })
    // [
    //   {
    //     code: '1253623652',
    //     name: 'Công ty TNHH 1 TV Sao Vàng',
    //     point: '100',
    //     businessAreas: 'MTT-Máy trợ thở',
    //   },
    //   {
    //     code: '1207663839',
    //     name: 'Công ty TNHH ABC',
    //     point: '100',
    //     businessAreas: 'LV3-Lĩnh vực 3, MTT-Máy trợ thở',
    //   },
    //   {
    //     code: '012562535',
    //     name: 'Công ty cổ phần Phần Mềm Bravo',
    //     point: '60',
    //     businessAreas: 'MTT-Máy trợ thở, THUNGCARTTON- THÙNG CARTTON, TBPT-Thiết bị phẫu thuật, LV3-Lĩnh vực 3',
    //   },
    //   {
    //     code: 'TH01',
    //     name: 'Công ty TNHH 1TV TH',
    //     point: '60',
    //     businessAreas: 'THUNGCARTTON- THÙNG CARTTON, TBPT-Thiết bị phẫu thuật',
    //   },
    //   {
    //     code: '***********',
    //     name: 'Tue Hai Corp',
    //     point: '54',
    //     businessAreas: 'MTT-Máy trợ thở, THUNGCARTTON- THÙNG CARTTON, TBPT-Thiết bị phẫu thuật ',
    //   },
    //   {
    //     code: 'CTK_12345',
    //     name: 'Công ty cung cấp phần mềm Citek',
    //     point: '54',
    //     businessAreas: 'TBPT-Thiết bị phẫu thuật, THUNGCARTTON- THÙNG CARTTON',
    //   },
    //   {
    //     code: 'TH6969',
    //     name: 'CÔNG TY CP GIẢI PHÁP TINH HOA',
    //     point: '54',
    //     businessAreas: 'TBPT-Thiết bị phẫu thuật, THUNGCARTTON- THÙNG CARTTON, LV3-Lĩnh vực 3',
    //   },
    //   {
    //     code: 'KC_12345',
    //     name: 'CÔNG TY CỔ PHẦN KIM CƯƠNG',
    //     point: '48',
    //     businessAreas: 'THUNGCARTTON- THÙNG CARTTON, TBPT-Thiết bị phẫu thuật',
    //   },
    // ]
  }

  clickDetail(data: any) {
    const dt = { ...data }
    this.dialog
      .open(SupplierTop10DetailComponent, { disableClose: false, data: dt })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  //#region Filter

  loadDataService = (node: NzCascaderOption, index: number) => {
    return new Promise<void>(async (resolve) => {
      if (index < 0) {
        this.lstDataService = []
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
        if (node.children && node.children.length > 0) {
          this.lstDataService.push(...node.children)
        }
      } else {
        const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
        node.children = data.map((v: any) => {
          return {
            value: v.id,
            label: v.name,
            isLast: v.isLast,
            isLeaf: !!v.isLast,
          }
        })
        if (node.children && node.children.length > 0) {
          this.lstDataService.push(...node.children)
        }
      }
      resolve()
    })
  }

  onChangeListChart() {
    this.dataSearch.isShowFilterCategory = false
    if (this.dataSearch.listChart.includes(3) || this.dataSearch.listChart.includes(4)) {
      this.dataSearch.isShowFilterCategory = true
    }
  }
  // /** Danh sách LVMH của NCC */
  // viewSupplierService(data: any) {
  //   this.dialog
  //     .open(SupplierServiceTop10Component, { data: { id: data.id, name: data.name } })
  //     .afterClosed()
  //     .subscribe(() => {
  //       this.searchData()
  //     })
  // }

  //#endregion

  //#region Search get data
  searchData() {
    this.notifyService.showloading()
    if (!this.dataSearch.serviceId || this.dataSearch.serviceId.length == 0) {
      this.notifyService.showError('Vui lòng chọn lĩnh vực mua hàng trước')
      return
    }
    if (!this.dataSearch.listChart || this.dataSearch.listChart.length == 0) {
      this.notifyService.showError('Vui lòng chọn chart hiển thị')
      return
    }
    //FAKE-DATA
    if (this.dataSearch.listChart.includes(3) || this.dataSearch.listChart.includes(4)) {
      if (this.dataSearch.category == null || this.dataSearch.category.trim() === '') {
        this.notifyService.showError('Vui lòng nhập tên hạng mục khi chọn chart 3-4')
        return
      }
      this.dataSearch.category = this.dataSearch.category.trim()
    }

    // this.dataSearch.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    this.serviceChoose = this.lstDataService.find((c) => c.value == this.dataSearch.serviceId)
    this.apiService.post(this.apiService.DASHBOARD.SUPPLIER, this.dataSearch).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        this.dataChart = res
        setTimeout(() => {
          if (this.dataSearch.listChart.includes(1)) this.loadChart1()
          if (this.dataSearch.listChart.includes(2)) this.loadChart2()
          if (this.dataSearch.listChart.includes(3)) this.loadChart3()
          if (this.dataSearch.listChart.includes(4)) this.loadChart4()
          if (this.dataSearch.listChart.includes(5)) this.loadChart5()
          if (this.dataSearch.listChart.includes(6)) this.loadChart6()
        }, 50)
      }
    })
  }
  //#endregion

  //#region Show Chart

  /** Chart 1: Doanh số cao nhất */
  loadChart1() {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chart1.nativeElement, am4charts.XYChart)
      chart.data = this.dataChart.lstTop10SupplierTotalPriceMax

      var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
      xAxis.dataFields.category = 'supplierName'
      xAxis.title.text = 'Nhà cung cấp'
      xAxis.renderer.cellStartLocation = 0.1
      xAxis.renderer.cellEndLocation = 0.9
      xAxis.renderer.grid.template.location = 0
      xAxis.renderer.labels.template.verticalCenter = 'middle'
      xAxis.renderer.labels.template.rotation = 45
      xAxis.renderer.minGridDistance = 20

      var yAxis = chart.yAxes.push(new am4charts.ValueAxis())
      yAxis.title.text = 'Doanh số (tỉ đồng)'
      yAxis.min = 0

      let series = chart.series.push(new am4charts.ColumnSeries())
      series.dataFields.valueY = 'totalPriceBillion'
      series.dataFields.categoryX = 'supplierName'
      series.columns.template.tooltipText = '{supplierName}: [bold]{valueY}[/]'

      series.heatRules.push({
        target: series.columns.template,
        property: 'fill',
        min: am4core.color('#96bade'),
        max: am4core.color('#295379'),
        dataField: 'valueY',
      })
      series.columns.template.events.on('hit', (ev: any) => {
        const dataItem = ev.target._dataItem.dataContext as any
        this.showDataChart1(dataItem.supplierId)
      })

      let labelBullet = series.bullets.push(new am4charts.LabelBullet())
      labelBullet.dy = -2
      labelBullet.label.verticalCenter = 'bottom'
      labelBullet.label.text = '[font-size:14px]{valueY}'
    })
  }

  /** Chart 2: Doanh số thấp nhất */
  loadChart2() {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chart2.nativeElement, am4charts.XYChart)
      chart.data = this.dataChart.lstTop10SupplierTotalPriceMin

      var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
      xAxis.dataFields.category = 'supplierName'
      xAxis.title.text = 'Nhà cung cấp'
      xAxis.renderer.cellStartLocation = 0.1
      xAxis.renderer.cellEndLocation = 0.9
      xAxis.renderer.grid.template.location = 0
      xAxis.renderer.labels.template.verticalCenter = 'middle'
      xAxis.renderer.labels.template.rotation = 45
      xAxis.renderer.minGridDistance = 20

      var yAxis = chart.yAxes.push(new am4charts.ValueAxis())
      yAxis.title.text = 'Doanh số (tỉ đồng)'
      yAxis.min = 0

      let series = chart.series.push(new am4charts.ColumnSeries())
      series.dataFields.valueY = 'totalPriceBillion'
      series.dataFields.categoryX = 'supplierName'
      series.columns.template.tooltipText = '{supplierName}: [bold]{valueY}[/]'

      series.heatRules.push({
        target: series.columns.template,
        property: 'fill',
        min: am4core.color('#96bade'),
        max: am4core.color('#295379'),
        dataField: 'valueY',
      })
      series.columns.template.events.on('hit', (ev: any) => {
        const dataItem = ev.target._dataItem.dataContext as any
        this.showDataChart2(dataItem.supplierId)
      })

      let labelBullet = series.bullets.push(new am4charts.LabelBullet())
      labelBullet.dy = -2
      labelBullet.label.verticalCenter = 'bottom'
      labelBullet.label.text = '[font-size:14px]{valueY}'
    })
  }

  /** Chart 3: Đơn giá cao nhất */
  loadChart3() {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chart3.nativeElement, am4charts.XYChart)

      chart.data = this.dataChart.lstTop10SupplierUnitPriceMax
      var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
      xAxis.dataFields.category = 'bidSupplierName'
      xAxis.title.text = 'Mã NCC-Mã TBMT'
      xAxis.renderer.cellStartLocation = 0.1
      xAxis.renderer.cellEndLocation = 0.9
      xAxis.renderer.grid.template.location = 0
      xAxis.renderer.labels.template.verticalCenter = 'middle'
      xAxis.renderer.labels.template.rotation = 60
      xAxis.renderer.labels.template.wrap = true
      xAxis.renderer.labels.template.maxWidth = 100
      xAxis.renderer.minGridDistance = 20

      var yAxis = chart.yAxes.push(new am4charts.ValueAxis())
      yAxis.title.text = 'Đơn giá'
      yAxis.min = 0

      let series = chart.series.push(new am4charts.ColumnSeries())
      series.dataFields.valueY = 'unitPrice'
      series.dataFields.categoryX = 'bidSupplierName'
      series.columns.template.tooltipText = '{categoryX}: [bold]{valueY}[/]'

      series.heatRules.push({
        target: series.columns.template,
        property: 'fill',
        min: am4core.color('#96bade'),
        max: am4core.color('#295379'),
        dataField: 'valueY',
      })
      series.columns.template.events.on('hit', (ev: any) => {
        const dataItem = ev.target._dataItem.dataContext as any
        this.showDataChart3(dataItem.bidSupplierId)
      })

      let labelBullet = series.bullets.push(new am4charts.LabelBullet())
      labelBullet.dy = -2
      labelBullet.label.verticalCenter = 'bottom'
      labelBullet.label.text = '[font-size:14px]{valueY}'
    })
  }

  /** Chart 4: Đơn giá thấp nhất */
  loadChart4() {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chart4.nativeElement, am4charts.XYChart)
      chart.data = this.dataChart.lstTop10SupplierUnitPriceMin

      var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
      xAxis.dataFields.category = 'bidSupplierName'
      xAxis.title.text = 'Mã NCC-Mã TBMT'
      xAxis.renderer.cellStartLocation = 0.1
      xAxis.renderer.cellEndLocation = 0.9
      xAxis.renderer.grid.template.location = 0
      xAxis.renderer.labels.template.verticalCenter = 'middle'
      xAxis.renderer.labels.template.rotation = 60
      xAxis.renderer.labels.template.wrap = true
      xAxis.renderer.labels.template.maxWidth = 100
      xAxis.renderer.minGridDistance = 20

      var yAxis = chart.yAxes.push(new am4charts.ValueAxis())
      yAxis.title.text = 'Đơn giá'
      yAxis.min = 0

      let series = chart.series.push(new am4charts.ColumnSeries())
      series.dataFields.valueY = 'unitPrice'
      series.dataFields.categoryX = 'bidSupplierName'
      series.columns.template.tooltipText = '{categoryX}: [bold]{valueY}[/]'

      series.heatRules.push({
        target: series.columns.template,
        property: 'fill',
        min: am4core.color('#96bade'),
        max: am4core.color('#295379'),
        dataField: 'valueY',
      })
      series.columns.template.events.on('hit', (ev: any) => {
        const dataItem = ev.target._dataItem.dataContext as any
        this.showDataChart3(dataItem.bidSupplierId)
      })

      let labelBullet = series.bullets.push(new am4charts.LabelBullet())
      labelBullet.dy = -2
      labelBullet.label.verticalCenter = 'bottom'
      labelBullet.label.text = '[font-size:14px]{valueY}'
    })
  }

  /** Chart 5: Điểm năng lực cao nhất */
  loadChart5() {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chart5.nativeElement, am4charts.XYChart)
      chart.data = this.dataChart.lstTop10SupplierScoreMax

      var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
      xAxis.dataFields.category = 'supplierName'
      xAxis.title.text = 'Nhà cung cấp'
      xAxis.renderer.cellStartLocation = 0.1
      xAxis.renderer.cellEndLocation = 0.9
      xAxis.renderer.grid.template.location = 0
      xAxis.renderer.labels.template.verticalCenter = 'middle'
      xAxis.renderer.labels.template.rotation = 45
      xAxis.renderer.minGridDistance = 20

      var yAxis = chart.yAxes.push(new am4charts.ValueAxis())
      yAxis.title.text = 'Điểm năng lực'
      yAxis.min = 0
      yAxis.max = 110

      let series = chart.series.push(new am4charts.ColumnSeries())
      series.dataFields.valueY = 'score'
      series.dataFields.categoryX = 'supplierName'
      series.name = 'Điểm năng lực'
      series.columns.template.tooltipText = '{supplierName}: [bold]{valueY}[/]'

      series.heatRules.push({
        target: series.columns.template,
        property: 'fill',
        min: am4core.color('#96bade'),
        max: am4core.color('#295379'),
        dataField: 'valueY',
      })
      series.columns.template.events.on('hit', (ev: any) => {
        const dataItem = ev.target._dataItem.dataContext as any
        this.showDataChart5(dataItem.supplierId)
      })

      let labelBullet = series.bullets.push(new am4charts.LabelBullet())
      labelBullet.dy = -2
      labelBullet.label.verticalCenter = 'bottom'
      labelBullet.label.text = '[font-size:14px]{valueY}'
    })
  }

  /** Chart 6: Điểm năng lực thấp nhất */
  loadChart6() {
    this.ngZone.runOutsideAngular(() => {
      let chart = am4core.create(this.chart6.nativeElement, am4charts.XYChart)
      chart.data = this.dataChart.lstTop10SupplierScoreMin

      var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
      xAxis.dataFields.category = 'supplierName'
      xAxis.title.text = 'Nhà cung cấp'
      xAxis.renderer.cellStartLocation = 0.1
      xAxis.renderer.cellEndLocation = 0.9
      xAxis.renderer.grid.template.location = 0
      xAxis.renderer.labels.template.verticalCenter = 'middle'
      xAxis.renderer.labels.template.rotation = 45
      xAxis.renderer.minGridDistance = 20

      var yAxis = chart.yAxes.push(new am4charts.ValueAxis())
      yAxis.title.text = 'Điểm năng lực'
      yAxis.min = 0
      yAxis.max = 110

      let series = chart.series.push(new am4charts.ColumnSeries())
      series.dataFields.valueY = 'score'
      series.dataFields.categoryX = 'supplierName'
      series.name = 'Điểm năng lực'
      series.columns.template.tooltipText = '{supplierName}: [bold]{valueY}[/]'

      series.heatRules.push({
        target: series.columns.template,
        property: 'fill',
        min: am4core.color('#96bade'),
        max: am4core.color('#295379'),
        dataField: 'valueY',
      })
      series.columns.template.events.on('hit', (ev: any) => {
        const dataItem = ev.target._dataItem.dataContext as any
        this.showDataChart6(dataItem.supplierId)
      })

      let labelBullet = series.bullets.push(new am4charts.LabelBullet())
      labelBullet.dy = -2
      labelBullet.label.verticalCenter = 'bottom'
      labelBullet.label.text = '[font-size:14px]{valueY}'
    })
  }

  //#endregion

  //#region Show data chart

  //#region Chart 1-2: Tổng giá
  showDataChart1(supplierId = '') {
    this.dataChart.currentChartId = 1
    this.dataChart.lstTop10SupplierTotalPrice = this.dataChart.lstTop10SupplierTotalPriceMax
    if (!this.dataChart || !this.dataChart.lstTop10SupplierTotalPrice || !this.dataChart.lstTop10SupplierTotalPrice.length) {
      this.notifyService.showError('Chưa có dữ liệu')
      return
    }
    this.dataChart.supplierId = supplierId
    this.isVisibleTotalPrice = true
    this.titleModal = `Tổng doanh số cao nhất [${this.serviceChoose.label}]`
    this.searchDataTotalPrice()
  }
  showDataChart2(supplierId = '') {
    this.dataChart.currentChartId = 2
    this.dataChart.lstTop10SupplierTotalPrice = this.dataChart.lstTop10SupplierTotalPriceMin
    if (!this.dataChart || !this.dataChart.lstTop10SupplierTotalPrice || !this.dataChart.lstTop10SupplierTotalPrice.length) {
      this.notifyService.showError('Chưa có dữ liệu')
      return
    }
    this.dataChart.supplierId = supplierId
    this.isVisibleTotalPrice = true
    this.titleModal = `Tổng doanh số thấp nhất [${this.serviceChoose.label}]`
    this.searchDataTotalPrice()
  }
  hideDataChartTotalPrice() {
    this.isVisibleTotalPrice = false
  }
  searchDataTotalPrice() {
    this.dataChart.lstTop10SupplierTotalPriceDisplay = this.dataChart.lstTop10SupplierTotalPriceMax.filter((c: any) =>
      this.dataChart.supplierId ? c.supplierId == this.dataChart.supplierId : true
    )
  }
  exportExcelDataTotalPrice() {
    setTimeout(() => {
      const tbl = document.getElementById('total-price-table')
      const wb = XLSX.utils.table_to_book(tbl)
      let fileName = `Tổng doanh số cao nhất [${this.serviceChoose.label}].xlsx`
      if (this.dataChart.currentChartId == 2) {
        fileName = `Tổng doanh số thấp nhất [${this.serviceChoose.label}].xlsx`
      }
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  showTotalPriceDetail(obj: any) {
    this.dataChart.supplierServiceId = obj.id
    this.isVisibleTotalPriceDetail = true
  }
  hideTotalPriceDetail() {
    this.dataChart.supplierServiceId = null
    this.isVisibleTotalPriceDetail = false
  }
  //#endregion

  //#region Chart 3-4: Đơn giá
  showDataChart3(bidSupplierId = '') {
    this.dataChart.currentChartId = 3
    this.dataChart.lstTop10SupplierUnitPrice = this.dataChart.lstTop10SupplierUnitPriceMax
    if (!this.dataChart || !this.dataChart.lstTop10SupplierUnitPrice || !this.dataChart.lstTop10SupplierUnitPrice.length) {
      this.notifyService.showError('Chưa có dữ liệu')
      return
    }
    this.dataChart.bidSupplierId = bidSupplierId
    this.isVisibleUnitPrice = true
    this.titleModal = `Đơn giá cao nhất [${this.dataSearch.category}]`
    this.searchDataUnitPrice()
  }
  showDataChart4(bidSupplierId = '') {
    this.dataChart.currentChartId = 4
    this.dataChart.lstTop10SupplierUnitPrice = this.dataChart.lstTop10SupplierUnitPriceMin
    if (!this.dataChart || !this.dataChart.lstTop10SupplierUnitPrice || !this.dataChart.lstTop10SupplierUnitPrice.length) {
      this.notifyService.showError('Chưa có dữ liệu')
      return
    }
    this.dataChart.bidSupplierId = bidSupplierId
    this.isVisibleUnitPrice = true
    this.titleModal = `Đơn giá thấp nhất [${this.dataSearch.category}]`
    this.searchDataUnitPrice()
  }
  hideDataChartUnitPrice() {
    this.isVisibleUnitPrice = false
  }
  searchDataUnitPrice() {
    this.dataChart.lstTop10SupplierUnitPriceDisplay = this.dataChart.lstTop10SupplierUnitPrice.filter((c: any) =>
      this.dataChart.bidSupplierId ? c.bidSupplierId == this.dataChart.bidSupplierId : true
    )
  }
  exportExcelDataUnitPrice() {
    setTimeout(() => {
      const tbl = document.getElementById('total-price-table')
      const wb = XLSX.utils.table_to_book(tbl)
      let fileName = `Đơn giá thấp nhất [${this.dataSearch.category}].xlsx`
      if (this.dataChart.currentChartId == 5) {
        fileName = `Đơn giá cao nhất [${this.dataSearch.category}].xlsx`
      }
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  showUnitPriceDetail(obj: any) {
    this.dataChart.supplierServiceId = obj.id
    this.isVisibleUnitPriceDetail = true
  }
  hideUnitPriceDetail() {
    this.dataChart.supplierServiceId = null
    this.isVisibleUnitPriceDetail = false
  }
  //#endregion

  //#region Chart 5-6: Điểm năng lực
  showDataChart5(supplierId = '') {
    this.dataChart.currentChartId = 5
    this.dataChart.lstTop10SupplierScore = this.dataChart.lstTop10SupplierScoreMax
    if (!this.dataChart || !this.dataChart.lstTop10SupplierScore || !this.dataChart.lstTop10SupplierScore.length) {
      this.notifyService.showError('Chưa có dữ liệu')
      return
    }
    this.dataChart.supplierId = supplierId
    this.isVisibleScore = true
    this.titleModal = `Điểm năng lực cao nhất [${this.serviceChoose.label}]`
    this.searchDataScore()
  }
  showDataChart6(supplierId = '') {
    this.dataChart.currentChartId = 6
    this.dataChart.lstTop10SupplierScore = this.dataChart.lstTop10SupplierScoreMin
    if (!this.dataChart || !this.dataChart.lstTop10SupplierScore || !this.dataChart.lstTop10SupplierScore.length) {
      this.notifyService.showError('Chưa có dữ liệu')
      return
    }
    this.dataChart.supplierId = supplierId
    this.isVisibleScore = true
    this.titleModal = `Điểm năng lực thấp nhất [${this.serviceChoose.label}]`
    this.searchDataScore()
  }
  hideDataChartScore() {
    this.isVisibleScore = false
  }
  searchDataScore() {
    this.dataChart.lstTop10SupplierScoreDisplay = this.dataChart.lstTop10SupplierScore.filter((c: any) =>
      this.dataChart.supplierId ? c.supplierId == this.dataChart.supplierId : true
    )
  }
  exportExcelDataScore() {
    setTimeout(() => {
      const tbl = document.getElementById('score-table')
      const wb = XLSX.utils.table_to_book(tbl)
      let fileName = `Điểm năng lực thấp nhất [${this.serviceChoose.label}].xlsx`
      if (this.dataChart.currentChartId == 5) {
        fileName = `Điểm năng lực cao nhất [${this.serviceChoose.label}].xlsx`
      }
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  showScoreDetail(obj: any) {
    this.dataChart.supplierServiceId = obj.id
    this.isVisibleScoreDetail = true
  }
  hideScoreDetail() {
    this.dataChart.supplierServiceId = null
    this.isVisibleScoreDetail = false
  }
  //#endregion

  //#endregion
}
