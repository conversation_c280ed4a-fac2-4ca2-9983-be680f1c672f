import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({ templateUrl: './supplier-top10-detail.component.html' })
export class SupplierTop10DetailComponent implements OnInit {
  modalTitle = 'chi tiết nhà cung cấp'
  dataObject: any = {}
  dataHistoryParent: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentStatus: any = {}
  enumProject: any
  enumRole: any
  action: any
  currentUser: any
  constructor(
    public authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<SupplierTop10DetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.dataObject = this.data
    // this.modalTitle = `Chi tiết ${this.data.name}`
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
