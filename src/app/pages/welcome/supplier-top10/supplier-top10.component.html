<nz-row nzGutter="8">
  <nz-col nzSpan="12">
    <nz-cascader nzPlaceHolder="Chọn lĩnh vực mua hàng" [(ngModel)]="dataSearch.serviceId"
      [nzLoadData]="loadDataService">
    </nz-cascader>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Từ ngày">
    </nz-date-picker>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Đến ngày">
    </nz-date-picker>
  </nz-col>
</nz-row>

<nz-row nzGutter="8" class="mt-2">
  <nz-col nzSpan="12">
    <nz-select nzShowSearch nzAllowClear nzMode="multiple" [(ngModel)]="dataSearch.listChart"
      nzPlaceHolder="Chọn chart hiển thị" (ngModelChange)="onChangeListChart()">
      <nz-option *ngFor="let item of lstChart" [nzLabel]="item.name" [nzValue]="item.id">
      </nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="12" *ngIf="dataSearch.isShowFilterCategory">
    <input nz-input [(ngModel)]="dataSearch.category"
      placeholder="Tìm chính xác theo tên hạng mục chào giá (áp dụng cho các chart 3-4)" />
  </nz-col>
</nz-row>

<nz-row class="mt-2">
  <button nz-button (click)="searchData()">
    <span nz-icon nzType="search"></span>Xem chart
  </button>
</nz-row>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="12" class="mt-1" *ngIf="dataSearch.listChart.includes(1)">
    <nz-card style="min-width:100%;" nzTitle="Chart 1. Doanh số cao nhất" [nzExtra]="actionChart1Template">
      <div id="chart1" #chart1 style="width: auto; height: 300px;"></div>
    </nz-card>
    <ng-template #actionChart1Template>
      <a (click)="showDataChart1()">
        <span nz-icon nzType="info-circle"></span> Xem chi tiết
      </a>
    </ng-template>
  </nz-col>

  <nz-col nzSpan="12" class="mt-1" *ngIf="dataSearch.listChart.includes(2)">
    <nz-card style="min-width:100%;" nzTitle="Chart 2. Doanh số thấp nhất" [nzExtra]="actionChart2Template">
      <div id="chart2" #chart2 style="width: auto; height: 300px;"></div>
    </nz-card>
    <ng-template #actionChart2Template>
      <a (click)="showDataChart2()">
        <span nz-icon nzType="info-circle"></span> Xem chi tiết
      </a>
    </ng-template>
  </nz-col>

  <nz-col nzSpan="12" class="mt-1" *ngIf="dataSearch.listChart.includes(3)">
    <nz-card style="min-width:100%;" nzTitle="Chart 3. Đơn giá cao nhất" [nzExtra]="actionChart3Template">
      <div id="chart3" #chart3 style="width: auto; height: 300px;"></div>
    </nz-card>
    <ng-template #actionChart3Template>
      <a (click)="showDataChart3()">
        <span nz-icon nzType="info-circle"></span> Xem chi tiết
      </a>
    </ng-template>
  </nz-col>

  <nz-col nzSpan="12" class="mt-1" *ngIf="dataSearch.listChart.includes(4)">
    <nz-card style="min-width:100%;" nzTitle="Chart 4. Đơn giá thấp nhất" [nzExtra]="actionChart4Template">
      <div id="chart4" #chart4 style="width: auto; height: 300px;"></div>
    </nz-card>
    <ng-template #actionChart4Template>
      <a (click)="showDataChart4()">
        <span nz-icon nzType="info-circle"></span> Xem chi tiết
      </a>
    </ng-template>
  </nz-col>

  <nz-col nzSpan="12" class="mt-1" *ngIf="dataSearch.listChart.includes(5)">
    <nz-card style="min-width:100%;" nzTitle="Chart 5. Điểm năng lực cao nhất" [nzExtra]="actionChart5Template">
      <div id="chart5" #chart5 style="width: auto; height: 300px;"></div>
    </nz-card>
    <ng-template #actionChart5Template>
      <a (click)="showDataChart5()">
        <span nz-icon nzType="info-circle"></span> Xem chi tiết
      </a>
    </ng-template>
  </nz-col>

  <nz-col nzSpan="12" class="mt-1" *ngIf="dataSearch.listChart.includes(6)">
    <nz-card style="min-width:100%;" nzTitle="Chart 6. Điểm năng lực thấp nhất" [nzExtra]="actionChart6Template">
      <div id="chart6" #chart6 style="width: auto; height: 300px;"></div>
    </nz-card>
    <ng-template #actionChart6Template>
      <a (click)="showDataChart6()">
        <span nz-icon nzType="info-circle"></span> Xem chi tiết
      </a>
    </ng-template>
  </nz-col>
</nz-row>

<!-- popup detail chart 1-2 -->
<nz-modal [(nzVisible)]="isVisibleTotalPrice" [nzWidth]="'60vw'" [nzTitle]="titleModal"
  (nzOnCancel)="hideDataChartTotalPrice()" [nzFooter]="null" *ngIf="dataChart">
  <ng-container *nzModalContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="16">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataChart.supplierId" nzPlaceHolder="Chọn NCC">
          <nz-option *ngFor="let item of dataChart.lstTop10SupplierTotalPrice"
            [nzLabel]="'(' + item.supplierCode + ') ' + item.supplierName" [nzValue]="item.supplierId">
          </nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSpan="8">
        <button nz-button (click)="searchDataTotalPrice()">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-2">
      <button nz-button (click)="exportExcelDataTotalPrice()" nzType="primary">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
    </nz-row>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" id="total-price-table" [nzData]="dataChart.lstTop10SupplierTotalPriceDisplay"
        [nzFrontPagination]="false" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th nzWidth="50px">TOP</th>
            <th>Mã nhà cung cấp</th>
            <th>Tên nhà cung cấp</th>
            <th>Doanh số (tỉ đồng)</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data of dataChart.lstTop10SupplierTotalPriceDisplay; let i = index">
            <tr (click)="showTotalPriceDetail(data)" style="cursor: pointer;">
              <td>{{ i + 1 }}</td>
              <td>{{ data.supplierCode }}</td>
              <td>{{ data.supplierName }}</td>
              <td class="text-right">{{ data.totalPriceBillion | number}}</td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
      <!-- <i>* Lưu ý: Click vào dòng để xem chi tiết</i> -->
    </nz-row>
  </ng-container>
</nz-modal>

<!-- popup detail chart 3-4 -->
<nz-modal [(nzVisible)]="isVisibleUnitPrice" [nzWidth]="'60vw'" [nzTitle]="titleModal"
  (nzOnCancel)="hideDataChartUnitPrice()" [nzFooter]="null" *ngIf="dataChart">
  <ng-container *nzModalContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="16">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataChart.bidSupplierId" nzPlaceHolder="Chọn NCC-Gói thầu">
          <nz-option *ngFor="let item of dataChart.lstTop10SupplierUnitPrice"
            [nzLabel]="item.supplierName + ' - ' + item.bidName" [nzValue]="item.bidSupplierId">
          </nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSpan="8">
        <button nz-button (click)="searchDataUnitPrice()">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-2">
      <button nz-button (click)="exportExcelDataUnitPrice()" nzType="primary">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
    </nz-row>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" id="unit-price-table" [nzData]="dataChart.lstTop10SupplierUnitPriceDisplay"
        [nzFrontPagination]="false" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th nzWidth="50px">TOP</th>
            <th>Mã nhà cung cấp</th>
            <th>Tên nhà cung cấp</th>
            <th>Mã TBMT</th>
            <th>Tên gói thầu</th>
            <th>{{ language_key?.PRICE || 'Đơn giá' }}</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data of dataChart.lstTop10SupplierUnitPriceDisplay; let i = index">
            <tr (click)="showUnitPriceDetail(data)" style="cursor: pointer;">
              <td>{{ i + 1 }}</td>
              <td>{{ data.supplierCode }}</td>
              <td>{{ data.supplierName }}</td>
              <td>{{ data.bidCode }}</td>
              <td>{{ data.bidName }}</td>
              <td class="text-right">{{ data.unitPrice | number}}</td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
      <i>* Lưu ý: Click vào dòng để xem chi tiết</i>
    </nz-row>
  </ng-container>
</nz-modal>

<!-- popup detail chart 5-6 -->
<nz-modal [(nzVisible)]="isVisibleScore" [nzWidth]="'60vw'" [nzTitle]="titleModal" (nzOnCancel)="hideDataChartScore()"
  [nzFooter]="null" *ngIf="dataChart">
  <ng-container *nzModalContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="16">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataChart.supplierId" nzPlaceHolder="Chọn NCC">
          <nz-option *ngFor="let item of dataChart.lstTop10SupplierScore"
            [nzLabel]="'(' + item.supplierCode + ') ' + item.supplierName" [nzValue]="item.supplierId">
          </nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSpan="8">
        <button nz-button (click)="searchDataScore()">
          <span nz-icon nzType="search"></span>
          {{ language_key?.SEARCH || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-2">
      <button nz-button (click)="exportExcelDataScore()" nzType="primary">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
    </nz-row>
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" id="score-table" [nzData]="dataChart.lstTop10SupplierScoreDisplay"
        [nzFrontPagination]="false" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th nzWidth="50px">TOP</th>
            <th>Mã nhà cung cấp</th>
            <th>Tên nhà cung cấp</th>
            <th>Điểm năng lực</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data of dataChart.lstTop10SupplierScoreDisplay; let i = index">
            <tr (click)="showScoreDetail(data)" style="cursor: pointer;">
              <td>{{ i + 1 }}</td>
              <td>{{ data.supplierCode }}</td>
              <td>{{ data.supplierName }}</td>
              <td class="text-right">{{ data.score | number: '1.0-2'}}</td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
      <i>* Lưu ý: Click vào dòng để xem chi tiết</i>
    </nz-row>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleScoreDetail" [nzWidth]="'90vw'" nzTitle="Thông tin năng lực"
  (nzOnCancel)="hideScoreDetail()" [nzFooter]="null" *ngIf="dataChart && dataChart.supplierServiceId">
  <ng-container *nzModalContent>
    <app-supplier-capacity-detail [data]="{ supplierServiceId: dataChart.supplierServiceId }">
    </app-supplier-capacity-detail>
  </ng-container>
</nz-modal>
<!-- end popup detail chart 5-6 -->
<nz-row nzGutter="6" class="mt-3">
  <nz-col nzSpan="16">
  <h2>Top 10 Nhà cung cấp</h2>
  </nz-col>
  <nz-col nzSpan="2">
     <nz-select ngModel="Toàn bộ">
      <nz-option nzValue="Toàn bộ" nzLabel="Toàn bộ"></nz-option>
    </nz-select>
  </nz-col>
</nz-row>
<div nz-row class="mt-3">
  <div class='table-scroll-item'>
    <nz-table class="mb-3" nz-col nzSpan="24" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
       [nzShowPagination]="false" nzBordered nzTableLayout="fixed">
      <thead>
        <tr>
          <th nzWidth="200px">Mã số doanh nghiệp</th>
          <th nzWidth="250px">Tên doanh nghiệp</th>
          <th nzWidth="100px">Điểm năng lực</th>
          <th nzWidth="250px">Lĩnh vực kinh doanh</th>
          <th nzWidth="100px">Tùy chọn</th>
        </tr>
      </thead>
      <tbody>
        <tr class="table-tbody" *ngFor="let data of listOfData">
          <td>{{ data.supplierCode }}</td>
          <td>{{ data.supplierName }}</td>
          <td>{{ data.score }}</td>
          <td>{{ data.itemName }}</td>
          <td>
            <button (click)="clickDetail(data)" nz-tooltip nzTooltipTitle="Chi tiết" nz-button nzShape="circle"
            class="mr-2">
            <i nz-icon nzType="eye"></i>
          </button>
            <!-- <button (click)="viewSupplierService(data)"nz-tooltip nzTooltipTitle="Danh sách Lĩnh vực kinh doanh" nz-button>
            <span nz-icon nzType="unordered-list"></span>
          </button> -->
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>
<div nz-col [nzSpan]="24" class="text-right">
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData()" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng
  </ng-template>
</div>