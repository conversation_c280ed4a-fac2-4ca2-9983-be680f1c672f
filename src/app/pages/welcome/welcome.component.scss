.limiter {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.header-container {
  position: relative;
  text-align: center; /* Center align the content */
}

.header-container h1 {
  font-weight: bold;
  font-size: 3rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  background-color: aliceblue;
  opacity: 0.8;
}

.container-login100 {
  position: relative;
  width: 100%;
  min-height: 100vh;
  position: relative;
}

.footer-wrapper {
  background-color: #333;
}

.footer {
  display: flex;
  justify-content: space-between;
  color: white;
  padding: 20px;
  text-align: center;
  max-width: 1200px; /* Adjust as needed */
  margin: 0 auto; /* Center the footer */
}

.footer p {
  margin: 0;
}

.footer a {
  color: white;
  text-decoration: none;
}
.container {
  height: calc(100vh - 70px);
  margin: 0;
}

.logo {
  width: 100%;
  height: 100%;
}
