import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { WelcomeRoutingModule } from './welcome-routing.module'
import { WelcomeComponent } from './welcome.component'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzCardModule } from 'ng-zorro-antd/card'
import { AnalysisComponent } from './analysis/analysis.component'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MaterialModule } from '../../app.module'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzTableModule } from 'ng-zorro-antd/table'
import { SupplierTop10Component } from './supplier-top10/supplier-top10.component'
import { BidModule } from '../bid/bid.module'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { SupplierTop10DetailComponent } from './supplier-top10/supplier-top10-detail/supplier-top10-detail.component'
@NgModule({
  imports: [
    CommonModule,
    WelcomeRoutingModule,
    NzCardModule,
    FormsModule,
    NzButtonModule,
    NzTableModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDatePickerModule,
    NzPopconfirmModule,
    NzPaginationModule,
    MaterialModule,
    ReactiveFormsModule,
    NzCollapseModule,
    CommonModule,
    NzStatisticModule,
    NzGridModule,
    NzTagModule,
    BidModule,
    NzCascaderModule,
  ],
  declarations: [WelcomeComponent, AnalysisComponent, SupplierTop10Component,SupplierTop10DetailComponent],
  exports: [WelcomeComponent],
  bootstrap: [WelcomeComponent],
})
export class WelcomeModule {}
