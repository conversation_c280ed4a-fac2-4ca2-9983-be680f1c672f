<nz-collapse>
  <nz-collapse-panel nzHeader="Tì<PERSON> kiếm" class="ant-bg-antiquewhite" [(nzActive)]="isCollapseFilter">

    <nz-row nzGutter="8">

      <nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left"><PERSON><PERSON> hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input placeholder="Nhập Mã hợp đồng" [(ngModel)]="dataSearch.code" name="code"
              pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Ti<PERSON><PERSON> đ<PERSON> hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input placeholder="Nhập Ti<PERSON><PERSON> đề hợp đồng" [(ngModel)]="dataSearch.name" name="name"
              pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>


      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Trạng thái</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn trạng thái" [(ngModel)]="dataSearch.status"
              name="status">
              <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>


      </nz-col>


      <nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Gói thầu</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Gói thầu" [(ngModel)]="dataSearch.bidId"
              name="bidId">
              <nz-option *ngFor="let item of dataBid" [nzLabel]="item.name" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Nhà cung cấp" [(ngModel)]="dataSearch.supplierId"
              name="supplierId">
              <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo HĐ - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.createdAtFrom" nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo HĐ - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.createdAtTo" nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hiệu lực - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.effectiveDateFrom"
              nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hiệu lực - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.effectiveDateTo"
              nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hết hạn - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.expiredDateFrom"
              nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hết hạn - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.expiredDateTo" nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>


    </nz-row>
  </nz-collapse-panel>
</nz-collapse>


<nz-row nzGutter="8" class="mt-3 text-center">
  <nz-col nzSpan="24">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button nzType="primary"
      (click)="clickAdd()">
      <span nz-icon nzType="plus"></span> Tạo mới
    </button>

    
    <button class="ml-2" nz-button (click)="searchData(true)"  nzType="primary">
      <span nz-icon nzType="search"></span>Tìm kiếm
    </button>

    <button class="ml-2" *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button
      (click)="clickExportExcelList()">
      <span nz-icon nzType="download"></span>Tải danh sách
    </button>
  </nz-col>

</nz-row>
<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="24">

    <nz-table class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered [nzScroll]="{ x: '2000px' }" nzTableLayout="fixed">
      <thead>
        <tr>
          <th nzWidth="250px">Mã hợp đồng</th>
          <th>Tiêu đề hợp đồng</th>
          <th>Gói thầu</th>
          <th>Nhà cung cấp</th>
          <th>Ngày hết hạn</th>
          <th>Ngày hiệu lực</th>
          <th>Trạng thái</th>
          <th>Ngày tạo</th>
          <th nzWidth="150px" nzRight>Tác vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td nzWidth="250px">{{ data.code }}</td>
          <td>{{ data.name }}</td>
          <td>{{ data.bidName }}</td>
          <td>{{ data.supplierName }}</td>
          <td>{{ data.expiredDate | date: 'dd/MM/yyyy' }}</td>
          <td>{{ data.effectiveDate | date: 'dd/MM/yyyy' }}</td>
          <td><nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName}}</nz-tag> </td>
          <td>{{ data.createdAt | date: 'dd/MM/yyyy' }}</td>
          <td nzRight nzWidth="150px">

            <button
              *ngIf="data.status === contractStatusOpen && data.isAllowContractManagerView && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-tooltip nzTooltipTitle="Chỉnh hợp đồng" (click)="clickEdit(data)" class="mr-2 mt-2 mb-2" nz-button
              nzType="dashed">
              <span nz-icon nzType="edit"></span>
            </button>

            <button
              *ngIf="data.isAllowViewContract && authenticationService.checkPermission([enumRole], action.View.code)"
              nz-tooltip nzTooltipTitle="Xem chi tiết" (click)="clickView(data)" class="mr-2 mt-2 mb-2" nz-button
              nzType="dashed">
              <span nz-icon nzType="eye"></span>
            </button>


            <button
              *ngIf="data.status === contractStatusOpen && data.isAllowApprovedContract && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [DUYỆT] hợp đồng ?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="onForwardProcessing(data)" class="mr-2 mt-2 mb-2  bg-primary" nz-tooltip
              nzTooltipTitle="[DUYỆT] hợp đồng" nz-button nzType="primary">
              <span nz-icon nzType="check"></span>
            </button>

            <button
              *ngIf="(data.status !== 'CANCEL' && data.status !== 'COMPLETE' && data.isAllowApprovedContract && authenticationService.checkPermission([enumRole], action.Delete.code))"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [HỦY] hợp đồng ?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="showForwardCancel(data)" class="mr-2 mt-2 mb-2" nz-tooltip nzTooltipTitle="[HỦY] hợp đồng"
              nz-button nzDanger>
              <span nz-icon nzType="close"></span>
            </button>

            <button
              *ngIf="data.status === 'PROCESSING' && data.isAllowContractManagerView && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn [Hoàn Thành] hợp đồng ?" nzPopconfirmPlacement="bottom"
              (nzOnConfirm)="onForwardComplete(data)" class="mr-2 mt-2 mb-2 ant-btn-success" nz-tooltip
              nzTooltipTitle="[HOÀN THÀNH] hợp đồng" nz-button nzType="primary">
              <span nz-icon nzType="check-circle"></span>
            </button>

            <!-- <button
              *ngIf="data.isAllowContractManagerView && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-tooltip nzTooltipTitle="Danh Sách Phụ Lục " (click)="clickIsVisibleAppendix(data)"
              class="mr-2 mt-2 mb-2" nz-button nzType="dashed" >
              <span nz-icon nzType="book"></span>
            </button> -->
            <button class="mt-2 mb-2"
              *ngIf="data.isAllowContractManagerView && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-tooltip nzTooltipTitle="Thông Tin PO" (click)="clickIsVisiblePO(data)" nz-button nzType="dashed">
              <span nz-icon nzType="shopping"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
 
  </nz-col>
  <nz-pagination [nzTotal]="total" class="mt-3" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>

<style>
  td[scope="OPEN"] {
    color: darkcyan;
    font-weight: bold;
  }

  td[scope="PROCESSING"] {
    color: blue;
    font-weight: bold;
  }

  td[scope="COMPLETE"] {
    color: orange;
    font-weight: bold;
  }

  td[scope="CANCEL"] {
    color: red;
    font-weight: bold;
  }
</style>

<nz-modal *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" [(nzVisible)]="isVisiblePO"
  nzTitle="Danh sách PO Hợp đồng" [nzWidth]="'80vw'" (nzOnCancel)="isVisiblePO = false" [nzFooter]="null">
  <ng-container *nzModalContent>
    <app-po [contract]="contract"></app-po>
  </ng-container>
</nz-modal>

<nz-modal *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
  [(nzVisible)]="isVisibleAppendix" nzTitle="Danh sách phụ lục Hợp đồng" [nzWidth]="'80vw'"
  (nzOnCancel)="isVisibleAppendix = false" [nzFooter]="null">
  <ng-container *nzModalContent>
    <app-contract-appendix [contract]="contract"></app-contract-appendix>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleCancel" nzTitle="Hủy Hợp Đồng" (nzOnCancel)="isVisibleCancel = false">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center; display: flex;">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Lý do hủy hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Lý do hủy hợp đồng">
            <textarea rows="4" nz-input [(ngModel)]="dataObject.reason" name="reason" required
              pattern=".{1,1000}"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter>
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
      [disabled]="!dataObject.reason || dataObject.reason === ''" nzType="primary" (click)="onForwardCancel()"
      nz-button>
      <span nz-icon nzType="delete"></span>Hủy
    </button>
    <button (click)="isVisibleCancel = false" nz-button>
      <span nz-icon nzType="close"></span> Đóng
    </button>
  </div>
</nz-modal>