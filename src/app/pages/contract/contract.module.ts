import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ContractRoutingModule } from './contract-routing.module'
import { ContractComponent } from './contract.component'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { CurrencyMaskModule, CURRENCY_MASK_CONFIG } from 'ng2-currency-mask'
import { MaterialModule } from '../../app.module'
import { DirectivesModule } from '../../directive/directives.module'
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCardModule } from 'ng-zorro-antd/card'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzDividerModule } from 'ng-zorro-antd/divider'
import { NzTypographyModule } from 'ng-zorro-antd/typography'
import { NzDrawerModule } from 'ng-zorro-antd/drawer'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzAvatarModule } from 'ng-zorro-antd/avatar'
import { NzMessageModule } from 'ng-zorro-antd/message'
import { NzProgressModule } from 'ng-zorro-antd/progress'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { NzBadgeModule } from 'ng-zorro-antd/badge'
import { AddOrEditContractComponent } from './add-or-edit-contract/add-or-edit-contract.component'
import { AddOrEditContractAppendixComponent } from './contract-appendix/add-or-edit-contract-appendix/add-or-edit-contract-appendix.component'
import { ContractAppendixComponent } from './contract-appendix/contract-appendix.component'
import { ContractDetailComponent } from './contract-detail/contract-detail.component'
import { ContractHistoryComponent } from './contract-history/contract-history.component'
import { ContractModalComponent } from './contract-modal/contract-modal.component'
import { AddOrEditInvoiceSuggestComponent } from './invoice-suggest/add-or-edit-invoice-suggest/add-or-edit-invoice-suggest.component'
import { InvoiceSuggestDetailComponent } from './invoice-suggest/invoice-suggest-detail/invoice-suggest-detail.component'
import { InvoiceSuggestComponent } from './invoice-suggest/invoice-suggest.component'
import { AddInvoiceComponent } from './invoice/add-invoice/add-invoice.component'
import { InvoiceComponent } from './invoice/invoice.component'
import { AddOrEditPOComponent } from './po/add-or-edit-po/add-or-edit-po.component'
import { PoAsnViewComponent } from './po/po-asn-view/po-asn-view.component'
import { POModalComponent } from './po/po-modal/po-modal.component'
import { PODetailComponent } from './po/po-detail/po-detail.component'
import { POComponent } from './po/po.component'
import { CustomCurrencyMaskConfig } from '../setting/setting.module'
import { DeliveryDateManangerComponent } from './delivery-date-mananger/delivery-date-mananger.component'
import { PoChildDetailComponent } from './po/po-detail/po-child-detail/po-child-detail.component'
import { ContractChildDetailComponent } from './contract-detail/contract-child-detail/contract-child-detail.component'
import { BillComponent } from './bill/bill.component'
import { AddOrEditBillComponent } from './bill/add-or-edit-bill/add-or-edit-bill.component'
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { BillDetailComponent } from './bill/bill-detail/bill-detail.component'
import { NzSwitchModule } from 'ng-zorro-antd/switch';
@NgModule({
  declarations: [
    ContractComponent,
    ContractModalComponent,
    ContractDetailComponent,
    ContractHistoryComponent,
    ContractAppendixComponent,
    AddOrEditContractComponent,
    AddOrEditContractAppendixComponent,
    POComponent,
    PODetailComponent,
    AddInvoiceComponent,
    POModalComponent,
    AddOrEditPOComponent,
    PoAsnViewComponent,
    InvoiceSuggestComponent,
    DeliveryDateManangerComponent,
    InvoiceComponent,
    InvoiceSuggestDetailComponent,
    ContractChildDetailComponent,
    AddOrEditInvoiceSuggestComponent,
    PoChildDetailComponent,
    BillComponent,
    AddOrEditBillComponent,
    BillDetailComponent,
  ],
  imports: [
    CommonModule,
    ContractRoutingModule,
    CurrencyMaskModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzTableModule,
    NzDividerModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDescriptionsModule,
    NzListModule,
    NzDatePickerModule,
    NzStatisticModule,
    NzPaginationModule,
    NzCollapseModule,
    NzCascaderModule,
    NzPopoverModule,
    NzTypographyModule,
    MaterialModule,
    NzPopconfirmModule,
    NzDrawerModule,
    NzCardModule,
    NzUploadModule,
    NzProgressModule,
    NzAvatarModule,
    NzMessageModule,
    DirectivesModule,
    NzTagModule,
    NzBadgeModule,
    NzRadioModule,
    NzSwitchModule
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
})
export class ContractModule {}
