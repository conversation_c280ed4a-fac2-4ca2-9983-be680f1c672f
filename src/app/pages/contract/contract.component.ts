import { Component, OnInit } from '@angular/core'
import { enumData } from '../../core/enumData'
import { ApiService, NotifyService, CoreService, AuthenticationService } from '../../services'
import { MatDialog } from '@angular/material/dialog'
import { ContractModalComponent } from './contract-modal/contract-modal.component'
import { AddOrEditContractComponent } from './add-or-edit-contract/add-or-edit-contract.component'
import * as XLSX from 'xlsx'
import moment from 'moment'
@Component({
  selector: 'app-contract',
  templateUrl: './contract.component.html',
})
export class ContractComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  dataObject: any = {}
  listOfData: any[] = []
  dataBid: any[] = []
  dataSupplier: any[] = []
  isCollapseFilter = false

  isVisiblePO = false
  isVisiblePayment = false
  isVisibleAppendix = false
  contract: any
  isVisibleCancel = false
  dataStatus = this.coreService.convertObjToArray(enumData.ContractStatus)
  ContractRole = enumData.Role.Contract
  currentUser: any
  screenWidth: any
  contractStatusOpen = enumData.ContractStatus.Open.code
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.loadBid()
    this.loadSupplier()
    this.searchData(true)
    this.dataStatus = this.coreService.convertObjToArray(enumData.ContractStatus)
    this.screenWidth = window.screen.width
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.CONTRACT_001.code
  }

  async searchData(reset = false) {
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.supplierId) {
      where.supplierId = this.dataSearch.supplierId
    }
    if (this.dataSearch.bidId) {
      where.bidId = this.dataSearch.bidId
    }

    if (this.dataSearch.createdAtFrom) {
      where.createdAtFrom = this.dataSearch.createdAtFrom
    }
    if (this.dataSearch.createdAtTo) {
      where.createdAtTo = this.dataSearch.createdAtTo
    }

    if (this.dataSearch.effectiveDateFrom) {
      where.effectiveDateFrom = this.dataSearch.effectiveDateFrom
    }
    if (this.dataSearch.effectiveDateTo) {
      where.effectiveDateTo = this.dataSearch.effectiveDateTo
    }

    if (this.dataSearch.expiredDateFrom) {
      where.expiredDateFrom = this.dataSearch.expiredDateFrom
    }
    if (this.dataSearch.expiredDateTo) {
      where.expiredDateTo = this.dataSearch.expiredDateTo
    }

    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }

    this.loading = true
    const dataSearch = {
      where: where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.CONTRACT.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditContractComponent, { disableClose: false })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditContractComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickView(object: any) {
    this.dialog.open(ContractModalComponent, { disableClose: false, data: { ...object, isContract: true } })
  }

  clickIsVisibleAppendix(data: any) {
    this.contract = data
    this.isVisibleAppendix = true
    this.apiService.eventCloseModal.subscribe((res) => {
      if (res === true) {
        this.isVisibleAppendix = false
      }
    })
  }
  clickIsVisiblePO(data: any) {
    this.contract = data
    this.isVisiblePO = true
    this.apiService.eventCloseModalPO.subscribe((res) => {
      if (res === true) {
        this.isVisiblePO = false
      }
    })
  }

  loadBid() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.FIND, { status: enumData.BidStatus.HoanTat.code }).then((res) => {
      this.notifyService.hideloading()
      this.dataBid = res || []
    })
  }

  loadSupplier() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SUPPLIER.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataSupplier = result || []
    })
  }

  onForwardProcessing(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.UPDATE_PROCESSING, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
      }
    })
  }

  showForwardCancel(data: any) {
    this.dataObject = data
    this.isVisibleCancel = true
  }

  onForwardCancel() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.UPDATE_CANCEL, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
        this.isVisibleCancel = false
      }
    })
  }

  ngOnDestroy() {
    this.apiService.eventCloseModal.next(false)
    this.apiService.eventCloseModalPO.next(false)
  }

  onForwardComplete(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.UPDATE_COMPLETE, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
      }
    })
  }

  clickExportExcelList() {
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    this.loading = true
    const where: any = {}
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.supplierId) {
      where.supplierId = this.dataSearch.supplierId
    }
    if (this.dataSearch.bidId) {
      where.bidId = this.dataSearch.bidId
    }

    if (this.dataSearch.createdAtFrom) {
      where.createdAtFrom = this.dataSearch.createdAtFrom
    }
    if (this.dataSearch.createdAtTo) {
      where.createdAtTo = this.dataSearch.createdAtTo
    }

    if (this.dataSearch.effectiveDateFrom) {
      where.effectiveDateFrom = this.dataSearch.effectiveDateFrom
    }
    if (this.dataSearch.effectiveDateTo) {
      where.effectiveDateTo = this.dataSearch.effectiveDateTo
    }

    if (this.dataSearch.expiredDateFrom) {
      where.expiredDateFrom = this.dataSearch.expiredDateFrom
    }
    if (this.dataSearch.expiredDateTo) {
      where.expiredDateTo = this.dataSearch.expiredDateTo
    }

    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }

    const dataSearch = {
      where: where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }

    this.apiService.post(this.apiService.CONTRACT.PAGINATION, dataSearch).then((data) => {
      this.loading = false
      if (data && data[0] && data[0].length > 0) {
        let date = new Date().toISOString()
        const fileName = 'Danh_sach_hop_dong_' + date + '.xlsx'
        let dataExcel: any[] = []
        data[0].forEach((s: any) => {
          dataExcel.push({
            'Mã hợp đồng': s.code,
            'Tiêu đề hợp đồng': s.name,
            'Gói thầu': s.bidName,
            'Nhà cung cấp': s.supplierName,
            'Ngày hết hạn': s.expiredDate ? moment(s.expiredDate).format('DD-MM-YYYY') : '',
            'Ngày hiệu lực': s.effectiveDate ? moment(s.effectiveDate).format('DD-MM-YYYY') : '',
            'Trạng thái': s.statusName,
            'Ngày tạo': s.createdAt ? moment(s.createdAt).format('DD-MM-YYYY') : '',
          })
        })
        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
        const wb: XLSX.WorkBook = XLSX.utils.book_new()
        ws['!cols'] = [{ width: 30 }, { width: 30 }, { width: 30 }, { width: 30 }, { width: 30 }, { width: 30 }, { width: 30 }, { width: 30 }]
        XLSX.utils.book_append_sheet(wb, ws, 'Danh sách Hợp Đồng')

        XLSX.writeFile(wb, fileName)
      }
    })
  }
}
