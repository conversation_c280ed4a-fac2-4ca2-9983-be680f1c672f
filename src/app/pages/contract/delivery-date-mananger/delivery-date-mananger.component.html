<nz-collapse>
  <nz-collapse-panel nzHeader="Tìm kiếm" class="ant-bg-antiquewhite">
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mã PO</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input placeholder="Nhập mã PO" [(ngModel)]="dataSearch.code" name="code" pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">G<PERSON><PERSON> thầ<PERSON></nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn <PERSON><PERSON> thầu" [(ngModel)]="dataSearch.bidId"
              name="bidId">
              <nz-option *ngFor="let item of dataBid" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Nhà cung cấp" [(ngModel)]="dataSearch.supplierId"
              name="supplierId">
              <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo PO - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo PO - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Trạng thái</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn trạng thái" [(ngModel)]="dataSearch.status"
              name="status">
              <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" class="mt-3 text-center">
        <button nz-button (click)="searchData(true)" class="mr-2"><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>
<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="8">
    <p>
      <span style="font-style: italic; color: green"> Màu xanh lá: Đúng ngày dự kiến &nbsp; </span>
    </p>
  </nz-col>
  <nz-col nzSpan="8">
    <p>
      <span style="font-style: italic; color: red"> Màu đỏ: Quá ngày dự kiến &nbsp; </span>
    </p>
  </nz-col>

  <nz-col nzSpan="8">
    <p>
      <span style="font-style: italic; color: blue"> Màu xanh dương: Sắp đến ngày dự kiến &nbsp; </span>
    </p>
  </nz-col>
  


</nz-row>
<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="24">

    <nz-table class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered [nzScroll]="{ x: '2000px' }" nzTableLayout="fixed">
      <thead>
        <tr>
          <th nzWidth="225px" nzLeft>Mã PO</th>
          <th>Gói thầu</th>
          <th>Nhà cung cấp</th>
          <th>Ngày dự kiến giao hàng</th>
          <th>Ngày tạo</th>
          <th nzWidth="200px">Trạng thái</th>
          <th nzWidth="120px" nzRight>Tác vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td [style.background-color]="data.deliveryColor ? data.deliveryColor : ''" nzLeft nzWidth="225px">{{
            data.code }}</td>
          <td [style.background-color]="data.deliveryColor ? data.deliveryColor : ''">{{ data.bidName }}</td>
          <td [style.background-color]="data.deliveryColor ? data.deliveryColor : ''">{{ data.supplierName }}</td>
          <td [style.background-color]="data.deliveryColor ? data.deliveryColor : ''">{{ data.deliveryDate | date :
            'dd/MM/yyyy' }}</td>
          <td [style.background-color]="data.deliveryColor ? data.deliveryColor : ''">{{ data.createdAt | date :
            'dd/MM/yyyy' }}</td>

          <td class="text-center">
            <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
          </td>
   
          <td nzRight nzWidth="120px">
            <button *ngIf="data.isAllowEditPO && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-tooltip nzTooltipTitle="Cập nhật ngày dự kiến giao hàng" (click)="showForwardCancel(data)"
              class="mr-2 mt-2 mb-2" nz-button nzType="dashed">
              <span nz-icon nzType="edit"></span>
            </button>

          
          </td>
        </tr>
      </tbody>
    </nz-table>
    <!-- </div> -->
  </nz-col>
  <nz-pagination [nzTotal]="total" class="mt-3" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>
<br />

<style>
  td[scope='OPEN'] {
    color: darkcyan;
    font-weight: bold;
  }

  td[scope='PROCESSING'] {
    color: blue;
    font-weight: bold;
  }

  td[scope='COMPLETE'] {
    color: orange;
    font-weight: bold;
  }

  td[scope='CANCEL'] {
    color: red;
    font-weight: bold;
  }
</style>

<nz-modal *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)" [(nzVisible)]="isVisiblePO"
  nzTitle="Danh sách PO Hợp đồng" [nzWidth]="'80vw'" (nzOnCancel)="isVisiblePO = false" [nzFooter]="null">
  <ng-container *nzModalContent>
    <app-po [contract]="contract"></app-po>
  </ng-container>
</nz-modal>

<nz-modal *ngIf="authenticationService.checkPermission([enumRole], action.Update.code)"
  [(nzVisible)]="isVisibleAppendix" nzTitle="Danh sách phụ lục Hợp đồng" [nzWidth]="'80vw'"
  (nzOnCancel)="isVisibleAppendix = false" [nzFooter]="null">
  <ng-container *nzModalContent>
    <app-contract-appendix [contract]="contract"></app-contract-appendix>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isEditDate" nzTitle="Cập nhật ngày dự kiến giao hàng" (nzOnCancel)="isEditDate = false">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center; display: flex">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label class="text-left" [nzSm]="24" [nzXs]="24">ngày dự kiến giao hàng</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-date-picker nzPlaceHolder="Chọn ngày dự kiến giao hàng" nzFormat="dd/MM/yyyy"
              [(ngModel)]="dataObject.deliveryDate" name="deliveryDate">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter>

    <button [disabled]="!dataObject.deliveryDate || dataObject.deliveryDate === ''" nzType="primary"
      (click)="onSaveDate()" nz-button>
      <span nz-icon nzType="save" nzTheme="outline"></span>Lưu
    </button>
    <button (click)="isEditDate = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>