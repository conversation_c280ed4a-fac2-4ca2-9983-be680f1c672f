import { Component, OnInit } from '@angular/core'

import { MatDialog } from '@angular/material/dialog'
import * as XLSX from 'xlsx'
import moment from 'moment'
import { enumData } from '../../../core/enumData'
import { NotifyService } from '../../../services/notify.service'
import { ApiService } from '../../../services/api.service'
import { CoreService } from '../../../services/core.service'
import { AuthenticationService } from '../../../services/authentication.service'
import { ContractModalComponent } from '../contract-modal/contract-modal.component'
@Component({
  selector: 'app-delivery-date-mananger',
  templateUrl: './delivery-date-mananger.component.html',
  styleUrls: ['./delivery-date-mananger.component.scss'],
})
export class DeliveryDateManangerComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  dataObject: any = {}
  listOfData: any[] = []
  dataBid: any[] = []
  dataSupplier: any[] = []
  isCollapseFilter = false
  enumData: any
  isVisiblePO = false
  allService: any
  isVisiblePayment = false
  isVisibleAppendix = false
  enumStatus: any
  contract: any
  isEditDate = false
  dataStatus = this.coreService.convertObjToArray(enumData.ContractStatus)
  ContractRole = enumData.Role.Contract
  currentUser: any
  screenWidth: any
  contractStatusOpen = enumData.ContractStatus.Open.code
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.PO_002.code
    this.loadBid()
    this.loadSupplier()
    this.searchData(true)
    this.dataStatus = this.coreService.convertObjToArray(this.enumData.PurchaseOrderStatus)
    this.screenWidth = window.screen.width
    this.loadService()
    this.enumStatus = this.enumData.PurchaseOrderStatus
  }
  loadService() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE.FIND, {}).then((result: any) => {
      this.notifyService.hideloading()
      this.allService = result
    })
  }

  async searchData(reset = false) {
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (reset) this.pageIndex = 1
    const where: any = {}
    where.getManager = true
    if (this.dataSearch.name && this.dataSearch.name !== '') {
      where.name = this.dataSearch.name
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.supplierId) {
      where.supplierId = this.dataSearch.supplierId
    }
    if (this.dataSearch.bidId) {
      where.bidId = this.dataSearch.bidId
    }

    if (this.dataSearch.createdAtFrom) {
      where.createdAtFrom = this.dataSearch.createdAtFrom
    }
    if (this.dataSearch.createdAtTo) {
      where.createdAtTo = this.dataSearch.createdAtTo
    }

    if (this.dataSearch.effectiveDateFrom) {
      where.effectiveDateFrom = this.dataSearch.effectiveDateFrom
    }
    if (this.dataSearch.effectiveDateTo) {
      where.effectiveDateTo = this.dataSearch.effectiveDateTo
    }

    if (this.dataSearch.expiredDateFrom) {
      where.expiredDateFrom = this.dataSearch.expiredDateFrom
    }
    if (this.dataSearch.expiredDateTo) {
      where.expiredDateTo = this.dataSearch.expiredDateTo
    }

    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }

    this.loading = true
    const dataSearch = {
      where: where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.PO.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
        for (const data of this.listOfData) {
          const random = this.getRandomInt(3)
          if (random === 0) {
            data.statusDate = 'Chờ duyệt'
            data.statusDateColor = 'blue'
          }
          if (random === 1) {
            data.statusDate = 'Chưa cập nhật'
            delete data.deliveryDate
            data.statusDateColor = 'red'
          }
          if (random === 2) {
            data.statusDate = 'Đã duyệt'
            data.statusDateColor = 'green'
          }
        }
      }
    })
  }

  getRandomInt(max: any) {
    return Math.floor(Math.random() * max)
  }

  clickEdit(object: any) {
    // this.dialog
    //   .open(AddOrEditContractComponent, { disableClose: false, data: object })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     if (res) this.searchData()
    //   })
  }

  clickView(object: any) {
    this.dialog.open(ContractModalComponent, { disableClose: false, data: { ...object, isContract: true } })
  }

  clickIsVisibleAppendix(data: any) {
    this.contract = data
    this.isVisibleAppendix = true
    this.apiService.eventCloseModal.subscribe((res) => {
      if (res === true) {
        this.isVisibleAppendix = false
      }
    })
  }
  clickIsVisiblePO(data: any) {
    this.contract = { ...data }
    this.isVisiblePO = true
    this.apiService.eventCloseModalPO.subscribe((res) => {
      if (res === true) {
        this.isVisiblePO = false
      }
    })
  }

  loadBid() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.FIND, { status: enumData.BidStatus.HoanTat.code }).then((res) => {
      this.notifyService.hideloading()
      this.dataBid = res || []
    })
  }

  loadSupplier() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SUPPLIER.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataSupplier = result || []
    })
  }

  showForwardCancel(data: any) {
    this.dataObject = data
    this.dataObject.isSupplier = false
    this.isEditDate = true
  }

  onSaveDate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.UPDATE_DELIVERY_DATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
        this.isEditDate = false
      }
    })
  }
}
