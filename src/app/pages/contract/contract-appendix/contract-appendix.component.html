<nz-collapse>
  <nz-collapse-panel nzHeader="Tìm kiếm" class="ant-bg-antiquewhite" [(nzActive)]="isCollapseFilter">

    <nz-row nzGutter="8">

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Mã phụ lục</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input placeholder="Nhập mã phụ lục" [(ngModel)]="dataSearch.code" name="code" pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Loại phụ lục</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn loại phụ lục hợp đồng"
              [(ngModel)]="dataSearch.type" name="type">
              <nz-option *ngFor="let item of dataContractTypeAppendix" [nzLabel]="item.name" [nzValue]="item.code">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateCreatAtFrom"
              nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateCreatAtTo" nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hiệu lực - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.effectiveDateFrom"
              nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hiệu lực - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.effectiveDateTo"
              nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hết hạn - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.expiredDateFrom"
              nzPlaceHolder="Nhập Từ ngày ">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày hết hạn - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.expiredDateTo" nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-row class="mt-1">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>Tìm kiếm
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <button *ngIf="contract?.status !== 'CANCEL' && authenticationService.checkPermission([enumRole], action.Create.code)"
    nz-button nzType="primary" (click)="clickAdd()">
    <span nz-icon nzType="plus"></span> Thêm mới phụ lục hợp đồng
  </button>
</nz-row>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <nz-table class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr class="text-nowrap">
          <th>STT</th>
          <th>Mã</th>
          <th>Tiêu đề</th>
          <th>Loại phụ lục</th>
          <th>Ngày hiệu lực</th>
          <th>Ngày hết hạn</th>
          <th>File phụ lục hợp đồng</th>
          <th>Ghi chú</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data; let i = index">
          <td class="mw-25">
            {{ i + 1 }}
          </td>
          <td class="mw-25">
            {{ data.code }}
          </td>
          <td class="mw-25">
            {{ data.title }}
          </td>
          <td class="mw-25">
            {{ data.typeName }}
          </td>
          <td class="mw-25">
            {{ data.effectiveDate | date: "dd/MM/yyyy" }}
          </td>
          <td class="mw-25">
            {{ data.expiredDate | date: "dd/MM/yyyy" }}
          </td>
          <td class="mw-25">
            <button nz-button nzType="primary" *ngIf="data.fileAttach">
              <a class="btn" href="{{ data.fileAttach }}" target="_blank">
                <span nz-icon nzType="down-square"></span> Tải về
              </a>
            </button>
          </td>
          <td class="mw-25">{{ data.description }}</td>
          <td class="text-nowrap">
            <button
              *ngIf="contract?.status !== 'CANCEL' && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-tooltip nzTooltipTitle="Chỉnh sửa phụ lục" (click)="clickEdit(data)" nz-button nzType="dashed">
              <span nz-icon nzType="edit"></span>
            </button>
          </td>

        </tr>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-col>
</nz-row>