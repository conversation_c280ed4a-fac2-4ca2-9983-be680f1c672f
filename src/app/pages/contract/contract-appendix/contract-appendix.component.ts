import { Component, Input, OnInit, SimpleChanges } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../core/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService } from '../../../services'
import { AddOrEditContractAppendixComponent } from './add-or-edit-contract-appendix/add-or-edit-contract-appendix.component'

@Component({
  selector: 'app-contract-appendix',
  templateUrl: './contract-appendix.component.html',
})
export class ContractAppendixComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = false
  dataSearch: any = {}
  dataContractTypeAppendix = this.coreService.convertObjToArray(enumData.ContractTypeAppendix)
  isCollapseFilter = false
  listOfData: any[] = []
  enumProject: any
  enumRole: any
  action: any
  @Input() contract: any
  currentUser: any
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }
  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.CONTRACT_001.code
  }

  ngOnChanges(changes: any) {
    if (changes !== undefined) {
      if (changes.currentValue !== null) {
        if (changes.contract.currentValue !== undefined) {
          this.searchData()
        }
      }
    }
  }

  async searchData(reset = false) {
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (this.dataSearch.effectiveDateFrom > this.dataSearch.effectiveDateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (this.dataSearch.expiredDateFrom > this.dataSearch.expiredDateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (reset) this.pageIndex = 1
    const where: any = { contractId: this.contract.id }
    this.loading = true

    if (this.dataSearch.expiredDateFrom) {
      where.expiredDateFrom = this.dataSearch.expiredDateFrom
    }
    if (this.dataSearch.expiredDateTo) {
      where.expiredDateTo = this.dataSearch.expiredDateTo
    }

    if (this.dataSearch.effectiveDateFrom) {
      where.effectiveDateFrom = this.dataSearch.effectiveDateFrom
    }
    if (this.dataSearch.effectiveDateTo) {
      where.effectiveDateTo = this.dataSearch.effectiveDateTo
    }

    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }
    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }
    if (this.dataSearch.type) {
      where.type = this.dataSearch.type
    }
    if (this.dataSearch.code) {
      where.code = this.dataSearch.code
    }
    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.CONTRACT_APPENDIX.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    const object: any = { objectId: this.contract.objectId, contractId: this.contract.id }
    this.apiService.eventCloseModal.next(true)
    this.dialog
      .open(AddOrEditContractAppendixComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
        this.apiService.eventCloseModal.next(false)
      })
  }

  clickEdit(object: any) {
    object.objectId = this.contract.objectId
    object.contractId = this.contract.id
    this.apiService.eventCloseModal.next(true)
    this.dialog
      .open(AddOrEditContractAppendixComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
        this.apiService.eventCloseModal.next(false)
      })
  }
}
