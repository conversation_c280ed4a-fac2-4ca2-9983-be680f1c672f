<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Tiêu đề phụ lục</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập tiêu đề phụ lục ">
            <input nz-input placeholder="Nhập 1-250 kí tự" [(ngModel)]="dataObject.title" name="title" required
              pattern=".{1,250}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Đối tượng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn đối tượng">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn đối tượng" [(ngModel)]="dataObject.objectId"
              name="objectId" required disabled>
              <nz-option *ngFor="let item of lstObject" [nzLabel]="item.code" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Loại phụ lục</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn loại phụ lục ">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn loại phụ lục hợp đồng"
              [(ngModel)]="dataObject.type" name="type" required>
              <nz-option *ngFor="let item of dataContractTypeAppendix" [nzLabel]="item.name" [nzValue]="item.code">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Ngày bắt đầu phụ lục</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="">
            <nz-date-picker [nzFormat]="dateFormat" name="effectiveDate" placeholder="Ngày bắt đầu phụ lục"
              [(ngModel)]="dataObject.effectiveDate" [ngModelOptions]="{standalone: true}" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Ngày hết hạn phụ lục</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="">
            <nz-date-picker [nzFormat]="dateFormat" name="expiredDate" nzPlaceHolder="Ngày hết hạn phụ lục"
              [(ngModel)]="dataObject.expiredDate" [ngModelOptions]="{standalone: true}" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" [nzFor]="'fileAttach'">
            <span nz-tooltip nzTooltipPlacement="topLeft" [nzTooltipTitle]="' File đính kèm'">
              Hình ảnh phụ lục</span>
          </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập dữ liệu!">
            <nz-input-group nzSearch style="display: flex;">
              <input nz-input [(ngModel)]="dataObject.fileAttach" [ngModelOptions]="{standalone: true}"
                [attr.id]="'fileAttach'" disabled>
              <button type="button" nzType="primary" nz-button nzSearch (click)="handleClear('fileAttach')">
                <span nz-icon nzType="delete"></span>
              </button>
            </nz-input-group>
            <label for="choose-file-partner" class="custom-file-upload">
              Chọn File
            </label>
            <input name="uploadDocument-partner" type="file" id="choose-file-partner" class="fileAttach"
              style="display: none;" (change)="handleFileInput($event)" />
          </nz-form-control>
          <nz-col nzSpan="12" class="ml-3">
            <nz-form-item nzFlex>
              <div class="tooltip" *ngIf="dataObject.fileAttach">
                <button nz-button nzType="primary" class="mr-3">
                  <a class="btn" href="{{ dataObject.fileAttach }}" target="_blank">
                    <span nz-icon nzType="down-square"></span> Tải về
                  </a>
                </button>
              </div>
            </nz-form-item>
          </nz-col>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Ghi chú</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nội dung ghi chú">
            <textarea rows="4" nz-input [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()">Lưu</button>
    </nz-col>
  </nz-row>
</form>