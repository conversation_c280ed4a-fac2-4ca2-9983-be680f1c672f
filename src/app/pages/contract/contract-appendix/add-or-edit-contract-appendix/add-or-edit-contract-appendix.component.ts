import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { enumData } from '../../../../core/enumData'
import { ApiService, CoreService, NotifyService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-contract-appendix.component.html' })
export class AddOrEditContractAppendixComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  dateFormat = 'dd/MM/yyyy'
  modalTitle = ''
  uploading = false
  fileList: NzUploadFile[] = []
  fileToUpload!: File
  isChangeFile = false
  dataContractTypeAppendix = this.coreService.convertObjToArray(enumData.ContractTypeAppendix)
  typeMoney = enumData.ContractTypeAppendix.Money.code
  maxSizeUpload = enumData.maxSizeUpload
  lstObject: any[] = []
  constructor(
    private dialogRef: MatDialogRef<AddOrEditContractAppendixComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService
  ) {}

  ngOnInit() {
    this.dataObject = { ...this.data }
    if (this.data.id) {
      this.modalTitle = 'Chỉnh sửa phụ lục hợp đồng'
      this.isEditItem = true
    } else {
      this.modalTitle = 'Thêm mới phụ lục hợp đồng'
    }
    this.loadObject()
    this.dataObject.objectId = this.data.objectId
  }

  loadObject() {
    this.apiService.post(this.apiService.OBJECT.FIND, {}).then((result) => {
      this.lstObject = result || []
    })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.expiredDate < this.dataObject.effectiveDate) {
      this.notifyService.showError('Ngày hết hạn phụ lục phải lớn hơn ngày bắt đầu phụ lục')
      return
    }
    this.dataObject.fileAttach = this.dataObject.fileAttach || ''

    if (!this.dataObject.id) {
      this.addData()
      return
    }
    this.updateData()
  }

  addData() {
    this.apiService.post(this.apiService.CONTRACT_APPENDIX.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(1)
      }
    })
  }

  updateData() {
    this.apiService.post(this.apiService.CONTRACT_APPENDIX.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(1)
      }
    })
  }
  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  async handleFileInput(event: any) {
    const files = event.target.files
    const fileToUpload = files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) {
          this.dataObject.fileAttach = res[0]
        }
      })
    }
  }

  handleClear(colName: string) {
    let file: any = document.querySelector(`.${colName}`)
    file.value = null
    this.dataObject[colName] = null
  }
}
