import { Input } from '@angular/core'
import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../core/enumData'
import { ApiService } from '../../../services'

@Component({
  selector: 'app-contract-history',
  templateUrl: './contract-history.component.html',
})
export class ContractHistoryComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []

  @Input() contract: any
  constructor(private apiService: ApiService) {}
  ngOnInit() {}

  ngOnChanges(changes: any) {
    if (changes !== undefined) {
      if (changes.currentValue !== null) {
        if (changes.contract.currentValue !== undefined) {
          this.searchData()
        }
      }
    }
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1

    this.loading = true
    const dataSearch = {
      where: { contractId: this.contract.id },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.CONTRACT_HISTORY.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }
}
