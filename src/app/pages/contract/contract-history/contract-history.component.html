<nz-row nzGutter="8">
  <nz-col nzSpan="24">
    <nz-table class="mb-3" #ajaxTableHistory [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr class="text-nowrap">
          <th>Thời gian thực hiện</th>
          <th>Ngư<PERSON><PERSON> thực hiện</th>
          <th>Trạng thái cũ</th>
          <th>Trạng thái hiện tại</th>
          <th><PERSON><PERSON> hợp đồng</th>
          <th><PERSON>hi chú</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTableHistory.data">
          <td class="mw-25"> {{ data.createdAt | date: "dd/MM/yyyy HH:mm" }} </td>
          <td class="mw-25">{{ data.employeeName }}</td>
          <td class="text-nowrap"> {{ data.statusCurrentName }} </td>
          <td class="text-nowrap"> <b>{{ data.statusConvertName }}</b> </td>
          <td class="mw-25"> {{ data.contractCode }} </td>
          <td class="mw-25"> {{ data.description }} </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-col>
</nz-row>