import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../core/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService } from '../../../services'
import { ContractDetailComponent } from '../contract-detail/contract-detail.component'
import { PODetailComponent } from '../po/po-detail/po-detail.component'
import { AddOrEditInvoiceSuggestComponent } from './add-or-edit-invoice-suggest/add-or-edit-invoice-suggest.component'
import { InvoiceSuggestDetailComponent } from './invoice-suggest-detail/invoice-suggest-detail.component'

@Component({
  selector: 'app-invoice-suggest',
  templateUrl: './invoice-suggest.component.html',
})
export class InvoiceSuggestComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  listOfData: any[] = []
  dataContract: any[] = []
  dataPO: any[] = []
  enumData = enumData
  invoiceSuggestRole = enumData.Role.InvoiceSuggest
  currentUser: any
  listStatus: any[] = this.coreService.convertObjToArray(enumData.InvoiceSuggestStatus)
  data: any
  isVisibleDelete = false
  reasonDelete = ''
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.PAYMENT_001.code
    this.loadContract()
    this.loadPOPurchaseOrder()
    this.searchData(true)
  }

  searchData(reset = false) {
    this.notifyService.showloading()
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.contractId) {
      where.contractId = this.dataSearch.contractId
    }
    if (this.dataSearch.poId) {
      where.poId = this.dataSearch.poId
    }
    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }

    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService
      .post(this.apiService.INVOICE_SUGGEST.PAGINATION, dataSearch)
      .then(async (data) => {
        if (data) {
          this.loading = false
          this.total = data[1]
          this.listOfData = data[0]
        }
      })
      .finally(() => {
        this.notifyService.hideloading()
      })
  }

  loadContract() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      if (result) {
        this.dataContract = result
      }
    })
  }

  loadPOPurchaseOrder() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      if (result) {
        this.dataPO = result
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditInvoiceSuggestComponent, { disableClose: false })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditInvoiceSuggestComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  clickDelete(object: any) {
    this.data = object
    this.reasonDelete = ''
    this.isVisibleDelete = true
  }

  confirmDelete() {
    this.notifyService.showloading()
    if (this.reasonDelete == null || this.reasonDelete.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do trước')
      return
    }
    const param = {
      id: this.data.id,
      poId: this.data.poId,
      reason: this.reasonDelete,
    }
    this.apiService.post(this.apiService.INVOICE_SUGGEST.DELETE, param).then((result) => {
      if (result) {
        this.isVisibleDelete = false
        this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
        this.searchData()
      }
    })
  }

  clickViewPO(object: any) {
    if (!object.poId) return
    this.dialog.open(PODetailComponent, { disableClose: false, data: { id: object.poId } })
  }

  clickViewContract(object: any) {
    if (!object.contractId) return
    this.dialog.open(ContractDetailComponent, { disableClose: false, data: { id: object.contractId } })
  }

  clickViewDetail(object: any) {
    this.dialog.open(InvoiceSuggestDetailComponent, { disableClose: false, data: object })
  }
}
