<nz-tabset *ngIf="data" matDialogContent>
  <nz-tab nzTitle="Thông tin đề nghị thanh toán">
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired><PERSON><PERSON><PERSON> thức</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Ch<PERSON><PERSON> hì<PERSON> thức" [(ngModel)]="dataObject.type"
              name="type" required disabled>
              <nz-option *ngFor="let item of dataTypeContract" [nzLabel]="item.name" [nzValue]="item.code">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" *ngIf="dataObject.type === enumContracts">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Hợp đồng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.contractCode" name="contractCode" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" *ngIf="dataObject.type === enumNonContracts">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>PO</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.poCode" name="poCode" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Tiến độ thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.paymentPlanName" name="paymentPlanName" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nz-tooltip nzTooltipTitle="Lấy theo PO">Số tiền cần thanh toán
          </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.money" name="money" required pattern=".{1,250}"
              currencyMask [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Số hóa đơn</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled placeholder="Nhập Số hóa đơn" [(ngModel)]="dataObject.invoiceNo" name="invoiceNo"
              required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Chiết khấu</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled placeholder="Nhập Chiết khấu" [(ngModel)]="dataObject.discount" name="discount"
              currencyMask [options]="{ precision: 2, allowNegative: false, align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col> -->

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>
            Thời gian quyết toán
          </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-date-picker disabled nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.invoiceDate" name="invoiceDate">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Đơn vị hưởng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled placeholder="Nhập Đơn vị hưởng" [(ngModel)]="dataObject.beneficiaryUnit"
              name="beneficiaryUnit" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Ngân hàng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled placeholder="Nhập Ngân hàng" [(ngModel)]="dataObject.bankName" name="bankName"
              required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Số tài khoản</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled placeholder="Nhập Số tài khoản" [(ngModel)]="dataObject.bankAccountNo"
              name="bankAccountNo" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>File liên quan</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-upload nzListType="picture-card" [nzHeaders]="{ authorization: 'authorization-text' }"
              [(nzFileList)]="dataObject.fileList" [nzPreview]="handlePreview" [nzShowButton]="false"
              [nzShowUploadList]="{ showPreviewIcon: true, showRemoveIcon: false, showDownloadIcon: true }">
              <div>
                <span nz-icon nzType="plus"></span>
                <div style="margin-top: 8px">Upload</div>
              </div>
            </nz-upload>
            <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
              (nzOnCancel)="previewVisible = false">
              <ng-template #modalContent>
                <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
              </ng-template>
            </nz-modal>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Nội dung thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <textarea disabled rows="4" nz-input [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </nz-tab>
  <nz-tab nzTitle="Lịch sử thanh toán">
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstInvoice" nzBordered>
        <thead>
          <tr>
            <th>Người TT</th>
            <th>Thời gian TT</th>
            <th>Số tiền TT</th>
            <th>File chứng từ</th>
            <th>Ghi chú</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of dataObject.lstInvoice">
            <td class="mw-25">{{ data.employeeName }}</td>
            <td>{{ data.invoiceDate | date: 'dd/MM/yyyy' }}</td>
            <td>{{ data.money | number }}</td>
            <td>
              <nz-badge [nzCount]="data.numFile" [nzStyle]="{ backgroundColor: '#706a6a' }">
                <button (click)="viewFiles(data)" nz-tooltip nzTooltipTitle="File chứng từ thanh toán" nz-button>
                  <span nz-icon nzType="file-protect"></span>
                </button>
              </nz-badge>
            </td>
            <td>{{data.description}}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-tab>
  <nz-tab nzTitle="Lịch sử cập nhật ĐNTT">
    <nz-row class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="dataObject.lstHistory" nzBordered>
        <thead>
          <tr>
            <th>Người cập nhật</th>
            <th>Trạng thái</th>
            <th>Nội dung cập nhật</th>
            <th>Thời gian cập nhật</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of dataObject.lstHistory">
            <td class="mw-25">{{ data.employeeName }}</td>
            <td class="mw-25">{{ data.statusName }}</td>
            <td class="mw-25">{{ data.description }}</td>
            <td class="mw-25">{{ data.createdAt | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-tab>
</nz-tabset>

<nz-modal [(nzVisible)]="isVisibleViewFiles" nzTitle="File chứng từ thanh toán"
  (nzOnCancel)="isVisibleViewFiles = false" [nzWidth]="'60vw'" [nzFooter]="null">
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <nz-col nzSpan="24">
        <nz-upload nzListType="picture-card" [nzHeaders]="{ authorization: 'authorization-text' }"
          [(nzFileList)]="fileList" [nzPreview]="handlePreview" [nzShowButton]="false"
          [nzShowUploadList]="{ showPreviewIcon: true, showRemoveIcon: false, showDownloadIcon: true }">
          <div>
            <span nz-icon nzType="plus"></span>
            <div style="margin-top: 8px">Upload</div>
          </div>
        </nz-upload>
        <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
          (nzOnCancel)="previewVisible = false">
          <ng-template #modalContent>
            <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
          </ng-template>
        </nz-modal>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>