import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core/enumData'
import { ApiService, CoreService, NotifyService } from '../../../../services'

@Component({
  selector: 'app-invoice-suggest-detail',
  templateUrl: './invoice-suggest-detail.component.html',
})
export class InvoiceSuggestDetailComponent implements OnInit {
  dataObject: any = { lstInvoice: [], lstHistory: [] }
  dataTypeContract = this.coreService.convertObjToArray(enumData.ContractTypePo)
  enumContracts = enumData.ContractTypePo.Contract.code
  enumNonContracts = enumData.ContractTypePo.NonContract.code
  previewImage: string | undefined = ''
  previewVisible = false
  fileList: any
  isVisibleViewFiles = false

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private coreService: CoreService,
    private notifyService: NotifyService,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    if (this.data && this.data.id) {
      this.loadDetail()
    }
  }

  loadDetail() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.INVOICE_SUGGEST.FIND_DETAIL, { id: this.data.id }).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }

  viewFiles(object: any) {
    this.fileList = object.lstFile.map((c: any) => {
      return {
        uid: c.id,
        name: c.fileName,
        status: 'done',
        url: c.fileUrl,
      }
    })
    this.isVisibleViewFiles = true
  }

  handlePreview = async (file: any) => {
    const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
      new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
      })

    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }
}
