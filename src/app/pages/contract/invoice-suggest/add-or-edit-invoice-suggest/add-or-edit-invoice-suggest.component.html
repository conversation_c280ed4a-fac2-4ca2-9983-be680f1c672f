<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Ch<PERSON><PERSON> hình thức</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Chọn hình thức">
            <nz-select
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn hì<PERSON> thức"
              [(ngModel)]="dataObject.type"
              name="type"
              required
              (ngModelChange)="changePaymentType($event)"
              [disabled]="!!dataObject.id"
            >
              <nz-option *ngFor="let item of dataTypeContract" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" *ngIf="dataObject.type === enumContracts">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Chọn hợp đồng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn hợp đồng cần thanh toán">
            <nz-select
              *ngIf="!dataObject.id"
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn hợp đồng"
              [(ngModel)]="dataObject.contractId"
              name="contractId"
              required
              (ngModelChange)="changeContract($event)"
            >
              <nz-option *ngFor="let item of dataContract" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
            <input *ngIf="!!dataObject.id" nz-input disabled [(ngModel)]="dataObject.contractCode" name="contractCode" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" *ngIf="dataObject.type === enumNonContracts">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Chọn PO</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn PO ">
            <nz-select
              *ngIf="!dataObject.id"
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn PO cần thanh toán"
              [(ngModel)]="dataObject.poId"
              name="poId"
              required
              (ngModelChange)="changePO($event)"
            >
              <nz-option *ngFor="let item of dataPO" [nzLabel]="item.code + ' - ' + item.title" [nzValue]="item.id"> </nz-option>
            </nz-select>
            <input *ngIf="!!dataObject.id" nz-input disabled [(ngModel)]="dataObject.poCode" name="poCode" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Chọn tiến độ thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn tiến độ thanh toán ">
            <nz-select
              *ngIf="!dataObject.id"
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn tiến độ thanh toán"
              [(ngModel)]="dataObject.paymentPlanId"
              name="paymentPlanId"
              required
              (ngModelChange)="changePaymentPlan($event)"
            >
              <nz-option *ngFor="let item of dataPaymentPlan" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
            <input *ngIf="!!dataObject.id" nz-input disabled [(ngModel)]="dataObject.paymentPlanName" name="paymentPlanName" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nz-tooltip nzTooltipTitle="Lấy theo tiến độ thanh toán"> Số tiền cần thanh toán </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.poMoney" name="poMoney" required currencyMask [options]="{ align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nz-tooltip nzTooltipTitle="Lấy theo tiến độ thanh toán"> Số tiền đã ĐNTT </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.suggestPaid" name="suggestPaid" required currencyMask [options]="{ align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired> Số tiền ĐNTT </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input
              nz-input
              [(ngModel)]="dataObject.money"
              name="money"
              required
              pattern=".{1,250}"
              currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
            />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Số hóa đơn</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Số hóa đơn">
            <input nz-input placeholder="Nhập Số hóa đơn" [(ngModel)]="dataObject.invoiceNo" name="invoiceNo" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Chiết khấu</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input placeholder="Nhập Chiết khấu" [(ngModel)]="dataObject.discount" name="discount" currencyMask
              [options]="{ precision: 2, allowNegative: false, align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col> -->

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired> Thời gian quyết toán </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn Thời gian quyết toán">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.invoiceDate" name="invoiceDate" required> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Đơn vị hưởng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Đơn vị hưởng">
            <input disabled nz-input placeholder="Nhập Đơn vị hưởng" [(ngModel)]="dataObject.beneficiaryUnit" name="beneficiaryUnit" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Ngân hàng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Ngân hàng">
            <input nz-input placeholder="Nhập Ngân hàng" [(ngModel)]="dataObject.bankName" name="bankName" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Số tài khoản</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Số tài khoản">
            <input nz-input placeholder="Nhập Số tài khoản" [(ngModel)]="dataObject.bankAccountNo" name="bankAccountNo" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>File liên quan</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-upload
              [nzAction]="apiService.UploadUrl"
              nzListType="picture-card"
              [nzHeaders]="{ authorization: 'authorization-text' }"
              [(nzFileList)]="dataObject.fileList"
              [nzPreview]="handlePreview"
              (nzChange)="onChangeImage($event)"
            >
              <div>
                <span nz-icon nzType="plus"></span>
                <div style="margin-top: 8px">Upload</div>
              </div>
            </nz-upload>
            <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null" (nzOnCancel)="previewVisible = false">
              <ng-template #modalContent>
                <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
              </ng-template>
            </nz-modal>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Nội dung thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nội dung thanh toán">
            <textarea rows="4" nz-input [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button *ngIf="!dataObject.id" [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()" class="mr-2">
        Tạo đề nghị thanh toán
      </button>
      <button nz-button *ngIf="dataObject.id" [disabled]="!frmAdd.form.valid" nzType="primary" class="mr-2" (click)="onSave()">
        Lưu thông tin đề nghị thanh toán
      </button>
      <button *ngIf="dataObject.status === enumData.InvoiceSuggestStatus.Unpaid.code" (click)="onDelete()" nz-button nzType="primary" nzDanger>
        Xóa đề nghị thanh toán
      </button>
    </nz-col>
  </nz-row>
</form>

<nz-modal
  [(nzVisible)]="isVisibleDelete"
  nzTitle="Nhập lý do xóa đề nghị thanh toán"
  (nzOnCancel)="isVisibleDelete = false"
  nzWidth="60vw"
  [nzFooter]="null"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <h4>Lý do:</h4>
      <nz-col nzSpan="24">
        <textarea nz-input rows="5" auto placeholder="Nhập nội dung gửi" [(ngModel)]="reasonDelete"></textarea>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button
          nz-popconfirm
          nzPopconfirmTitle="Bạn có chắc muốn xóa đề nghị thanh toán?"
          nzPopconfirmPlacement="top"
          (nzOnConfirm)="confirmDelete()"
          nz-button
          class="ant-btn-blue"
        >
          Xác nhận xóa
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>
