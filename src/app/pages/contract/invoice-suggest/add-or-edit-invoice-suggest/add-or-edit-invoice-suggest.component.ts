import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { enumData } from '../../../../core/enumData'
import { ApiService, CoreService, NotifyService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-invoice-suggest.component.html' })
export class AddOrEditInvoiceSuggestComponent implements OnInit {
  dataObject: any = {}
  isEditItem = false
  dateFormat = 'yyyy/MM/dd'
  modalTitle = ''
  uploading = false
  fileList: NzUploadFile[] = []
  isChangeFile = false
  dataPO: any[] = []
  dataContract: any[] = []
  maxSizeUpload = enumData.maxSizeUpload
  dataTypeContract = this.coreService.convertObjToArray(enumData.ContractTypePo)
  enumContracts = enumData.ContractTypePo.Contract.code
  enumNonContracts = enumData.ContractTypePo.NonContract.code
  previewImage: string | undefined = ''
  previewVisible = false
  isVisibleDelete = false
  reasonDelete = ''
  enumData = enumData
  dataPaymentPlan: any[] = []
  isLoadPO = false
  isLoadContract = false

  constructor(
    private dialogRef: MatDialogRef<AddOrEditInvoiceSuggestComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private notifyService: NotifyService,
    public apiService: ApiService,
    private coreService: CoreService
  ) {}

  ngOnInit() {
    this.modalTitle = 'Thêm mới Đề nghị thanh toán'
    if (this.data && this.data.id) {
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sửa thông tin Đề nghị thanh toán'
      this.dataObject = { ...this.data }
      const lstFile = this.dataObject.__files__ || []
      this.dataObject.fileList = lstFile.map((c: any) => {
        return {
          uid: c.id,
          name: c.fileName,
          status: 'done',
          url: c.fileUrl,
        }
      })
    }
  }

  //#region Xử lý Form
  changePaymentType(event: any) {
    this.dataObject.poId = null
    this.dataObject.contractId = null
    this.dataObject.paymentPlanId = null
    if (event === this.enumNonContracts) {
      this.loadPO()
    } else if (event == this.enumContracts) {
      this.loadContract()
    }
  }

  loadContract() {
    if (this.isLoadContract) return
    this.dataContract = []
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.isLoadContract = true
      this.dataContract = result || []
      if (this.dataObject.contractId) this.loadPaymentPlan({ contractId: this.dataObject.contractId })
    })
  }
  changeContract(contractId: any) {
    this.dataObject.poId = null
    this.dataObject.paymentPlanId = null
    this.dataPaymentPlan = []

    this.dataObject.money = null
    this.dataObject.poMoney = null
    this.dataObject.suggestPaid = null
    this.dataObject.supplierId = null
    this.dataObject.beneficiaryUnit = null
    this.dataObject.bankName = null
    this.dataObject.bankAccountNo = null
    if (!contractId) return

    const contract = this.dataContract.find((c) => c.id == contractId)
    if (contract) {
      this.dataObject.supplierId = contract.supplierId
      this.dataObject.beneficiaryUnit = contract.supplierName
      this.dataObject.bankName = contract.supplierBankName
      this.dataObject.bankAccountNo = contract.supplierBankNumber
    }
    this.loadPaymentPlan({ contractId })
  }

  loadPO() {
    if (this.isLoadPO) return
    this.dataPO = []
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.FIND, { contractId: 'isNull' }).then((result) => {
      this.notifyService.hideloading()
      this.isLoadPO = true
      this.dataPO = result || []
      if (this.dataObject.poId) this.loadPaymentPlan({ poId: this.dataObject.poId })
    })
  }
  /** Số tiền cần thanh toán: Lấy theo PO */
  changePO(poId: any) {
    this.dataObject.contractId = null
    this.dataObject.money = null
    this.dataObject.poMoney = null
    this.dataObject.suggestPaid = null
    this.dataObject.supplierId = null
    this.dataObject.beneficiaryUnit = null
    this.dataObject.bankName = null
    this.dataObject.bankAccountNo = null
    this.dataObject.paymentPlanId = null
    this.dataPaymentPlan = []
    if (!poId) return
    const po = this.dataPO.find((c) => c.id == poId)
    if (po) {
      this.dataObject.supplierId = po.supplierId
      this.dataObject.beneficiaryUnit = po.supplierName
      this.dataObject.bankName = po.supplierBankName
      this.dataObject.bankAccountNo = po.supplierBankNumber
    }
    this.loadPaymentPlan({ poId })
  }

  loadPaymentPlan(param: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT_PROGRESS.FIND, param).then((result) => {
      this.notifyService.hideloading()
      this.dataPaymentPlan = result || []
    })
  }

  changePaymentPlan(paymentPlanId: any) {
    this.dataObject.money = null
    this.dataObject.poMoney = null
    this.dataObject.suggestPaid = null
    if (!paymentPlanId) return
    const paymentPlan = this.dataPaymentPlan.find((c) => c.id == paymentPlanId)
    if (paymentPlan) {
      this.dataObject.poMoney = +paymentPlan.money
      this.dataObject.suggestPaid = +paymentPlan.suggestPaid
    }
  }

  handlePreview = async (file: any) => {
    const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
      new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
      })

    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  onChangeImage(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          // const img = {
          //   imgName: info.file.name,
          //   imgUrl: info.file.response[0],
          // }

          // update url to download img
          info.file.url = info.file.response[0]
          const file = info.fileList.find((c) => c.uid == info.file.uid)
          if (file) file.url = info.file.response[0]

          // call api add img
          // this.apiService.post(this.apiService.MATERIAL.ADD_IMAGE, img)
        }

        break
      case 'error':
        break
      case 'removed':
        {
          // const img = {
          //   imgName: info.file.name,
          //   imgUrl: info.file.url,
          // }
          // call api remove img
          // this.apiService.post(this.apiService.MATERIAL.REMOVE_IMAGE, img)
        }

        break
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (!this.dataObject.fileList || this.dataObject.fileList.length == 0) {
      this.notifyService.showError('Vui lòng upload các file liên quan trước!')
      return
    }

    if (!this.isEditItem) {
      if (this.dataObject.money > this.dataObject.poMoney - this.dataObject.suggestPaid) {
        this.notifyService.showError('[Số tiền ĐNTT] và [Số tiền đã ĐNTT] không được vượt [Số tiền cần thanh toán của PO]!')
        return
      }
    }

    if (!(this.dataObject.invoiceDate)) {
      this.notifyService.showError(`Vui lòng chọn hợp thời gian quyết toán`)
      return
    }

    if (!this.isEditItem) {
      this.apiService.post(this.apiService.INVOICE_SUGGEST.CREATE, this.dataObject).then((result) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(1)
      })
    } else {
      this.apiService.post(this.apiService.INVOICE_SUGGEST.UPDATE, this.dataObject).then((result) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(1)
      })
    }
  }
  //#endregion

  //#region Delete
  onDelete() {
    this.reasonDelete = ''
    this.isVisibleDelete = true
  }

  confirmDelete() {
    this.notifyService.showloading()
    if (this.reasonDelete == null || this.reasonDelete.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do trước')
      return
    }
    const param = {
      id: this.dataObject.id,
      reason: this.reasonDelete,
    }
    this.apiService.post(this.apiService.INVOICE_SUGGEST.DELETE, param).then((result) => {
      this.isVisibleDelete = false
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.closeDialog(1)
    })
  }
  //#endregion

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
