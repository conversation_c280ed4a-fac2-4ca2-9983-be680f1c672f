<nz-row nzGutter="8">
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn hợp đồng" [(ngModel)]="dataSearch.contractId" name="contractId">
      <nz-option *ngFor="let item of dataContract" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn PO" [(ngModel)]="dataSearch.poId" name="poId">
      <nz-option *ngFor="let item of dataPO" [nzLabel]="item.code" [nzValue]="item.id"> </nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Trạng thái">
      <nz-option *ngFor="let item of listStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <button nz-button (click)="searchData(true)"><span nz-icon nzType="search"></span>Tìm kiếm</button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button *ngIf="authenticationService.checkPermission([enumRole], action.Create.code)" nz-button nzType="primary" (click)="clickAdd()">
      <span nz-icon nzType="plus"></span> Tạo mới
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    [nzScroll]="{ x: '2000px' }"
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th nzWidth="250px" nzLeft>Mã ĐNTT</th>
        <th>Mã PO</th>
        <th>Mã hợp đồng</th>
        <th>Tiến độ thanh toán</th>
        <th>Số tiền cần TT</th>
        <th>Thời gian quyết toán</th>
        <th>Số hóa đơn</th>
        <th>Nội dung thanh toán</th>
        <th>Trạng thái thanh toán</th>
        <th nzWidth="250px" nzRight>Tùy chọn</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td nzLeft nzWidth="250px">
          <a (click)="clickViewDetail(data)" style="cursor: pointer">
            {{ data.code }}
          </a>
        </td>
        <td>
          <a (click)="clickViewPO(data)" style="cursor: pointer" *ngIf="data.poId">
            {{ data.poCode }}
          </a>
        </td>
        <td>
          <a (click)="clickViewContract(data)" style="cursor: pointer" *ngIf="data.contractId">
            {{ data.contractCode }}
          </a>
        </td>
        <td>{{ data.paymentPlanName }}</td>
        <td>{{ data.money | number }}</td>
        <td>{{ data.invoiceDate | date : 'dd/MM/yyyy' }}</td>
        <td>{{ data.invoiceNo }}</td>
        <td>{{ data.description }}</td>
        <td>{{ data.statusName }}</td>
        <td nzRight nzWidth="250px">
          <button
            *ngIf="authenticationService.checkPermission([enumRole], action.Update.code) && data.statusName !== 'Đã hoàn thành'"
            nz-tooltip
            nzTooltipTitle="Chỉnh sửa thông tin đề nghị thanh toán"
            (click)="clickEdit(data)"
            class="mr-2"
            nz-button
            nzType="primary"
          >
            <span nz-icon nzType="form"></span>
          </button>
          <button
            *ngIf="data.status === enumData.InvoiceSuggestStatus.Unpaid.code && authenticationService.checkPermission([enumRole], action.Delete.code)"
            nz-tooltip
            nzTooltipTitle="Xóa đề nghị thanh toán"
            (click)="clickDelete(data)"
            nz-button
            nzType="primary"
            nzDanger
          >
            <span nz-icon nzType="delete"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-row>

<nz-modal
  [(nzVisible)]="isVisibleDelete"
  nzTitle="Nhập lý do xóa đề nghị thanh toán"
  (nzOnCancel)="isVisibleDelete = false"
  [nzWidth]="'60vw'"
  [nzFooter]="null"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-2">
      <h4>Lý do:</h4>
      <nz-col nzSpan="24">
        <textarea nz-input rows="5" auto placeholder="Nhập nội dung gửi" [(ngModel)]="reasonDelete"></textarea>
      </nz-col>
    </nz-row>
    <nz-row class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button
          nz-popconfirm
          nzPopconfirmTitle="Bạn có chắc muốn xóa đề nghị thanh toán?"
          nzPopconfirmPlacement="top"
          (nzOnConfirm)="confirmDelete()"
          nz-button
          class="ant-btn-blue"
        >
          Xác nhận xóa
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>
