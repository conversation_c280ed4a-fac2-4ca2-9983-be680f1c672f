<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <!-- File Hóa đơn -->
      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzFor="fileXml" nzRequired class="text-left">File hóa đơn - File XML</nz-form-label>
          <nz-form-control nzSpan="24" [nzErrorTip]="'Vui lòng upload file file hóa đơn'">
            <label for="fileXml" class="custom-file-upload">
              <span nz-icon nzType="upload"></span> {{ language_key?.SupplierRegistration_UploadFile || 'Upload File' }}
            </label>
            <input
              [disabled]="isEditItem"
              class="hidden"
              type="file"
              id="fileXml"
              accept=".xml"
              [(ngModel)]="dataObject.fileXml"
              name="fileXml"
              (change)="handleFileInput($event, 'fileXml')"
            />
            <div class="tooltip" *ngIf="dataObject.fileXml && dataObject.fileXml.length > 0">
              <a href="{{ dataObject.fileXml }}" target="_blank"> {{ language_key?.SupplierRegistration_ViewFile || 'Xem file' }} </a>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzFor="fileAttach" nzRequired class="text-left">File hóa đơn - File PDF</nz-form-label>
          <nz-form-control nzSpan="24" [nzErrorTip]="'Vui lòng upload file file hóa đơn'">
            <label for="fileAttach" class="custom-file-upload">
              <span nz-icon nzType="upload"></span> {{ language_key?.SupplierRegistration_UploadFile || 'Upload File' }}
            </label>
            <input
              class="hidden"
              type="file"
              id="fileAttach"
              accept=".pdf"
              [(ngModel)]="dataObject.fileAttach"
              name="fileAttach"
              (change)="handleFileInput($event, 'fileAttach')"
            />
            <div class="tooltip" *ngIf="dataObject.fileAttach && dataObject.fileAttach.length > 0">
              <a href="{{ dataObject.fileAttach }}" target="_blank"> {{ language_key?.SupplierRegistration_ViewFile || 'Xem file' }} </a>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired> {{ language_key?.Bill_Lookup_Code || 'Mã tra cứu hóa đơn' }} </nz-form-label>
          <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Error_Bill_Lookup_Code || 'Vui lòng nhập mã tra cứu hóa đơn!'">
            <input nz-input [(ngModel)]="dataObject.billLookupCode" name="billLookupCode " pattern=".{1,50}" autocomplete="off" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Website tra cứu</nz-form-label>
          <nz-form-control nzSpan="24" [nzErrorTip]="'Vui lòng chọn Website tra cứu!'">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.billLookupId"
              name="billLookupId"
              nzPlaceHolder=""
              [nzPlaceHolder]="'Website tra cứu'"
              required
            >
              <nz-option *ngFor="let item of lstLinkLookupBill" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ 'Đường link website' }}</nz-form-label>
          <nz-form-control nzSpan="24" [nzErrorTip]="'Vui lòng chọn đường link website!'">
            <nz-select
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.billLookupId"
              name="link"
              [nzPlaceHolder]="'Đường link website tra cứu'"
              required
              disabled
            >
              <nz-option *ngFor="let item of lstLinkLookupBill" [nzLabel]="item.link" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex class="d-flex">
          <nz-form-label class="text-left mr-4" nzRequired>Xuất hóa đơn cho</nz-form-label>
          <nz-radio-group
            (ngModelChange)="onChangeReferencesInvoice()"
            class="mt-1"
            [(ngModel)]="dataObject.referencesInvoice"
            name="referencesInvoice"
          >
            <label nz-radio nzValue="C">Theo hợp đồng</label>
            <label nz-radio nzValue="P">Theo PO</label>
          </nz-radio-group>
        </nz-form-item>
      </nz-col>

      <!-- Po -->
      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Chọn PO</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn PO!">
            <nz-select
              [disabled]="!dataObject.referencesInvoice || (dataObject.referencesInvoice === referencesInvoice.C.code && !dataObject.contractId)"
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.poId"
              name="poId"
              nzPlaceHolder="Chọn PO"
              (ngModelChange)="onchangePo($event)"
            >
              <nz-option *ngFor="let item of lstPO" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <!-- Hợp đồng -->
      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn hợp đồng !">
            <nz-select
              [disabled]="!dataObject.referencesInvoice || dataObject.referencesInvoice === referencesInvoice.P.code"
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.contractId"
              name="contractId"
              nzPlaceHolder="Chọn hợp đồng "
              (ngModelChange)="onchangeContract($event)"
            >
              <nz-option *ngFor="let item of lstContract" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- Tên PO -->
      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tên PO</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select disabled nzShowSearch nzAllowClear [(ngModel)]="dataObject.poId" name="poName">
              <nz-option *ngFor="let item of lstPO" [nzLabel]="item.title" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- Tên hợp đồng -->
      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tên hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select disabled nzShowSearch nzAllowClear [(ngModel)]="dataObject.contractId" name="contractName">
              <nz-option *ngFor="let item of lstContract" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Chọn công ty thêm hóa đơn từ hệ thống Bizzi</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn PO!">
            <nz-select
              [disabled]="isEditItem"
              nzShowSearch
              nzAllowClear
              [(ngModel)]="dataObject.bizziCompanyId"
              name="bizziCompanyId"
              nzPlaceHolder="Chọn công ty"
              required
            >
              <nz-option *ngFor="let item of lstCompany" [nzLabel]="item.name" [nzValue]="item.company_id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- Ghi chú -->
      <nz-col [nzSpan]="24">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Ghi chú</nz-form-label>
          <nz-form-control nzSpan="24">
            <textarea nz-input placeholder="Nhập ghi chú" [(ngModel)]="dataObject.description" name="description" rows="2"> </textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>

    <nz-collapse>
      <nz-collapse-panel
        *ngIf="dataObject.referencesInvoice === referencesInvoice.P.code"
        nzHeader="Danh sách hàng hóa"
        class="ant-bg-antiquewhite"
        [nzActive]="true"
      >
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstProduct.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã vật tư</th>
                <th>Vật tư</th>
                <th>Tên hàng hóa</th>
                <th>Mô tả hàng hóa</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Thành tiền</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstProduct; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.itemCode }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.price | number }}</td>
                <td>{{ data.money | number }}</td>
                <td>{{ data.note }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit"><span nz-icon nzType="close"></span>Thoát</button>

      <button
        class="mr-4 button-save"
        nz-button
        [disabled]="!frmAdd.form.valid && !dataObject.fileAttach && !dataObject.fileXml"
        nzType="primary"
        (click)="onSave()"
      >
        <span nz-icon nzType="save"></span>Lưu
      </button>
    </nz-col>
  </nz-row>
</form>

<form nz-form #frmAdd="ngForm"></form>
