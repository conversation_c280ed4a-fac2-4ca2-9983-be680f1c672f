import { Location } from '@angular/common'
import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload'
import { Observable, Observer } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, CoreService, NotifyService } from 'src/app/services'

@Component({
  selector: 'app-add-or-edit-bill',
  templateUrl: './add-or-edit-bill.component.html',
  styleUrls: ['./add-or-edit-bill.component.scss'],
})
export class AddOrEditBillComponent {
  dataObject: any = {}
  isEditItem = false
  maxSizeUpload = enumData.maxSizeUpload
  lstPO: any = []
  lstContract: any = []
  lstCurrency: any = []
  id: any
  modalTitle = 'Tạo mới hóa đơn'
  uploadUrl: string = this.apiService.UploadUrl
  referencesInvoice = enumData.referencesInvoice
  lstCompany: any[] = []
  lstLinkLookupBill: any = []
  constructor(
    private dialogRef: MatDialogRef<AddOrEditBillComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private apiService: ApiService,
    private notifyService: NotifyService,
    private router: Router,
    public coreService: CoreService,
    private activatedRoute: ActivatedRoute,
    private location: Location
  ) {}
  language_key: any
  async ngOnInit() {
    await this.loadAllDataSelect()

    this.loadCompany()
    this.loadBillLookup()
    if (this.data && this.data.id) {
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sửa hóa đơn'
      this.onLoadDetail(this.data.id)
    }
  }

  async loadCompany() {
    this.apiService.get(this.apiService.BILL.GET_COMPANY, {}).then((data) => {
      if (data) {
        this.lstCompany = data.listCompany
      }
    })
  }

  async loadBillLookup() {
    this.apiService.post(this.apiService.BILL_LOOKUP.FIND, {}).then((data) => {
      if (data) {
        this.lstLinkLookupBill = data
      }
    })
  }

  onLoadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BILL.FIND_DETAIL, { id: id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res

        this.loadPOContract(res.contractId)
      }
    })
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.PO.FIND, {}),
      this.apiService.post(this.apiService.CONTRACT.FIND, {}),
      // this.apiService.post(this.apiService.BILL_LOOKUP.FIND, {}),
      // this.apiService.get(this.apiService.BILL.GET_COMPANY, {}),
    ]).then(async (res) => {
      this.lstPO = res[0]
      this.lstContract = res[1]
      // this.lstLinkLookupBill = res[2]
      // this.lstCompany = res[3]
    })
    this.notifyService.hideloading()
  }

  handleFileInput(event: any, fieldName: string) {
    const inputElement = event.target as HTMLInputElement
    const fileToUpload = inputElement?.files?.[0]

    if (fileToUpload && fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(
        `${this.language_key?.SupplierRegistration_MaximumSizeToUpload || 'Kích thước tối đa để upload là'} ${this.maxSizeUpload}MB, ${
          this.language_key?.SupplierRegistration_ChooseAnotherFile || 'vui lòng chọn file khác'
        }`
      )
      return
    }
    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }

  closeDialog() {
    this.dialogRef.close()
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.BILL.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.BILL.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      if (file.size! > enumData.maxSizeUpload * 1024 * 1024) {
        this.notifyService.showWarning('Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.')
        observer.complete()
        return
      }
      observer.next(true)
      observer.complete()
    })

  handleChange1(event: NzUploadChangeParam): void {
    // Lấy file cuối cùng từ danh sách file (chỉ giữ 1 file)
    const fileList = event.fileList.slice(-1)

    // Cập nhật vào `dataObject.fileXml`
    this.dataObject.fileXml = fileList

    // Debug (Kiểm tra xem file XML có được lưu hay không)
  }

  handleChange(info: any): void {
    if (info.file.status !== 'uploading') {
    }
    if (info.file.status === 'done') {
      ;(this.dataObject.fileList = info.fileList.map((file: any) => ({
        uid: file.uid,
        name: file.name ?? file.fileName,
        fileUrl: file ? file.response[0] : file.response[0],
        fileName: file.name ?? file.fileName,
        dataType: file.type ?? file.dataType,
      }))),
        this.notifyService.showSuccess(`${info.file.name} file uploaded successfully`)
    } else if (info.file.status === 'removed') {
      // Handle file removal
      this.dataObject.fileList = info.fileList.map((file: any) => ({
        uid: file.uid,
        name: file.name ?? file.fileName,
        fileUrl: file ? file.response[0] : file.response[0],
        fileName: file.name ?? file.fileName,
        dataType: file.type ?? file.dataType,
      }))
    } else if (info.file.status === 'error') {
      this.notifyService.showError(`${info.file.name} file upload failed.`)
    }
  }

  goBack(): void {
    this.dialogRef.close()
  }

  async onchangePo(poId: string) {
    if (this.dataObject.referencesInvoice === enumData.referencesInvoice.P.code) {
      this.dataObject.lstProduct = []
      this.dataObject.contractId = null
      this.dataObject.poName = null
    }

    if (!poId) return
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.PO.LOAD_PO_PRODUCT, { poId: poId }),
      this.apiService.post(this.apiService.PO.FIND_CONTRACT_PO, { poId: poId }),
    ]).then(async (res) => {
      if (this.dataObject.referencesInvoice === enumData.referencesInvoice.P.code) {
        this.dataObject.lstProduct = res[0]
        this.dataObject.contractId = res[1].contractId
      }
    })
    this.notifyService.hideloading()
  }

  onChangeReferencesInvoice() {
    this.dataObject.contractId = null
    this.dataObject.poId = null
    this.dataObject.lstProduct = []
    this.loadAllDataSelect()
  }

  loadPOContract(contractId: string) {
    this.apiService.post(this.apiService.PO.FIND_LIST_PO_CONTRACT, { contractId: contractId }).then((res) => {
      if (res) {
        this.lstPO = res
      }
    })
  }

  addObject1() {
    this.apiService.post(this.apiService.BILL.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }
  onchangeContract(contractId: string) {
    if (!contractId) return

    if (this.dataObject.referencesInvoice === enumData.referencesInvoice.C.code) {
      this.dataObject.lstProduct = []
      this.dataObject.poId = null
    }
    this.notifyService.showloading()

    this.apiService.post(this.apiService.PO.FIND_LIST_PO_CONTRACT, { contractId: contractId }).then((res) => {
      // Danh sách po của contract Đã chọn
      if (res) {
        this.notifyService.hideloading()
        this.lstPO = res
      }
    })
  }
}
