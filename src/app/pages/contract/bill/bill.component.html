<nz-collapse>
  <nz-collapse-panel [nzHeader]="language_key?.SEARCH_ADVANCED || 'Tìm kiếm'">
    <nz-row class="mt-3" nzGutter="8">
      <nz-col nzSpan="8">
        <input nz-input [(ngModel)]="dataSearch.code" name="code" [placeholder]="'Tìm theo mã hóa đơn'" class="input-enter" />
      </nz-col>

      <nz-col nzSpan="8">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" [nzPlaceHolder]="'Chọn trạng thái'">
          <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.paymentStatus"
          name="paymentStatus"
          [nzPlaceHolder]="'Chọn trạng thái thanh toán'"
        >
          <nz-option *ngFor="let item of dataPaymentStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>

    <nz-row class="mt-4">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.PurchaseOrder_Search || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" (click)="clickAdd()"><span nz-icon nzType="plus"></span> Tạo mới</button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    [nzScroll]="{ x: '2000px' }"
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th nzWidth="120px" nzLeft>Mã hóa đơn</th>
        <th>Hợp đồng</th>
        <th>PO</th>
        <th>Trị giá</th>
        <th>Đơn vị tiền tệ</th>
        <th>Thuế VAT</th>
        <th>Tổng trị giá</th>
        <th>Trạng thái</th>
        <!-- <th>Trạng thái thanh toán</th> -->
        <th nzWidth="150px" nzRight>{{ language_key?.PurchaseOrder_Action || 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData">
        <td nzWidth="120px" nzLeft>{{ data.code }}</td>
        <td>{{ data.contractName }}</td>
        <td>{{ data.poCode }}</td>
        <td class="text-right">{{ data.invoiceValue | number }}</td>
        <td>{{ data.currencyName }}</td>
        <td>{{ data.vat }}</td>
        <td class="text-right">{{ data.totalInvoiceValue | number }}</td>
        <td class="mw-25">
          <nz-tag *ngIf="data.statusName" [nzColor]="data.statusColor" style="width: 180px; font-weight: 600; border-radius: 30px">
            <div style="display: flex; align-items: center; justify-content: center">
              <div class="dot"></div>
              <span class="ml-1"> {{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>
        <!-- <td class="mw-25">
          <nz-tag *ngIf="data.paymentStatusName" [nzColor]="data.paymentStatusColor" style="width: 180px; font-weight: 600; border-radius: 30px">
            <div style="display: flex; align-items: center; justify-content: center">
              <div class="dot"></div>
              <span class="ml-1"> {{ data.paymentStatusName }}</span>
            </div>
          </nz-tag>
        </td> -->

        <td nzWidth="150px" nzRight class="text-center">
          <button nz-tooltip (click)="showDetail(data)" [nzTooltipTitle]="'Xem chi tiết'" class="btn-primary mb-2 mt-2 mr-2" nz-button>
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code"
            nz-tooltip
            (click)="clickEdit(data)"
            [nzTooltipTitle]="'Cập nhật'"
            nzShape="circle"
            class="mr-2"
            nz-button
            nzTooltipPlacement="top"
          >
            <i nz-icon class="text-icon" nzType="edit"></i>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code"
            (nzOnConfirm)="onSendBill(data)"
            nz-tooltip
            [nzTooltipTitle]="'Gửi hóa đơn'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn gửi hóa đơn  ?"
            nzPopconfirmPlacement="bottom"
            nzShape="circle"
            class="mr-2"
          >
            <i nz-icon nzType="check" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.WAIT_CONFIRM.code"
            (nzOnConfirm)="onConfirm(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="'Xác nhận'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn xác nhận ?"
            nzPopconfirmPlacement="bottom"
            class="mr-2"
          >
            <i nz-icon nzType="send" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code"
            (nzOnConfirm)="onDelete(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="language_key?.DELETE || 'Hủy Hóa Đơn'"
            nz-button
            nzDanger
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn Hủy hóa đơn  ?"
            nzPopconfirmPlacement="bottom"
            class="mr-2"
          >
            <i nz-icon nzType="delete" class="text-icon"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
