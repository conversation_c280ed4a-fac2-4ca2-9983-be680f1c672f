import { Component, OnInit, Input, Optional, Inject } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { ApiService, NotifyService } from 'src/app/services'
@Component({
  selector: 'app-contract-child-detail',
  templateUrl: './contract-child-detail.component.html',
  styleUrls: ['./contract-child-detail.component.scss'],
})
export class ContractChildDetailComponent implements OnInit {
  dataObject: any = {}
  modalTitle = 'Chi Tiết Hợp Đồng'
  @Input()
  public contract: any

  constructor(
    private apiService: ApiService,
    private activatedRoute: ActivatedRoute,
    private notifyService: NotifyService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.activatedRoute.paramMap.subscribe((params) => {
      let data = params.get('id')
      if (data) {
        // this.isShowTimeline = !this.isShowTimeline
        this.data = { id: data }
      }
      //  else {
      //   this.isShowTimeline = this.isShowTimeline
      // }
    })
    if (this.contract?.id) {
      this.loadDetail(this.contract?.id)
    } else if (this.data?.id) {
      this.loadDetail(this.data?.id)
    }
  }

  loadDetail(id: string) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.DETAIL, { id }).then((res) => {
      this.dataObject = res
      this.notifyService.hideloading()
    })
  }
}
