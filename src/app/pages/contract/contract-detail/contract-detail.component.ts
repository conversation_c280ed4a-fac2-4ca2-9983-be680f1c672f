import { Component, OnInit, Input, Optional, Inject } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiService, NotifyService } from '../../../services'
import { Router } from '@angular/router'
@Component({
  selector: 'app-contract-detail',
  templateUrl: './contract-detail.component.html',
})
export class ContractDetailComponent implements OnInit {
  dataObject: any = {}
  modalTitle = 'Chi Tiết Hợp Đồng'
  @Input()
  public contract: any

  constructor(
    private apiService: ApiService,
    private router: Router,
    private notifyService: NotifyService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    if (this.contract?.id) {
      this.loadDetail(this.contract?.id)
    } else if (this.data?.id) {
      this.loadDetail(this.data?.id)
    }
  }
  viewDetail(data: any) {
    const url = this.router.serializeUrl(this.router.createUrlTree(['/contract/detail-contract-child']))
    window.open(`${url};id=${data.id}`, '_blank')
  }

  loadDetail(id: string) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.DETAIL, { id }).then((res) => {
      this.dataObject = res
      this.notifyService.hideloading()
    })
  }
}
