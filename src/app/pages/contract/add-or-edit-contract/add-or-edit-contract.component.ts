import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiService, NotifyService } from '../../../services'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { Observable, Observer } from 'rxjs'
import { enumData } from '../../../core/enumData'
@Component({ templateUrl: './add-or-edit-contract.component.html' })
export class AddOrEditContractComponent implements OnInit {
  dataObject: any = { lstPaymentProgress: [] }
  isEditItem = false
  dataBid: any[] = []
  dataSupplier: any[] = []
  uploading = false
  modalTitle = 'Thêm <PERSON>ớ<PERSON>'
  fileList: NzUploadFile[] = []
  fileToUpload!: File
  dataEmployee: any[] = []
  dataContract: any[] = []
  maxSizeUpload = enumData.maxSizeUpload
  datalstObject: any[] = []
  dataPaymentPlan: any = {}

  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditContractComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.loadBid()
    this.loadEmployee()
    this.loadObject()
    this.loadContract()
    if (this.data && this.data.id) {
      this.isEditItem = true
      this.loadDetail(this.data.id)
    } else {
      this.dataObject.isGenChild = false
      this.dataObject.isPaymentProgress = false
    }
  }

  loadContract() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.FIND_MIRROR, {}).then((res) => {
      this.notifyService.hideloading()
      this.dataContract = res || []
    })
  }

  loadDetail(id: string) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.DETAIL, { id: id }).then((result) => {
      this.dataObject = result
      this.loadSupplierByBid(this.dataObject.bidId)
      this.notifyService.hideloading()
      let unique = result.anotherRoleIds.filter(function (elem: any, index: any, self: string | any[]) {
        return index === self.indexOf(elem)
      })
      this.dataObject.anotherRoleIds = unique
      this.dataObject.manageContractId = result.manageContractId
      this.dataObject.confirmContractId = result.confirmContractId
    })
  }

  updateChecked(data: any, isChecked: boolean) {
    if (isChecked) data.numSelectedEmp++
    else data.numSelectedEmp--
  }

  onSave() {
    if (!this.dataObject.anotherRoleIds) {
      this.notifyService.showError('Vui lòng thêm ít nhất một thành viên xem hợp đồng')
      return
    }

    if (!this.dataObject.fileAttach) {
      this.notifyService.showError('Vui lòng upload File hợp đồng')
      return
    }

    this.dataObject.expiredDate = new Date(this.dataObject.expiredDate)
    this.dataObject.effectiveDate = new Date(this.dataObject.effectiveDate)
    if (this.dataObject.expiredDate.getTime() < this.dataObject.effectiveDate.getTime()) {
      this.notifyService.showError('Ngày hết hạn phải lớn hơn ngày hiệu lực')
      return
    }

    let sum = 0

    if (this.dataObject.isPaymentProgress) {
      if (this.dataObject.lstPaymentProgress.length == 0) {
        this.notifyService.showError('Vui lòng thêm tiến độ thanh toán của hợp đồng')
        return
      }
      for (const paymentProgress of this.dataObject.lstPaymentProgress) {
        sum += +paymentProgress.percent
        paymentProgress.time = new Date(paymentProgress.time)
        if (
          paymentProgress.time.getTime() < this.dataObject.effectiveDate.getTime() ||
          paymentProgress.time.getTime() > this.dataObject.expiredDate.getTime()
        ) {
          this.notifyService.showError('Ngày thanh toán phải lớn hơn ngày hiệu lực và nhỏ hơn ngày hết hạn')
          return
        }
      }
      if (sum > 100) {
        this.notifyService.showError('Tổng phần trăm tiến độ vượt quá 100!')
        return
      }
      if (sum < 100) {
        this.notifyService.showError('Tổng phần trăm tiến độ chưa đủ 100!')
        return
      }
    }

    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateData()
      return
    }
    this.addData()
  }

  addData() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog(1)
      }
    })
  }

  updateData() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog(1)
      }
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]) => {
    return new Observable((observer: Observer<boolean>) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        this.notifyService.showError('Bạn chỉ có thể tải lên file ảnh JPG')
        observer.complete()
        return
      }
      const isLt2M = file.size! / 1024 / 1024 < 2
      if (!isLt2M) {
        this.notifyService.showError('Bạn chỉ có thể tải lên file nhỏ hơn 2MB')
        observer.complete()
        return
      }
      observer.next(isJpgOrPng && isLt2M)
      observer.complete()
    })
  }

  previewImage: string | undefined = ''
  previewVisible = false

  handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  loadBid() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.FIND, { status: enumData.BidStatus.HoanTat.code }).then((res) => {
      this.notifyService.hideloading()
      this.dataBid = res || []
    })
  }

  loadSupplierByBid(event: any) {
    this.dataSupplier = []
    if (event && this.dataObject && this.dataObject.bidId) {
      this.notifyService.showloading()
      this.apiService.post(this.apiService.SUPPLIER.FIND, { bidId: this.dataObject.bidId, isSuccessBid: true }).then((result) => {
        this.notifyService.hideloading()
        this.dataSupplier = result || []
      })
    }
  }
  loadEmployee() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((res) => {
      this.notifyService.hideloading()
      this.dataEmployee = res || []
    })
  }

  async handleFileInput(event: any) {
    const files = event.target.files
    const fileToUpload = files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) {
          this.dataObject.fileAttach = res[0]
        }
      })
      // .catch((err) => {
      //   this.notifyService.showError(err)
      // })
    }
  }

  loadObject() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OBJECT.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      if (result) {
        this.datalstObject = result
      }
    })
  }

  //#region Tiến độ thanh toán của HĐ
  onAdd() {
    if (this.dataPaymentPlan.percent == null || this.dataPaymentPlan.percent == '') {
      this.notifyService.showError('Vui lòng nhập Tiến độ thực hiện (%)')
      return
    }
    if (this.dataPaymentPlan.percent <= 0) {
      this.notifyService.showError('Vui lòng nhập Tiến độ thực hiện (%) lớn hơn 0')
      return
    }

    const data = {
      name: this.dataPaymentPlan.name,
      percent: this.dataPaymentPlan.percent,
      time: this.dataPaymentPlan.time,
      description: this.dataPaymentPlan.description,
    }
    this.dataObject.lstPaymentProgress.push(data)
    this.dataObject.isChangePaymentProgress = true
    this.dataPaymentPlan.name = ''
    this.dataPaymentPlan.percent = ''
    this.dataPaymentPlan.time = ''
    this.dataPaymentPlan.description = ''
  }

  onDelete(index: any) {
    this.dataObject.isChangePaymentProgress = true
    this.dataObject.lstPaymentProgress.splice(index, 1)
  }

  changeEvent(event: any) {
    if (Number(event.target.value) === 100 || Number(event.target.value) > 100) {
      this.dataPaymentPlan.percent = 100
    }
  }
  handleClear(colName: string) {
    let file: any = document.querySelector(`.${colName}`)
    file.value = null
    this.dataObject[colName] = null
  }
  //#endregion
}
