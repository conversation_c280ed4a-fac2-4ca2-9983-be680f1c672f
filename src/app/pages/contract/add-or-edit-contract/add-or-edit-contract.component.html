<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiêu đề hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tiêu đề hợp đồng (1-50 kí tự)!">
            <input nz-input placeholder="Nhập 1-50 kí tự" [(ngModel)]="dataObject.name" name="name" required
              pattern=".{1,50}" autocomplete="off" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" nzRequired class="text-left">Đối tượng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn đối tượng">
            <nz-select [disabled]="isEditItem" nzShowSearch nzAllowClear nzPlaceHolder="Chọn đối tượng"
              [(ngModel)]="dataObject.objectId" name="objectId" required>
              <nz-option *ngFor="let item of datalstObject" [nzLabel]="item.code" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" nzFor="anotherRoleIds" nzRequired class="text-left">
            Các thành viên xem hợp đồng
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thành viên xem hợp đồng !">
            <nz-select nzShowSearch nzAllowClear nzMode="multiple" nzPlaceHolder="Chọn thành viên xem hợp đồng "
              [(ngModel)]="dataObject.anotherRoleIds" name="anotherRoleIds" required>
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Người duyệt hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người duyệt hợp đồng">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn thành viên "
              [(ngModel)]="dataObject.confirmContractId" name="confirmContractId" required>
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Người quản lý hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người quản lý hợp đồng">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn thành viên "
              [(ngModel)]="dataObject.manageContractId" name="manageContractId" required>
              <nz-option *ngFor="let item of dataEmployee" [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                [nzValue]="item.id"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Gói thầu</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Gói thầu">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Gói thầu" [(ngModel)]="dataObject.bidId"
              name="bidId" (ngModelChange)="loadSupplierByBid($event)" required>
              <nz-option *ngFor="let item of dataBid" [nzLabel]="item.name" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Hợp đồng tham chiếu</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Hợp đồng tham chiếu">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Hợp đồng tham chiếu"
              [(ngModel)]="dataObject.contractMirrorId" name="contractMirrorId">
              <nz-option *ngFor="let item of dataContract" [nzLabel]="item.name" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>




      <nz-col nzSpan="12">
        <nz-form-item nzFlex >
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Nhà cung cấp</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Nhà cung cấp">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Nhà cung cấp" [(ngModel)]="dataObject.supplierId"
              name="supplierId" required [nzDropdownStyle]="{color: 'green'}" [disabled]="!dataObject.bidId">
              <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày hiệu lực</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Ngày hiệu lực">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.effectiveDate"
              nzPlaceHolder="Nhập Ngày hiệu lực" name="effectiveDate" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày hết hạn</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Ngày hết hạn">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.expiredDate" nzPlaceHolder="Nhập Ngày hết hạn"
              name="expiredDate" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" [nzRequired]="true" [nzFor]="'fileAttach'" class="text-left">
            <span nz-tooltip nzTooltipPlacement="topLeft" [nzTooltipTitle]="' File đính kèm'">
              File hợp đồng</span>
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập dữ liệu!">
            <nz-input-group nzSearch style="display: flex;">
              <input nz-input [(ngModel)]="dataObject.fileAttach" [ngModelOptions]="{standalone: true}"
                [attr.id]="'fileAttach'" disabled required="true">
              <button type="button" nzType="primary" nz-button nzSearch (click)="handleClear('fileAttach')">
                <span nz-icon nzType="delete"></span>
              </button>
            </nz-input-group>
            <label for="choose-file-partner" class="custom-file-upload">
              Chọn File
            </label>
            <input name="uploadDocument-partner" type="file" required="true" class="fileAttach" accept="application/pdf"
              id="choose-file-partner" style="display: none;" (change)="handleFileInput($event)" />
          </nz-form-control>
          <nz-col nzSpan="12" class="ml-3">
            <nz-form-item nzFlex>
              <div class="tooltip" *ngIf="dataObject.fileAttach">
                <button nz-button nzType="primary">
                  <a class="btn" href="{{ dataObject.fileAttach }}" target="_blank">
                    <span nz-icon nzType="down-square"></span> Tải về
                  </a>
                </button>
              </div>
            </nz-form-item>
          </nz-col>
        </nz-form-item>
      </nz-col>



      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Giá trị hợp đồng</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Giá trị hợp đồng">
            <input nz-input placeholder="Nhập Giá trị hợp đồng" currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
              [(ngModel)]="dataObject.value" name="price" pattern=".{1,11}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Tiến độ thanh toán</nz-form-label>
          <nz-form-control nzSpan="24">
              <nz-switch [(ngModel)]="dataObject.isPaymentProgress" name="isPaymentProgress"></nz-switch>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
  

      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ghi chú
          </nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Nhập ghi chú!">
            <textarea nz-input placeholder="Ghi chú" [(ngModel)]="dataObject.description" name="note" pattern=".{1,500}"
              rows="3" auto></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

    </nz-row>


    <nz-collapse *ngIf="dataObject.isPaymentProgress" class="mt-3">
      <nz-collapse-panel nzHeader="Cấu hình Tiến độ thanh toán" class="ant-bg-antiquewhite" nzActive="true">
        <form nz-form #frmAdd2="ngForm">
          <nz-row nzGutter="8">
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên tiến độ</nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22">
                  <input nz-input placeholder="Nhập tên tiến độ" [(ngModel)]="dataPaymentPlan.name" name="name"
                    required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiến độ thực hiện (%)
                </nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22">
                  <input nz-input placeholder="Nhập số phần trăm tiến độ" numbersOnly [min]="0" [max]="100" minMax
                    (blur)="changeEvent($event)" [(ngModel)]="dataPaymentPlan.percent" name="percent" required
                    pattern=".{1,3}" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời gian</nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22" nzErrorTip="Vui lòng chọn thời gian!">
                  <nz-date-picker nzFormat="dd-MM-yyyy" name="time" placeholder="Chọn thời gian"
                    [(ngModel)]="dataPaymentPlan.time" [ngModelOptions]="{standalone: true}" required>
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </nz-col>


            <nz-col nzSpan="24">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left">Ghi chú</nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22">
                  <input type="text" nz-input placeholder="Nhập Ghi chú" [(ngModel)]="dataPaymentPlan.description"
                    name="description" pattern=".{1,250}" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col nzSpan="24" class="text-center">
              <button nz-button nzType="primary" (click)="onAdd()" [disabled]="!frmAdd2.form.valid">
                Thêm tiến độ
              </button>
            </nz-col>

          </nz-row>
        </form>

        <nz-col nzSpan="24" class="mt-2">
          <nz-table [nzShowPagination]="false" [nzData]="dataObject.lstPaymentProgress.length > 0 ? [''] : []"
            nzBordered>
            <thead>
              <tr>
                <th>Tên tiến độ</th>
                <th>Tiến độ thực hiện (%)</th>
                <th>Số tiền</th>
                <th>Thời gian</th>
                <th>Ghi chú</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstPaymentProgress; let i = index">
                <td>{{ data.name }}</td>
                <td>{{ data.percent }} (%)</td>
                <td>{{ data.percent*dataObject.value/100 | number }}</td>
                <td>{{ data.time | date: "dd/MM/yyyy" }}</td>
                <td>{{ data.description }}</td>
                <td>
                  <button nz-tooltip nzTooltipTitle="Xóa" nz-button nzDanger (click)="onDelete(i)">
                    <span nz-icon nzType="delete"></span>
                  </button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>
  </div>


  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid " nzType="primary" (click)="onSave()">Lưu</button>
    </nz-col>
  </nz-row>
</form>