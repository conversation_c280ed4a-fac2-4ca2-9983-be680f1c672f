import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { ContractComponent } from './contract.component'
import { InvoiceSuggestComponent } from './invoice-suggest/invoice-suggest.component'
import { InvoiceComponent } from './invoice/invoice.component'
import { POComponent } from './po/po.component'
import { DeliveryDateManangerComponent } from './delivery-date-mananger/delivery-date-mananger.component'
import { PoChildDetailComponent } from './po/po-detail/po-child-detail/po-child-detail.component'
import { ContractChildDetailComponent } from './contract-detail/contract-child-detail/contract-child-detail.component'
import { BillComponent } from './bill/bill.component'

const routes: Routes = [
  { path: 'contract', component: ContractComponent },
  { path: 'po', component: POComponent },
  { path: 'invoice-suggest', component: InvoiceSuggestComponent },
  { path: 'invoice-manage', component: InvoiceComponent },
  { path: 'delivery-manager', component: DeliveryDateManangerComponent },
  { path: 'detail-child', component: PoChildDetailComponent },
  { path: 'detail-contract-child', component: ContractChildDetailComponent },
  { path: 'bill', component: BillComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ContractRoutingModule {}
