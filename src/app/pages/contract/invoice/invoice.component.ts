import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../core/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService } from '../../../services'
import { ContractDetailComponent } from '../../contract/contract-detail/contract-detail.component'
import { PODetailComponent } from '../../contract/po/po-detail/po-detail.component'
import { InvoiceSuggestDetailComponent } from '../invoice-suggest/invoice-suggest-detail/invoice-suggest-detail.component'
import { AddInvoiceComponent } from './add-invoice/add-invoice.component'

@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
})
export class InvoiceComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  dataSearch: any = {}
  listOfData: any[] = []
  dataContract: any[] = []
  dataPO: any[] = []
  enumData = enumData
  invoiceRole = enumData.Role.Payment
  currentUser!: any
  listStatus: any[] = this.coreService.convertObjToArray(enumData.InvoiceSuggestStatus)
  data: any
  statusPaid = enumData.InvoiceSuggestStatus.Paid.code
  isAllowAdd = false
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.PAYMENT_002.code
    this.isAllowAdd = this.authenticationService.checkPermission([this.enumProject.Features.PAYMENT_002.code], this.enumProject.Action.Create.code)
    this.loadContract()
    this.loadPOPurchaseOrder()
    this.searchData(true)
  }

  searchData(reset = false) {
    this.notifyService.showloading()
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.contractId) {
      where.contractId = this.dataSearch.contractId
    }
    if (this.dataSearch.poId) {
      where.poId = this.dataSearch.poId
    }
    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }

    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.INVOICE_SUGGEST.PAGINATION, dataSearch).then(async (data) => {
      if (data) {
        this.notifyService.hideloading()
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  loadContract() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CONTRACT.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataContract = result || []
    })
  }

  loadPOPurchaseOrder() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataPO = result || []
    })
  }

  clickViewPO(object: any) {
    if (!object.poId) return
    this.dialog.open(PODetailComponent, { disableClose: false, data: { id: object.poId } })
  }

  clickViewContract(object: any) {
    if (!object.contractId) return
    this.dialog.open(ContractDetailComponent, { disableClose: false, data: { id: object.contractId } })
  }

  clickViewDetail(object: any) {
    this.dialog.open(InvoiceSuggestDetailComponent, { disableClose: false, data: object })
  }

  clickPayment(object: any) {
    if (object.status == this.statusPaid) {
      this.notifyService.showError(`Đề nghị thanh toán đã hoàn tất`)
      return
    }
    this.dialog
      .open(AddInvoiceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }
}
