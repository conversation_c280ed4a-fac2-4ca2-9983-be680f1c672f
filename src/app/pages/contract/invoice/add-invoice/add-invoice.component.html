<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Số tiền cần thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.moneyNeedPaid" name="moneyNeedPaid" required
              pattern=".{1,250}" currencyMask [options]="{ allowNegative: false, align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Số tiền thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input [(ngModel)]="dataObject.money" name="money" required pattern=".{1,250}" currencyMask
              [options]="{ allowNegative: false, align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>
            Thời gian thanh toán
          </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn Thời gian thanh toán">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.invoiceDate" name="invoiceDate" required>
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>File liên quan</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-upload [nzAction]="apiService.UploadUrl" nzListType="picture-card"
              [nzHeaders]="{ authorization: 'authorization-text' }" [(nzFileList)]="dataObject.fileList"
              [nzPreview]="handlePreview" (nzChange)="onChangeImage($event)">
              <div>
                <span nz-icon nzType="plus"></span>
                <div style="margin-top: 8px">Upload</div>
              </div>
            </nz-upload>
            <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null"
              (nzOnCancel)="previewVisible = false">
              <ng-template #modalContent>
                <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
              </ng-template>
            </nz-modal>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Ghi chú</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <textarea rows="4" nz-input [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn xác nhận thanh toán?" nzPopconfirmPlacement="top"
        (nzOnConfirm)="onSave()" nz-button [disabled]="!frmAdd.form.valid" nzType="primary">Xác nhận thanh toán
      </button>
    </nz-col>
  </nz-row>
</form>