import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { enumData } from '../../../../core/enumData'
import { ApiService, NotifyService } from '../../../../services'

@Component({
  selector: 'app-add-invoice',
  templateUrl: './add-invoice.component.html',
})
export class AddInvoiceComponent implements OnInit {
  modalTitle = 'Cập nhật thanh toán'
  dataObject: any = {}
  previewImage = ''
  previewVisible = false

  constructor(
    private dialogRef: MatDialogRef<AddInvoiceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private notifyService: NotifyService,
    public apiService: ApiService
  ) {}

  ngOnInit() {
    if (this.data) {
      this.dataObject.invoiceSuggestId = this.data.id
      this.dataObject.moneyNeedPaid = +this.data.money - +this.data.moneyPaid
      this.dataObject.money = this.dataObject.moneyNeedPaid
    }
  }

  onSave() {
    this.notifyService.showloading()
    if (!this.dataObject.money) {
      this.notifyService.showError(`Vui lòng nhập [Số tiền thanh toán]`)
      return
    }
    if (!(this.dataObject.money > 0)) {
      this.notifyService.showError(`Vui lòng nhập [Số tiền thanh toán] hợp lệ`)
      return
    }
    if (this.dataObject.money > this.dataObject.moneyNeedPaid) {
      this.notifyService.showError(`Vui lòng nhập [Số tiền thanh toán] không vượt quá [Số tiền cần thanh toán] của ĐNTT`)
      return
    }
    if (!this.dataObject.fileList || this.dataObject.fileList.length === 0) {
      this.notifyService.showError(`Vui lòng upload [File liên quan] ĐNTT`)
      return
    }
    if (!(this.dataObject.invoiceDate)) {
      this.notifyService.showError(`Vui lòng chọn hợp thời gian thanh toán`)
      return
    }
    this.apiService.post(this.apiService.INVOICE.CREATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(1)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  handlePreview = async (file: any) => {
    const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
      new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
      })

    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  onChangeImage(info: { file: NzUploadFile; fileList: NzUploadFile[] }) {
    switch (info.file.status) {
      case 'uploading':
        break
      case 'done':
        {
          // update url to download img
          info.file.url = info.file.response[0]
          const file = info.fileList.find((c) => c.uid == info.file.uid)
          if (file) file.url = info.file.response[0]
        }
        break
      case 'error':
        break
      case 'removed':
        break
    }
  }
}
