<nz-row nzGutter="8">
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn hợp đồng" [(ngModel)]="dataSearch.contractId"
      name="contractId">
      <nz-option *ngFor="let item of dataContract" [nzLabel]="item.name" [nzValue]="item.id">
      </nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn PO" [(ngModel)]="dataSearch.poId" name="poId">
      <nz-option *ngFor="let item of dataPO" [nzLabel]="item.code" [nzValue]="item.id">
      </nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Trạng thái">
      <nz-option *ngFor="let item of listStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
    </nz-select>
  </nz-col>
  <nz-col nzSpan="6">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>Tìm kiếm
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered [nzScroll]="{ x: '2000px' }" nzTableLayout="fixed">
    <thead>
      <tr>
        <th nzWidth="250px" nzLeft>Mã ĐNTT</th>
        <th>Mã PO</th>
        <th>Hợp đồng</th>
        <th>Số tiền ĐNTT</th>
        <th>Số tiền đã thanh toán</th>
        <th>Số tiền cần TT</th>
        <th>Thời gian quyết toán</th>
        <th>Số hóa đơn</th>
        <th>Nội dung thanh toán</th>
        <th>Trạng thái thanh toán</th>
        <th zWidth="250px" nzRight *ngIf="isAllowAdd">Tùy chọn</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td nzWidth="250px" nzLeft>
          <a (click)="clickViewDetail(data)" style="cursor: pointer;">
            {{ data.code }}
          </a>
        </td>
        <td>
          <a (click)="clickViewPO(data)" style="cursor: pointer;" *ngIf="data.poId">
            {{ data.poCode }}
          </a>
        </td>
        <td>
          <a (click)="clickViewContract(data)" style="cursor: pointer;" *ngIf="data.contractId">
            {{ data.contractCode }}
          </a>
        </td>
        <td>{{data.money | number}}</td>
        <td>{{data.moneyPaid | number}}</td>
        <td>{{(data.money - data.moneyPaid) | number}}</td>
        <td>{{data.invoiceDate | date: 'dd/MM/yyyy'}}</td>
        <td>{{data.invoiceNo}}</td>
        <td>{{data.description}}</td>
        <td>{{data.statusName}}</td>
        <td zWidth="250px" nzRight>
          <button nz-tooltip nzTooltipTitle="Cập nhật thanh toán" (click)="clickPayment(data)" nz-button
            nzType="primary" *ngIf="data.isPaymentPO && data.status != statusPaid">
            <span nz-icon nzType="form"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>