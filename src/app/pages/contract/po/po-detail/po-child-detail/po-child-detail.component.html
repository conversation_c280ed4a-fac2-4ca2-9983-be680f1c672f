<form nz-form #frmAdd="ngForm">
  <nz-row *ngIf="show !== true">
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div>
    <nz-collapse>
      <nz-collapse-panel nzHeader="Thông tin chung" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" disabled class="text-left">Mã PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.code }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" disabled class="text-left">Tiêu đề PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.title }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="anotherRoleIds" disabled class="text-left"> Các thành viên xem PO
              </nz-form-label>
              <nz-form-control nzSpan="24">
                <ng-container *ngFor="let item of dataObject?.anotherView">
                  <b><nz-tag> {{ item }}</nz-tag></b>
                </ng-container>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Người duyệt PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.confirmName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Người hủy PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.cancelName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Người thanh toán PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.paymentName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Người chỉnh sửa PO</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.editName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="!data.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.supplierName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="!data.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Gói thầu</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.bidName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="!data.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">PR</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.prName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="data.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Mã HĐ </nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.contractCode }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="data.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Tiến độ thanh toán (theo HĐ) </nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.contractPaymentPlanName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="data.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>% Tiến độ thanh toán (theo HĐ) </nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.contractPaymentPlanPercent }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Bên mua hàng</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.company }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Đơn vị tiền tệ</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.currency }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Số tiền</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.money | number }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Email</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.email }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Số điện thoại</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.phone }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" disabled>Trạng thái </nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.statusName }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
              <nz-form-control nzSpan="24">
                <b>{{ dataObject.description }}</b>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse class="mt-3">
      <nz-collapse-panel nzHeader="Danh sách hàng hóa" class="ant-bg-antiquewhite">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstProduct.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false"
            nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã vật tư</th>
                <th>Vật tư</th>
                <th>Tên hàng hóa</th>
                <th>Mô tả hàng hóa</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Thành tiền</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstProduct; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.itemCode }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.price | number }}</td>
                <td>{{ data.money | number }}</td>
                <td>{{ data.note }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>

    <!-- lstChild -->
    <nz-collapse *ngIf="dataObject.lstChild.length> 0" class="mt-3">
      <nz-collapse-panel nzHeader="Danh sách pr thành phần" class="ant-bg-antiquewhite">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstChild.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false"
            nzBordered>
            <thead>
              <tr>
                <th>Mã PO</th>
                <th>Tên PO</th>
                <th>Giá trị PO</th>
                <th>Tiến độ thanh toán</th>
                <th>Sản phẩm</th>
                <th>Số lượng</th>
                <th>Đơn vị tính</th>
                <th>Trạng thái</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstChild; let i = index">
                <td>{{data.code }}</td>
                <td>{{ data.title }}</td>
                <td>{{data.money | number }} {{ data.currency }}</td>
                <td>{{ dataObject.contractPaymentPlanName }}</td>
                <td>{{ data.__products__[0]?.name }}</td>
                <td>{{ data.__products__[0]?.quantity }}</td>
                <td>{{ data.__products__[0]?.unit }}</td>

                <td class="mw-25">
                  <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse class="mt-3" *ngIf="!data.contractId">
      <nz-collapse-panel nzHeader="Danh sách Tiến độ thanh toán" class="ant-bg-antiquewhite">
        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-table [nzShowPagination]="false" [nzData]="dataObject.lstPaymentProgress" nzBordered>
              <thead>
                <tr>
                  <th>Tên tiến độ</th>
                  <th>Tiến độ thực hiện (%)</th>
                  <th>Số tiền</th>
                  <th>Thời gian</th>
                  <th>Ghi chú</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of dataObject.lstPaymentProgress">
                  <td>{{ data.name }}</td>
                  <td>{{ data.percent }} (%)</td>
                  <td>{{ data.money | number }}</td>
                  <td>{{ data.time | date : 'dd/MM/yyyy' }}</td>
                  <td>{{ data.description }}</td>
                </tr>
              </tbody>
            </nz-table>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <!-- TO DO -->
    <nz-collapse class="mt-3" *ngIf="!data.contractId">
      <nz-collapse-panel nzHeader="Danh sách hóa đơn" class="ant-bg-antiquewhite">
        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-table [nzShowPagination]="false" [nzData]="dataObject.lstFile" nzBordered>
              <thead>
                <tr class="text-nowrap">
                  <th>{{ 'Tên file' }}</th>
                  <th>{{ 'Tiên tiến độ' }}</th>
                  <th>{{ 'URL' }}</th>
                  <th>{{ 'Số hóa đơn' }}</th>

                  <th>{{ 'Ngày gửi hóa đơn' }}</th>
                  <th>{{ 'File hóa đơn' }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let dataH of dataObject.lstFile; let i = index">
                  <td class="text-nowrap">{{ dataH.fileName }}</td>
                  <td class="text-nowrap">{{ dataH.progressName }}</td>
                  <td class="text-nowrap">{{ dataH.url }}</td>
                  <td class="text-nowrap">{{ dataH.invoiceNo }}</td>
                  <td>{{ dataH.createdAt | date : 'dd/MM/yyyy' }}</td>
                  <td class="text-center">
                    <a [href]="dataH.fileUrl" download>{{ dataH.fileName }}</a>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse class="mt-3">
      <nz-collapse-panel nzHeader="Lịch sử" class="ant-bg-antiquewhite">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstHistory.length > 0 ? [''] : []" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Thời gian thực hiện</th>
                <th>Người thực hiện</th>
                <th>Trạng thái cũ</th>
                <th>Trạng thái hiện tại</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstHistory; let i = index">
                <td class="mw-25">
                  {{ i + 1 }}
                </td>
                <td class="mw-25">
                  {{ data.createdAt | date : 'dd/MM/yyyy HH:mm' }}
                </td>
                <td class="mw-25">
                  {{ data.employeeName || data.supplierName }}
                </td>
                <td class="text-nowrap">
                  {{ data.statusCurrentName }}
                </td>
                <td class="text-nowrap">
                  <b>{{ data.statusConvertName }}</b>
                </td>
                <td class="mw-25">
                  {{ data.description }}
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button nzType="primary" class="mr-3" (click)="onClose()"><span nz-icon
          nzType="close"></span>Đóng</button>
      <button nz-button class="mr-3 ant-btn-success" *ngIf="
          dataObject.status === enumStatus.Delivery.code &&
          data.isAllowApprovedPO &&
          authenticationService.checkPermission(enumRole, action.Update.code)
        " (click)="setItemCompletePO(dataObject)">
        <span nz-icon nzType="check"></span>Hoàn Thành
      </button>
      <button *ngIf="
          dataObject.status === enumStatus.Delivery.code &&
          data.isAllowApprovedPO &&
          authenticationService.checkPermission(enumRole, action.Update.code)
        " nz-button nzDanger (click)="clickRefusePO(dataObject)">
        <span nz-icon nzType="delete"></span>Từ Chối
      </button>

      <button *ngIf="
          dataObject.status === enumStatus.Open.code ||
          (data.status === enumStatus.DeliveryRefuse.code &&
            data.isAllowCancelPO &&
            authenticationService.checkPermission([enumRole], action.Delete.code))
        " nz-button nzDanger (click)="clickRefusePO(dataObject)">
        <span nz-icon nzType="stop"></span>Hủy PO
      </button>
    </nz-col>
  </nz-row>
</form>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Nhập lý do từ chối" (nzOnCancel)="isVisible = false" [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">Lý do</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập tên (1-250 kí tự)!">
            <textarea nz-input rows="5" auto placeholder="Nhập lý do" [(ngModel)]="dataRefuse.reason"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>

  <div *nzModalFooter class="text-center">
    <button [disabled]="!dataRefuse.reason || dataRefuse.reason === ''" nzDanger (click)="onSendRefuse()" nz-button>
      <span nz-icon nzType="delete"></span>Hủy
    </button>
    <button (click)="isVisible = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>

<style>
  td[scope='OPEN'] {
    color: darkcyan;
    font-weight: bold;
  }

  td[scope='APPROVED'] {
    color: blue;
    font-weight: bold;
  }

  td[scope='DELIVERY'] {
    color: green;
    font-weight: bold;
  }

  td[scope='COMFIRM'] {
    color: navy;
    font-weight: bold;
  }

  td[scope='COMPLETE'] {
    color: orange;
    font-weight: bold;
  }

  td[scope='REFUSE'] {
    color: red;
    font-weight: bold;
  }
</style>

<nz-modal [(nzVisible)]="isVisibleCancel" nzTitle="Nhập lý do hủy PO" (nzOnCancel)="isVisibleCancel = false"
  [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">Lý do</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập lý do !">
            <textarea nz-input rows="5" auto placeholder="Nhập lý do tù chối"
              [(ngModel)]="dataCancel.reason"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div class="text-center">

  </div>
</nz-modal>