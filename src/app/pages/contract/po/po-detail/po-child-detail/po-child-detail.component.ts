import { Component, Inject, Input, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService } from 'src/app/services'

@Component({
  selector: 'app-po-child-detail',
  templateUrl: './po-child-detail.component.html',
  styleUrls: ['./po-child-detail.component.scss'],
})
export class PoChildDetailComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  dataObject: any = {
    lstProduct: [],
    lstPaymentProgress: [],
    lstHistory: [],
  }
  dateFormat = 'yyyy/MM/dd'
  modalTitle = ''
  dataCompany: any[] = []
  dataCurrency: any[] = []
  dataUnit: any[] = []
  dataEmployee: any
  dataRefuse: any = {}
  isVisible = false
  dataSubmitRefuse: any
  enumData = enumData
  @Input() contract: any
  @Input() show: any
  enumRole: any
  action: any
  enumProject: any
  currentUser: any
  enumStatus: any
  isVisibleCancel = false
  dataObjectCancel: any = {}
  dataCancel: any

  constructor(
    private dialogRef: MatDialogRef<PoChildDetailComponent>,
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private activatedRoute: ActivatedRoute,
    public authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.modalTitle = 'Xem thông tin PO'
    this.loadAllDataSelect()
    this.activatedRoute.paramMap.subscribe((params) => {
      let data = params.get('id')
      if (data) {
        // this.isShowTimeline = !this.isShowTimeline
        this.data = { id: data }
      }
      //  else {
      //   this.isShowTimeline = this.isShowTimeline
      // }
    })
    this.loadDetail(this.data)
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.PO_001.code
    this.enumStatus = this.enumData.PurchaseOrderStatus
  }

  async loadDetail(data: any) {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.PO.DETAIL, { id: this.data.id }).then((res) => {
      this.dataObject = res
    })
    await this.apiService.post(this.apiService.INVOICE_SUGGEST.FIND_DETAIL_FILE, { id: data.id }).then(async (result) => {
      this.dataObject.lstFile = result
    })
    this.notifyService.hideloading()
  }

  loadAllDataSelect() {
    const lstPromise = [
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.company }), //0
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }), //1
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }), //2
      this.apiService.post(this.apiService.EMPLOYEE.FIND, {}), //3
    ]

    Promise.all(lstPromise).then(async (res) => {
      this.dataCompany = res[0]
      this.dataCurrency = res[1]
      this.dataUnit = res[2]
      this.dataEmployee = res[3]
    })
  }

  onClose() {
    this.dialogRef.close(1)
  }

  setItemCompletePO(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.COMPLETE, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.onClose()
      }
    })
  }

  clickRefusePO(data: any) {
    this.dataSubmitRefuse = data
    this.isVisible = true
  }

  onSendRefuse() {
    const data = this.dataSubmitRefuse
    if (this.dataRefuse.reason == null || this.dataRefuse.reason.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do')
      return
    }
    data.reason = this.dataRefuse.reason
    this.apiService.post(this.apiService.PO.REFUSE, data).then(() => {
      this.notifyService.showSuccess('Gửi xác nhận từ chối PO thành công.')
      this.isVisible = false
      this.onClose()
    })
  }

  clickCancelPO(data: any) {
    this.dataObjectCancel = data
    this.isVisibleCancel = true
  }

  onSendCancel() {
    const data = this.dataObjectCancel
    if (this.dataCancel.reason == null || this.dataCancel.reason.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do')
      return
    }
    data.reason = this.dataCancel.reason
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.CANCEL, data).then((result) => {
      this.notifyService.showSuccess(result.message)
      this.isVisibleCancel = false
      this.onClose()
      this.notifyService.hideloading()
    })
  }
}
