import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-po.component.html' })
export class AddOrEditPOComponent implements OnInit {
  dataObject: any = { lstProduct: [], lstPaymentProgress: [], lstHistory: [] }
  isEditItem = false
  modalTitle = 'THÊM MỚI PO'
  dataCompany: any[] = []
  dataCurrency: any[] = []
  dataUnit: any[] = []
  dataProduct: any = {}
  dataAppendix: any
  dataEmployee: any
  dataTypeContract = this.coreService.convertObjToArray(enumData.ContractTypePo)
  dataContract: any
  dataPaymentPlan: any = {}
  enumContract = enumData.ContractTypePo.Contract.code
  enumNonContract = enumData.ContractTypePo.NonContract.code
  dataContractPaymentPlan: any
  dataSupplier: any
  dataBid: any
  dataMaterial: any[] = []
  allService: any[] = []
  dataPr: any
  editCache: { [key: string]: { edit: boolean; data: any } } = {}
  screenWidth: any
  lstObject: any
  cascaderDataOptions: any[] = []
  isLoadContract = false
  dateFormat = 'dd/MM/yyyy'
  enumProject: any
  enumRole: any
  enumData: any
  constructor(
    private dialogRef: MatDialogRef<AddOrEditPOComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private notifyService: NotifyService,
    private apiService: ApiService,
    private coreService: CoreService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  async ngOnInit() {
    this.screenWidth = window.screen.width
    this.loadAllDataSelect()
    if (this.data && this.data.id) {
      this.isEditItem = true
      this.loadDetail(this.data.id)
      this.modalTitle = 'CẬP NHẬT PO'
    } else if (this.data && this.data.contractId) {
      this.dataObject.paymentPlanType = this.enumContract
      this.dataObject.contractId = this.data.contractId
      this.dataObject.contractPaymentPlanId = this.data.contractPaymentPlanId
      if (this.data.contractId) {
        this.isLoadContract = false
        this.apiService.post(this.apiService.PAYMENT_PROGRESS.FIND, { contractId: this.data.contractId }).then((result) => {
          this.notifyService.hideloading()
          this.dataContractPaymentPlan = result || []
        })
        this.onChangeTypeContract(this.enumContract)
      }
    }
    if (this.dataObject && this.dataObject.contractId) this.onChangeContract(this.dataObject.contractId)
  }

  loadDetail(id: string) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.DETAIL, { id: id }).then((result) => {
      this.notifyService.hideloading()
      this.dataObject = result
      let indexTemp = 0
      this.dataObject.lstProduct.forEach((element: any) => {
        element.index = indexTemp + ''
        this.editCache[element.index ? element.index : indexTemp] = {
          edit: false,
          data: {
            index: element.index ? element.index : indexTemp,
            ...element,
          },
        }
        indexTemp++
      })
      if (this.data.contractId) {
        this.isLoadContract = false
        this.apiService.post(this.apiService.PAYMENT_PROGRESS.FIND, { contractId: this.data.contractId }).then((result) => {
          this.notifyService.hideloading()
          this.dataContractPaymentPlan = result || []
        })
        this.onChangeTypeContract(this.enumContract)
      }

      this.dataObject.anotherRoleIds = result.anotherRoleIds.filter(function (elem: any, index: any, self: string | any[]) {
        return index === self.indexOf(elem)
      })
    })
  }

  onAddProduct() {
    if (!this.dataProduct.serviceChoose || this.dataProduct.serviceChoose.length == 0) {
      this.notifyService.showError('Vui lòng chọn vật tư.')
      return
    }

    if (this.dataObject.serviceLevel1) {
      this.dataObject.serviceLevel1 = this.dataProduct.serviceLevel1
      if (this.dataObject.serviceLevel1 !== this.dataProduct.serviceChoose[0]) {
        this.notifyService.showError('Vui lòng chọn vật tư cùng cấp độ 1 !')
        return
      }
    } else {
      this.dataProduct.serviceLevel1 = this.dataProduct.serviceChoose[0]
      this.dataObject.serviceLevel1 = this.dataProduct.serviceChoose[0]
    }
    this.dataProduct.serviceId = this.dataProduct.serviceChoose[this.dataProduct.serviceChoose.length - 1]

    const objService = this.allService.find((x: any) => x.id === this.dataProduct.serviceId)
    const len = this.dataObject.lstProduct.length
    const item = {
      name: this.dataProduct.name,
      description: this.dataProduct.description,
      note: this.dataProduct.note,
      unit: this.dataProduct.unit,
      quantity: this.dataProduct.quantity,
      price: this.dataProduct.price,
      money: Number(this.dataProduct.price * this.dataProduct.quantity),
      serviceId: this.dataProduct.serviceId,
      serviceName: objService?.name,
      index: '' + (len + 1),
    }

    this.dataObject.lstProduct = [...this.dataObject.lstProduct, item]
    this.dataObject.isChangeProduct = true
    this.editCache[item.index] = {
      edit: false,
      data: { ...item },
    }
  }

  onDeleteProduct(index: any) {
    this.dataObject.isChangeProduct = true
    this.dataObject.lstProduct.splice(index, 1)
    this.dataObject.lstProduct = this.dataObject.lstProduct.filter((x: any) => x.serviceId !== null)
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.lstProduct.length === 0) {
      this.notifyService.showError('Vui lòng thêm ít nhất sản phẩm')
      return
    }
    this.dataObject.lstProduct.forEach((item: any) => {
      item.money = item.quantity * item.price
    })

    if (this.dataObject.paymentPlanType === this.enumNonContract) {
      if (this.dataObject.lstPaymentProgress.length == 0) {
        this.notifyService.showError('Vui lòng thêm tiến độ thanh toán của PO')
        return
      }
      let sum = 0
      for (const paymentProgress of this.dataObject.lstPaymentProgress) {
        sum += +paymentProgress.percent
      }
      if (sum > 100) {
        this.notifyService.showError('Tổng phần trăm tiến độ vượt quá 100!')
        return
      }
      if (sum < 100) {
        this.notifyService.showError('Tổng phần trăm tiến độ chưa đủ 100!')
        return
      }
    }

    if (!this.dataObject.id) {
      this.addData()
      return
    }

    this.updateData()
  }

  addData() {
    this.apiService.post(this.apiService.PO.CREATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(1)
    })
  }

  updateData() {
    this.dataObject.isChangeProduct = true
    this.apiService.post(this.apiService.PO.UPDATE, this.dataObject).then((result) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(1)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }

  loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.company }), //0
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.currency }), //1
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }), //2
      this.apiService.post(this.apiService.EMPLOYEE.FIND, {}), //3
      this.apiService.post(this.apiService.SUPPLIER.FIND, { bidId: this.dataObject.bidId }), //4
      this.apiService.post(this.apiService.BID.FIND, { status: enumData.BidStatus.HoanTat.code }), // 5
      this.apiService.post(this.apiService.SERVICE.FIND, {}), // 6
      this.apiService.post(this.apiService.PR.FIND, { status: [enumData.PRStatus.Approved.code, enumData.PRStatus.Processing.code] }), //7
      this.apiService.post(this.apiService.OBJECT.FIND, {}), //8
      this.apiService.post(this.apiService.MATERIAL.FIND, {}), // 9
    ]).then(async (res) => {
      this.notifyService.hideloading()
      this.dataCompany = res[0]
      this.dataCurrency = res[1]
      this.dataUnit = res[2]
      this.dataEmployee = res[3]
      this.dataSupplier = res[4]
      this.dataBid = res[5] || []
      this.allService = res[6]
      this.cascaderDataOptions = this.genCascaderData()
      this.dataPr = res[7]
      this.lstObject = res[8]
      this.dataMaterial = res[9]
      setTimeout(() => {
        this.setServiceLevel1()
      }, 1000)
    })
  }

  loadSup() {
    this.notifyService.showloading()
    this.dataObject.supplierId = ''
    Promise.all([
      this.apiService.post(this.apiService.SUPPLIER.FIND, { bidId: this.dataObject.bidId }), //4
    ]).then(async (res) => {
      this.notifyService.hideloading()

      this.dataSupplier = res[0]
    })
  }

  loadItem() {
    for (const item of this.dataMaterial) {
      if (item.id === this.dataProduct.itemId) {
        this.dataProduct.name = item.name
        break
      }
    }
  }

  /** Tạo cascader data */
  genCascaderData(parentId = null) {
    const res = []
    const lstService = this.allService.filter((c) => c.parentId == parentId)
    for (const service of lstService) {
      const data: any = {
        value: service.id,
        label: service.name,
        isLeaf: !!service.isLast,
      }
      if (!data.isLeaf) {
        data.children = this.genCascaderData(data.value)
      }
      res.push(data)
    }
    return res
  }

  onChangeTypeContract(event: any) {
    if (event === this.enumContract) {
      this.loadDataContract()
    } else {
      this.dataObject.contractId = null
      this.dataObject.contractPaymentPlanId = null
    }
  }

  loadDataContract() {
    if (!this.isLoadContract) {
      this.notifyService.showloading()
      this.isLoadContract = true
      this.apiService
        .post(this.apiService.CONTRACT.FIND, {
          status: [this.enumData.ContractStatus.Open.code, this.enumData.ContractStatus.Processing.code, this.enumData.ContractStatus.Complete.code],
        })
        .then((result) => {
          this.notifyService.hideloading()
          this.dataContract = result || []
        })
    }
  }

  async onChangeContract(event: any) {
    this.dataContractPaymentPlan = []
    this.dataObject.contractPaymentPlanId = null
    if (event) {
      let id
      this.notifyService.showloading()
      await this.apiService.post(this.apiService.PAYMENT_PROGRESS.FIND, { contractId: event }).then((result) => {
        this.notifyService.hideloading()
        this.dataContractPaymentPlan = result || []
        if (result[0] && result[0].__contract__ && result[0].__contract__.__bid__) {
          id = result[0].__contract__.__bid__.prId
        }
      })
      if (id) {
        await this.apiService.post(this.apiService.PR.FIND_ITEM_DETAIL, { id: id }).then((result) => {
          if (result && result.length > 0) {
            result.forEach((item: any, index: any) => {
              const data = {
                index: index,
                quantity: item.quantity,
                price: 0,
                money: 0,
                serviceId: item.serviceId,
                itemId: item.itemId,
                serviceName: item.__service__.code,
                name: item.productName,
                nameText: item.productName,
                description: '',
                note: '',
                unit: item.unit,
              }
              this.dataObject.lstProduct = [...this.dataObject.lstProduct, data]
              this.editCache[data.index] = {
                edit: false,
                data: { ...data },
              }
              this.dataObject.serviceLevel1 = result[0].serviceId
            })
          }
        })
      }
    }
  }

  onAddPaymentPlan() {
    if (this.dataPaymentPlan.percent == null || this.dataPaymentPlan.percent == '' || this.dataPaymentPlan.percent == 0) {
      this.notifyService.showError('Vui lòng nhập phần trăm')
      return
    }
    if (!this.dataPaymentPlan.time) {
      this.notifyService.showError('Vui lòng nhập chọn ngày')
      return
    }
    let percent = 0
    if (this.dataPaymentPlan) percent = this.dataPaymentPlan.percent.replace(/[^0-9]*/g, '')
    const data = {
      name: this.dataPaymentPlan.name,
      percent: percent,
      time: this.dataPaymentPlan.time,
      description: this.dataPaymentPlan.description,
    }
    this.dataObject.isChangePaymentProgress = true
    this.dataObject.lstPaymentProgress.push(data)
  }

  onDeletePayment(index: any) {
    this.dataObject.isChangePaymentProgress = true
    this.dataObject.lstPaymentProgress.splice(index, 1)
  }

  changeEvent(event: any) {
    if (Number(event.target.value) === 100 || Number(event.target.value) > 100) {
      this.dataPaymentPlan.percent = 100
    }
  }

  startEdit(index: string) {
    this.editCache[index].edit = true
  }

  saveEdit(id: string) {
    const index = this.dataObject.lstProduct.findIndex((item: any) => item.index === id)
    this.editCache[id].edit = false
    Object.assign(this.dataObject.lstProduct[index], this.editCache[id].data)
  }

  cancelEdit(id: string) {
    const index = this.dataObject.lstProduct.findIndex((item: any) => item.index === id)
    if (this.editCache[id].data.isNew !== true) {
      this.editCache[id] = {
        data: { ...this.dataObject.lstProduct[index] },
        edit: false,
      }
    } else {
      this.dataObject.lstProduct = this.dataObject.lstProduct.filter((d: any) => d.index !== id)
      delete this.editCache[id]
    }
  }

  onChangeServiceChoose(event: string, index: number, item: any) {
    if (event) {
      item.isEditService = true
      let name = this.dataObject.lstProduct[index].service.filter((x: any) => x.id === event[0])
      this.dataObject.lstProduct[index].serviceText = name[0].code
      this.dataObject.lstProduct[index].serviceId = name[0].id
    }
  }

  onChangeType(event: string, index: number, item: any) {
    if (event) {
      item.isEditType = true
      this.dataObject.lstProduct[index].typeText = event
    }
  }
  onChangeName(event: string, index: number, item: any) {
    if (event) {
      item.isEditName = true
      this.dataObject.lstProduct[index].nameText = event
    }
  }

  onGroupName(event: string, index: number, item: any) {
    if (event) {
      item.isEditName = true
      this.dataObject.lstProduct[index].groupText = event
    }
  }

  onDesName(event: string, index: number, item: any) {
    if (event) {
      item.isEditDes = true
      this.dataObject.lstProduct[index].descriptionText = event
    }
  }

  onNoteName(event: string, index: number, item: any) {
    if (event) {
      item.isEditNote = true
      this.dataObject.lstProduct[index].noteText = event
    }
  }

  async onChangePr(event: any) {
    this.dataObject.lstProduct = []

    if (!event) return
    await this.apiService.post(this.apiService.PR.FIND_ITEM_DETAIL, { id: event }).then((result) => {
      if (result && result.length > 0) {
        result.forEach((item: any, index: any) => {
          const data = {
            index: index,
            quantity: item.quantity,
            price: 0,
            money: 0,
            serviceId: item.serviceId,
            itemId: item.itemId,
            serviceName: item.__service__.code,
            name: item.productName,
            nameText: item.productName,
            description: '',
            note: '',
            unit: item.unit,
          }
          this.dataObject.lstProduct = [...this.dataObject.lstProduct, data]
          this.editCache[data.index] = {
            edit: false,
            data: { ...data },
          }
          this.dataObject.serviceLevel1 = result[0].serviceId
        })
      }
    })
  }

  setServiceLevel1() {
    if (this.allService.length > 0 && this.dataObject.lstProduct.length > 0) {
      let childId = this.dataObject.lstProduct[0].serviceId
      for (let index = 0; index < 4; index++) {
        const element = this.allService.find((s) => s.id === childId)
        if (element) {
          if (element.level === 1) {
            this.dataObject.serviceLevel1 = element.id
            break
          } else {
            childId = element.parentId
          }
        } else {
          break
        }
      }
    }
  }
}
