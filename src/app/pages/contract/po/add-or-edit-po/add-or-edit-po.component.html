<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-collapse>
      <nz-collapse-panel nzHeader="Thông tin chung" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" nzRequired class="text-left">Tiêu đề PO</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tiêu đề PO ">
                <input nz-input placeholder="Nhập 1-250 kí tự" [(ngModel)]="dataObject.title" name="title" required pattern=".{1,250}" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label nzSpan="24" nzFor="anotherRoleIds" nzRequired class="text-left"> Các thành viên xem PO </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thành viên !">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzMode="multiple"
                  nzPlaceHolder="Chọn thành viên "
                  [(ngModel)]="dataObject.anotherRoleIds"
                  name="anotherRoleIds"
                  required
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Người duyệt PO</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người duyệt PO">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn thành viên " [(ngModel)]="dataObject.confirmId" name="confirmId" required>
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Người hủy PO</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người hủy PO">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn thành viên " [(ngModel)]="dataObject.cancelId" name="cancelId" required>
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Người thanh toán PO</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người thanh toán PO">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn thành viên "
                  [(ngModel)]="dataObject.poPaymentId"
                  name="poPaymentId"
                  required
                >
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Người chỉnh sửa PO</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn người chỉnh sửa PO">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn thành viên " [(ngModel)]="dataObject.editPOId" name="editPOId" required>
                  <nz-option
                    *ngFor="let item of dataEmployee"
                    [nzLabel]="'(' + item.departmentName + ') ' + item.name"
                    [nzValue]="item.id"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Ngày giao hàng dự kiến</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng Nhập Ngày giao hàng dự kiến">
                <nz-date-picker
                  nzFormat="dd-MM-yyyy"
                  [(ngModel)]="dataObject.deliveryDate"
                  name="deliveryDate"
                  nzPlaceHolder="Nhập Ngày giao hàng "
                  required
                >
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" nzRequired class="text-left">Đối tượng</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn đối tượng">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn đối tượng" required [(ngModel)]="dataObject.objectId" name="objectId">
                  <nz-option *ngFor="let item of lstObject" [nzLabel]="item.code" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Hình thức</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn hình thức">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn hình thức"
                  [(ngModel)]="dataObject.paymentPlanType"
                  name="paymentPlanType"
                  required
                  (ngModelChange)="onChangeTypeContract($event)"
                >
                  <nz-option *ngFor="let item of dataTypeContract" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12" *ngIf="dataObject.paymentPlanType === enumContract">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Hợp đồng</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn hợp đồng">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn hợp đồng"
                  [(ngModel)]="dataObject.contractId"
                  name="contractId"
                  required
                  (ngModelChange)="onChangeContract($event)"
                >
                  <nz-option *ngFor="let item of dataContract" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12" *ngIf="dataObject.paymentPlanType === enumContract && dataObject.contractId">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiến độ thanh toán (theo HĐ) </nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Tiến độ thanh toán (theo HĐ)">
                <nz-select
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn tiến độ thanh toán (theo HĐ)"
                  [(ngModel)]="dataObject.contractPaymentPlanId"
                  name="contractPaymentPlanId"
                  required
                >
                  <nz-option *ngFor="let item of dataContractPaymentPlan" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="8" *ngIf="dataObject.paymentPlanType === enumNonContract">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Gói thầu</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn gói thầu ">
                <nz-select
                  (ngModelChange)="loadSup()"
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn gói thầu"
                  [(ngModel)]="dataObject.bidId"
                  name="bidId"
                >
                  <nz-option *ngFor="let item of dataBid" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="dataObject.paymentPlanType === enumNonContract">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Nhà cung cấp</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn nhà cung cấp ">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn nhà cung cấp" [(ngModel)]="dataObject.supplierId" name="supplierId" required>
                  <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="8" *ngIf="dataObject.paymentPlanType === enumNonContract">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Chọn PR</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng PR ">
                <nz-select
                  (ngModelChange)="onChangePr($event)"
                  nzShowSearch
                  nzAllowClear
                  nzPlaceHolder="Chọn PR"
                  [(ngModel)]="dataObject.prId"
                  name="prId"
                >
                  <nz-option *ngFor="let item of dataPr" [nzLabel]="item.code" [nzValue]="item.id"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>

        <nz-row nzGutter="8">
          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Bên mua hàng</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Bên mua hàng ">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Bên mua hàng" [(ngModel)]="dataObject.company" name="company" required>
                  <nz-option *ngFor="let item of dataCompany" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Đơn vị tiền tệ</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập chọn đơn vị tiền tệ">
                <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn đơn vị tiền tệ" [(ngModel)]="dataObject.currency" name="currency" required>
                  <nz-option *ngFor="let item of dataCurrency" [nzLabel]="item.code" [nzValue]="item.code"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Email</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập email hợp lệ!">
                <input
                  nz-input
                  type="email"
                  placeholder="Nhập email hợp lệ"
                  [(ngModel)]="dataObject.email"
                  name="email"
                  pattern="^[A-Za-z0-9_.]{2,32}@([a-zA-Z0-9]{2,12})(.[a-zA-Z]{2,12})+$"
                  required
                  autocomplete="off"
                />
              </nz-form-control>
            </nz-form-item>
          </nz-col>
          <nz-col nzSpan="12">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left" nzRequired>Số điện thoại</nz-form-label>
              <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Số điện thoại 1-15 kí tự">
                <input nz-input placeholder="Nhập Số điện thoại" [(ngModel)]="dataObject.phone" numbersOnly name="phone" required pattern=".{1,12}" />
              </nz-form-control>
            </nz-form-item>
          </nz-col>

          <nz-col nzSpan="24">
            <nz-form-item nzFlex>
              <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
              <nz-form-control nzSpan="24">
                <textarea rows="4" nz-input placeholder="Nhập Mô tả" [(ngModel)]="dataObject.description" name="description"></textarea>
              </nz-form-control>
            </nz-form-item>
          </nz-col>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse class="mt-3">
      <nz-collapse-panel nzHeader="Danh sách hàng hóa" class="ant-bg-antiquewhite" nzActive="true">
        <form nz-form #frmAdd2="ngForm">
          <nz-row nzGutter="8">
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Chọn LVMH</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng Chọn LVMH">
                  <nz-cascader
                    [nzShowSearch]="true"
                    nzPlaceHolder="Chọn LVMH"
                    [nzOptions]="cascaderDataOptions"
                    name="serviceChoose"
                    id="serviceChoose"
                    [(ngModel)]="dataProduct.serviceChoose"
                  >
                  </nz-cascader>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Hàng hóa </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Hàng hóa ">
                  <!-- <input nz-input placeholder="Nhập Hàng hóa " [(ngModel)]="dataObject.productName"
                    name="productName" required /> -->
                  <nz-select
                    nzShowSearch
                    nzAllowClear
                    [nzPlaceHolder]="'Chọn Item'"
                    [(ngModel)]="dataProduct.itemId"
                    name="itemId"
                    id="itemId"
                    (ngModelChange)="loadItem()"
                    [ngModelOptions]="{ standalone: true }"
                  >
                    <nz-option *ngFor="let item of this.dataMaterial" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left">Tên hàng hóa</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập tên hàng hóa">
                  <input nz-input placeholder="Nhập tên hàng hóa" [(ngModel)]="dataProduct.name" name="name" pattern=".{0,250}" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Đơn vị tính</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Đơn vị tính">
                  <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Đơn vị tính" [(ngModel)]="dataProduct.unit" name="unit" required>
                    <nz-option *ngFor="let item of dataUnit" [nzLabel]="item.code" [nzValue]="item.code"> </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Số lượng</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập số lượng">
                  <input
                    nz-input
                    placeholder="Nhập số lượng"
                    numbersOnly
                    [(ngModel)]="dataProduct.quantity"
                    name="quantity"
                    required
                    pattern=".{1,100}"
                    currencyMask
                    [options]="{
                      prefix: '',
                      precision: 0,
                      allowNegative: false,
                      align: 'left'
                    }"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Đơn giá</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập đơn giá">
                  <input
                    nz-input
                    placeholder="Nhập đơn giá"
                    currencyMask
                    [options]="{
                      prefix: '',
                      precision: 0,
                      allowNegative: false,
                      align: 'left'
                    }"
                    [(ngModel)]="dataProduct.price"
                    name="price"
                    required
                    pattern=".{1,11}"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left">Mô tả hàng hóa</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mô tả hàng hóa">
                  <input nz-input placeholder="Nhập mô tả hàng hóa" [(ngModel)]="dataProduct.description" name="description" pattern=".{1,250}" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="24">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left">Ghi chú</nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22">
                  <input type="text" nz-input placeholder="Nhập Ghi chú" [(ngModel)]="dataProduct.note" name="note" pattern=".{1,250}" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col nzSpan="24" class="text-center">
              <button nz-button nzType="primary" (click)="onAddProduct()" [disabled]="!frmAdd2.form.valid">
                <span nz-icon nzType="plus"></span> Thêm hàng hóa
              </button>
            </nz-col>
          </nz-row>
        </form>

        <nz-col nzSpan="24" class="mt-3">
          <!-- <div class="table-scroll-x" [ngStyle]="{'min-width': screenWidth > 1150 ? '100%' : '' }"> -->
          <nz-table
            class="mb-3"
            [nzFrontPagination]="false"
            [nzData]="dataObject.lstProduct"
            [nzShowPagination]="false"
            nzBordered
            [nzScroll]="{ x: 'auto' }"
            nzTableLayout="fixed"
          >
            <thead>
              <tr class="text-nowrap">
                <th class="text-center" nzWidth="120px"></th>
                <th class="text-center" nzWidth="70px">Tên LVKD</th>
                <th class="text-center" nzWidth="160px">Tên hàng hóa</th>
                <th class="text-center" nzWidth="130px">Đơn vị tính</th>
                <th class="text-center" nzWidth="110px">Số lượng</th>
                <th class="text-center" nzWidth="130px">Giá</th>
                <th class="text-center" nzWidth="130px">Thành tiền</th>
                <th class="text-center" nzWidth="220px">Mô tả hàng hóa</th>
                <th class="text-center" nzWidth="220px">Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let dataProduct of dataObject.lstProduct; let i = index" class="editable-row text-nowrap">
                <td class="text-nowrap">
                  <a
                    nz-button
                    nzDanger
                    nz-tooltip
                    nzTooltipTitle="Xóa"
                    class="mr-3"
                    nz-popconfirm
                    nzPopconfirmTitle="Bạn có chắc muốn [XÓA] ITEM?"
                    (nzOnConfirm)="onDeleteProduct(i)"
                  >
                    <span nz-icon nzType="delete"></span>
                  </a>
                  <ng-container *ngIf="!editCache[dataProduct.index].edit; else saveTpl">
                    <button nz-tooltip nzTooltipTitle="Sửa" nz-button (click)="startEdit(dataProduct.index)">
                      <span nz-icon nzType="edit"></span>
                    </button>
                  </ng-container>
                  <ng-template #saveTpl>
                    <button nz-tooltip nzTooltipTitle="Lưu" class="mr-3" nz-button nzType="primary" (click)="saveEdit(dataProduct.index)">
                      <span nz-icon nzType="save"></span>
                    </button>
                    <button nz-tooltip nzTooltipTitle="Huỷ" nz-button nzDanger (click)="cancelEdit(dataProduct.index)">
                      <span nz-icon nzType="close"></span>
                    </button>
                  </ng-template>
                </td>

                <td>{{ dataProduct.serviceName }}</td>
                <ng-container *ngIf="!editCache[dataProduct.index].edit; else typeName">
                  <ng-container *ngIf="!dataProduct.isEditName">
                    <td>{{ dataProduct.name }}</td>
                  </ng-container>
                  <ng-container *ngIf="dataProduct.isEditName">
                    <td>{{ dataProduct.nameText }}</td>
                  </ng-container>
                </ng-container>
                <ng-template #typeName>
                  <td class="text-center">
                    <input
                      (ngModelChange)="onChangeName($event, i, editCache[dataProduct.index].data)"
                      style="width: 140px"
                      nz-input
                      placeholder="Nhập tên hàng hóa"
                      [(ngModel)]="editCache[dataProduct.index].data.name"
                      name="name"
                      required
                      pattern=".{1,250}"
                    />
                  </td>
                </ng-template>

                <ng-container *ngIf="!editCache[dataProduct.index].edit; else unitInput">
                  <td>{{ dataProduct.unit }}</td>
                </ng-container>
                <ng-template #unitInput>
                  <td class="text-center">
                    <nz-select
                      nzShowSearch
                      [(ngModel)]="editCache[dataProduct.index].data.unit"
                      [ngModelOptions]="{ standalone: true }"
                      nzPlaceHolder="Chọn đơn vị tính"
                    >
                      <nz-option *ngFor="let item of dataUnit" [nzLabel]="item.code" [nzValue]="item.code"> </nz-option>
                    </nz-select>
                  </td>
                </ng-template>

                <ng-container *ngIf="!editCache[dataProduct.index].edit; else quantityInput">
                  <td class="text-right">{{ dataProduct.quantity | number }}</td>
                </ng-container>

                <ng-template #quantityInput>
                  <td class="text-center">
                    <input
                      nz-input
                      placeholder="Nhập số lượng"
                      numbersOnly
                      style="width: 90px"
                      [(ngModel)]="editCache[dataProduct.index].data.quantity"
                      name="quantity"
                      required
                      pattern=".{1,100}"
                      currencyMask
                      [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
                    />
                  </td>
                </ng-template>

                <ng-container *ngIf="!editCache[dataProduct.index].edit; else priceInput">
                  <td class="text-right">{{ dataProduct.price | number }}</td>
                </ng-container>

                <ng-template #priceInput>
                  <td class="text-center">
                    <input
                      nz-input
                      placeholder="Nhập số lượng"
                      numbersOnly
                      style="width: 110px"
                      [(ngModel)]="editCache[dataProduct.index].data.price"
                      name="price"
                      required
                      pattern=".{1,100}"
                      currencyMask
                      [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
                    />
                  </td>
                </ng-template>

                <td class="text-right">{{ dataProduct.price * dataProduct.quantity | number }}</td>

                <ng-container *ngIf="!editCache[dataProduct.index].edit; else desInput">
                  <ng-container *ngIf="!dataProduct.isEditDes">
                    <td>{{ dataProduct.description }}</td>
                  </ng-container>
                  <ng-container *ngIf="dataProduct.isEditDes">
                    <td>{{ dataProduct.descriptionText }}</td>
                  </ng-container>
                </ng-container>

                <ng-template #desInput>
                  <td class="text-center">
                    <input
                      (ngModelChange)="onDesName($event, i, editCache[dataProduct.index].data)"
                      style="width: 300px"
                      nz-input
                      placeholder="Nhập mô tả hàng hóa"
                      [(ngModel)]="editCache[dataProduct.index].data.description"
                      name="description"
                      pattern=".{1,250}"
                    />
                  </td>
                </ng-template>

                <ng-container *ngIf="!editCache[dataProduct.index].edit; else noteInput">
                  <ng-container *ngIf="!dataProduct.isEditNote">
                    <td>{{ dataProduct.note }}</td>
                  </ng-container>
                  <ng-container *ngIf="dataProduct.isEditNote">
                    <td>{{ dataProduct.noteText }}</td>
                  </ng-container>
                </ng-container>

                <ng-template #noteInput>
                  <td class="text-center">
                    <input
                      (ngModelChange)="onNoteName($event, i, editCache[dataProduct.index].data)"
                      style="width: 300px"
                      nz-input
                      placeholder="Nhập ghi chú"
                      [(ngModel)]="editCache[dataProduct.index].data.note"
                      name="note"
                      pattern=".{1,250}"
                    />
                  </td>
                </ng-template>
              </tr>
            </tbody>
          </nz-table>
          <!-- </div> -->
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse class="mt-3" *ngIf="dataObject.paymentPlanType === enumNonContract">
      <nz-collapse-panel nzHeader="Cấu hình Tiến độ thanh toán" class="ant-bg-antiquewhite" nzActive="true">
        <form nz-form #frmAdd2="ngForm">
          <nz-row nzGutter="8">
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Tên tiến độ</nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22">
                  <input nz-input placeholder="Nhập tên tiến độ" [(ngModel)]="dataPaymentPlan.name" name="name" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiến độ thực hiện (%) </nz-form-label>
                <nz-form-control nzSpan="24">
                  <input
                    nz-input
                    placeholder="Nhập số phần trăm tiến độ"
                    [min]="0"
                    [max]="100"
                    minMax
                    (blur)="changeEvent($event)"
                    [(ngModel)]="dataPaymentPlan.percent"
                    name="percent"
                    required
                    pattern=".{1,3}"
                    numbersOnly
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời gian </nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn thời gian!">
                  <nz-date-picker
                    [nzFormat]="dateFormat"
                    name="time"
                    placeholder="Chọn thời gian"
                    [(ngModel)]="dataPaymentPlan.time"
                    [ngModelOptions]="{ standalone: true }"
                    required
                  >
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="24">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left">Ghi chú</nz-form-label>
                <nz-form-control [nzSm]="24" [nzXs]="22">
                  <input
                    type="text"
                    nz-input
                    placeholder="Nhập Ghi chú"
                    [(ngModel)]="dataPaymentPlan.description"
                    name="description"
                    pattern=".{0,250}"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="24" class="text-center">
              <button nz-button nzType="primary" (click)="onAddPaymentPlan()" [disabled]="!frmAdd2.form.valid">
                <span nz-icon nzType="plus"></span> Thêm tiến độ
              </button>
            </nz-col>
          </nz-row>
        </form>

        <nz-col nzSpan="24" class="mt-3" *ngIf="dataObject.lstPaymentProgress">
          <nz-table class="mb-3" [nzShowPagination]="false" nzBordered [nzData]="dataObject.lstPaymentProgress.length > 0 ? [''] : []">
            <thead>
              <tr>
                <th>STT</th>
                <th>Tên tiến độ</th>
                <th>Tiến độ thực hiện (%)</th>
                <!-- <th>Giá trị</th> -->
                <th>Thời gian</th>
                <th>Ghi chú</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let progress of dataObject.lstPaymentProgress; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ progress.name }}</td>
                <td>{{ progress.percent }} (%)</td>
                <!-- <td>{{ progress.percent*dataObject.money/100 | number }}</td> -->
                <td>{{ progress.time | date : 'dd/MM/yyyy' }}</td>
                <td>{{ progress.description }}</td>
                <td>
                  <button nz-tooltip nzTooltipTitle="Xóa" nz-button nzDanger (click)="onDeletePayment(i)">
                    <span nz-icon nzType="delete"></span>
                  </button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()"><span nz-icon nzType="save"></span> Lưu</button>
    </nz-col>
  </nz-row>
</form>
