import { Component, Input, OnInit, SimpleChanges } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { enumData } from '../../../core/enumData'
import { ApiService, CoreService, NotifyService, AuthenticationService } from '../../../services'
import { AddOrEditPOComponent } from './add-or-edit-po/add-or-edit-po.component'
import { POModalComponent } from './po-modal/po-modal.component'
import { PODetailComponent } from './po-detail/po-detail.component'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
@Component({
  selector: 'app-po',
  templateUrl: './po.component.html',
})
export class POComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = true
  isVisible = false
  dataSearch: any = {}
  dataObject: any = {}
  dataObjectUpdate: any = {}
  isEditStatus = false

  dataRefuse: any = {}
  listOfData: any[] = []
  dataBid: any[] = []
  dataSupplier: any[] = []
  data: any
  enumData: any
  isVisibleCancel = false
  dataStatus: any
  dataOrderStatus: any
  screenWidth: any
  allService: any
  enumProject: any
  enumRole: any
  action: any
  @Input() contract: any
  currentUser: any
  enumStatus: any
  dataCancel: any = {}
  dataObjectCancel: any
  showAddPo: boolean = true
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    public coreService: CoreService,
    private dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.action = this.enumProject.Action
    this.enumRole = this.enumProject.Features.PO_001.code
    this.loadBid()
    this.loadSupplier()
    this.searchData(true)
    this.dataStatus = this.coreService.convertObjToArray(this.enumData.PurchaseOrderStatus)
    this.dataOrderStatus = this.coreService.convertObjToArray(this.enumData.PoOrder)
    this.screenWidth = window.screen.width
    this.loadService()
    this.enumStatus = this.enumData.PurchaseOrderStatus
    if (this.contract && this.contract.status === enumData.ContractStatus.Cancel.code) {
      this.showAddPo = false
    }
  }

  loadService() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SERVICE.FIND, {}).then((result: any) => {
      this.notifyService.hideloading()
      this.allService = result
    })
  }

  async searchData(reset = false) {
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }
    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }
    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }
    if (this.dataSearch.bidId) {
      where.bidId = this.dataSearch.bidId
    }
    if (this.dataSearch.title) {
      where.title = this.dataSearch.title
    }
    if (this.dataSearch.supplierId) {
      where.supplierId = this.dataSearch.supplierId
    }
    if (this.dataSearch.code) {
      where.code = this.dataSearch.code
    }
    if (this.contract && this.contract.id) {
      where.contractId = this.contract.id
    }
    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.PO.PAGINATION, dataSearch).then(async (data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes !== undefined) {
      if (changes['currentValue'] !== null) {
        if (changes['contract'].currentValue !== undefined) {
          this.searchData()
        }
      }
    }
  }

  handleCancel() {
    this.isVisible = false
  }

  loadBid() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BID.FIND, { status: enumData.BidStatus.HoanTat.code }).then((res) => {
      this.notifyService.hideloading()
      this.dataBid = res || []
    })
  }

  loadSupplier() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.SUPPLIER.FIND, {}).then((result) => {
      this.notifyService.hideloading()
      this.dataSupplier = result || []
    })
  }

  setItemCompletePO(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.COMPLETE, data).then((result) => {
      if (result) {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
      }
    })
  }

  clickRefusePO(data: any) {
    this.data = data
    this.isVisible = true
  }

  onSendRefuse() {
    const data = this.data
    if (this.dataRefuse.reason == null || this.dataRefuse.reason.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do')
      return
    }
    data.reason = this.dataRefuse.reason
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.REFUSE, data).then(() => {
      this.notifyService.showSuccess('Gửi xác nhận từ chối PO thành công.')
      this.isVisible = false
      this.searchData()
      this.notifyService.hideloading()
    })
  }

  clickViewPOModel(object: any) {
    this.dialog
      .open(POModalComponent, { disableClose: false, data: { ...object, isPO: true } })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  clickViewPODetail(object: any) {
    this.dialog
      .open(PODetailComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickAddPo() {
    this.dialog
      .open(AddOrEditPOComponent, { disableClose: false, data: { contractId: this.contract?.id } })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditPOComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  setItemConfirmPO(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.APPROVED, data).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
        this.notifyService.hideloading()
      }
    })
  }

  showForwardCancel(data: any) {
    this.dataObjectUpdate = data
    this.dataObjectUpdate.isSupplier = false
    this.isEditStatus = true
  }

  onSaveDate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.UPDATE_STATUS, this.dataObjectUpdate).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
        this.isEditStatus = false
      }
    })
  }

  clickExportExcel() {
    this.notifyService.showloading()
    let lstDataExport: any[] = []

    //#region header
    const title: any = {
      zenId: 'CẤU HÌNH THÊM MỚI PO',
      title: '',
      anotherRoleIds: '',
      confirmId: '',
      poPaymentId: '',
      editPOId: '',
      cancelPOId: '',
      objectCode: '',
      supplierCode: '',
      bidCode: '',
      deliveryDate: '',
      company: '',
      currency: '',
      email: '',
      phone: '',
      description: '',
      blank: '',
      zenListId: 'CẤU HÌNH CÁC TRƯỜNG DANH SÁCH',
      serviceCode: '',
      name: '',
      unit: '',
      price: '',
      quantity: '',
      descriptionProduct: '',
      note: '',
      blank_percent: '',
      zenListDetailId: 'CẤU HÌNH TIẾN ĐỘ THANH TOÁN',
      namePayment: '',
      percent: '',
      time: '',
      note_percent: '',
    }
    lstDataExport.push(title)
    const numColTable1 = 17
    const header: any = {
      zenId: 'Cột',
      title: 'Tiêu đề PO (*)',
      anotherRoleIds: 'Các thành viên xem PO (Mã Nhân Viên) (*)',
      confirmId: 'Người duyệt PO (Mã Nhân Viên) (*)',
      poPaymentId: 'Người thanh toán PO (Mã Nhân Viên) (*)',
      editPOId: 'Người chỉnh sửa PO (Mã Nhân Viên) (*)',
      cancelPOId: 'Người Hủy PO (Mã Nhân Viên) (*)',
      objectCode: 'Mã đối tượng  (*)',
      supplierCode: 'Mã Nhà cung cấp (*)',
      bidCode: 'Mã Gói thầu',
      deliveryDate: 'Ngày giao hàng (*)',
      company: 'Bên mua hàng (*)',
      currency: 'Đơn vị tiền tệ (*)',
      email: 'Email (*)',
      phone: 'Số điện thoại (*)',
      description: 'Mô tả',
      blank: '',
      zenListId: 'Cột',
      serviceCode: 'Mã vật tư (*)',
      name: 'Tên hàng hóa',
      unit: 'Mã Đơn vị tính (*)',
      quantity: 'Số lượng (*)',
      price: 'Đơn giá (*)',
      descriptionProduct: 'Mô tả hàng hóa',
      note: 'Ghi chú',
      blank_percent: '',
      zenListDetailId: 'Cột',
      namePayment: 'Tên độ thực hiện',
      percent: 'Tiến độ thực hiện (%)',
      time: 'Thời gian (DD/MM/YYY)',
      note_percent: 'Ghi chú',
    }
    lstDataExport.push(header)

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    let date = new Date().toISOString()
    const fileName = 'TEMPLATE_IMPORT_PO_' + date + '.xlsx'
    const sheetName = 'IMPORT PO'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    //#region custom data before export
    let i1 = 1
    wb.Sheets[sheetName]['!merges'] = [
      {
        s: { r: 0, c: 0 },
        e: { r: 0, c: numColTable1 - 2 },
      },
      {
        s: { r: 0, c: numColTable1 + 2 },
        e: { r: 0, c: numColTable1 + 7 },
      },
      {
        s: { r: 0, c: 26 },
        e: { r: 0, c: 30 },
      },
    ]
    ws['!cols'] = [
      { width: 10 },
      { width: 30 },
      { width: 50 },
      { width: 50 },
      { width: 50 },
      { width: 50 },
      { width: 50 },
      { width: 50 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 10 },
      { width: 10 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 10 },
      { width: 10 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
      { width: 30 },
    ]

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any[] = []
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'title',
          'anotherRoleIds',
          'confirmId',
          'poPaymentId',
          'editPOId',
          'cancelPOId',
          'objectCode',
          'supplierCode',
          'bidCode',
          'deliveryDate',
          'company',
          'currency',
          'email',
          'phone',
          'description',
          'blank',
          'zenListId',
          'serviceCode',
          'name',
          'unit',
          'price',
          'quantity',
          'descriptionProduct',
          'note',
          'blank_percent',
          'zenListDetailId',
          'namePayment',
          'percent',
          'time',
          'note_percent',
        ],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      jsonData.shift()
      // Tách và kiểm tra data từng bảng
      const lstDataTable1: any[] = []
      const lstDataTable2: any[] = []
      const lstDataTable3: any[] = []
      let strErr = ''
      if (jsonData.length == 0 ) {
        this.notifyService.showError('File không có PO để import')
        return
      }

      for (const row of jsonData) {
        // add data table 1
        if (row.zenId != null && row.zenId != '') {
          const dataTable1: any = {}
          dataTable1.zenId = (row.zenId + '').trim()
          dataTable1.title = row.title
          dataTable1.anotherRoleIds = row.anotherRoleIds
          dataTable1.confirmId = row.confirmId
          dataTable1.poPaymentId = row.poPaymentId
          dataTable1.editPOId = row.editPOId
          dataTable1.cancelPOId = row.cancelPOId
          dataTable1.objectCode = row.objectCode
          dataTable1.supplierCode = row.supplierCode
          dataTable1.bidCode = row.bidCode
          dataTable1.deliveryDate = row.deliveryDate
          dataTable1.company = row.company
          dataTable1.currency = row.currency
          dataTable1.email = row.email
          dataTable1.phone = row.phone
          dataTable1.description = row.description

          if (dataTable1.title == null || dataTable1.title === '') {
            strErr += 'Tên PO không được để trống<br>'
          }

          if (dataTable1.anotherRoleIds == null || dataTable1.anotherRoleIds === '') {
            strErr += 'Các thành viên xem PO không được để trống<br>'
          }

          if (dataTable1.confirmId == null || dataTable1.confirmId === '') {
            strErr += 'Thành viên duyệt PO không được để trống<br>'
          }

          if (dataTable1.editPOId == null || dataTable1.editPOId === '') {
            strErr += 'Thành viên chỉnh sữa PO không được để trống<br>'
          }

          if (dataTable1.cancelPOId == null || dataTable1.cancelPOId === '') {
            strErr += 'Thành viên Hủy PO không được để trống<br>'
          }

          if (dataTable1.poPaymentId == null || dataTable1.poPaymentId === '') {
            strErr += 'Thành viên thanh toán PO không được để trống<br>'
          }

          if (dataTable1.objectCode == null || dataTable1.objectCode === '') {
            strErr += 'Mã đối tượng không được để trống<br>'
          }

          if (dataTable1.supplierCode == null || dataTable1.supplierCode === '') {
            strErr += 'Mã nhà cung cấp không được để trống<br>'
          }

          if (typeof dataTable1.supplierCode !== 'string' && dataTable1.supplierCode !== null) {
            dataTable1.supplierCode = dataTable1.supplierCode.toString()
          }

          if (typeof dataTable1.bidCode !== 'string' && dataTable1.supplierCode !== null) {
            dataTable1.bidCode = dataTable1.bidCode.toString()
          }

          if (dataTable1.deliveryDate == null || dataTable1.deliveryDate === '') {
            strErr += 'Ngày giao hàng không được để trống<br>'
          }

          if (dataTable1.deliveryDate) {
            if (typeof dataTable1.deliveryDate === 'number') {
              let deliveryDate = this.coreService.excelDateToJSDate(dataTable1.deliveryDate)
              dataTable1.deliveryDate = moment(deliveryDate).format('YYYY-MM-DD')
            } else {
              if (dataTable1.deliveryDate) {
                var dateFormat = 'DD/MM/YYYY'
                let check = moment(dataTable1.deliveryDate, dateFormat, true).isValid()
                if (!check) {
                  strErr += 'Ngày giao hàng Không Tồn Tại \n'
                }
                var dateParts = dataTable1.deliveryDate.split('/')
                var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
                dataTable1.deliveryDate = moment(dateObject).format('YYYY-MM-DD')
              }
            }
          }

          if (dataTable1.phone == null || dataTable1.phone === '') {
            strErr += 'Số điện thoại không được để trống<br>'
          }

          if (dataTable1.email == null || dataTable1.email === '') {
            strErr += 'Email  không được để trống<br>'
          }

          if (dataTable1.currency == null || dataTable1.currency === '') {
            strErr += 'Đơn vị tiền tệ không được để trống<br>'
          }

          if (dataTable1.company == null || dataTable1.currency === '') {
            strErr += 'Bên mua hàng không được để trống<br>'
          }

          lstDataTable1.push(dataTable1)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (const row of jsonData) {
        // add data table 2
        if (row.zenListId != null && row.zenListId != '') {
          const dataTable2: any = {}
          dataTable2.zenListId = (row.zenListId + '').trim()
          dataTable2.serviceCode = row.serviceCode
          dataTable2.name = row.name
          dataTable2.unit = row.unit
          dataTable2.price = row.price
          dataTable2.quantity = row.quantity
          dataTable2.money = row.quantity * row.price
          dataTable2.descriptionProduct = row.descriptionProduct
          dataTable2.note = row.note
          const trade = lstDataTable1.find((c) => c.zenId == dataTable2.zenListId)
          if (!trade) {
            strErr += `Cột [${row.zenListId}] không tồn tại, không xác định thuộc mã nào<br>`
          }

          if (dataTable2.name == null || dataTable2.name === '') {
            strErr += 'Tên hàng hóa không được để trống<br>'
          }

          if (dataTable2.serviceCode == null || dataTable2.serviceCode === '') {
            strErr += 'Mã vật tư  không được để trống<br>'
          }

          if (dataTable2.unit == null || dataTable2.unit === '') {
            strErr += 'Đơn vị tính  không được để trống<br>'
          }
          if (dataTable2.price == null || typeof dataTable2.price !== 'number') {
            strErr += 'Giá tiền là số, không được để trống<br>'
          }

          if (dataTable2.quantity == null || typeof dataTable2.quantity !== 'number') {
            strErr += 'Số lượng là số, không được để trống<br>'
          }

          if (typeof row.serviceCode !== 'string') {
            row.serviceCode = row.serviceCode.toString()
          }
          let item = this.allService.find((c: any) => c.code === row.serviceCode)
          if (!item) {
            this.notifyService.showError('Mã vật tư bị sai')
            return
          }
          let childId = item.id
          dataTable2.serviceId = childId
          for (let index = 0; index < 4; index++) {
            const element = this.allService.find((s: any) => s.id === childId)
            if (element) {
              if (element.level === 1) {
                row.serviceLevel1 = element.id
                break
              } else {
                childId = element.parentId
              }
            } else {
              break
            }
          }
          dataTable2.serviceLevel1 = row.serviceLevel1
          lstDataTable2.push(dataTable2)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }

      for (const row of jsonData) {
        // add data table 3
        if (row.zenListDetailId != null && row.zenListDetailId != '') {
          const dataTable3: any = {}
          dataTable3.zenListDetailId = (row.zenListDetailId + '').trim()
          dataTable3.time = row.time

          if (dataTable3.time) {
            if (typeof dataTable3.time === 'number') {
              let signDateFm = this.coreService.excelDateToJSDate(dataTable3.time)
              dataTable3.time = moment(signDateFm).format('YYYY-MM-DD')
            } else {
              if (dataTable3.time) {
                var dateFormat = 'DD/MM/YYYY'
                let check = moment(dataTable3.time, dateFormat, true).isValid()
                if (!check) {
                  strErr += 'Thời gian Không Tồn Tại \n'
                }
                var dateParts = dataTable3.time.split('/')
                var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
                dataTable3.time = moment(dateObject).format('YYYY-MM-DD')
              }
            }
          }
          dataTable3.namePayment = row.namePayment
          dataTable3.percent = Number(row.percent) * 100
          dataTable3.note_percent = row.note_percent
          const trade = lstDataTable1.find((c) => c.zenId == dataTable3.zenListDetailId)
          if (!trade) {
            strErr += `Cột [${row.zenListDetailId}] không tồn tại, không xác định thuộc mã nào<br>`
          }
          if (dataTable3.percent == null || typeof dataTable3.percent !== 'number') {
            strErr += 'Phần trăm là số, không được để trống<br>'
          }
          lstDataTable3.push(dataTable3)
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return
      }
      lstDataTable1.forEach((item) => {
        lstDataTable2.forEach((x) => {
          if (item.zenId === x.zenListId) {
            item.serviceLevel1 = x.serviceLevel1
          }
        })
      })
      const params = { lstDataTable1, lstDataTable2, lstDataTable3 }
      this.notifyService.showloading()
      this.apiService.post(this.apiService.PO.CREATE_EXCEL, params).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })
    }
  }

  clickExportExcelList() {
    this.notifyService.showloading()
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    const where: any = {}
    if (this.dataSearch.dateFrom) {
      where.dateFrom = this.dataSearch.dateFrom
    }
    if (this.dataSearch.dateTo) {
      where.dateTo = this.dataSearch.dateTo
    }
    if (this.dataSearch.status) {
      where.status = this.dataSearch.status
    }
    if (this.dataSearch.bidId) {
      where.bidId = this.dataSearch.bidId
    }
    if (this.dataSearch.title) {
      where.title = this.dataSearch.title
    }
    if (this.dataSearch.supplierId) {
      where.supplierId = this.dataSearch.supplierId
    }
    if (this.contract && this.contract.id) {
      where.contractId = this.contract.id
    }
    if (this.dataSearch.code) {
      where.code = this.dataSearch.code
    }
    const dataSearch = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
    }
    this.apiService.post(this.apiService.PO.PAGINATION, dataSearch).then(async (data) => {
      if (data) {
        this.notifyService.hideloading()
        if (data && data[0].length > 0) {
          let date = new Date().toISOString()
          const fileName = 'Danh_sach_po_' + date + '.xlsx'
          let dataExcel: any[] = []
          data[0].forEach((s: any) => {
            dataExcel.push({
              'Mã PO': s.code,
              'Tên PO': s.title,
              'Giá trị PO': s.money,
              'Đơn vị tiền tệ': s.currency,
              'Nhà cung cấp': s.supplierName,
              'Gói thầu': s.bidName,
              'Mã HĐ': s.contractCode,
              'Tiến độ thanh toán (theo HĐ)': s.contractPaymentPlanName,
              'Trạng thái': s.statusName,
              'Ngày tạo': s.createdAt ? moment(s.createdAt).format('YYYY-MM-DD') : '',
            })
          })
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataExcel)
          const wb: XLSX.WorkBook = XLSX.utils.book_new()
          ws['!cols'] = [
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
            { width: 30 },
          ]
          XLSX.utils.book_append_sheet(wb, ws, 'Danh sách PO')

          XLSX.writeFile(wb, fileName)
        }
      }
    })
  }

  clickCancelPO(data: any) {
    this.dataObjectCancel = data
    this.isVisibleCancel = true
  }

  onSendCancel() {
    const data = this.dataObjectCancel
    if (this.dataCancel.reason == null || this.dataCancel.reason.trim().length === 0) {
      this.notifyService.showError('Vui lòng nhập lý do')
      return
    }
    data.reason = this.dataCancel.reason
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PO.CANCEL, data).then((result) => {
      this.notifyService.showSuccess(result.message)
      this.isVisibleCancel = false
      this.searchData()
      this.notifyService.hideloading()
    })
  }
}
