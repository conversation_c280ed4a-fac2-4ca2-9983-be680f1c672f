import { Component, Input, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { AsnDetailComponent } from 'src/app/pages/asn/asn-detail/asn-detail.component'
import { enumData } from '../../../../core/enumData'
import { ApiService, CoreService, NotifyService } from '../../../../services'

@Component({
  selector: 'app-po-asn-view',
  templateUrl: './po-asn-view.component.html',
})
export class PoAsnViewComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  dataSearch: any = {}
  dataWarehouse: any[] = []
  dataBranch: any[] = []
  dataPurchasePlan: any[] = []

  @Input() data: any
  constructor(private notifyService: NotifyService, private apiService: ApiService, public coreService: CoreService, private dialog: MatDialog) {}

  ngOnInit() {
    this.loadAllSelect()
  }

  ngOnChanges(changes: any) {
    if (changes !== undefined) {
      if (changes.currentValue !== null) {
        if (changes.data.currentValue !== undefined) {
          this.dataSearch.statusId = enumData.StatusFilter.Active.value
          this.searchData()
        }
      }
    }
  }

  searchData(reset = false) {
    this.loading = true
    if (this.dataSearch.dateFrom > this.dataSearch.dateTo) {
      this.loading = false
      this.notifyService.showError('Từ ngày phải sớm hơn đến ngày')
      return
    }
    if (reset) this.pageIndex = 1
    const where: any = {}
    if (this.data.isContract) where.contractId = this.data.id
    if (this.data.isPO) where.poId = this.data.id
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.warehouseId && this.dataSearch.warehouseId !== '') {
      where.warehouseId = this.dataSearch.warehouseId
    }
    if (this.dataSearch.branchId && this.dataSearch.branchId !== '') {
      where.branchId = this.dataSearch.branchId
    }
    if (this.dataSearch.purchasePlanId && this.dataSearch.purchasePlanId !== '') {
      where.purchasePlanId = this.dataSearch.purchasePlanId
    }
    if (this.dataSearch.dateFrom) where.dateFrom = this.dataSearch.dateFrom
    if (this.dataSearch.dateTo) where.dateTo = this.dataSearch.dateTo

    if (this.dataSearch.statusId > 0) {
      if (this.dataSearch.statusId === enumData.StatusFilter.Active.value) where.isDeleted = false
      if (this.dataSearch.statusId === enumData.StatusFilter.InActive.value) where.isDeleted = true
    }
    const dataSearch = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.ASN.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  viewDetailInbound(object: any) {
    this.dialog.open(AsnDetailComponent, { disableClose: false, data: object })
  }

  loadAllSelect() {
    this.notifyService.showloading()
    const lstPromise = [
      this.apiService.post(this.apiService.WAREHOUSE.FIND, {}),
      this.apiService.post(this.apiService.BRANCH.FIND, {}),
      this.apiService.post(this.apiService.PURCHASE_PLAN.FIND, {}),
    ]
    Promise.all(lstPromise).then((res) => {
      this.notifyService.hideloading()
      this.dataWarehouse = res[0]
      this.dataBranch = res[1]
      this.dataPurchasePlan = res[2]
    })
  }
}
