<nz-collapse>
  <nz-collapse-panel nzHeader="Tìm kiếm" class="ant-bg-antiquewhite">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Mã nhập kho</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input nz-col placeholder="Nhập Mã Kho" [(ngModel)]="dataSearch.code" name="code"
              pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Kho</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn kho" [(ngModel)]="dataSearch.warehouseId"
              name="warehouseId">
              <nz-option *ngFor="let item of dataWarehouse" [nzLabel]="item.code + ' - ' + item.name"
                [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Chi nhánh</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn chi nhánh" [(ngModel)]="dataSearch.branchId"
              name="branchId">
              <nz-option *ngFor="let item of dataBranch" [nzLabel]="item.name" [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Kế hoạch</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Kế hoạch" [(ngModel)]="dataSearch.purchasePlanId"
              name="purchasePlanId">
              <nz-option *ngFor="let item of dataPurchasePlan" [nzLabel]="item.code + ' - ' + item.name"
                [nzValue]="item.id">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" name="dateFrom"
              nzPlaceHolder="Nhập Từ ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" name="dateTo"
              nzPlaceHolder="Nhập Đến ngày">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" class="mt-2 text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>Tìm kiếm
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="24">
    <nz-table class="mb-3" #ajaxTableAsn [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr class="text-nowrap">
          <th>Mã nhập kho</th>
          <th>Mã kế hoạch</th>
          <th>Kho</th>
          <th>Chi nhánh</th>
          <th>Thời gian nhập kho</th>
          <th>Số lượng</th>
          <th>Ghi chú</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTableAsn.data">
          <td>{{ data.code }}</td>
          <td class="mw-25">{{ data.__purchasePlan__ ? data.__purchasePlan__?.code : '' }}</td>
          <td class="mw-25">{{ data.__warehouse__ ? data.__warehouse__?.code : '' }}</td>
          <td class="mw-25">{{ data.__branch__ ? data.__branch__?.code : '' }}</td>
          <td>{{ data.asnDate | date: 'dd/MM/yyyy' }}</td>
          <td>{{ data.quantity | number }}</td>
          <td>{{ data.description }}</td>
          <td>
            <button (click)="viewDetailInbound(data)" nz-tooltip nzTooltipTitle="[Xem] nhập kho" nz-button
              nzType="primary">
              <span nz-icon nzType="eye"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger>
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total>
      {{ range[0] }}-{{ range[1] }} of {{ total }} items
    </ng-template>
  </nz-col>
</nz-row>