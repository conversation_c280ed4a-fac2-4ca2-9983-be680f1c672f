<nz-collapse>
  <nz-collapse-panel nzHeader="Tìm kiếm" class="ant-bg-antiquewhite">
    <nz-row nzGutter="8">
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Mã PO</nz-form-label>
          <nz-form-control nzSpan="24">
            <input nz-input placeholder="Nhập mã PO" [(ngModel)]="dataSearch.code" name="code" pattern=".{1,50}" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">G<PERSON><PERSON> thầ<PERSON></nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn <PERSON><PERSON> thầu" [(ngModel)]="dataSearch.bidId" name="bidId">
              <nz-option *ngFor="let item of dataBid" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left">Nhà cung cấp</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn Nhà cung cấp" [(ngModel)]="dataSearch.supplierId" name="supplierId">
              <nz-option *ngFor="let item of dataSupplier" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo PO - Từ ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateFrom" nzPlaceHolder="Nhập Từ ngày "> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Ngày tạo PO - Đến ngày</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataSearch.dateTo" nzPlaceHolder="Nhập Đến ngày"> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">Trạng thái</nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn trạng thái" [(ngModel)]="dataSearch.status" name="status">
              <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" class="mt-3 text-center">
        <button nz-button (click)="searchData(true)" class="mr-2"><span nz-icon nzType="search"></span>Tìm kiếm</button>
        <button *ngIf="authenticationService.checkPermission([enumRole], action.Export.code)" nz-button (click)="clickExportExcelList()">
          <span nz-icon nzType="download"></span>Tải danh sách
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="24">
    <button
      *ngIf="showAddPo && authenticationService.checkPermission([enumRole], action.Create.code)"
      nz-button
      nzType="primary"
      (click)="clickAddPo()"
      class="mr-2"
    >
      <span nz-icon nzType="plus"></span>Thêm mới
    </button>

    <button class="mr-2" *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)" nz-button (click)="clickExportExcel()">
      <span nz-icon nzType="import"></span>Tải Template Excel
    </button>

    <input
      class="hidden"
      type="file"
      id="file"
      (change)="clickImportExcel($event)"
      onclick="this.value=null"
      *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <label for="file" class="lable-custom-file" *ngIf="authenticationService.checkPermission([enumRole], action.Import.code)">
      <span nz-icon nzType="upload"></span> Nhập excel
    </label>
  </nz-col>
</nz-row>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="24">
    <nz-table
      #ajaxTable
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
      [nzScroll]="{ x: '2000px' }"
      nzTableLayout="fixed"
    >
      <thead>
        <tr>
          <th nzWidth="250px">Mã PO</th>
          <th>Tên PO</th>
          <th>Giá trị PO</th>
          <th>Nhà cung cấp</th>
          <th>Gói thầu</th>
          <th>Mã HĐ</th>
          <th>Tiến độ thanh toán (theo HĐ)</th>
          <th>Trạng thái</th>
          <th>Trạng thái đơn hàng</th>
          <th>Ngày tạo</th>
          <th nzWidth="150px" nzRight>Tác vụ</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of ajaxTable.data">
          <td nzWidth="250px">
            <a (click)="clickViewPODetail(data)" style="cursor: pointer">
              {{ data.code }}
            </a>
          </td>
          <td>{{ data.title }}</td>
          <td class="text-right">{{ data.money | number }} {{ data.currency }}</td>
          <td class="mw-25">{{ data.supplierName }}</td>
          <td class="mw-25">{{ data.bidName }}</td>
          <td class="mw-25">{{ data.contractCode }}</td>
          <td class="mw-25">{{ data.contractPaymentPlanName }}</td>
          <td class="mw-25">
            <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
          </td>
          <td class="mw-25">
            <nz-tag *ngIf="data.statusOrderName" class="tag-status" [nzColor]="data.statusOrderColor"> {{ data.statusOrderName }}</nz-tag>
          </td>
          <td class="mw-25">{{ data.createdAt | date : 'dd/MM/yyyy' }}</td>
          <td nzWidth="150px" nzRight>
            <button
              *ngIf="
                (data.status === enumStatus.Open.code || data.status === enumStatus.DeliveryRefuse.code) &&
                data.isAllowCancelPO &&
                authenticationService.checkPermission([enumRole], action.Delete.code)
              "
              nz-tooltip
              nzTooltipTitle="Hủy PO"
              (click)="clickCancelPO(data)"
              nzPopconfirmPlacement="bottom"
              class="mr-2 mt-2 mb-2"
              nz-button
              nzDanger
            >
              <span nz-icon nzType="stop"></span>
            </button>

            <button
              *ngIf="
                data.status === enumStatus.Open.code &&
                data.isAllowApprovedPO &&
                authenticationService.checkPermission([enumRole], action.Update.code)
              "
              nz-tooltip
              nzTooltipTitle="Duyệt PO"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn duyệt PO ? "
              (nzOnConfirm)="setItemConfirmPO(data)"
              nzPopconfirmPlacement="bottom"
              class="mr-2 mt-2 mb-2 ant-btn-success"
              nz-button
            >
              <span nz-icon nzType="check-circle"></span>
            </button>

            <button
              *ngIf="
                data.status === enumStatus.Open.code && data.isAllowEditPO && authenticationService.checkPermission([enumRole], action.Update.code)
              "
              nz-tooltip
              nzTooltipTitle="Chỉnh sửa thông tin chung"
              class="mr-2 mt-2 mb-2"
              (click)="clickEdit(data)"
              nz-button
              nzType="dashed"
            >
              <span nz-icon nzType="form"></span>
            </button>

            <button
              *ngIf="data.isAllowEditPO && authenticationService.checkPermission([enumRole], action.Update.code)"
              nz-tooltip
              nzTooltipTitle="Cập nhật trạng thái đơn"
              (click)="showForwardCancel(data)"
              class="mr-2 mt-2 mb-2"
              nz-button
              nzType="dashed"
            >
              <span nz-icon nzType="edit"></span>
            </button>

            <button
              *ngIf="
                data.status === enumStatus.Delivery.code &&
                data.isAllowApprovedPO &&
                authenticationService.checkPermission([enumRole], action.Update.code)
              "
              nz-tooltip
              nzTooltipTitle="Hoàn thành PO"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc muốn xác nhận hoàn thành PO ? "
              (nzOnConfirm)="setItemCompletePO(data)"
              nzPopconfirmPlacement="bottom"
              class="mr-2 ant-btn-success mt-2 mb-2"
              nz-button
            >
              <span nz-icon nzType="file-done"></span>
            </button>
            <button
              *ngIf="
                data.status === enumStatus.Delivery.code &&
                data.isAllowApprovedPO &&
                authenticationService.checkPermission([enumRole], action.Update.code)
              "
              nz-tooltip
              nzTooltipTitle="Từ chối nhận PO"
              (click)="clickRefusePO(data)"
              nzPopconfirmPlacement="bottom"
              class="mr-2 mt-2 mb-2"
              nz-button
              nzDanger
            >
              <span nz-icon nzType="warning"></span>
            </button>

            <button
              nz-tooltip
              nzTooltipTitle="Chi tiết"
              class="mt-2 mb-2"
              *ngIf="authenticationService.checkPermission([enumRole], action.View.code)"
              (click)="clickViewPOModel(data)"
              nz-button
            >
              <span nz-icon nzType="eye"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-col>
  <nz-pagination
    class="mt-3"
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisibleCancel" nzTitle="Nhập lý do hủy PO" (nzOnCancel)="isVisibleCancel = false" [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">Lý do</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập lý do !">
            <textarea nz-input rows="5" auto placeholder="Nhập lý do tù chối" [(ngModel)]="dataCancel.reason"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter class="text-center">
    <button [disabled]="!dataCancel.reason || dataCancel.reason === ''" nzDanger (click)="onSendCancel()" nz-button>
      <span nz-icon nzType="delete"></span>Hủy
    </button>
    <button (click)="isVisibleCancel = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Nhập lý do từ chối" (nzOnCancel)="handleCancel()" [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">Lý do</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" nzErrorTip="Vui lòng nhập tên (1-250 kí tự)!">
            <textarea nz-input rows="5" auto placeholder="Nhập lý do" [(ngModel)]="dataRefuse.reason"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter class="text-center">
    <button [disabled]="!dataRefuse.reason || dataRefuse.reason === ''" nzDanger (click)="onSendRefuse()" nz-button>
      <span nz-icon nzType="delete"></span>Hủy
    </button>
    <button (click)="isVisible = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>

<nz-modal [(nzVisible)]="isEditStatus" nzTitle="Cập nhật trạng thái đơn hàng" (nzOnCancel)="isEditStatus = false">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center; display: flex">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label class="text-left" [nzSm]="24" [nzXs]="24">Trạng thái đơn</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-select nzShowSearch [(ngModel)]="dataObjectUpdate.orderStatus" [ngModelOptions]="{ standalone: true }" nzPlaceHolder="Trạng thái đơn">
              <nz-option *ngFor="let item of dataOrderStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter>
    <!-- <button
      *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
      [disabled]="!dataObject.reason || dataObject.reason === ''"
      nzType="primary"
      (click)="onSaveDate()"
      nz-button
    > -->
    <button [disabled]="!dataObjectUpdate.orderStatus" nzType="primary" (click)="onSaveDate()" nz-button>
      <span nz-icon nzType="save" nzTheme="outline"></span>Lưu
    </button>
    <button (click)="isEditStatus = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>
