import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { enumData } from '../../../core/enumData'
import { AuthenticationService, CoreService, NotifyService } from '../../../services'
import { ProcessLineDetailComponent } from './detail/process-line-detail.component'

@Component({
  selector: 'app-process-line',
  templateUrl: './process-line.component.html',
})
export class ProcessLineComponent {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  lstPageSize = enumData.Page.lstPageSize
  total = enumData.Page.total
  loading = false
  dataSearch: any = {}
  data: any = []
  branches: any[] = []
  items: any[] = []
  statuses: any[] = Object.values(enumData.ManufacturingProcessLineStatus)

  constructor(
    private dialog: MatDialog,
    private apiService: ApiFnbService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private router: Router,
    private authenticationService: AuthenticationService
  ) {}

  ngOnInit() {
    this.loadData()
    this.searchData()
  }

  async searchData(reset = false, clearFilter = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    if (clearFilter) this.dataSearch = {}

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    await this.apiService.post(this.apiService.MANUFACTURING.PROCESS_LINE_PAGINATION, dataSearch).then((res: any) => {
      if (res) {
        this.loading = false
        this.total = res[1]
        this.data = res[0]
        this.notifyService.hideloading()
      }
    })
  }

  async loadData() {
    const [branches, items] = await Promise.all([
      this.apiService.post(this.apiService.BRANCH.LOAD_DATA, {}),
      this.apiService.post(this.apiService.ITEM.LOAD_DATA, {}),
    ])
    this.branches = branches || []
    this.items = items || []
  }

  async onChangeBranch(branchId: string): Promise<void> {
    this.dataSearch.branchId = branchId
    if (branchId) {
      this.items = await this.apiService.post(this.apiService.ITEM.LOAD_DATA, {
        branchId,
      })
    }
  }

  viewDetail(data: any) {
    this.dialog
      .open(ProcessLineDetailComponent, { disableClose: false, data: { id: data.id } })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.searchData()
        }
      })
  }
}
