<div class="container">
  <!-- Collapse search section -->
  <nz-collapse nzBordered="false" [nzActive]="true">
    <nz-collapse-panel [nzActive]="true" nzHeader="Tìm kiếm">
      <nz-row nzGutter="12" class="mt-3">
        <nz-col nzSpan="6">
          <nz-select
            (ngModelChange)="onChangeBranch($event)"
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataSearch.branchId"
            name="branchId"
            nzPlaceHolder="Chi nhánh"
          >
            <nz-option *ngFor="let item of branches" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.itemId" name="itemId" nzPlaceHolder="Nguyên vật liệu">
            <nz-option *ngFor="let item of items" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Trạng thái">
            <nz-option *ngFor="let item of statuses" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
          </nz-select>
        </nz-col>
        <!-- Ngày tạo -->
        <nz-col nzSpan="6">
          <nz-range-picker [(ngModel)]="dataSearch.createdAtRange" nzFormat="dd/MM/yyyy" [nzPlaceHolder]="['Từ ngày', 'Đến ngày']"></nz-range-picker>
        </nz-col>
        <nz-col nzSpan="24" class="text-center mt-3">
          <button nz-button nzType="dashed" (click)="searchData(true, true)" class="mr-2"><span nz-icon nzType="redo"></span> Xóa bộ lọc</button>
          <button nz-button class="mr-2" nzType="dashed" (click)="searchData(true)"><span nz-icon nzType="search"></span> Tìm kiếm</button>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <div class="branch">
    <nz-row class="mt-1">
      <nz-col nzSpan="24">
        <nz-table #ajaxTable [nzData]="data" [nzLoading]="loading" [nzFrontPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>Mã lệnh sản xuất</th>
              <th>Chi nhánh</th>
              <th>Số lượng dự kiến</th>
              <th>Sô lượng thực nhập</th>
              <th>Trạng thái</th>
              <th>Ngày tạo</th>
              <th nzRight>Tác vụ</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let data of ajaxTable.data">
              <tr style="cursor: pointer">
                <td>{{ data.code }}</td>
                <td>{{ data.branchName }}</td>
                <td class="text-right">{{ data.planQuantity }}</td>
                <td class="text-right">{{ data.importedQuantity }}</td>
                <td class="text-center">
                  <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
                </td>
                <td class="text-center">
                  {{ data.createdAt | date : 'dd/MM/yyyy HH:mm:ss' }}
                </td>
                <td class="text-center">
                  <button nz-button nzType="primary" (click)="viewDetail(data)">
                    <i nz-icon nzType="eye" nzTheme="outline"></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-row>
    <nz-row justify="center">
      <div nz-col nzSpan="24" class="text-right">
        <nz-pagination
          class="my-2"
          [nzTotal]="total"
          [(nzPageIndex)]="pageIndex"
          [(nzPageSize)]="pageSize"
          (nzPageIndexChange)="searchData()"
          (nzPageSizeChange)="searchData(true)"
          [nzShowTotal]="rangeTemplate"
          nzShowSizeChanger
          [nzPageSizeOptions]="lstPageSize"
        >
        </nz-pagination>
        <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
      </div>
    </nz-row>
  </div>
</div>
