<div class="tab-content" style="padding: 20px">
  <nz-row style="justify-content: center; margin-bottom: 20px">
    <nz-col class="text-center text-heading my-2">{{ 'Chi tiết lệnh sản xuất:' + dataDetail.code }}</nz-col>
  </nz-row>
  <!-- Header info -->
  <nz-card class="filter-card mb-3">
    <nz-row nzGutter="16" nzAlign="middle">
      <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
        <p><strong>Mã chi nhánh:</strong> {{ dataDetail.branchCode }}</p>
        <p><strong>Tên chi nhánh:</strong> {{ dataDetail.branchName }}</p>
      </nz-col>

      <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
        <p><strong>SL đã nhập kho:</strong> {{ dataDetail.importedQuantity }}</p>
        <p><strong>SL cần sản xuất:</strong> {{ dataDetail.planQuantity }}</p>
      </nz-col>
      <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
        <p>
          <strong>Trạng thái:</strong>
          <nz-tag class="tag-status ml-2" [nzColor]="dataDetail.statusColor">
            {{ dataDetail.statusName }}
          </nz-tag>
        </p>
        <p><strong>Ngày tạo:</strong> {{ dataDetail.createdAt | date : 'dd/MM/yyyy HH:mm' }}</p>
      </nz-col>
    </nz-row>
  </nz-card>

  <!-- Production requirements table -->
  <nz-card class="table-card">
    <div class="mt-2 mb-2">
      <b style="font-size: 20px">Danh sách nguyên vật liệu đã xuất</b>
    </div>
    <nz-table
      #detailTable
      [nzData]="dataDetail.__details__"
      [nzBordered]="true"
      [nzShowPagination]="false"
      [nzPageSize]="20"
      [nzScroll]="{ x: '1200px' }"
      nzSize="middle"
    >
      <thead>
        <tr>
          <th nzWidth="120px" nzLeft>Mã</th>
          <th nzWidth="200px">Tên nguyên liệu</th>
          <th nzWidth="120px">Đơn vị tính</th>
          <th nzWidth="100px" class="text-right">SL dự kiến xuất</th>
          <th nzWidth="100px" class="text-right">SL xuất</th>
          <th nzWidth="100px" class="text-right">SL hao hụt</th>
        </tr>
      </thead>

      <tbody>
        <tr *ngFor="let item of detailTable.data; let i = index">
          <td class="text-center">{{ item?.code }}</td>
          <td>
            {{ item?.name }}
          </td>
          <td class="text-center">
            {{ item?.baseUnitName }}
          </td>
          <td class="text-right">
            {{ item.planQuantity | number : '1.0-2' : 'vi' }}
          </td>
          <td class="text-right">
            {{ item.actualQuantity | number : '1.0-2' : 'vi' }}
          </td>
          <td class="text-right">
            {{ item.lossQuantity | number : '1.0-2' : 'vi' }}
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-card>
  <nz-card class="table-card">
    <div class="mt-2 mb-2">
      <b style="font-size: 20px">Danh sách BTP/TP đầu ra</b>
    </div>
    <nz-table
      #productionTable
      [nzData]="dataDetail.__products__"
      [nzBordered]="true"
      [nzShowPagination]="false"
      [nzPageSize]="20"
      [nzScroll]="{ x: '1200px' }"
      nzSize="middle"
    >
      <thead>
        <tr>
          <th nzWidth="120px" nzLeft>Mã SP</th>
          <th nzWidth="200px">Tên sản phẩm</th>
          <th nzWidth="120px">Đơn vị tính</th>
          <th nzWidth="140px" class="text-center">SL tồn</th>
          <th nzWidth="100px" class="text-center">SL nhập dự kiến</th>
          <th nzWidth="100px" class="text-center">SL thực nhập</th>
          <th *ngIf="dataDetail.isDisabled" nzWidth="100px" class="text-center">SL hao hụt</th>
        </tr>
      </thead>

      <tbody>
        <tr *ngFor="let item of productionTable.data; let i = index">
          <td class="text-center">{{ item?.__product__?.code }}</td>
          <td>{{ item?.__product__?.name }}</td>
          <td class="text-center">
            {{ item?.__product__?.__unitOrder__?.name }}
          </td>
          <td class="text-right">
            {{ item.inventory | number : '1.0-2' : 'vi' }}
          </td>
          <td class="text-right">
            {{ item.planQuantity | number : '1.0-2' : 'vi' }}
          </td>
          <td class="text-right">
            <input [disabled]="dataDetail.isDisabled" nz-input type="number" [(ngModel)]="item.importUserInput" class="text-right" />
          </td>
          <td *ngIf="dataDetail.isDisabled" class="text-right">
            {{ item.planQuantity - item.importUserInput | number : '1.0-2' : 'vi' }}
          </td>
        </tr>
      </tbody>
    </nz-table>
    <nz-row class="mt-2" style="justify-content: flex-end">
      <button nz-button nzType="default" class="refresh-btn mr-4" (click)="viewPlan()">
        <i nz-icon nzType="backward"></i>
        Kế hoạch sản xuất
      </button>
      <button
        [disabled]="dataDetail.isDisabled"
        nz-button
        nzType="primary"
        class="refresh-btn"
        nz-popconfirm
        nzPopconfirmTitle="Bạn có chắc chắn muốn nhập BTP/TP không?"
        nzPopconfirmOkText="Đồng ý"
        nzPopconfirmCancelText="Hủy"
        (nzOnConfirm)="onImportConfirm()"
      >
        <i nz-icon nzType="import"></i>
        Nhập BTP/TP
      </button>
    </nz-row>
  </nz-card>
</div>
