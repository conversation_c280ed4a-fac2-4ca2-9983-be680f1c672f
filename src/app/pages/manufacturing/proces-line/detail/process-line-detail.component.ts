import { Component, Inject, OnInit } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { NotifyService } from '../../../../services'

@Component({
  selector: 'app-process-line-detail',
  templateUrl: './process-line-detail.component.html',
})
export class ProcessLineDetailComponent implements OnInit {
  dataDetail: any = {}
  combinedData: any[] = []
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<ProcessLineDetailComponent>,
    private notifyService: NotifyService,
    private apiService: ApiFnbService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initData(this.data.id)
  }

  async initData(id: string): Promise<void> {
    this.notifyService.showloading()
    this.dataDetail = await this.apiService.post(this.apiService.MANUFACTURING.PROCESS_LINE_DETAIL, { id })
    this.notifyService.hideloading()
  }

  async onImportConfirm(): Promise<void> {
    if (this.dataDetail.isDisabled) {
      return
    }
    this.notifyService.showloading()
    const body = {
      processLineId: this.dataDetail.id,
      products: this.dataDetail.__products__.map((item: any) => {
        return {
          productId: item?.__product__?.id,
          quantity: item.importUserInput,
        }
      }),
    }
    await this.apiService.post(this.apiService.MANUFACTURING.IMPORT_PRODUCT, body).then((res) => {
      this.notifyService.hideloading()
      this.notifyService.showSuccess(res.message)
      this.dialogRef.close()
    })
  }

  viewPlan(): void {
    this.dialogRef.close()
    this.router.navigate(['/manufacture/manufacturing-plan-detail'], {
      state: { id: this.dataDetail.planId },
    })
  }
}
