import { DragDropModule } from '@angular/cdk/drag-drop'
import { CommonModule } from '@angular/common'
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatDialogModule } from '@angular/material/dialog'
import { NzBadgeModule } from 'ng-zorro-antd/badge'
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzEmptyModule } from 'ng-zorro-antd/empty'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzImageModule } from 'ng-zorro-antd/image'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzStepsModule } from 'ng-zorro-antd/steps'
import { NzSwitchModule } from 'ng-zorro-antd/switch'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { CURRENCY_MASK_CONFIG, CurrencyMaskConfig, CurrencyMaskModule } from 'ng2-currency-mask'
import { NgxPrintModule } from 'ngx-print'
import { MaterialModule } from '../../app.module'
import { DemandComponent } from './demand/demand.component'
import { ItemComponent } from './item/item.component'
import { ManufacturingPlanDetailComponent } from './manufacturing-plan/detail/manufacturing-plan-detail.component'
import { ManufacturingPlanComponent } from './manufacturing-plan/manufacturing-plan.component'
import { ModalOrderProductNotEnoughConfirmComponent } from './manufacturing-plan/modal-order-product-not-enough-confirm/modal-order-product-not-enough-confirm.component'
import { ManufacturingRoutingModule } from './manufacturing-routing.module'
import { PreparationComponent } from './preparation/preparation.component'
import { PreparationProcessingComponent } from './preparation/processing/preparation-processing.component'
import { ProcessLineDetailComponent } from './proces-line/detail/process-line-detail.component'
import { ProcessLineComponent } from './proces-line/process-line.component'
import { AddOrUpdateRecipeComponent } from './recipe/add-or-update-recipe/add-or-update-recipe.component'
import { SortItemPriorityComponent } from './recipe/add-or-update-recipe/sort-item-priority.component'
import { ItemRecipeComponent } from './recipe/item-recipe/item-recipe.component'
import { ModalSummaryRecipeComponent } from './recipe/modal-summary-recipe/modal-summary-recipe.component'
import { RecipeComponent } from './recipe/recipe.component'
import { FlowchartRecipeComponent } from './recipe/flowchart-recipe/flowchart-recipe.component'

export const CustomCurrencyMaskConfig: CurrencyMaskConfig = {
  allowNegative: false,
  decimal: '.',
  precision: 0,
  prefix: '',
  suffix: '',
  thousands: '.',
  align: '',
}

@NgModule({
  declarations: [
    ProcessLineDetailComponent,
    ProcessLineComponent,
    ManufacturingPlanComponent,
    ManufacturingPlanDetailComponent,
    DemandComponent,
    PreparationProcessingComponent,
    PreparationComponent,
    ModalOrderProductNotEnoughConfirmComponent,
    ItemComponent,
    RecipeComponent,
    AddOrUpdateRecipeComponent,
    ItemRecipeComponent,
    SortItemPriorityComponent,
    ModalSummaryRecipeComponent,
    FlowchartRecipeComponent
  ],
  imports: [
    ManufacturingRoutingModule,
    CommonModule,
    FormsModule,
    NzButtonModule,
    NzTableModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDatePickerModule,
    NzTimePickerModule,
    NzPopconfirmModule,
    NzPaginationModule,
    MaterialModule,
    ReactiveFormsModule,
    CurrencyMaskModule,
    NzInputNumberModule,
    NzUploadModule,
    NzRadioModule,
    NzCascaderModule,
    NzTagModule,
    NzBadgeModule,
    NzImageModule,
    NgxPrintModule,
    NzCollapseModule,
    NzSwitchModule,
    NzDropDownModule,
    NzBreadCrumbModule,
    NzStepsModule,
    MatDialogModule,
    NzPopoverModule,
    NzEmptyModule,
    NzButtonModule,
    DragDropModule,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ManufacturingModule {}
