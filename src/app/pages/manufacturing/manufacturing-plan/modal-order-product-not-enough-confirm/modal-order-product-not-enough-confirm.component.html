<div class="p-4">
  <h2 mat-dialog-title class="text-center">{{ title }}</h2>
  <nz-row class="mt-4">
    <nz-table style="width: 100%" #ajaxTable [nzData]="orderItems" [nzBordered]="true" [nzShowPagination]="false">
      <thead>
        <tr>
          <th nzMinWidth="200px">Tên hàng hoá</th>
          <th nzMinWidth="400px">Đơn vị tính</th>
          <th nzMinWidth="160px">Tồn</th>
          <th nzWidth="160px">Cần đặt</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of orderItems">
          <td>{{ item.name }}</td>
          <td>{{ item.unitName }}</td>
          <td class="text-right">{{ item.inventoryQuantity }}</td>
          <td class="text-right">{{ item.quantity }}</td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>

  <nz-row class="p-4">
    <nz-col nzSpan="24" class="text-center">
      <button nz-button nzType="primary" (click)="onConfirm()">Xác nhận yêu cầu</button>
      <button class="ml-2" nz-button (click)="onCancel()">Huỷ</button>
    </nz-col>
  </nz-row>
</div>
