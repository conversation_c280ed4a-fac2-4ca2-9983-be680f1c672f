import { Component, Inject, OnInit } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiService, NotifyService } from '../../../../services'
import { ApiFnbService } from 'src/app/services/apiFnb.service'

@Component({
  selector: 'app-modal-order-product-not-enough-confirm',
  templateUrl: './modal-order-product-not-enough-confirm.component.html',
})
export class ModalOrderProductNotEnoughConfirmComponent implements OnInit {
  branch: any
  partMaster: any
  orderItems: any[] = []
  totalItems = 0
  title: string = 'Xác nhận đặt các nguyên vật liệu còn thiếu để sản xuất '

  constructor(
    private apiService: ApiFnbService,
    private notifyService: NotifyService,
    public dialogRef: MatDialogRef<ModalOrderProductNotEnoughConfirmComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {}

  ngOnInit(): void {
    this.orderItems = this.data.orderItems || []
    console.log(this.orderItems)
    this.title = this.data.title
    this.calculateTotals()
  }

  calculateTotals(): void {
    this.totalItems = this.orderItems.reduce((sum, item) => sum + (item.quantity || 0), 0)
  }

  async onConfirm(): Promise<void> {
    try {
      this.notifyService.showloading()
      await this.apiService
        .post(this.apiService.ORDER.CREATE_ORDER_BY_MANUFACTURE_NOT_ENOUGH_INVENTORY, {
          ...this.data.body,
        })
        .then((res) => {
          this.notifyService.showSuccess(res.message)
          this.notifyService.hideloading()
          this.dialogRef.close({ confirmed: true })
        })
        .catch((err) => {
          this.notifyService.showError(err.message)
        })
    } catch (error) {
      this.notifyService.showError('Có lỗi xảy ra khi tạo order')
    } finally {
      this.notifyService.hideloading()
    }
  }

  onCancel(): void {
    this.dialogRef.close({ confirmed: false })
  }
}
