<nz-row nzGutter="24">
  <nz-col class="text-center text-heading my-2" nzSpan="24">
    {{ 'Thông tin kế hoạch sản xuất của chi nhánh :' + dataDetail.branchName | uppercase }}
  </nz-col>
</nz-row>

<nz-tabset>
  <nz-tab *ngIf="!!dataDetail" nzTitle="Kế hoạch sản xuất">
    <ng-container>
      <div class="tab-content">
        <!-- Header info -->
        <nz-card class="filter-card mb-3">
          <nz-row nzGutter="16" nzAlign="middle">
            <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
              <p><strong>Mã chi nhánh:</strong> {{ dataDetail.branchCode }}</p>
              <p><strong>Tên chi nhánh:</strong> {{ dataDetail.branchName }}</p>
            </nz-col>

            <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
              <p><strong>SL đã nhập kho:</strong> {{ dataDetail.importedQuantity }}</p>
              <p><strong>SL cần sản xuất:</strong> {{ dataDetail.planQuantity - dataDetail.importedQuantity }}</p>
            </nz-col>
            <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
              <p>
                <strong>Trạng thái:</strong>
                <nz-tag class="tag-status ml-2" [nzColor]="dataDetail.statusColor">
                  {{ dataDetail.statusName }}
                </nz-tag>
              </p>
              <p><strong>Ngày tạo:</strong> {{ dataDetail.createdAt | date : 'dd/MM/yyyy HH:mm' }}</p>
            </nz-col>
          </nz-row>
        </nz-card>

        <!-- Production requirements table -->
        <nz-card class="table-card">
          <div class="mt-2 mb-2">
            <b style="font-size: 20px">Danh sách sản phẩm cần sản xuất</b>
          </div>
          <nz-table
            #productionTable
            [nzData]="dataDetail.__products__"
            [nzBordered]="true"
            [nzShowPagination]="false"
            [nzPageSize]="20"
            [nzScroll]="{ x: '1200px' }"
            nzSize="middle"
          >
            <thead>
              <tr>
                <th nzWidth="50px" nzLeft>
                  <label nz-checkbox [(ngModel)]="selectAll" (ngModelChange)="onSelectAll($event)"></label>
                </th>
                <th nzWidth="120px" nzLeft>Mã SP</th>
                <th nzWidth="200px">Tên sản phẩm</th>
                <th nzWidth="120px">Đơn vị tính</th>
                <th nzWidth="100px" class="text-center">SL kế hoạch</th>
                <th nzWidth="100px" class="text-center">SL đã nhập kho</th>
                <th nzWidth="100px" class="text-center">SL đang sản xuất</th>
                <th nzWidth="100px" class="text-center">SL cần sản xuất</th>
                <th nzWidth="100px" class="text-center">SL xuất</th>
              </tr>
            </thead>

            <tbody>
              <tr *ngFor="let item of productionTable.data; let i = index">
                <td class="text-center">
                  <label
                    [disabled]="item.neededQuantity <= 0"
                    (ngModelChange)="onSelectItem(item, $event)"
                    nz-checkbox
                    [(ngModel)]="item.selected"
                  ></label>
                </td>
                <td class="text-center">{{ item.__product__?.code }}</td>
                <td>
                  <div *ngIf="!item.isHaveRecipe" nz-tooltip nzTooltipTitle="Chưa thiết lập công thức">
                    {{ item.__product__?.name }}
                    <nz-tag nzColor="red">
                      <span nz-icon nzType="question-circle" nzTheme="outline"></span>
                    </nz-tag>
                  </div>
                  <div *ngIf="item.isHaveRecipe">
                    {{ item.__product__?.name }}
                  </div>
                </td>
                <td class="text-center">
                  {{ item.__product__?.__unitOrder__?.name }}
                </td>
                <td class="text-right">
                  {{ item.quantity | number : '1.0-2' : 'vi' }}
                </td>
                <td class="text-right">
                  {{ item.importedQuantity | number : '1.0-2' : 'vi' }}
                </td>
                <td class="text-right">
                  {{ item.processQuantity | number : '1.0-2' : 'vi' }}
                </td>
                <td class="text-right">
                  {{ item.neededQuantity | number : '1.0-2' : 'vi' }}
                </td>

                <td class="text-right">
                  <input
                    (ngModelChange)="onChangeQuantity($event, item)"
                    [disabled]="item.neededQuantity <= 0"
                    nz-input
                    type="number"
                    [(ngModel)]="item.quantityUserInput"
                    class="text-right"
                  />
                </td>
              </tr>
            </tbody>
          </nz-table>
          <nz-row class="mt-2">
            <button (click)="loadDsNvlNeeded()" nz-button [disabled]="isDisableCalNvl()" nzType="primary" class="refresh-btn">
              <i nz-icon nzType="play-circle"></i>
              Tính toán DS nguyên vật liệu
            </button>
          </nz-row>
        </nz-card>
        <nz-card class="table-card" *ngIf="combinedData.length > 0">
          <div class="mt-2 mb-2">
            <b style="font-size: 20px">Danh sách NVL/VTTH để sản xuất</b>
          </div>
          <nz-table
            nzLoading="{{ isLoading }}"
            #productionTable
            [nzData]="combinedData"
            [nzBordered]="true"
            [nzShowPagination]="false"
            [nzPageSize]="20"
            [nzScroll]="{ x: '1200px' }"
            nzSize="middle"
          >
            <thead>
              <tr>
                <th nzWidth="120px" nzLeft>Mã SP</th>
                <th nzWidth="200px">Tên sản phẩm</th>
                <th nzWidth="120px">Đơn vị tính</th>
                <th nzWidth="140px" class="text-center">SL tồn</th>
                <th nzWidth="100px" class="text-center">SL xuất</th>
                <th nzWidth="100px" class="text-center">SL thiếu</th>
                <th nzWidth="100px" class="text-center">SL thực xuất</th>
              </tr>
            </thead>

            <tbody>
              <tr
                nz-tooltip
                nzTooltipTitle="{{ item.quantityNeedOrder ? 'Không đủ tồn kho để xuất' : '' }}"
                *ngFor="let item of productionTable.data; let i = index"
                [ngClass]="{
                  'bg-danger-border': item.quantityNeedOrder > 0
                }"
              >
                <td class="text-start">
                  <div style="display: flex; align-items: end; justify-content: start; gap: 5px">
                    <div>{{ item?.code }}</div>
                    <div *ngIf="!!item.isWarningNotInBranch" nz-tooltip nzTooltipTitle="Chưa thêm hàng hoá vào chi nhánh">
                      {{ item.productName }} <nz-tag nzColor="red"> <span nz-icon nzType="question-circle" nzTheme="outline"></span> </nz-tag>
                    </div>
                  </div>
                </td>
                <td>{{ item?.name }}</td>
                <td class="text-center">
                  {{ item?.baseUnitName }}
                </td>
                <td class="text-right">
                  {{ item.inventory | number : '1.0-2' : 'vi' }}
                </td>
                <td class="text-right">
                  {{ item.quantitative | number : '1.0-2' : 'vi' }}
                </td>
                <td class="text-right">
                  {{ item.quantityNeedOrder | number : '1.0-2' : 'vi' }}
                </td>
                <td class="text-right">
                  <input
                    (ngModelChange)="onChangeQuantitative($event, item)"
                    [disabled]="item.quantityNeedOrder > 0"
                    nz-input
                    type="number"
                    [(ngModel)]="item.quantitativeUserInput"
                    class="text-right"
                  />
                </td>
              </tr>
            </tbody>
          </nz-table>
          <nz-row class="mt-2">
            <button (click)="exportItemNeeded()" nz-button [disabled]="isDisableCalNvl()" nzType="primary" class="refresh-btn">
              <i nz-icon nzType="export"></i>
              Xác nhận xuất DS nguyên vật liệu / Vật tư tiêu hao
            </button>
            <button [disabled]="!isOrderNotEnough" (click)="orderProductNotEnough()" nz-button nzType="default" class="btn-primary ml-4">
              <i nz-icon nzType="add"></i>
              Tạo đơn hàng cho những nguyên vật liệu thiếu
            </button>
          </nz-row>
        </nz-card>
      </div>
    </ng-container>
  </nz-tab>
  <nz-tab *ngIf="!!processLineData" nzTitle="Danh sách lệnh sản xuất">
    <nz-row class="mt-2">
      <nz-col nzSpan="24">
        <nz-table
          #ajaxTable
          [nzData]="processLineData"
          [nzLoading]="processLineLoading"
          [nzScroll]="{ x: '1400px',  }"
          [nzFrontPagination]="false"
          nzBordered
        >
          <thead>
            <tr>
              <th nzWidth="120px">Mã lệnh sản xuất</th>
              <th nzWidth="150px">Chi nhánh</th>
              <th nzWidth="200px">Số lượng dự kiến</th>
              <th nzWidth="100px">Sô lượng thực nhập</th>
              <th nzWidth="200px">Trạng thái</th>
              <th nzWidth="200px">Ngày tạo</th>
              <th nzRight nzWidth="100px">Tác vụ</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let data of ajaxTable.data">
              <tr style="cursor: pointer">
                <td>{{ data.code }}</td>
                <td>{{ data.branchName }}</td>
                <td class="text-right">{{ data.planQuantity }}</td>
                <td class="text-right">{{ data.importedQuantity }}</td>
                <td class="text-center">
                  <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
                </td>
                <td class="text-center">
                  {{ data.createdAt | date : 'dd/MM/yyyy HH:mm:ss' }}
                </td>
                <td class="text-center" nzRight>
                  <button nz-button nzType="primary" (click)="viewDetailProcessLine(data)">
                    <i nz-icon nzType="eye" nzTheme="outline"></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-row>
  </nz-tab>
  <nz-tab *ngIf="dataDetail?.processLineDetailSummary?.length > 0" nzTitle="Danh sách nguyên vật liệu hao hụt">
    <nz-row class="mt-2">
      <nz-col nzSpan="24">
        <nz-table
          #detailTable
          [nzData]="dataDetail?.processLineDetailSummary"
          [nzBordered]="true"
          [nzShowPagination]="false"
          [nzPageSize]="20"
          [nzScroll]="{ x: '1200px' }"
          nzSize="middle"
        >
          <thead>
            <tr>
              <th nzWidth="120px" nzLeft>Mã</th>
              <th nzWidth="200px">Tên nguyên liệu</th>
              <th nzWidth="120px">Đơn vị tính</th>
              <th nzWidth="100px" class="text-right">SL dự kiến xuất</th>
              <th nzWidth="100px" class="text-right">SL xuất</th>
              <th nzWidth="100px" class="text-right">SL hao hụt</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngFor="let item of dataDetail?.processLineDetailSummary; let i = index">
              <td class="text-center">{{ item?.productCode }}</td>
              <td>
                {{ item?.productName }}
              </td>
              <td class="text-center">
                {{ item?.baseUnitName }}
              </td>
              <td class="text-right">
                {{ item.planQuantity | number : '1.0-2' : 'vi' }}
              </td>
              <td class="text-right">
                {{ item.actualQuantity | number : '1.0-2' : 'vi' }}
              </td>
              <td class="text-right">
                {{ item.lossQuantity | number : '1.0-2' : 'vi' }}
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-row>
  </nz-tab>
</nz-tabset>
