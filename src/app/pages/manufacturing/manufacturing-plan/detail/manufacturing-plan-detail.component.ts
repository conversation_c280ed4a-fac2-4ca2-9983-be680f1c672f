import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { NotifyService } from '../../../../services'
import { ProcessLineDetailComponent } from '../../proces-line/detail/process-line-detail.component'
import { ModalOrderProductNotEnoughConfirmComponent } from '../modal-order-product-not-enough-confirm/modal-order-product-not-enough-confirm.component'

interface ProductGroup {
  id: string
  name: string
}

@Component({
  selector: 'app-manufacturing-plan-detail',
  templateUrl: './manufacturing-plan-detail.component.html',
})
export class ManufacturingPlanDetailComponent implements OnInit {
  branches: any[] = []
  branchProducts: any[] = []
  dataDetail: any = {}
  selectAll: boolean = false
  itemsNeeded: any[] = []
  materialsNeeded: any[] = []
  combinedData: any[] = []
  processLineData: any[] = []
  processLineLoading: boolean = false
  isLoading: boolean = false
  isOrderNotEnough: boolean = false
  processLineDetailData: any = []
  processLineDetailDataLoading: boolean = false
  afterTitle: string = ''

  constructor(private dialog: MatDialog, private notifyService: NotifyService, private apiService: ApiFnbService, private router: Router) {}

  ngOnInit(): void {
    this.initData(history.state.id)
  }

  async initData(id: string): Promise<void> {
    this.notifyService.showloading()
    const dataSearch = {
      where: { planId: id },
      skip: 0,
      take: 100000000000,
    }
    const [planDetailRes, processLineRes] = await Promise.all([
      this.apiService.post(this.apiService.MANUFACTURING.PLAN_DETAIL, { id }),
      await this.apiService.post(this.apiService.MANUFACTURING.PROCESS_LINE_PAGINATION, dataSearch),
    ])
    this.dataDetail = planDetailRes
    this.processLineData = processLineRes?.[0] || []
    this.notifyService.hideloading()
  }

  onSelectAll(ev: any): void {
    this.selectAll = ev
    this.dataDetail.__products__
      ?.filter((x: any) => x.neededQuantity > 0)
      .forEach((item: any) => {
        item.selected = ev
      })
  }
  onSelectItem(item: any, eve: any): void {
    item.selected = eve
    this.selectAll = this.dataDetail?.__products__?.every((item: any) => item.selected)
  }

  isDisableCalNvl(): boolean {
    return this.dataDetail?.__products__?.length === 0 || this.dataDetail?.__products__?.every((item: any) => !item.selected)
  }

  async loadDsNvlNeeded(): Promise<void> {
    this.notifyService.showloading()
    const isHasOverMax = this.dataDetail.__products__.filter((item: any) => item.quantityUserInput > item.neededQuantity && item.checked).length > 0
    if (isHasOverMax) {
      this.notifyService.showError('Số lượng sản xuất không được lớn hơn số lượng cần thiết')
      return
    }

    const itemsSelected = this.dataDetail.__products__.filter((item: any) => item.selected)
    this.afterTitle = itemsSelected.reduce((acc: any, item: any) => {
      return acc + `${item.__product__?.name} với SL : (${item.quantityUserInput}).`
    }, '')

    this.isLoading = true

    await this.apiService
      .post(this.apiService.MANUFACTURING.LOAD_NVL_NEEDED, {
        branchId: this.dataDetail.branchId,
        planProducts: this.dataDetail.__products__.map((item: any) => ({
          planProductId: item.id,
          quantity: item.quantityUserInput,
        })),
      })
      .then((res) => {
        this.itemsNeeded = res?.itemsNeeded
        this.materialsNeeded = res?.materialsNeeded
        this.combinedData = this.itemsNeeded.concat(this.materialsNeeded)
        this.isLoading = false
        this.isOrderNotEnough = this.combinedData.some((item) => {
          return item.quantityNeedOrder > 0
        })
      })
      .catch((err) => {
        this.isLoading = false
        this.notifyService.showError(err.message || 'Lỗi khi tải danh sách nguyên vật liệu cần thiết.')
      })

    this.notifyService.hideloading()
  }

  async exportItemNeeded(): Promise<void> {
    const body = {
      planId: this.dataDetail.id,
      planProducts: this.dataDetail.__products__
        .filter((item: any) => item.selected)
        .map((item: any) => ({
          planProductId: item.id,
          quantity: item.quantityUserInput,
        })),
      products: this.combinedData.map((item) => {
        return {
          itemId: item.itemId,
          materialId: item.materialId,
          quantity: item.quantitativeUserInput,
          planQuantity: item.quantitative,
        }
      }),
    }
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.MANUFACTURING.EXPORT_TO_MANUFACTURING, body).then((res) => {
      this.notifyService.hideloading()
      this.notifyService.showSuccess(res.message || 'Xuất nguyên vật liệu thành công!.')
      this.combinedData = []

      this.initData(this.dataDetail.id)
    })
  }

  viewDetailProcessLine(data: any) {
    this.dialog
      .open(ProcessLineDetailComponent, { disableClose: false, data: { id: data.id } })
      .afterClosed()
      .subscribe((result) => {
        this.initData(this.dataDetail.id)
      })
  }

  onChangeQuantity(ev: any, item: any): void {
    this.combinedData = []
  }
  orderProductNotEnough() {
    const productsNotEnough = this.combinedData.filter((item) => item.quantityNeedOrder > 0)
    const materials = productsNotEnough
      .filter((item) => item.materialId)
      .map((item) => ({
        materialId: item.materialId,
        quantity: item.quantitativeUserInput,
      }))
    const items = productsNotEnough
      .filter((item) => item.itemId)
      .map((item) => ({
        itemId: item.itemId,
        quantity: item.quantitativeUserInput,
      }))
    const body = {
      materials,
      items,
      branchId: this.dataDetail.branchId,
    }

    this.dialog
      .open(ModalOrderProductNotEnoughConfirmComponent, {
        data: {
          orderItems: productsNotEnough.map((item) => ({
            name: item.productName || item.name,
            unitName: item.baseUnitName,
            inventoryQuantity: item.inventory,
            quantity: item.quantityNeedOrder,
          })),
          body,
          title: `Xác nhận đặt các nguyên vật liệu còn thiếu để sản xuất ${this.afterTitle} của chi nhánh ${this.dataDetail.branchName}`,
        },
      })
      .afterClosed()
      .subscribe((result) => {
        if (result?.confirmed) {
          this.isOrderNotEnough = false
        }
      })
  }
  onChangeQuantitative(ev: any, item: any): void {
    item.quantitativeUserInput = ev
    this.isOrderNotEnough = this.combinedData.some((item) => {
      return item.quantityNeedOrder > 0
    })
  }
}
