import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { enumData } from '../../../core/enumData'
import { User } from '../../../models/user.model'
import { AuthenticationService, CoreService, NotifyService } from '../../../services'
declare var Object: any
@Component({
  selector: 'app-manufacturing-plan',
  templateUrl: './manufacturing-plan.component.html',
})
export class ManufacturingPlanComponent implements OnInit {
  stateId = 'app-manufacturing-plan'
  currentUser: User | undefined
  enumData = null
  modalTitle: any
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  dataFilterStatus: any
  listOfData: any = []
  dataSearch: any = {}
  loading = true
  branches: any[] = []
  items: any[] = []

  constructor(
    private notifyService: NotifyService,
    private apiService: ApiFnbService,
    private coreService: CoreService,
    private router: Router,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x?.enumData))
  }

  async searchData(reset = false, clearFilter = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    if (clearFilter) this.dataSearch = {}

    const dataSearch = {
      where: {
        ...this.dataSearch,
      },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.MANUFACTURING.PAGINATION_PLAN, dataSearch).then((data: any) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  ngOnInit() {
    this.dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
    this.dataSearch.isDeleted = enumData.StatusFilter.Active.value
    this.searchData()
    this.initData()
  }
  async initData() {
    const [branches, items] = await Promise.all([
      this.apiService.post(this.apiService.BRANCH.LOAD_DATA, {}),
      this.apiService.post(this.apiService.ITEM.LOAD_DATA, {}),
    ])
    this.branches = branches
    this.items = items
  }

  viewDetail(id: any) {
    this.router.navigate(['/manufacture/manufacturing-plan-detail'], {
      state: {
        id,
      },
    })
  }
}
