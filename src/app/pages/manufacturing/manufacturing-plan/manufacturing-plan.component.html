<nz-collapse nzBordered="false" [nzActive]="true">
  <nz-collapse-panel [nzActive]="true" nzHeader="Tìm kiếm">
    <nz-row nzGutter="24">
      <nz-col nzSpan="12" class="mt-3">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.branchId" name="branchId" class="mb-1" nzPlaceHolder="Chi nhánh">
          <nz-option *ngFor="let item of branches" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-col>

      <nz-col nzSpan="12" class="mt-3">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.itemId" name="itemId" class="mb-1" nzPlaceHolder="Chọn nguy<PERSON><PERSON> vật liệu">
          <nz-option *ngFor="let item of items" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-col>

      <nz-col nzSpan="24" class="text-center mt-3">
        <button nz-button nzType="dashed" (click)="searchData(true, true)" class="mr-2"><span nz-icon nzType="redo"></span> Xóa bộ lọc</button>
        <button class="mr-2" nz-button nzType="dashed" (click)="searchData()"><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<div nz-row class="mt-3">
  <nz-table
    class="mb-3"
    nz-col
    nzSpan="24"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
  >
    <thead>
      <tr>
        <th>Mã chi nhánh</th>
        <th>Chi nhánh</th>
        <th>Số lượng đã nhập kho</th>
        <th>Số lượng đang sản xuất</th>
        <th>Tổng số lượng dự kiến</th>
        <th>Số lượng cần sản xuất còn lại</th>
        <th>Trạng thái</th>
        <th>Ngày tạo</th>
        <th nzWidth="150px">Tác vụ</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="mw-25 text-center">{{ data.branchCode }}</td>
        <td class="mw-25">{{ data.branchName }}</td>
        <td class="mw-25 text-right">
          <b>{{ data.importedQuantity | number : '1.0-2' : 'vi' }}</b>
        </td>
        <td class="mw-25 text-right">
          <b>{{ data.processQuantity | number : '1.0-2' : 'vi' }}</b>
        </td>
        <td class="mw-25 text-right">
          <b>{{ data.planQuantity | number : '1.0-2' : 'vi' }}</b>
        </td>
        <td class="mw-25 text-right">
          <b>{{ data.planQuantity - data.importedQuantity | number : '1.0-2' : 'vi' }}</b>
        </td>
        <td class="text-center">
          <nz-tag class="tag-status" [nzColor]="data.statusColor">
            {{ data.statusName }}
          </nz-tag>
        </td>
        <td class="text-center">
          {{ data.createdAt | date : 'dd/MM/yyyy' }}
        </td>
        <td class="text-center">
          <button nz-button nzType="primary" (click)="viewDetail(data.id)"><span nz-icon nzType="eye"></span></button>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <div nz-col [nzSpan]="24" class="text-right">
    <nz-pagination
      class="my-2"
      *ngIf="total > 0"
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> Dòng {{ range[0] }} đến {{ range[1] }} trong {{ total }} dòng </ng-template>
  </div>
</div>
