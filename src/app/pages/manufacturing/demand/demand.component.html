<!-- Header section -->
<nz-row class="mb-3">
  <nz-col nzSpan="24">
    <div class="page-header">
      <h2 class="page-title">
        <i nz-icon nzType="bar-chart" nzTheme="outline"></i>
        <PERSON><PERSON> c<PERSON>u sản xuất theo chi nh<PERSON>h
      </h2>
    </div>
  </nz-col>
</nz-row>

<!-- Filter section -->
<nz-card class="filter-card mb-3">
  <nz-row nzGutter="24" nzAlign="middle">
    <nz-col nzSpan="12">
      <label class="filter-label">Chi nhánh <span class="required">*</span></label>
      <nz-select
        (ngModelChange)="onChangeBranch($event)"
        nzShowSearch
        nzAllowClear="false"
        [(ngModel)]="dataObject.branchId"
        nzPlaceHolder="Chọn chi nhánh"
        class="full-width"
      >
        <nz-option *ngFor="let item of branches" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="12">
      <label class="filter-label">Chọn nguyên vật liệu </label>
      <nz-select
        nzMode="multiple"
        nzAllowClear="true"
        (ngModelChange)="onChangeItem($event)"
        nzShowSearch
        name="itemIds"
        [(ngModel)]="dataObject.itemIds"
        nzPlaceHolder="Chọn nguyên vật liệu"
        class="full-width"
      >
        <nz-option *ngFor="let item of items" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"></nz-option>
      </nz-select>
    </nz-col>
    <nz-col nzSpan="24" style="margin-top: auto; text-align: center; margin-top: 10px">
      <button nz-button [disabled]="checkSelectLeastOne()" nzType="primary" (click)="createManufacturingPlan()">
        <span nz-icon nzType="plus"></span>
        Tạo kế hoạch sản xuất
      </button>
    </nz-col>
  </nz-row>
</nz-card>

<!-- Summary cards -->
<nz-row nzGutter="16" class="mb-3" *ngIf="dataObject.branchId">
  <nz-col nzSpan="6">
    <nz-card class="summary-card total-products">
      <nz-statistic nzTitle="Tổng sản phẩm" [nzValue]="branchProducts.length" [nzValueStyle]="{ color: '#1890ff' }">
        <ng-template #nzPrefix>
          <i nz-icon nzType="appstore" nzTheme="outline"></i>
        </ng-template>
      </nz-statistic>
    </nz-card>
  </nz-col>

  <nz-col nzSpan="6">
    <nz-card class="summary-card need-production">
      <nz-statistic nzTitle="Cần sản xuất" [nzValue]="branchProducts.length" [nzValueStyle]="{ color: '#f5222d' }">
        <ng-template #nzPrefix>
          <i nz-icon nzType="exclamation-circle" nzTheme="outline"></i>
        </ng-template>
      </nz-statistic>
    </nz-card>
  </nz-col>
</nz-row>

<!-- Production requirements table -->
<nz-card class="table-card" *ngIf="dataObject.branchId">
  <div class="table-header">
    <h3>Chi tiết nhu cầu sản xuất</h3>
  </div>

  <nz-table
    #productionTable
    [nzData]="branchProducts"
    [nzBordered]="true"
    [nzShowPagination]="false"
    [nzPageSize]="20"
    [nzScroll]="{ x: '1200px' }"
    nzSize="middle"
    nzLoading="{{ isLoading }}"
  >
    <thead>
      <tr>
        <th nzWidth="50px" nzLeft>
          <label nz-checkbox [(ngModel)]="selectAll" (ngModelChange)="onSelectAll($event)"></label>
        </th>
        <th nzWidth="120px" nzLeft>Mã SP</th>
        <th nzWidth="200px">Tên sản phẩm</th>
        <th nzWidth="120px">Đơn vị tính</th>
        <th nzWidth="100px" class="text-center">Tồn</th>
        <th nzWidth="100px" class="text-center">Min</th>
        <th nzWidth="100px" class="text-center">Max</th>
        <th nzWidth="100px" class="text-center">Đang chờ sản xuất</th>
        <th nzWidth="100px" class="text-center">Cần sản xuất</th>
      </tr>
    </thead>

    <tbody>
      <tr (click)="onSelectItem(!item.selected, item)" *ngFor="let item of productionTable.data; let i = index">
        <td class="text-center" nzLeft>
          <label nz-checkbox (ngModelChange)="onSelectItem($event, item)" [(ngModel)]="item.selected"></label>
        </td>
        <td class="product-code">{{ item.productCode }}</td>
        <td>
          <div *ngIf="!item.isHaveRecipe" nz-tooltip nzTooltipTitle="Chưa thiết lập công thức">
            {{ item.productName }}
            <nz-tag nzColor="red">
              <span nz-icon nzType="question-circle" nzTheme="outline"></span>
            </nz-tag>
          </div>
          <div *ngIf="item.isHaveRecipe">
            {{ item.productName }}
          </div>
        </td>
        <td>
          {{ item.productUnitName }}
        </td>
        <td class="text-center">{{ item.inventoryQuantity | number : '1.0-2' : 'vi' }}</td>
        <td class="text-center">{{ item.min | number : '1.0-2' : 'vi' }}</td>
        <td class="text-center">{{ item.max | number : '1.0-2' : 'vi' }}</td>
        <td class="text-right">
          {{ item.waitManufacturingQuantity | number : '1.0-2' : 'vi' }}
        </td>
        <td class="text-right" nzRight>
          <input
            max="{{ item.needManufacturingQuantity }}"
            nz-input
            type="number"
            class="form-control"
            [(ngModel)]="item.quantityUserInput"
            (click)="$event.stopPropagation()"
          />
        </td>
      </tr>
    </tbody>
  </nz-table>

  <nz-pagination
    class="my-2"
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="loadProductionRequirements()"
    (nzPageSizeChange)="loadProductionRequirements()"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
    [nzPageSizeOptions]="lstPageSize"
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-card>

<!-- Empty state -->
<nz-empty *ngIf="!dataObject.branchId" nzNotFoundImage="simple" nzNotFoundContent="Vui lòng chọn chi nhánh để xem nhu cầu sản xuất"> </nz-empty>
