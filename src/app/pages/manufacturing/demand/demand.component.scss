// Page header
.page-header {
  .page-title {
    margin: 0 0 8px 0;
    color: #262626;
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .page-description {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
  }
}

// Filter section
.filter-card {
  .filter-label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #262626;

    .required {
      color: #f5222d;
    }
  }

  .full-width {
    width: 100%;
  }

  .refresh-btn {
    width: 100%;
    margin-top: 20px;
  }
}

// Summary cards
.summary-card {
  text-align: center;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  &.total-products {
    border-left: 4px solid #1890ff;
  }

  &.need-production {
    border-left: 4px solid #f5222d;
  }

  &.sufficient-stock {
    border-left: 4px solid #52c41a;
  }

  &.total-required {
    border-left: 4px solid #722ed1;
  }
}

// Table section
.table-card {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #262626;
      font-size: 18px;
      font-weight: 600;
    }

    .table-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// Table styling
.ant-table {
  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  // Row priority styling
  .urgent-row {
    background-color: #fff2f0;
    border-left: 3px solid #f5222d;
  }

  .high-row {
    background-color: #fff7e6;
    border-left: 3px solid #fa8c16;
  }

  .normal-row {
    background-color: #f6ffed;
    border-left: 3px solid #52c41a;
  }

  // Product info
  .product-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #1890ff;
  }

  .product-info {
    .product-name {
      display: block;
      font-weight: 500;
      color: #262626;
    }

    .product-unit {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  // Stock indicators
  .inventory-current {
    font-weight: 600;

    &.low-stock {
      color: #f5222d;
      background-color: #fff2f0;
      padding: 2px 6px;
      border-radius: 4px;
    }
  }

  .sales-rate {
    color: #1890ff;
    font-weight: 500;
  }

  .forecast-sales {
    color: #722ed1;
    font-weight: 500;
  }

  .required-production {
    font-weight: 600;

    &.urgent-production {
      color: #f5222d;
      background-color: #fff2f0;
      padding: 2px 6px;
      border-radius: 4px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .summary-card {
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .table-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .filter-card {
    .ant-row {
      flex-direction: column;
    }

    .ant-col {
      width: 100% !important;
      margin-bottom: 12px;
    }
  }
}

// Loading states
.loading-card {
  text-align: center;
  padding: 40px;
  color: #8c8c8c;
}

.disableRow {
  background-color: #f5f5f5 !important;
  color: #bfbfbf !important;
  cursor: not-allowed !important;

  &:hover {
    box-shadow: none !important;
  }
}
