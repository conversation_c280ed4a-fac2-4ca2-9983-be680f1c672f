import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { enumData } from '../../../core/enumData'
import { NotifyService } from '../../../services'

interface ProductGroup {
  id: string
  name: string
}

@Component({
  selector: 'app-demand',
  templateUrl: './demand.component.html',
  styleUrls: ['./demand.component.scss'],
})
export class DemandComponent implements OnInit {
  dataObject: any = {
    branchId: '',
    itemId: '',
  }
  items: any[] = []
  branches: any[] = []
  lstPageSize = enumData.Page.lstPageSize
  branchProducts: any[] = []
  total: number = 0
  pageIndex: any = 1
  pageSize: any = enumData.Page.pageSize
  selectAll: boolean = false
  isLoading: boolean = false

  constructor(private notifyService: NotifyService, private apiService: ApiFnbService, private router: Router) {}

  ngOnInit(): void {
    this.initData()
  }

  async initData(): Promise<void> {
    const [branches] = await Promise.all([this.apiService.post(this.apiService.BRANCH.LOAD_DATA, {}), ,])
    this.branches = branches
  }

  onChangeItem(itemIds: string) {
    console.log({
      itemIds,
    })
    this.dataObject.itemIds = itemIds
    if (this.dataObject.branchId || itemIds) {
      this.loadProductionRequirements()
    }
  }
  async onChangeBranch(branchId: string): Promise<void> {
    this.dataObject.branchId = branchId
    if (branchId) {
      this.loadProductionRequirements()
      this.items = await this.apiService.post(this.apiService.ITEM.LOAD_DATA, {
        branchId: this.dataObject.branchId,
      })
    } else {
      this.branchProducts = []
      this.items = []
    }
  }

  async loadProductionRequirements(): Promise<void> {
    this.isLoading = true
    await this.apiService
      .post(this.apiService.BRANCH_PRODUCT.DEMAND_LOAD_DETAIL, {
        where: {
          branchId: this.dataObject.branchId,
          itemIds: this.dataObject.itemIds,
        },
        skip: (this.pageIndex - 1) * this.pageSize,
        take: this.pageSize,
      })
      .then((res) => {
        this.branchProducts = res[0]
        this.total = res[1]
        this.isLoading = false
      })
      .catch((error) => {
        this.isLoading = false
        this.notifyService.showError('Lỗi khi tải dữ liệu yêu cầu sản xuất: ' + error.message)
      })
  }

  refreshData(): void {
    if (this.dataObject.branchId) {
      this.loadProductionRequirements()
    }
  }

  async createManufacturingPlan(): Promise<void> {
    const branchProducts = this.branchProducts
      .filter((item) => item.selected)
      .map((item) => ({
        branchProductId: item.id,
        quantity: item.quantityUserInput,
        needManufacturingQuantity: item.needManufacturingQuantity,
        max: item.max,
        waitManufacturingQuantity: item.waitManufacturingQuantity,
        inventoryQuantity: item.inventoryQuantity,
      }))

    // check it has quantityUserInput = 0
    const hasQuantityUserInputZero = branchProducts.some((item) => item.quantity === 0)
    if (hasQuantityUserInputZero) {
      this.notifyService.showError('Số lượng sản phẩm không được bằng 0.')
      return
    }

    // check overmax
    const overMax = branchProducts.some((item) => {
      const max = item.max - +item.waitManufacturingQuantity - +item.inventoryQuantity
      return item.quantity > max
    })

    if (overMax) {
      this.notifyService.showError('Số lượng sản phẩm vượt quá số lượng cần sản xuất.')
      return
    }
    if (branchProducts.length === 0) {
      this.notifyService.showError('Vui lòng chọn ít nhất một sản phẩm để tạo kế hoạch sản xuất.')
      return
    }
    const body = {
      branchId: this.dataObject.branchId,
      branchProducts: branchProducts,
    }

    this.notifyService.showloading()
    await this.apiService.post(this.apiService.MANUFACTURING.CREATE_PLAN, body).then((res) => {
      this.notifyService.hideloading()
      this.notifyService.showSuccess(res.message || 'Tạo kế hoạch sản xuất thành công.')

      this.router.navigate(['/manufacture/manufacturing-plan-detail'], {
        state: { id: res.planId },
      })
    })
  }

  onSelectAll(ev: any) {
    this.selectAll = ev
    this.branchProducts
      .filter((x) => x.quantityUserInput > 0)
      .forEach((item) => {
        item.selected = ev
      })
  }

  onSelectItem(e: any, item: any) {
    item.selected = e
    this.selectAll = this.branchProducts.filter((x) => x.quantityUserInput > 0).every((i) => i.selected)
  }

  checkSelectLeastOne(): boolean {
    const isLasteOneSelected = this.branchProducts.some((item) => item.selected)
    return !isLasteOneSelected
  }
}
