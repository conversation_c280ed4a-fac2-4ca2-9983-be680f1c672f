<!-- Filter section -->
<nz-collapse nzBordered="false" [nzActive]="true">
  <nz-collapse-panel [nzActive]="true" nzHeader="Tìm kiếm">
    <nz-row nzGutter="12" class="mt-2">
      <!-- Mã công thức chế biến -->
      <nz-col nzSpan="6">
        <input nz-input (keydown.enter)="onSearch()" [(ngModel)]="dataSearch.code" name="code" placeholder="Mã công thức" />
      </nz-col>
      <!-- Tên công thức chế biến -->
      <nz-col nzSpan="6">
        <input nz-input (keydown.enter)="onSearch()" [(ngModel)]="dataSearch.name" name="code" placeholder="Tên công thức" />
      </nz-col>
      <!-- Ch<PERSON>n nguyên vật liệu  -->
      <nz-col nzSpan="6">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.itemId" name="itemId" nzPlaceHolder="Nguyên vật liệu">
          <nz-option *ngFor="let item of items" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
        </nz-select>
      </nz-col>
      <!-- Ngày tạo -->
      <nz-col nzSpan="6">
        <nz-range-picker [(ngModel)]="dataSearch.createdAtRange" nzFormat="dd/MM/yyyy" [nzPlaceHolder]="['Từ ngày', 'Đến ngày']"></nz-range-picker>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button nzType="dashed" class="mr-2" (click)="onSearch(true, true)"><span nz-icon nzType="redo"></span>Xóa bộ lọc</button>
        <button nzType="dashed" (click)="onSearch()" nz-button nzType="default" class="mr-2"><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<!-- List of buttons -->
<nz-row class="my-2">
  <button class="mr-2 btn-dash-primary" nzType="primary" nz-button (click)="onAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button>
  <button class="mr-2 btn-dash-primary" nz-button (click)="clickDownloadTemplateExcel()"><span nz-icon nzType="download"></span>Tải mẫu</button>
  <input
    class="hidden"
    type="file"
    id="file-emp"
    (change)="clickImportExcel($event)"
    placeholder="Upload file"
    accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
  />
  <label nz-button for="file-emp" class="lable-custom-file mr-2 btn-dash-primary"> <span nz-icon nzType="upload"></span> Nhập excel </label>
  <button class="mr-2" nzType="primary" nz-button (click)="onDownloadExcel()"><span nz-icon nzType="download"></span> Tải Excel</button>
</nz-row>

<!-- table section -->
<nz-row class="mt-1">
  <nz-card class="recipe-card" style="width: 100%">
    <nz-table
      nz-col
      nzSpan="24"
      class="mb-3"
      #outerTable
      [nzData]="recipes"
      [nzLoading]="loading"
      nzBordered
      [nzShowPagination]="false"
      [(nzPageSize)]="pageSize"
    >
      <thead>
        <tr>
          <th nzWidth="20%">Mã công thức</th>
          <th nzWidth="25%">Tên công thức</th>
          <th nzWidth="10%">Giá vốn</th>
          <th nzWidth="10%">Ngày tạo</th>
          <th nzWidth="4%">Trạng thái</th>
          <th nzWidth="10%">Thao tác</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data of outerTable.data">
          <tr>
            <td>
              {{ data.code }}
            </td>
            <td>
              <span class="recipe-name">{{ data.name }}</span>
            </td>
            <td class="text-right">{{ data.costPrice | number : '1.0-2' : 'vi' }} ₫</td>
            <td>{{ data.createdAt | date : 'dd/MM/yyyy HH:mm' }}</td>
            <td>
              <nz-tag style="width: 100%" align="center" [nzColor]="data.statusColor">{{ data.statusName }}</nz-tag>
            </td>
            <td>
              <button
                nz-popconfirm
                nzPopconfirmTitle="Bạn có chắc muốn ngưng hoạt động công thức này?"
                nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="handleUpdateActive(data)"
                nz-tooltip
                nzTooltipTitle="Ngưng hoạt động"
                nz-button
                nzType="default"
                nzDanger
                *ngIf="data.isBtnStop"
                class="m-2"
              >
                <i nz-icon nzType="stop" style="color: red"></i>
              </button>
              <button
                nz-popconfirm
                nzPopconfirmTitle="Bạn có chắc muốn hoạt động công thức này?"
                nzPopconfirmPlacement="bottom"
                (nzOnConfirm)="handleUpdateActive(data)"
                nz-tooltip
                nzTooltipTitle="Hoạt động"
                nz-button
                nzType="primary"
                *ngIf="data.isBtnRestore"
                class="m-2"
              >
                <i nz-icon nzType="caret-right"></i>
              </button>
              <button nz-tooltip nzTooltipTitle="Xem chi tiết" (click)="onViewDetail(data.id)" nz-button nzType="default" class="m-2">
                <i nz-icon nzType="eye"></i>
              </button>
              <!-- view flowchart -->
              <button nz-tooltip nzTooltipTitle="Xem sơ đồ công thức" (click)="onViewFlowchart(data.id)" nz-button nzType="default" class="m-2">
                <i nz-icon nzType="info"></i>
              </button>
              <button *ngIf="data.isBtnApprove" nz-tooltip nzTooltipTitle="Duyệt" (click)="approve(data)" nz-button nzType="default" class="m-2">
                <i nz-icon nzType="check"></i>
              </button>
              <button *ngIf="data.isBtnReject" nz-tooltip nzTooltipTitle="Từ chối" (click)="reject(data)" nz-button nzType="default" class="m-2">
                <i nz-icon nzType="close"></i>
              </button>
              <button
                *ngIf="data.isBtnSendApprove"
                nz-tooltip
                nzTooltipTitle="Gửi duyệt"
                (click)="sendApprove(data)"
                nz-button
                nzType="default"
                class="m-2"
              >
                <i nz-icon nzType="send"></i>
              </button>

              <button *ngIf="data.isBtnUpdate" nz-tooltip nzTooltipTitle="Chỉnh sửa" (click)="onEdit(data.id)" nz-button nzType="default" class="m-2">
                <i nz-icon nzType="edit"></i>
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-card>
</nz-row>

<nz-row justify="center">
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      *ngIf="total > 0"
      class="my-2"
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="onSearch()"
      (nzPageSizeChange)="onSearch(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
      [nzPageSizeOptions]="lstPageSize"
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
  </div>
</nz-row>
