<div class="p-4">
  <h1 class="title">{{ dataObject.name }}</h1>

  <!-- Chart -->
  <div class="chart">
    <svg class="connections">
      <ng-container *ngFor="let ing of ingredients; let i = index">
        <line
          [attr.x1]="centerX"
          [attr.y1]="centerY"
          [attr.x2]="(getIngredientPosition(i, ingredients.length).x * width) / 100"
          [attr.y2]="(getIngredientPosition(i, ingredients.length).y * height) / 100"
          stroke="#4B5563"
          stroke-width="2"
          marker-end="url(#arrow)"
        ></line>
      </ng-container>
      <defs>
        <marker id="arrow" markerWidth="6" markerHeight="6" refX="5" refY="3" orient="auto">
          <path d="M0,0 L0,6 L6,3 z" fill="#4B5563"></path>
        </marker>
      </defs>
    </svg>

    <div class="center-node">
      <div class="text-center">{{ dataObject.name }}</div>
      <div class="text-center">Đầu ra: {{ dataObject.outputQuantity }} {{ getOutputUnitName() }}</div>
      <div style="display: flex; justify-content: center; align-items: center; flex-wrap: wrap; margin-top: 8px; gap: 6px">
        <img
          *ngFor="let img of dataObject.itemImages"
          [src]="img.url || img.fileUrl"
          [alt]="img.name || img.fileName"
          style="width: 100px; height: 100px; object-fit: contain; cursor: pointer; transition: transform 0.2s ease, box-shadow 0.2s ease"
        />
      </div>
    </div>

    <ng-container *ngFor="let ing of ingredients; let i = index">
      <div
        class="node"
        [ngClass]="{
          'vtth-node': ing.productType === 'VTTH',
          'nvl-node': ing.productType === 'NVL',
          'rcp-node': ing.productType === 'RCP'
        }"
        [ngStyle]="{
          left: getIngredientPosition(i, ingredients.length).x + '%',
          top: getIngredientPosition(i, ingredients.length).y + '%'
        }"
      >
        <div *ngIf="ing.productType === 'NVL' && ing.itemIds?.length">
          <div class="text-center" *ngFor="let itemId of ing.itemIds">
            <span>{{ getItemName(itemId, ing) }}</span>
            <span> ({{ ing.quantitative }} {{ getUnitName(ing.baseUnitId, i) }})</span>
          </div>
        </div>
        <div *ngIf="ing.productType === 'VTTH'">{{ getMaterialName(ing.materialId) }}</div>

        <div class="text-center" *ngIf="ing.productType === 'RCP'" (click)="onViewChildRecipe(ing.recipeId)">
          <span> {{ getRecipeName(ing.recipeId) }} ({{ ing.quantitative }} {{ getUnitName(ing.baseUnitId, i) }})</span>
        </div>

        <div
          *ngIf="ing.ingredientItems?.length"
          style="display: flex; justify-content: center; align-items: center; flex-wrap: wrap; margin-top: 8px; gap: 6px"
        >
          <ng-container *ngFor="let item of ing.ingredientItems">
            <img
              *ngFor="let img of item.images"
              [src]="img.url || img.fileUrl"
              [alt]="img.name || img.fileName"
              style="
                width: 50%;
                height: 50%;
                object-fit: contain;
                border-radius: 4px;
                border: 1px solid #d1d5db;
                cursor: pointer;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
              "
            />
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>

  <div style="margin: auto; display: flex; justify-content: end">
    <div style="margin: 10px 0; border: 1px solid #ccc; padding: 12px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15)">
      <h2>Chú thích</h2>
      <div style="display: flex; align-items: center">
        <div style="background: linear-gradient(90deg, #f87171, #b72f2f); width: 20px; height: 20px; margin: 5px; border-radius: 4px"></div>
        <span> Công thức</span>
      </div>
      <div style="display: flex; align-items: center">
        <div style="background: linear-gradient(90deg, #a7f3d0, #34d399); width: 20px; height: 20px; margin: 5px; border-radius: 4px"></div>
        <span> Vật tư tiêu hao </span>
      </div>

      <div style="display: flex; align-items: center">
        <div style="background: linear-gradient(90deg, #bfdbfe, #3b82f6); width: 20px; height: 20px; margin: 5px; border-radius: 4px"></div>
        <span> Nguyên vật liệu </span>
      </div>

      <div style="display: flex; align-items: center">
        <div style="background: linear-gradient(90deg, #fde68a, #fbbf24); width: 20px; height: 20px; margin: 5px; border-radius: 4px"></div>
        <span> Bán thành phẩm </span>
      </div>
    </div>
  </div>
</div>
