.chart {
  position: relative;
  margin: auto;
  height: 800px;
  width: 1200px;
}

.center-node {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(90deg, #f87171, #b72f2f);
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: bold;
  color: white;
  border: 2px solid #ec6d6d;
}

.node {
  position: absolute;
  transform: translate(-50%, -50%);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  width: 300px;
}

.connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.vtth-node {
  background: linear-gradient(90deg, #a7f3d0, #34d399);
  border: 3px solid #34d399;
  color: #0f172a;
}

.nvl-node {
  background: linear-gradient(90deg, #bfdbfe, #3b82f6);
  border: 3px solid #3b82f6;
  color: #0f172a;
}

.rcp-node {
  background: linear-gradient(90deg, #fde68a, #fbbf24);
  border: 3px solid #fbbf24;
  color: #0f172a;
  cursor: pointer;
}
