import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { enumData } from 'src/app/core/enumData'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { NotifyService } from 'src/app/services/notify.service'

@Component({
  selector: 'app-flowchart-recipe',
  templateUrl: './flowchart-recipe.component.html',
  styleUrls: ['./flowchart-recipe.component.scss'],
})
export class FlowchartRecipeComponent {
  title: string = 'Sơ đồ tổng quát công thức'
  id: string | null = null
  recipes: any = []
  unitsMap: { [key: string]: any[] } = {}
  selectedRecipe: any = null

  itemDetail: string = ''
  itemImage: any

  previewImage: string | undefined = ''
  previewVisible = false

  centerX = 600
  centerY = 400
  width = 1200
  height = 800

  dataObject: any = {
    code: '',
    name: '',
    description: '',
  }

  childRecipeIngredientsMap: { [key: string]: any[] } = {}

  itemGroupSells: any = []
  itemGroups: any = []
  productType: any = Object.values(enumData.RECIPE_INGREDIENT_ITEM_TYPE)
  materialTypes: any = []
  baseUnits: any = []
  ingredients: any = []
  pageSize = enumData.Page.pageSizeMax
  formErrors: string[] = []
  isEdit: boolean = false
  isView: boolean = false
  materials: any = []

  constructor(
    private dialog: MatDialog,
    private router: Router,
    private readonly apiService: ApiFnbService,
    private readonly notifyService: NotifyService,
    @Optional() public dialogRef: MatDialogRef<FlowchartRecipeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit(): Promise<void> {
    this.initData(this.data.id)
    await Promise.all([this.loadMaterials(), this.loadItemGroupSell(), this.loadItemGroup(), this.loadBaseUnit(), this.loadRecipe()])
  }

  async loadMaterials() {
    await this.apiService.post(this.apiService.MATERIAL.LOAD_DATA, {}).then((res) => {
      this.materials = res
    })
  }

  async loadBaseUnit() {
    await this.apiService.post(this.apiService.UNIT.LOAD_DATA, {}).then((res) => {
      this.baseUnits = res
    })
  }

  async loadItemGroup(itemId?: string) {
    await this.apiService.post(this.apiService.ITEM_GROUP.LOAD_DATA, {}).then((res) => {
      this.itemGroups = res
    })
  }

  async loadItemGroupSell() {
    await this.apiService.post(this.apiService.ITEM_GROUP_SELL.LOAD_DATA, {}).then((res) => {
      this.itemGroupSells = res
    })
  }

  async loadRecipe() {
    this.apiService.post(this.apiService.RECIPE.LOAD_DATA, {}).then((res) => {
      this.recipes = res.filter((x: any) => x.id !== this.id)
    })
  }

  async initData(id: string) {
    this.notifyService.showloading()

    try {
      const res = await this.apiService.post(this.apiService.RECIPE.FIND_DETAIL, { id })

      if (res) {
        this.dataObject = res.result
        this.ingredients = res.result.recipeIngredients
        this.unitsMap = res.unitsMap
        this.id = id
      }
    } catch (error) {
    } finally {
      this.notifyService.hideloading()
    }
  }

  onChangeBaseUnit(ev: any, data: any) {
    data.ingredientItems = []
  }

  getProductTypeName(code: string): string {
    const found = this.productType.find((pt: any) => pt.code === code)
    return found ? found.name : code || '-'
  }

  getMaterialName(id: any): string {
    const found = this.materials.find((m: any) => m.id === id)
    return found ? found.name : '-'
  }

  getItemGroupName(id: any): string {
    const found = this.itemGroups.find((g: any) => g.id === id)
    return found ? found.name : '-'
  }

  getRecipeName(id: any): string {
    const found = this.recipes.find((r: any) => r.id === id)
    return found ? found.name : '-'
  }

  getItemName(id: any, ing: any): string {
    const found = ing.items?.find((i: any) => i.id === id)
    return found ? found.name : '-'
  }

  onViewChildRecipe(id: string) {
    if (!id) {
      this.notifyService.showError('Không tìm thấy công thức chế biến')
      return
    }
    this.dialog.open(FlowchartRecipeComponent, {
      data: {
        id,
      },
      disableClose: false,
    })
  }

  getUnitName(unitId: any, index: number): string {
    const units = this.unitsMap[index] || []
    const found = units.find((u) => u.id === unitId)
    return found ? found.name : '-'
  }

  getOutputUnitName(): string {
    const unit = this.baseUnits.find((u: any) => u.id === this.dataObject?.outputUnitId)
    return unit ? unit.name : this.dataObject?.outputUnitId
  }

  openRecipeDetail(recipe: any) {
    this.selectedRecipe = recipe
  }

  goBack() {
    this.selectedRecipe = null
  }

  getIngredientPosition(index: number, total: number) {
    const centerX = 50
    const centerY = 50
    let radius = 150
    if (total <= 3) radius = 30
    else if (total <= 6) radius = 40
    else if (total <= 9) radius = 45
    else radius = 90

    if (total === 1) return { x: centerX, y: centerY - radius }

    const angleStep = (2 * Math.PI) / total
    const angle = angleStep * index - Math.PI / 2
    const x = Math.max(0, Math.min(100, centerX + radius * Math.cos(angle)))
    const y = Math.max(0, Math.min(100, centerY + radius * Math.sin(angle)))

    return { x, y }
  }

  countCategory(type: string) {
    return this.ingredients.filter((i: any) => i.category === type).length
  }

  getProductTypeCount(type: string): number {
    return this.ingredients.filter((ing: any) => ing.productType === type).length
  }
}
