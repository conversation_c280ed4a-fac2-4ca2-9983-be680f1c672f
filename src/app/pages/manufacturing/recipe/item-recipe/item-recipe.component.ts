import { Component, OnInit } from '@angular/core'
import { enumData } from '../../../../core/enumData'
import { ApiFnbService, CoreService, NotifyService } from '../../../../services'

@Component({
  selector: 'app-item-recipe',
  templateUrl: './item-recipe.component.html',
})
export class ItemRecipeComponent implements OnInit {
  stateId = 'app-item-recipe'
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  lstPageSize = enumData.Page.lstPageSize
  total = enumData.Page.total
  listOfData: any[] = []
  loading = false
  dataSearch: any = {}
  enumData = enumData
  dataObject: any = {}

  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  lstStatus = this.coreService.convertObjToArray(enumData.ItemStatus)
  itemTypes = this.coreService.convertObjToArray(enumData.ItemType)
  constructor(private notifyService: NotifyService, private apiService: ApiFnbService, private coreService: CoreService) {}

  ngOnInit(): void {
    this.searchData()
  }

  async searchData(reset = false, clearFilter = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    if (clearFilter) {
      this.dataSearch = {
        type: this.enumData.ItemType.NVL.code,
        isDeleted: false,
        lossRateNotNull: true,
      }
    }

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    await this.apiService.post(this.apiService.ITEM.PAGINATION, dataSearch).then((res: any) => {
      if (res) {
        this.loading = false
        this.total = res[1]
        this.listOfData = res[0]
        this.notifyService.hideloading()
      }
    })
  }
}
