<!-- Filter section -->
<nz-collapse nzBordered="false" [nzActive]="true">
  <nz-collapse-panel [nzActive]="true" nzHeader="Tìm kiếm">
    <nz-row nzGutter="24" class="mt-2">
      <!-- Mã  -->
      <nz-col nzSpan="8">
        <input nz-input (keydown.enter)="searchData()" [(ngModel)]="dataSearch.code" name="code" placeholder="Mã nguyên vật liệu" />
      </nz-col>

      <!-- Tên -->
      <nz-col nzSpan="8">
        <input nz-input (keydown.enter)="searchData()" [(ngModel)]="dataSearch.name" name="name" placeholder="Tên nguyên vật liệu" />
      </nz-col>

      <nz-col nzSpan="8">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" nzPlaceHolder="Trạng thái">
          <nz-option *ngFor="let item of lstStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <!-- Phân loại bán thành phẩm, thành phẩm, nguyên vật liệu của nguyên vật liệu -->
    <nz-row nzGutter="24" class="mt-2">
      <!-- Trạng thái -->
      <nz-col nzSpan="8">
        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.isDeleted" ngModel="" name="isDeleted" nzPlaceHolder="Trạng thái hoạt động">
          <nz-option *ngFor="let item of dataFilterStatus" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-3">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button nzType="dashed" class="mr-2" (click)="searchData(true, true)"><span nz-icon nzType="redo"></span>Xóa bộ lọc</button>
        <button nz-button nzType="dashed" (click)="searchData()"><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<!-- List of buttons -->
<nz-row class="my-2"> </nz-row>

<!-- Table section -->
<nz-row class="mt-1">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #ajaxTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
  >
    <thead>
      <tr>
        <th nzWidth="100px">Mã</th>
        <th>Phân loại</th>
        <th>Tên hệ thống</th>
        <th>Ngành hàng</th>
        <th>Tỉ lệ hao hụt</th>
        <th>Ngày tạo</th>
        <th nzWidth="180px">Trạng thái</th>
        <th nzWidth="180px">Trạng thái hoạt động</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td class="text-center">{{ data.code }}</td>
        <td class="text-center">{{ data.typeName }}</td>
        <td class="text-center">{{ data.nameSystem }}</td>
        <td class="text-center">{{ data.itemIndustryName }}</td>
        <td class="text-center">{{ data.lossRate }}%</td>
        <td class="text-center">{{ data.createdAt | date : 'dd/MM/yyyy HH:mm' }}</td>
        <td class="text-center">
          <nz-tag class="tag-status" [nzColor]="data.statusColor" style="width: 80%">{{ data.statusName }}</nz-tag>
        </td>
        <td class="text-center">
          <nz-tag class="tag-status" [nzColor]="'red'" *ngIf="data.isDeleted" style="width: 80%"> Ngưng hoạt động</nz-tag>
          <nz-tag class="tag-status" [nzColor]="'green'" *ngIf="!data.isDeleted" style="width: 80%">Đang hoạt động</nz-tag>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row justify="center">
  <div nz-col nzSpan="24" class="text-right">
    <nz-pagination
      *ngIf="total > 0"
      class="my-2"
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="searchData()"
      (nzPageSizeChange)="searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
      [nzPageSizeOptions]="lstPageSize"
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
  </div>
</nz-row>
