import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ApiFnbService, NotifyService } from '../../../../services'

interface ItemData {
  key: string
  slBt: number
  maHang: string
  nguyenLieu: string
  dv: string
  dinhLuong: number
  giaDv: number
  cost: number
}

@Component({
  selector: 'app-recipe',
  templateUrl: './modal-summary-recipe.component.html',
})
export class ModalSummaryRecipeComponent implements OnInit {
  summary: any = {}
  title: string = 'Tổng hợp Nguyên vật liệu/<PERSON><PERSON> thành phẩm cho công thức'
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiFnbService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<ModalSummaryRecipeComponent>
  ) {}
  ngOnInit(): void {
    this.title = this.data?.title || this.title
    this.summary = this.data?.data
  }

  closeDialog(): void {
    this.dialogRef.close()
  }

  async onApprove() {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.RECIPE.APPROVE, { id: this.data.recipeId }).then(async () => {
      this.notifyService.hideloading()
      this.closeDialog()
    })
  }

  expandedRows: Set<any> = new Set<any>()

  toggleExpand(item: any): void {
    if (this.expandedRows.has(item)) {
      this.expandedRows.delete(item)
    } else {
      this.expandedRows.add(item)
    }
  }

  isExpanded(item: any): boolean {
    return this.expandedRows.has(item)
  }
}
