<div class="p-4">
  <h3>Th<PERSON><PERSON> phẩm [{{ summary.name }}] ({{ summary.outputQuantity }} {{ summary.outputUnitName }})</h3>
  <nz-table [nzData]="summary.summary" [nzBordered]="true" [nzSize]="'middle'" nzShowPagination="false">
    <thead>
      <tr>
        <th nzWidth="50"></th>
        <th nzWidth="100">Nguyên liệu</th>
        <th nzWidth="100">Đơn vị</th>
        <th nzWidth="100"><PERSON><PERSON><PERSON> l<PERSON></th>
        <th nzWidth="100">Giá / ĐV</th>
        <th nzWidth="100">Cost</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let item of summary.summary">
        <tr (click)="toggleExpand(item)" [ngClass]="{ 'bg-light': isExpanded(item) }">
          <td style="width: 10%">
            <button nz-button nzType="text" nzShape="circle" *ngIf="item.summary?.length">
              <i nz-icon [nzType]="isExpanded(item) ? 'down' : 'right'"></i>
            </button>
          </td>
          <td style="width: 20%">{{ item.name }}</td>
          <td style="width: 20%" style="text-align: center">{{ item.baseUnitName || '---' }}</td>
          <td style="width: 20%" style="text-align: right">{{ item.actualQuantitative }}</td>
          <td style="width: 20%" style="text-align: right">{{ item.averagePrice | number : '1.0-2' : 'vi' }}</td>
          <td style="width: 20%" style="text-align: right">{{ item.costPrice | number : '1.0-2' : 'vi' }}</td>
        </tr>

        <tr *ngIf="item.summary?.length && isExpanded(item)">
          <td colspan="24" style="padding: 0">
            <nz-table [nzData]="item.summary" [nzBordered]="true" [nzSize]="'small'" style="margin: 10px" nzShowPagination="false">
              <thead>
                <tr>
                  <th nzWidth="100">Nguyên liệu</th>
                  <th nzWidth="100">Đơn vị</th>
                  <th nzWidth="100">Định lượng</th>
                  <th nzWidth="100">Giá / ĐV</th>
                  <th nzWidth="100">Cost</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let subItem of item.summary">
                  <td style="width: 20%">{{ subItem.name }}</td>
                  <td style="width: 20%" style="text-align: center">{{ subItem.baseUnitName || '---' }}</td>
                  <td style="width: 20%" style="text-align: right">{{ subItem.actualQuantitative }}</td>
                  <td style="width: 20%" style="text-align: right">{{ subItem.averagePrice | number : '1.0-2' : 'vi' }}</td>
                  <td style="width: 20%" style="text-align: right">{{ subItem.costPrice | number : '1.0-2' : 'vi' }}</td>
                </tr>
              </tbody>
            </nz-table>
          </td>
        </tr>
      </ng-container>

      <tr>
        <td colspan="4"></td>
        <td style="text-align: right; font-weight: bold">Tổng giá</td>
        <td style="text-align: right; font-weight: bold; color: orangered">
          {{ summary.costPrice | number : '1.0-2' : 'vi' }}
        </td>
      </tr>
      <tr>
        <td colspan="4"></td>
        <td style="text-align: right; font-weight: bold">Lượng dùng cho 1 [{{ summary.outputUnitName }}]</td>
        <td style="text-align: right; font-weight: bold; color: orangered">
          {{ summary.costPrice / summary.outputQuantity | number : '1.0-2' : 'vi' }}
        </td>
      </tr>
    </tbody>
  </nz-table>

  <nz-row nzGutter="10" style="margin-top: 20px; width: 100%; justify-content: flex-end">
    <button nz-button nzType="default" size="large" (click)="closeDialog()">Hủy</button>
    <button nz-button nzType="primary" size="large" class="ml-2" (click)="onApprove()">Xác nhận duyệt</button>
  </nz-row>
</div>
