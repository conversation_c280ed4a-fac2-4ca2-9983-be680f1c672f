import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { enumData } from '../../../core/enumData'
import { ApiFnbService, AuthenticationService, CoreService, NotifyService } from '../../../services'
import { FlowchartRecipeComponent } from './flowchart-recipe/flowchart-recipe.component'
import { ModalSummaryRecipeComponent } from './modal-summary-recipe/modal-summary-recipe.component'

@Component({
  selector: 'app-recipe',
  templateUrl: './recipe.component.html',
  styleUrl: './recipe.component.scss',
})
export class RecipeComponent {
  stateId = 'app-recipe'
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  lstPageSize = enumData.Page.lstPageSize
  total = 0
  data: any = []
  dataSearch: any = {}
  dataObject: any = {}
  recipes: any = []
  loading: any = false
  items: any = []
  // dữ liệu recipe chưa có item code hoặc chưa gắn Item code
  dataCreate: any = []
  titleForModal: string = 'Tạo nhanh Nguyên vật liệu/Bán thành phẩm cho công thức'

  currentApproveId: string = ''

  openModalAddRecipe() {
    this.router.navigate(['manufacture/recipe/add'], {
      state: { status: 'add' },
    })
  }

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiService: ApiFnbService,
    private coreService: CoreService
  ) {}

  openModalEditRecipe(data: any) {
    this.router.navigate(['/manufacture/recipe/edit'], {
      state: { status: 'edit' },
    })
  }
  handleUpdateActive(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.RECIPE.UPDATE_STATUS, { id: data.id, isDeleted: !data?.isDeleted }).then(() => {
      this.notifyService.showSuccess('Cập nhật trạng thái thành công')
      this.onSearch()
    })
  }

  async ngOnInit() {
    this.loadSelectBox()
    this.onSearch()
  }

  async loadSelectBox() {
    await this.apiService.post(this.apiService.ITEM.LOAD_DATA, {}).then((res) => {
      this.items = res
    })
  }

  onAdd() {
    this.router.navigate(['manufacture/recipe/add'], {})
  }

  async onSearch(reset = false, clearFilter = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    if (clearFilter) this.dataSearch = {}

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    await this.apiService.post(this.apiService.RECIPE.PAGINATION, dataSearch).then((res: any) => {
      if (res) {
        this.loading = false
        this.total = res[1]
        this.recipes = res[0]
        this.notifyService.hideloading()
      }
    })
  }

  onViewDetail(id: string) {
    this.router.navigate(['manufacture/recipe/add'], {
      state: { id: id, isView: true },
    })
  }

  onViewFlowchart(id: string) {
    this.dialog
      .open(FlowchartRecipeComponent, { disableClose: false, data: { id: id } })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.onSearch()
        }
      })
  }

  onEdit(id: string) {
    this.router.navigate(['manufacture/recipe/add'], {
      state: { id: id, isEdit: true },
    })
  }

  clickDownloadTemplateExcel() {}
  clickImportExcel(data: any) {}

  // lstHeader: IColum[] = [
  //   { code: 'code', name: 'Mã công thức', type: 'string' },
  //   { code: 'name', name: 'Tên công thức', type: 'string' },
  //   { code: 'costPrice', name: 'Giá vốn', type: 'number' },
  //   { code: 'createdAt', name: 'Ngày tạo', type: 'date' },
  //   { code: 'isDeleted', name: 'Trạng thái hoạt động', type: 'string' },
  // ]

  async onDownloadExcel() {
    // this.notifyService.showloading()
    // const dataSearch = { where: this.dataSearch, skip: 0, take: enumData.Page.pageSizeMax }
    // const [lstData, _num] = await this.apiService.post(this.apiService.RECIPE.PAGINATION, dataSearch)
    // const mappedData = lstData.map((item) => ({
    //   ...item,
    //   isDeleted: item.isDeleted ? 'Ngưng hoạt động' : 'Đang hoạt động',
    // }))
    // await this.excelService.onDownloadExcelJs({ lstData: mappedData, lstHeader: this.lstHeader, excelName: 'DS_THIET_LAP_CONG_THUC' })
    // this.notifyService.hideloading()
  }

  sendApprove(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.RECIPE.SEND_APPROVE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res?.message || 'Gửi duyệt công thức thành công')
      this.onSearch()
    })
  }

  async approve(data: any) {
    this.notifyService.showloading()
    const summary = await this.apiService.post(this.apiService.RECIPE.LOAD_SUMMARY, {
      id: data.id,
    })
    this.notifyService.hideloading()
    this.dialog
      .open(ModalSummaryRecipeComponent, {
        data: {
          title: 'Công thức chế biến [' + data.name + ']',
          data: summary,
          recipeId: data.id,
        },
      })
      .afterClosed()
      .subscribe(async (result) => {
        this.notifyService.showSuccess('Duyệt công thức thành công')
        this.onSearch()
      })
  }

  reject(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.RECIPE.REJECT, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res?.message || 'Từ chối công thức thành công')
      this.onSearch()
    })
  }
}
