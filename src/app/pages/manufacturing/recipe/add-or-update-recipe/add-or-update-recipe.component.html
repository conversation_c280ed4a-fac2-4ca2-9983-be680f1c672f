<nz-tabset>
  <nz-tab nzTitle="{{ title }}">
    <div class="p-4">
      <nz-row nzGutter="24">
        <nz-col class="text-center text-heading" *ngIf="isEdit || isView" nzSpan="24">
          {{ title + ' ' + dataObject?.name + ' [' + dataObject?.code + ']' | uppercase }}
        </nz-col>

        <nz-col class="text-center text-heading" *ngIf="!isEdit && !isView" nzSpan="24">
          {{ title | uppercase }}
        </nz-col>
      </nz-row>

      <div nz-row nzAlign="bottom">
        <!-- Tên công thức -->
        <div nz-col [nzSpan]="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" class="text-left" nzRequired>Tên công thức</nz-form-label>
            <nz-form-control [nzSm]="22">
              <input [disabled]="isView" nz-input placeholder="Nhập tên công thức" [(ngModel)]="dataObject.name" name="name" required />
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- /** nhóm bán hàng */ -->
        <div nz-col [nzSpan]="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left">Nhóm bán hàng</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="24" nzErrorTip="Vui lòng chọn nhóm bán hàng!">
              <nz-select
                [disabled]="isView"
                nzShowSearch
                nzAllowClear
                name="itemGroupSellId"
                [(ngModel)]="dataObject.itemGroupSellId"
                nzPlaceHolder="Nhóm bán hàng"
              >
                <nz-option *ngFor="let item of itemGroupSells" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col [nzSpan]="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Đơn vị đầu ra</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="24" nzErrorTip="Vui lòng chọn đơn vị đầu ra!">
              <nz-select
                [disabled]="isView"
                nzShowSearch
                nzAllowClear
                name="outputUnitId"
                [(ngModel)]="dataObject.outputUnitId"
                nzPlaceHolder="Đơn vị đầu ra"
                required
              >
                <nz-option *ngFor="let item of baseUnits" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col [nzSpan]="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Đầu ra</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="24" nzErrorTip="Vui lòng nhập số lượng đầu ra/ Trên đơn vị đầu ra!">
              <nz-input-number
                [disabled]="isView"
                min="0"
                style="width: 100%"
                [(ngModel)]="dataObject.outputQuantity"
                name="outputQuantity"
                placeholder="Số lượng đầu ra/ Trên đơn vị đầu ra"
                required
              ></nz-input-number>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col [nzSpan]="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Giá vốn (COST) {{ getSubtitleCostPrice() }}</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="24">
              <b style="font-size: large; color: rgb(233, 118, 3)"> {{ getCostPriceTotal() | number : '1.0-2' : 'vi' }} ₫ </b>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col [nzSpan]="8">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" [nzXs]="24" class="text-left" nzRequired>Giá (COST) {{ getSubtitleCostPriceEachUnit() }}</nz-form-label>
            <nz-form-control [nzSm]="22" [nzXs]="24">
              <b style="font-size: large; color: rgb(233, 118, 3)">
                {{ getCostPriceTotal() / dataObject.outputQuantity | number : '1.0-2' : 'vi' }} ₫
              </b>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Mô tả -->
        <div nz-col [nzSpan]="24">
          <nz-form-item nzFlex>
            <nz-form-label [nzSm]="24" class="text-left">Mô tả</nz-form-label>
            <nz-form-control [nzSm]="24">
              <textarea
                [disabled]="isView"
                rows="3"
                nz-input
                placeholder="Nhập mô tả"
                [(ngModel)]="dataObject.description"
                name="description"
              ></textarea>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Thêm dòng mới -->
      <div nz-col [nzSpan]="24" class="mt-4">
        <div nz-col [nzSpan]="24" *ngIf="!isView">
          <nz-row style="align-items: center; column-gap: 10px">
            <nz-form-item nzFlex style="align-items: center; column-gap: 10px">
              <button (click)="onAddRow()" nz-button nzType="primary"><span nz-icon nzType="plus"></span>Thêm thành phần</button>
            </nz-form-item>
            <span nz-tooltip nzTooltipTitle="Công thức chế biến này được tạo ra từ nhiều bán thành phẩm " style="color: orange; cursor: pointer">
              <span style="font-size: 24px" nz-icon nzType="info-circle"></span>
            </span>
          </nz-row>
        </div>
        <nz-row>
          <nz-table nz-col nzSpan="24" [nzData]="[0]" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th style="width: 10%">Phân loại</th>
                <th style="width: 20%">NVL/VTTH/BTP</th>
                <th style="width: 10%">Định danh</th>
                <th style="width: 10%">Đơn vị tính</th>
                <th style="width: 10%">Định lượng</th>
                <th style="width: 10%">Tỉ lệ hao hụt (%)</th>
                <th style="width: 10%">Định lượng thực tế</th>
                <th style="width: 10%">Giá / ĐVT</th>
                <th style="width: 10%">COST</th>
                <th style="width: 10%">Thao tác</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of ingredients; let i = index">
                <td>
                  <nz-select
                    [disabled]="isView"
                    nzShowSearch
                    nzAllowClear
                    name="productType"
                    [(ngModel)]="data.productType"
                    nzPlaceHolder="Phân loại hàng hoá"
                    required
                    (ngModelChange)="onChangeProductType(data)"
                  >
                    <nz-option *ngFor="let item of productType" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.code"> </nz-option>
                  </nz-select>
                </td>
                <td>
                  <nz-select
                    *ngIf="data.productType === 'VTTH'"
                    [disabled]="isView"
                    nzShowSearch
                    nzAllowClear
                    name="materialId"
                    [(ngModel)]="data.materialId"
                    nzPlaceHolder="Chọn VTTH"
                    required
                    (ngModelChange)="onChangeMaterial($event, data, i)"
                  >
                    <nz-option *ngFor="let item of materials" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                  <nz-select
                    *ngIf="data.productType === 'NVL'"
                    [disabled]="isView"
                    nzShowSearch
                    nzAllowClear
                    name="itemGroupId"
                    [(ngModel)]="data.itemGroupId"
                    nzPlaceHolder="Chọn nhóm nguyên liệu"
                    (ngModelChange)="onChangeItemGroup($event, data, i)"
                    required
                  >
                    <nz-option *ngFor="let item of itemGroups" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                  <nz-select
                    *ngIf="data.productType === 'RCP'"
                    [disabled]="isView"
                    nzShowSearch
                    nzAllowClear
                    name="recipeId"
                    [(ngModel)]="data.recipeId"
                    nzPlaceHolder="Chọn bán thành phẩm"
                    required
                    (ngModelChange)="onChangeRecipe($event, data, i)"
                  >
                    <nz-option *ngFor="let item of recipes" [nzLabel]="item.code + ' - ' + item.name" [nzValue]="item.id"> </nz-option>
                  </nz-select>
                </td>
                <td>
                  <nz-select
                    nzMode="multiple"
                    [disabled]="isView || data.productType !== 'NVL' || !data.itemGroupId"
                    nzShowSearch
                    nzAllowClear
                    name="itemIds"
                    [(ngModel)]="data.itemIds"
                    nzPlaceHolder="{{ data.productType === 'NVL' ? 'Chọn nguyên liệu' : '' }}"
                    (ngModelChange)="onChangeItems($event, data, i)"
                  >
                    <nz-option *ngFor="let item of data.items" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </td>

                <td>
                  <nz-select
                    [disabled]="isView || (!data.itemGroupId && !data.materialId && data.recipeId)"
                    (ngModelChange)="onChangeBaseUnit($event, data)"
                    [(ngModel)]="data.baseUnitId"
                    name="baseUnitId"
                    nzShowSearch
                    nzPlaceHolder="Chọn đơn vị tính"
                    required
                  >
                    <nz-option *ngFor="let item of unitsMap[i]" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
                  </nz-select>
                </td>
                <td class="text-right">
                  <input
                    type="number"
                    [disabled]="isView"
                    min="0"
                    style="width: 100%; text-align: right"
                    nz-input
                    placeholder="Nhập định lượng"
                    [(ngModel)]="data.quantitative"
                    (ngModelChange)="onChangeQuantitative($event, data)"
                    name="quantitative"
                    required
                  />
                </td>

                <td class="text-right">
                  <div class="container-flex">
                    <input
                      [disabled]="true"
                      type="number"
                      min="0"
                      style="width: 100%; text-align: right"
                      nz-input
                      placeholder="Tỉ lệ hao hụt (%)"
                      [(ngModel)]="data.lossRate"
                      (ngModelChange)="onChangeLostRate($event, data)"
                      name="lossRate"
                      required
                    />
                    <span
                      *ngIf="data.lossRate == null && data.itemIds && data.itemIds.length > 0 && data.productType === 'NVL'"
                      nz-tooltip
                      nzTooltipTitle="Chưa thiết lập tỉ lệ hao hụt trong Nguyên vật liệu!"
                      style="color: orange; cursor: pointer"
                    >
                      <span style="font-size: 24px" nz-icon nzType="info-circle"></span>
                    </span>
                  </div>
                </td>

                <td class="text-right">
                  <input
                    type="number"
                    [disabled]="true"
                    min="0"
                    style="width: 100%; text-align: right"
                    nz-input
                    [(ngModel)]="data.actualQuantitative"
                    name="actualQuantitative"
                    required
                  />
                </td>
                <td class="text-right">{{ getCostPrice(data) | number : '1.0-2' : 'vi' }} ₫</td>
                <td class="text-right">{{ getCostPrice(data) * data?.actualQuantitative | number : '1.0-2' : 'vi' }} ₫</td>

                <td class="text-center">
                  <button
                    nz-tooltip
                    *ngIf="!isView"
                    nzTooltipTitle="Xoá thành phần"
                    nz-button
                    nzType="primary"
                    nzGhost
                    nzDanger
                    (click)="onDeleteRow(data)"
                    class="m-2"
                  >
                    <span nz-icon nzType="delete"></span>
                  </button>
                  <button
                    *ngIf="data.productType == 'RCP'"
                    nz-tooltip
                    nzTooltipTitle="Xem chi tiết công thức con"
                    nz-button
                    nzType="primary"
                    class="btn-primary m-2"
                    nzGhost
                    (click)="onViewChildRecipe(data.recipeId)"
                  >
                    <span nz-icon nzType="eye"></span>
                  </button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-row>

        <!-- Action buttons -->
        <div class="text-start mt-3">
          <button (click)="closeAddOrUpdate()" nz-button class="mr-2"><span nz-icon nzType="close"></span> Đóng</button>
          <button nz-button nzType="primary" class="mr-2" (click)="onSubmit()" *ngIf="!isView"><span nz-icon nzType="save"></span> Lưu lại</button>
          <button *ngIf="isView" nz-button nzType="default" class="btn-primary" (click)="onSummary()">
            <span nz-icon nzType="save"></span> Xem chi tiết
          </button>
        </div>
      </div>
    </div>
  </nz-tab>
  <nz-tab nzTitle="Danh sách nguyên vật liệu">
    <app-item-recipe></app-item-recipe>
  </nz-tab>
</nz-tabset>
