import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { isNumber } from 'mathjs'
import { enumData } from '../../../../core/enumData'
import { ApiFnbService, NotifyService } from '../../../../services'
import { ModalSummaryRecipeComponent } from '../modal-summary-recipe/modal-summary-recipe.component'

@Component({
  selector: 'app-add-or-update-recipe',
  templateUrl: './add-or-update-recipe.component.html',
})
export class AddOrUpdateRecipeComponent {
  title: string = 'Thêm mới công thức chế biến'
  dataObject: any = {
    code: '',
    name: '',
    description: '',
  }
  itemGroupSells: any = []
  itemGroups: any = []
  productType: any = Object.values(enumData.RECIPE_INGREDIENT_ITEM_TYPE)
  materialTypes: any = []
  baseUnits: any = []
  ingredients: any = []

  pageSize = enumData.Page.pageSizeMax
  formErrors: string[] = []
  isEdit: boolean = false
  isView: boolean = false
  id: string | null = null
  recipes: any = []
  materials: any = []
  unitsMap: { [key: string]: any[] } = {}

  constructor(
    private dialog: MatDialog,
    private router: Router,
    private readonly apiService: ApiFnbService,
    private readonly notifyService: NotifyService,
    @Optional() public dialogRef: MatDialogRef<AddOrUpdateRecipeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  async ngOnInit(): Promise<void> {
    this.id = this.data?.id || history.state.id
    this.isEdit = history.state.isEdit
    this.isView = history.state.isView

    if (this.id) {
      this.title = this.isEdit ? 'Chỉnh sửa công thức chế biến' : 'Thông tin công thức chế biến'
      this.initData(this.id)
    }

    await Promise.all([this.loadMaterials(), this.loadItemGroupSell(), this.loadItemGroup(), this.loadBaseUnit(), this.loadRecipe()])
  }

  async loadMaterials() {
    await this.apiService.post(this.apiService.MATERIAL.LOAD_DATA, {}).then((res) => {
      this.materials = res
    })
  }
  async loadBaseUnit() {
    await this.apiService.post(this.apiService.UNIT.LOAD_DATA, {}).then((res) => {
      this.baseUnits = res
    })
  }
  async loadItemGroup(itemId?: string) {
    await this.apiService.post(this.apiService.ITEM_GROUP.LOAD_DATA, {}).then((res) => {
      this.itemGroups = res
    })
  }
  async loadItemGroupSell() {
    await this.apiService.post(this.apiService.ITEM_GROUP_SELL.LOAD_DATA, {}).then((res) => {
      this.itemGroupSells = res
    })
  }

  async initData(id: string) {
    this.notifyService.showloading()

    try {
      const res = await this.apiService.post(this.apiService.RECIPE.FIND_DETAIL, { id })
      this.notifyService.hideloading()

      if (res) {
        // Gán sau khi Promise.all đã resolve xong
        this.dataObject = res.result
        this.ingredients = res.result.recipeIngredients
        this.unitsMap = res.unitsMap
      }
    } catch (error) {
      this.notifyService.hideloading()
      console.error('Error loading recipe detail:', error)
    }
  }

  async loadRecipe() {
    this.apiService.post(this.apiService.RECIPE.LOAD_DATA, {}).then((res) => {
      this.recipes = res.filter((x: any) => x.id !== this.id)
    })
  }

  closeAddOrUpdate() {
    if (this.data?.id) {
      this.dialogRef.close({
        id: this.data.id,
        isEdit: this.isEdit,
      })
    } else this.router.navigate(['/manufacture/recipe'])
  }

  async onSubmit() {
    this.formErrors = [] // Reset errors before submitting
    if (!this.dataObject.code || !this.dataObject.name) {
      this.notifyService.showInfo('Vui lòng nhập đầy đủ mã sản phẩm, tên sản phẩm ')
    }

    // Check if all ingredients have valid values
    this.ingredients.forEach((ingredient: any, index: number) => {
      if (!ingredient.name || !ingredient.baseUnitId || ingredient.ingredientItems.length === 0) {
        this.notifyService.showInfo(`Thành phần thứ ${index + 1} chưa đầy đủ thông tin.`)
        return
      }
    })

    // If there are errors, do not submit the form
    if (this.formErrors.length > 0) {
      return
    }

    const body = {
      ...this.dataObject,
      name: this.dataObject.name,
      outputUnitId: this.dataObject.outputUnitId,
      outputQuantity: this.dataObject.outputQuantity,
      costPrice: this.getCostPriceTotal(),
      description: this.dataObject.description,
      recipeIngredients: this.ingredients.map((ingredient: any) => {
        return {
          name: ingredient.name,
          baseUnitId: ingredient.baseUnitId,
          productType: ingredient.productType,
          quantitative: ingredient.quantitative,
          averagePrice: this.getCostPrice(ingredient),
          costPrice: this.getCostPrice(ingredient) * (ingredient.actualQuantitative || 0),
          actualQuantitative: ingredient.actualQuantitative || 0,
          lossRate: ingredient.lossRate,
          itemGroupId: ingredient.itemGroupId,
          recipeIngredientItems: ingredient.ingredientItems,
        }
      }),
    }

    this.notifyService.showloading()

    if (this.isEdit) {
      await this.apiService
        .post(this.apiService.RECIPE.UPDATE, {
          id: this.id,
          ...body,
        })
        .then((res: any) => {
          this.notifyService.hideloading()
          this.notifyService.showSuccess(res.message)
          this.router.navigate(['/manufacture/recipe'])
        })
      return
    }
    /// add new

    await this.apiService.post(this.apiService.RECIPE.CREATE, body).then((res: any) => {
      this.notifyService.hideloading()
      this.notifyService.showSuccess(res.message)
      this.router.navigate(['/manufacture/recipe'])
    })
  }

  onAddRow() {
    const index = this.ingredients.length + 1
    this.ingredients = [
      ...this.ingredients,
      {
        index: index,
        name: '',
        unit: '',
        ingredientItems: [],
        lossRate: null,
      },
    ]
  }

  onDeleteRow(data: any) {
    this.ingredients = this.ingredients.filter((item: any) => item.index !== data.index)
  }

  // onEditPriority(data: any) {
  //   this.dialog
  //     .open(SortItemPriorityComponent, {
  //       data: {
  //         disabled: this.isView,
  //         ingredientItems: data.ingredientItems || [],
  //         productType: data.productType,
  //         materialTypeId: data.materialTypeId,
  //         itemIdExist: this.dataObject.itemId,
  //         baseUnitId: data.baseUnitId,
  //       },
  //     })
  //     .afterClosed()
  //     .subscribe(async (result) => {
  //       if (!result?.baseUnitId) {
  //         this.notifyService.showInfo('Vui lòng chọn đơn vị tính trước khi sắp xếp nguyên liệu')
  //       }
  //       if (result) {
  //         data.ingredientItems = result?.result
  //         data.baseUnitId = result?.baseUnitId

  //         //tìm tỉ lệ hao hụt đề xuất đầu tiên
  //         const lossRate = result?.result.find((item: any) => item.lossRate > 0)?.lossRate || 0
  //         data.lossRate = lossRate
  //       }
  //     })
  // }
  onChangeBaseUnit(ev: any, data: any) {
    data.ingredientItems = []
  }
  onChangeQuantitative(ev: any, data: any) {
    const actualQuantitative = ((data.quantitative || 0) * (data.lossRate || 0)) / 100 + (data.quantitative || 0)
    data.actualQuantitative = isNumber(actualQuantitative) ? +actualQuantitative : 0
    if (ev && ev > 0) {
      data.quantitative = isNumber(ev) ? +ev : 0
    } else {
      data.costPrice = 0
    }
  }
  onChangeLostRate(ev: any, data: any) {
    const actualQuantitative = (data.quantitative * data.lossRate) / 100 + data.quantitative
    data.lossRate = +ev
    data.actualQuantitative = isNumber(actualQuantitative) ? +actualQuantitative : 0
  }

  getCostPriceTotal() {
    let totalCostPrice = 0
    this.ingredients.forEach((item: any) => {
      const costPrice = this.getCostPrice(item)
      totalCostPrice += costPrice * (item.actualQuantitative || 0)
    })
    return totalCostPrice
  }

  async onChangeRecipe(ev: any, data: any, index: number) {
    const recipe = this.recipes.find((item: any) => item.id === ev)
    data.name = recipe.name
    data.baseUnitId = recipe.outputUnitId
    data.costPrice = recipe.costPrice / (recipe.outputQuantity / data.actualQuantitative)
    data.averagePrice = recipe.costPrice / recipe.outputQuantity
    data.lossRate = 0

    data.ingredientItems = [
      {
        recipeId: recipe.id,
        itemCode: recipe.code,
        itemName: recipe.name,
        productType: enumData.RECIPE_INGREDIENT_ITEM_TYPE.RECIPE.code,
        costPrice: data.costPrice,
        baseUnitId: recipe.outputUnitId,
        outputQuantity: recipe.outputQuantity,
      },
    ]

    await this.loadUnitsForIngredient(data, index)
  }

  async loadUnitsForIngredient(data: any, index: number): Promise<void | any[]> {
    const key = index
    data.baseUnitId = ''
    if (data.productType === enumData.RECIPE_INGREDIENT_ITEM_TYPE.RECIPE.code) {
      const recipe = this.recipes.find((item: any) => item.id === data.recipeId)
      if (recipe) {
        this.unitsMap[key] = await this.baseUnits.filter((unit: any) => unit.id === recipe.outputUnitId)
        data.baseUnitId = this.unitsMap[key][0]?.id || ''
      }
    } else if (data.productType === enumData.RECIPE_INGREDIENT_ITEM_TYPE.VTTH.code) {
      const units = await this.apiService.post(this.apiService.MATERIAL.LOAD_UNIT, {
        materialId: data.materialId,
      })
      this.unitsMap[key] = units
      data.baseUnitId = units[0]?.id || ''
    } else if (data.productType === enumData.RECIPE_INGREDIENT_ITEM_TYPE.NVL.code) {
      if (data.itemIds && data.itemIds.length <= 0) {
        return []
      }
      const units = await this.apiService.post(this.apiService.ITEM.LOAD_UNIT, {
        itemIds: data.itemIds || [],
      })
      this.unitsMap[key] = units
      data.baseUnitId = units[0]?.id || ''
    } else {
      this.unitsMap[key] = []
    }
  }
  onViewChildRecipe(id: string) {
    if (!id) {
      this.notifyService.showError('Không tìm thấy công thức chế biến')
      return
    }
    this.dialog.open(AddOrUpdateRecipeComponent, {
      data: {
        id,
      },
      disableClose: false,
    })
  }

  async onChangeItemGroup(ev: any, data: any, index: number) {
    if (!ev) {
      data.items = []
      data.itemIds = []
      return
    }
    data.items = await this.apiService.post(this.apiService.ITEM.LOAD_DATA, {
      itemGroupId: ev,
    })

    const currentGroup = this.itemGroups.find((group: any) => group.id === ev)
    data.name = currentGroup?.name || ''
    data.lossRate = data.items.find((item: any) => item.lossRate != null)?.lossRate

    data.itemIds = data.items.map((item: any) => item.id)
    data.itemGroupId = ev
    data.ingredientItems = data.itemIds.map((id: any, index: any) => {
      return {
        priority: index,
        itemId: id,
        productType: enumData.RECIPE_INGREDIENT_ITEM_TYPE.NVL.code,
      }
    })

    await this.loadUnitsForIngredient(data, index)
  }

  async onChangeMaterial(ev: any, data: any, index: number) {
    data.baseUnitId = ''
    const material = this.materials.find((item: any) => item.id === ev)
    data.name = material.name
    data.lossRate = 0
    data.ingredientItems = [
      {
        priority: 0,
        materialId: ev,
        productType: enumData.RECIPE_INGREDIENT_ITEM_TYPE.VTTH.code,
        lossRate: 0,
      },
    ]
    await this.loadUnitsForIngredient(data, index)
  }

  async onChangeProductType(data: any) {
    data.items = []
    data.itemGroupId = ''
    data.itemIds = []
  }

  async onChangeItems(ev: any, data: any, index: number) {
    data.items = await this.apiService.post(this.apiService.ITEM.LOAD_DATA, {
      itemGroupId: data.itemGroupId,
      itemIds: ev,
    })

    data.lossRate = data.items.find((item: any) => item.lossRate != null)?.lossRate

    data.ingredientItems = ev.map((id: any, index: any) => {
      const item = data.items.find((item: any) => item.id === id)
      return {
        priority: index,
        itemId: item.id,
        productType: enumData.RECIPE_INGREDIENT_ITEM_TYPE.NVL.code,
      }
    })

    await this.loadUnitsForIngredient(data, index)
  }

  getCostPrice(data: any) {
    if (!data || !data.productType) {
      return 0
    }
    if (data.productType === enumData.RECIPE_INGREDIENT_ITEM_TYPE.RECIPE.code && data.recipeId) {
      const recipe = this.recipes.find((item: any) => item.id === data.recipeId)
      return recipe.costPrice / recipe.outputQuantity
    } else if (data.productType === enumData.RECIPE_INGREDIENT_ITEM_TYPE.VTTH.code && data.materialId) {
      const material = this.materials.find((item: any) => item.id === data.materialId)
      return material.averagePrice * data.actualQuantitative
    } else if (data.productType === enumData.RECIPE_INGREDIENT_ITEM_TYPE.NVL.code && data.itemIds && data.itemIds.length > 0) {
      if (data.items && data.items.length <= 0 && data.itemIds.length <= 0) {
        return 0
      }
      const totalCostPrice = data.itemIds.reduce((acc: number, itemId: string) => {
        const item = data.items.find((i: any) => i.id === itemId)
        return acc + Number(item?.averagePrice || 0) * Number(item?.openingStock || 0)
      }, 0)

      const totalOpeningStock = data.items.reduce((acc: number, _: any) => {
        const item = data.items.find((i: any) => i.id === _.id)
        return acc + Number(item?.openingStock)
      }, 0)

      return totalOpeningStock > 0 ? totalCostPrice / totalOpeningStock : 0
    }
    return 0
  }

  getSubtitleCostPrice(): string {
    const unit = this.baseUnits.find((unit: any) => unit.id === this.dataObject?.outputUnitId)
    if (unit && !!this.dataObject?.outputQuantity) {
      return `/ ${this.dataObject?.outputQuantity} ${unit.name}`
    }
    return ''
  }

  getSubtitleCostPriceEachUnit(): string {
    const unit = this.baseUnits.find((unit: any) => unit.id === this.dataObject?.outputUnitId)
    if (unit && !!this.dataObject?.outputQuantity) {
      return `/ 1 ${unit.name}`
    }
    return ''
  }

  async onSummary() {
    const summary = await this.apiService.post(this.apiService.RECIPE.LOAD_SUMMARY, {
      id: this.dataObject.id,
    })
    this.dialog
      .open(ModalSummaryRecipeComponent, {
        data: {
          title: 'Công thức chế biến [' + this.dataObject.name + ']',
          data: summary,
        },
      })
      .afterClosed()
      .subscribe(async (result) => {})
  }
}
