import { moveItemInArray } from '@angular/cdk/drag-drop'
import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { enumData } from '../../../../core/enumData'
import { ApiFnbService, NotifyService } from '../../../../services'

@Component({
  selector: 'app-sort-item-priority',
  template: `
    <div class="container">
      <h2 matDialogTitle class="text-center p-2">
        {{ title }}
        <span
          nz-tooltip
          nzTooltipTitle="Chọn đơn vị tính để đảm bảo các thành phần cùng đơn vị có thể thay thế nhau khi sản xuất! "
          style="color: orange; cursor: pointer"
        >
          <span style="font-size: 24px" nz-icon nzType="info-circle"></span>
        </span>
      </h2>
      <nz-row *ngIf="productType === 'NVL'" nzGutter="16" class="mb-2">
        <nz-col nzSpan="6">
          <nz-select
            [(ngModel)]="dataSearch.baseUnitId"
            name="baseUnitId"
            nzShowSearch
            nzAllowClear="true"
            nzPlaceHolder="Chọn đơn vị tính"
            required
            [disabled]="disabled"
          >
            <nz-option *ngFor="let item of baseUnits" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-select
            [(ngModel)]="dataSearch.itemIndustryId"
            name="itemIndustryId"
            nzShowSearch
            nzAllowClear="true"
            nzPlaceHolder="Chọn ngành"
            required
            [disabled]="disabled"
          >
            <nz-option *ngFor="let item of itemIndustries" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-select
            nzAllowClear="true"
            [(ngModel)]="dataSearch.itemGroupId"
            name="itemGroupId"
            nzShowSearch
            nzPlaceHolder="Chọn nhóm"
            required
            [disabled]="disabled"
          >
            <nz-option *ngFor="let item of itemGroups" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="6"><input nzAllowClear="true" nz-input [(ngModel)]="dataSearch.name" name="name" placeholder="Tên nguyên vật liệu" /></nz-col>
        <nz-col nzSpan="24" class="text-center mt-2">
          <nz-row style="justify-content: center; align-items: center">
            <button nz-button nzType="dashed" class="mr-2" (click)="clearFilter()"><span nz-icon nzType="redo"></span>Xóa bộ lọc</button>
            <button nz-button (click)="loadSelectBox()"><span nz-icon nzType="search"></span>Tìm kiếm</button>
          </nz-row>
        </nz-col>
      </nz-row>

      <nz-row *ngIf="productType === 'VTTH'" nzGutter="16" class="mb-2">
        <nz-col nzSpan="8">
          <nz-select
            [(ngModel)]="dataSearch.baseUnitId"
            name="baseUnitId"
            nzShowSearch
            nzAllowClear="true"
            nzPlaceHolder="Chọn đơn vị tính"
            required
            [disabled]="disabled"
          >
            <nz-option *ngFor="let item of baseUnits" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-select
            [(ngModel)]="dataSearch.materialTypeId"
            name="materialTypeId"
            nzShowSearch
            nzAllowClear="true"
            nzPlaceHolder="Chọn loại"
            required
            [disabled]="disabled"
          >
            <nz-option *ngFor="let item of materialTypes" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="8"><input nzAllowClear="true" nz-input [(ngModel)]="dataSearch.name" name="name" placeholder="Tên vật tư tiêu hao" /></nz-col>
        <nz-col nzSpan="24" class="text-center mt-2">
          <nz-row style="justify-content: center; align-items: center">
            <button nz-button nzType="dashed" class="mr-2" (click)="clearFilter()"><span nz-icon nzType="redo"></span>Xóa bộ lọc</button>
            <button nz-button (click)="loadMaterial()"><span nz-icon nzType="search"></span>Tìm kiếm</button>
          </nz-row>
        </nz-col>
      </nz-row>

      <div *ngIf="productType === 'NVL'">
        <nz-select
          (ngModelChange)="onChange($event)"
          nzMode="multiple"
          [(ngModel)]="ingredientItems"
          name="ingredientItems"
          nzShowSearch
          nzPlaceHolder="Chọn nguyên vật liệu"
          required
          [disabled]="disabled"
        >
          <nz-option *ngFor="let item of lstItem" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
        </nz-select>

        <div matDialogContent>
          <p>Kéo để sắp xếp các mục theo thứ tự ưu tiên</p>
          <div cdkDropList [cdkDropListData]="itemsSortData" class="sortable-list" (cdkDropListDropped)="disabled ? null : drop($event)">
            <div class="sortable-item" *ngFor="let item of itemsSortData" cdkDrag [cdkDragDisabled]="disabled">
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="productType !== 'NVL'">
        <nz-select
          (ngModelChange)="onChange($event)"
          nzMode="multiple"
          [(ngModel)]="ingredientItems"
          name="ingredientItems"
          nzShowSearch
          nzPlaceHolder="Chọn vật tư tiêu hao"
          required
          [disabled]="disabled"
        >
          <nz-option *ngFor="let item of lstMaterial" [nzValue]="item.id" [nzLabel]="item.name"></nz-option>
        </nz-select>

        <div matDialogContent>
          <p>Kéo để sắp xếp các mục theo thứ tự ưu tiên</p>
          <div cdkDropList [cdkDropListData]="materialsSortData" class="sortable-list" (cdkDropListDropped)="disabled ? null : drop($event)">
            <div class="sortable-item" *ngFor="let item of materialsSortData" cdkDrag [cdkDragDisabled]="disabled">
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>

      <div matDialogActions class="text-center p-2">
        <button nz-button (click)="closeDialog(null)" class="mr-2">Đóng</button>
        <button nz-button nzType="primary" (click)="onSave()" [disabled]="disabled">Lưu</button>
      </div>
    </div>
  `,
  styles: [
    `
      .container {
        padding: 16px;
      }
      .sortable-list {
        min-height: 200px;
        border: 1px dashed #ccc;
        border-radius: 4px;
        padding: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .sortable-item {
        background-color: rgb(0 112 204 / 15%);
        padding: 8px;
        border-radius: 4px;
        cursor: move;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    `,
  ],
})
export class SortItemPriorityComponent implements OnInit {
  itemsSortData: any[] = []
  materialsSortData: any[] = []
  ingredientItems: any = []
  lstItem: any = []
  lstMaterial: any = []
  disabled: boolean = false
  productType: string = ''
  materialTypeId: string = ''
  itemGroupId: string = ''
  title = ''
  itemIdExist: string = ''

  // NVL
  itemIndustries: any[] = []
  itemGroups: any[] = []
  dataSearch: any = {
    itemIndustryId: null,
    itemGroupId: null,
    name: '',
  }
  // VTTH
  materialTypes: any[] = []
  baseUnits: any[] = []

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<SortItemPriorityComponent>,
    private readonly apiService: ApiFnbService,
    private readonly notifyService: NotifyService
  ) {}

  async ngOnInit(): Promise<void> {
    this.itemsSortData = this.data.ingredientItems
    this.materialsSortData = this.data.ingredientItems
    this.ingredientItems = this.data.ingredientItems.map((item: any) => {
      if (item.materialId) {
        return item.materialId
      }
      return item.itemId
    })
    this.disabled = this.data.disabled
    this.productType = this.data.productType
    this.materialTypeId = this.data.materialTypeId
    this.itemGroupId = this.data.itemGroupId
    this.itemIdExist = this.data.itemIdExist
    this.dataSearch.baseUnitId = this.data.baseUnitId
    if (this.data.disabled) {
      this.title = 'Xem danh sách ' + (this.productType === 'NVL' ? 'nguyên vật liệu' : 'vật tư tiêu hao') + ' cho thành phần'
    } else {
      this.title = 'Chọn danh sách ' + (this.productType === 'NVL' ? 'nguyên vật liệu' : 'vật tư tiêu hao') + ' cho thành phần'
    }
    this.notifyService.showloading()
    await this.loadSelectBox()
    await this.loadMaterial()
    this.notifyService.hideloading()
  }

  async loadSelectBox() {
    this.notifyService.showloading()
    const [itemIndustries, itemGroups, items, baseUnitIds] = await Promise.all([
      this.apiService.post(this.apiService.ITEM_INDUSTRY.LOAD_DATA, {}),
      this.apiService.post(this.apiService.ITEM_GROUP.LOAD_DATA, {
        itemIndustryId: this.itemGroupId,
      }),
      this.apiService.post(this.apiService.ITEM.LOAD_DATA, {
        itemIndustryId: this.dataSearch.itemIndustryId,
        itemGroupId: this.dataSearch.itemGroupId,
        name: this.dataSearch.name,
        baseUnitId: this.dataSearch.baseUnitId,
      }),
      this.apiService.post(this.apiService.UNIT.LOAD_DATA, {}),
    ])
    this.itemIndustries = itemIndustries
    this.itemGroups = itemGroups
    this.baseUnits = baseUnitIds
    if (this.dataSearch.baseUnitId) {
      this.lstItem = items
    } else {
      this.lstItem = []
    }
    this.notifyService.hideloading()
  }

  async loadMaterial() {
    this.notifyService.showloading()
    const [materialTypes, materials] = await Promise.all([
      this.apiService.post(this.apiService.MATERIAL_TYPE.LOAD_DATA, {}),
      this.apiService.post(this.apiService.MATERIAL.LOAD_DATA, {
        materialTypeId: this.dataSearch.materialTypeId,
        baseUnitId: this.dataSearch.baseUnitId,
        name: this.dataSearch.name,
      }),
    ])
    this.materialTypes = materialTypes
    if (this.dataSearch.baseUnitId) {
      this.lstItem = materials
    } else {
      this.lstItem = []
    }
    this.notifyService.hideloading()
  }

  drop(event: any): void {
    const keySortData = this.productType === 'NVL' ? 'itemsSortData' : 'materialsSortData'
    moveItemInArray(this[keySortData], event.previousIndex, event.currentIndex)
  }

  onSave(): void {
    const keySortData = this.productType === 'NVL' ? 'itemsSortData' : 'materialsSortData'
    const result = this[keySortData].map((item, index) => ({
      ...item,
      priority: index + 1,
    }))
    this.closeDialog({
      result,
      baseUnitId: this.dataSearch.baseUnitId,
    })
  }

  closeDialog(res: any): void {
    this.dialogRef.close(res)
  }

  onChange(val: any) {
    const keyObject = this.productType === 'NVL' ? 'lstItem' : 'lstMaterial'
    const keySortData = this.productType === 'NVL' ? 'itemsSortData' : 'materialsSortData'
    const keyData = this.productType === 'NVL' ? 'itemId' : 'materialId'
    this.ingredientItems = val

    const itemMap = new Map(this[keyObject].map((item: any) => [item.id, item]))

    const existingItems = this[keySortData].filter((item) => val.includes(item.id))

    const existingIds = new Set(existingItems.map((item) => item.id))
    const newItems = val
      .filter((id: any) => !existingIds.has(id))
      .map((id: any) => {
        const item = itemMap.get(id) as any
        return {
          ...item,
          [keyData]: item.id,
          label: `${item.code} - ${item.name} - tồn đầu kỳ (${item.openingStock}) - giá bình quân đầu kỳ (${new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(item.averagePrice)}) `,
          name: item.name,
          productType: this.productType,
          averagePrice: this.productType == enumData.ProductType.NVL.code ? +item.producePriceEachBaseUnit : +item.averagePrice,
        }
      })

    this[keySortData] = [...existingItems, ...newItems]
  }

  clearFilter() {
    this.dataSearch = {
      itemIndustryId: null,
      itemGroupId: null,
      name: '',
    }
    this.loadSelectBox()
  }
}
