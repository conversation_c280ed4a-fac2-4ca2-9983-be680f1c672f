import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { enumData } from '../../../core/enumData'
import { AuthenticationService, CoreService, NotifyService } from '../../../services'

@Component({
  selector: 'app-preparation',
  templateUrl: './preparation.component.html',
})
export class PreparationComponent {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  lstPageSize = enumData.Page.lstPageSize
  total = 20
  dataSearch: any = {}
  dataObject: any = {}
  loading: any = false
  branches: any = []
  preparations: any = []
  items: any = []
  listOfData: any = []

  clickDownloadTemplateExcel() {}
  clickImportExcel(data: any) {}
  onDownloadExcel() {}
  constructor(
    private router: Router,
    private dialog: MatDialog,
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private apiService: ApiFnbService,
    private coreService: CoreService
  ) {}

  async ngOnInit() {
    this.initData()
  }

  async onLoadData() {
    this.loading = true
    this.apiService
      .post(this.apiService.PREPARATION.LOAD_PREPARATION, {
        where: {
          ...this.dataObject,
        },
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
      })
      .then((res) => {
        console.log('res', res)
        this.listOfData = res
        this.loading = false
      })
      .catch(() => {
        this.loading = false
      })
  }

  async initData() {
    const [branches, items] = await Promise.all([
      this.apiService.post(this.apiService.BRANCH.LOAD_DATA, {}),
      this.apiService.post(this.apiService.ITEM.LOAD_DATA_BY_BRANCH_ID, {
        id: this.dataObject.branchId,
      }),
    ])
    this.branches = branches
    this.items = items
    if (branches.length > 0) {
      this.dataObject.branchId = branches[0].id
    }
  }

  async onChangeBranch(ev: any) {
    this.dataObject.branchId = ev
    await Promise.all([
      [
        this.apiService
          .post(this.apiService.ITEM.LOAD_DATA_BY_BRANCH_ID, {
            id: this.dataObject.branchId,
          })
          .then((res) => {
            this.items = res
          }),
      ],
      this.onLoadData(),
    ])
  }
  onChangeItem(ev: any) {
    this.dataObject.itemId = ev
    this.onLoadData()
  }

  onProduce(data: any) {
    this.router.navigate(['/manufacture/preparation-processing'], {
      state: {
        data: data,
      },
    })
  }
}
