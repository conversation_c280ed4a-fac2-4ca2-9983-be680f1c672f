<div nz-row style="width: 100%; padding: 20px" [nzGutter]="[24, 24]">
  <!-- Left Column -->
  <div nz-col [nzXs]="24" [nzLg]="12">
    <!-- Recipe Information Card -->
    <nz-card class="custom-card">
      <div class="card-header-custom">
        <h2 class="card-title">Thông tin sơ chế</h2>
        <p class="card-description">Thông tin chi tiết công thức sơ chế</p>
      </div>
      <div class="card-content">
        <nz-alert nzType="info" nzMessage="Chi tiết công thức" nzShowIcon class="info-alert">
          <div class="info-item"><strong>Mã:</strong> {{ preparation.code || 'Không có' }}</div>
          <div class="info-item"><strong>Tên:</strong> {{ preparation.name }}</div>
          <div class="info-item"><strong><PERSON><PERSON> tả:</strong> {{ preparation.description }}</div>
        </nz-alert>
      </div>
    </nz-card>

    <!-- Input Quantity Card -->
    <nz-card class="custom-card">
      <div class="card-header-custom">
        <h2 class="card-title">Nguyên liệu đầu vào</h2>
        <p class="card-description">Nhập số lượng nguyên liệu cần xử lý</p>
      </div>
      <div class="card-content">
        <div nz-row [nzGutter]="16" class="info-descriptions">
          <div nz-col [nzSpan]="12" class="info-item">
            <h4>Mã nguyên liệu:</h4>
            <h2>{{ preparation.inputItem?.code }}</h2>
          </div>
          <div nz-col [nzSpan]="12" class="info-item">
            <h4>Tên nguyên liệu:</h4>
            <h2>{{ preparation.inputItem?.name }}</h2>
          </div>
          <div nz-col [nzSpan]="12" class="info-item">
            <h4>Đơn vị tính:</h4>
            <h2>{{ preparation.inputUnit?.name || 'Đơn vị cơ bản' }}</h2>
          </div>
          <div nz-col [nzSpan]="12" class="info-item">
            <h4>Số lượng tiêu chuẩn:</h4>
            <h2>{{ preparation.inputQuantity }} {{ preparation.inputUnit?.name || '' }}</h2>
          </div>
          <div nz-col [nzSpan]="12" class="info-item">
            <h4>Tồn kho hiện tại:</h4>
            <h2>{{ branchProduct.inventoryQuantity }} {{ preparation.inputUnit?.name || '' }}</h2>
          </div>
        </div>
        <div class="input-container">
          <nz-form-item>
            <nz-form-label>Số lượng thực tế</nz-form-label>
            <nz-form-control>
              <nz-input-group [nzAddOnAfter]="preparation.inputUnit?.name">
                <input
                  nz-input
                  type="number"
                  min="0"
                  step="0.01"
                  [(ngModel)]="dataObject.inputQuantity"
                  (ngModelChange)="onChangeInputQuantity($event)"
                />
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>

        <button nz-button nzType="primary" class="calculate-button" type="button" (click)="calculateOutputs()">Tính toán</button>
      </div>
    </nz-card>
  </div>

  <!-- Right Column -->
  <div nz-col [nzXs]="24" [nzLg]="12">
    <!-- Output Result -->
    <nz-card class="custom-card">
      <div class="card-header-custom">
        <h2 class="card-title">Kết quả đầu ra</h2>
        <p class="card-description">Sản phẩm sau khi xử lý</p>
      </div>
      <div class="card-content">
        <ng-container *ngIf="calculatedOutputs?.length > 0; else emptyState">
          <nz-table #outputTable [nzData]="calculatedOutputs" [nzShowPagination]="false" [nzFrontPagination]="false">
            <thead>
              <tr>
                <th>Sản phẩm</th>
                <th>Tồn kho</th>
                <th>Số lượng</th>
                <th>Số lượng đầu ra thực tế</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let output of calculatedOutputs" [ngClass]="{ highlight: output.isMainProperty }">
                <td class="font-medium">{{ output?.outputItem?.name || 'Hao hụt' }}</td>
                <td class="text-right">{{ output.inventoryQuantity }}</td>
                <td class="text-right">{{ output.calculatedQuantity }}</td>
                <td class="text-right">
                  <nz-input-number
                    style="width: 100%"
                    nz-input
                    type="number"
                    min="0"
                    step="0.01"
                    (ngModelChange)="onChangeOutputQuantity($event, output)"
                    [(ngModel)]="output.outputQuantity"
                  />
                </td>
              </tr>
            </tbody>
          </nz-table>
          <p *ngIf="dataObject.isNotEqualInputQuantity" style="color: red">Số lượng đầu ra không bằng số lượng đầu vào</p>
        </ng-container>

        <ng-template #emptyState>
          <div class="empty-state">
            <span nz-icon nzType="info-circle" class="empty-icon"></span>
            <p>Nhập số lượng và nhấn "Tính toán" để xem kết quả đầu ra.</p>
          </div>
        </ng-template>
      </div>
    </nz-card>

    <!-- Confirmation Card -->
    <nz-card class="custom-card" style="margin-top: auto">
      <div class="card-header-custom">
        <h2 class="card-title">Xác nhận</h2>
        <p class="card-description">Hoàn tất quá trình sơ chế</p>
      </div>
      <div class="card-content">
        <div nz-row [nzGutter]="16">
          <div nz-col [nzSpan]="12">
            <button nz-button nzType="default" class="full-width-button" type="button" (click)="cancelPreparation()">Hủy</button>
          </div>
          <div nz-col [nzSpan]="12">
            <button
              [disabled]="!this.dataObject.isCalculated"
              nz-button
              nzType="primary"
              class="full-width-button"
              nz-popconfirm
              nzPopconfirmTitle="Bạn có chắc chắn muốn xác nhận sơ chế này?"
              nzPopconfirmPlacement="top"
              (nzOnConfirm)="confirmPreparation()"
              nzOkText="Xác nhận"
              nzCancelText="Hủy"
            >
              Xác nhận sơ chế
            </button>
          </div>
        </div>
      </div>
    </nz-card>
  </div>
</div>
