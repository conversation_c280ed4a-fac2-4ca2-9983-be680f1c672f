import { Component } from '@angular/core'
import { Router } from '@angular/router'
import { ApiFnbService } from 'src/app/services/apiFnb.service'
import { NotifyService } from '../../../../services'

/**  màn hình sản xuất  */
@Component({
  selector: 'app-preparation-processing',
  templateUrl: './preparation-processing.component.html',
  styleUrls: ['./preparation-processing.component.scss'],
})
export class PreparationProcessingComponent {
  data: any = {}
  branch: any = {}
  preparation: any = {}
  calculatedOutputs: any = []
  branchProduct: any = {}
  dataObject: any = {}
  branchProductId: any = ''
  preparationId: any = ''

  constructor(private router: Router, private notifyService: NotifyService, private apiService: ApiFnbService) {}

  async ngOnInit() {
    this.initData()
  }
  async initData() {
    this.data = history.state.data
    this.branchProductId = history.state.data.branchProductId
    this.preparationId = history.state.data.preparationId
    this.notifyService.showloading()
    await this.apiService
      .post(this.apiService.PREPARATION.PREPARATION_PROCESS, {
        branchProductId: this.data.branchProductId,
        preparationId: this.data.preparationId,
      })
      .then((res) => {
        this.data = res
        this.branch = res.branch
        this.preparation = res.preparation
        this.branchProduct = res.branchProduct
        this.notifyService.hideloading()
      })
  }

  calculateOutputs() {
    if (!this.dataObject.quantity) {
      this.notifyService.showError('Vui lòng nhập số lượng')
      return
    }
    this.calculatedOutputs = this.preparation.outputItems.map((item: any) => {
      const output = {
        ...item,
        calculatedQuantity: (this.dataObject.quantity * item.ratio) / 100,
        outputQuantity: (this.dataObject.quantity * item.ratio) / 100,
      }
      return output
    })

    const lossQuantity = this.dataObject.quantity * (this.preparation.lossRatio / 100)
    this.calculatedOutputs.push({
      id: null,
      calculatedQuantity: lossQuantity,
      outputQuantity: lossQuantity,
    })

    this.dataObject.isCalculated = true
  }
  cancelPreparation() {
    this.router.navigate(['/manufacture/preparation'])
  }
  async confirmPreparation() {
    this.notifyService.showloading()
    const lossItem = this.calculatedOutputs.find((item: any) => item.id === null)

    await this.apiService
      .post(this.apiService.PREPARATION.PREPARATION, {
        branchProductId: this.branchProductId,
        preparationId: this.preparationId,
        inputQuantity: this.dataObject.quantity,
        lossQuantity: lossItem?.outputQuantity,
        outputs: this.calculatedOutputs.filter((item: any) => item.id !== null),
      })
      .then((res) => {
        this.notifyService.hideloading()
        this.notifyService.showSuccess(res.message)
        this.router.navigate(['/manufacture/preparation'])
      })
  }
  onChangeInputQuantity(ev: any) {
    this.dataObject.quantity = ev
    this.dataObject.isCalculated = false
  }

  onChangeOutputQuantity(ev: any, output: any) {
    output.outputQuantity = ev

    const totalOutput = this.calculatedOutputs.reduce((acc: number, item: any) => {
      return acc + Number(item.outputQuantity)
    }, 0)
    // số lượng đầu ra không bằng số lượng đầu vào
    this.dataObject.isNotEqualInputQuantity = totalOutput > this.dataObject.quantity
  }
}
