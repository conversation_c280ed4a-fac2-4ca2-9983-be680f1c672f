/* Layout and spacing */
.highlight {
  background-color: #a6dbff;
  padding: 4px 8px;
  border-radius: 4px;
}
.breadcrumb-container {
  margin-bottom: 24px;
}

.custom-card {
  margin-bottom: 24px;
  width: 95%;
}

.card-content {
  padding-top: 10px;
  padding-bottom: 10px;
}

.input-container {
  margin: 24px 0;
}

.calculate-button {
  width: 100%;
}

.full-width-button {
  width: 100%;
}

/* Card header styling */
.card-header-custom {
  background-color: #fef3c7;
  margin: -24px -24px 0 -24px;
  padding: 16px 24px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-description {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
}

/* Info alert and descriptions */
.info-alert {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px;
}

.info-item {
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-descriptions {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px;
}

/* Empty state */
.empty-state {
  text-align: center;
  color: #6b7280;
  padding: 40px 0;
}

.empty-icon {
  display: block;
  font-size: 36px;
  margin-bottom: 8px;
}

/* Typography */
.font-medium {
  font-weight: 500;
}
