import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { DemandComponent } from './demand/demand.component'
import { ItemComponent } from './item/item.component'
import { ManufacturingPlanDetailComponent } from './manufacturing-plan/detail/manufacturing-plan-detail.component'
import { ManufacturingPlanComponent } from './manufacturing-plan/manufacturing-plan.component'
import { PreparationComponent } from './preparation/preparation.component'
import { PreparationProcessingComponent } from './preparation/processing/preparation-processing.component'
import { ProcessLineComponent } from './proces-line/process-line.component'
import { AddOrUpdateRecipeComponent } from './recipe/add-or-update-recipe/add-or-update-recipe.component'
import { RecipeComponent } from './recipe/recipe.component'

const routes: Routes = [
  { path: 'preparation', component: PreparationComponent },
  { path: 'preparation-processing', component: PreparationProcessingComponent },
  { path: 'process-line', component: ProcessLineComponent },
  { path: 'demand', component: DemandComponent },
  { path: 'manufacturing-plan', component: ManufacturingPlanComponent },
  { path: 'manufacturing-plan-detail', component: ManufacturingPlanDetailComponent },
  { path: 'item', component: ItemComponent },
  { path: 'recipe', component: RecipeComponent },
  { path: 'recipe/edit', component: AddOrUpdateRecipeComponent },
  { path: 'recipe/add', component: AddOrUpdateRecipeComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ManufacturingRoutingModule {}
