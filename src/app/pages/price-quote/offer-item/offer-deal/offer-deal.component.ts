import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs/internal/Subscription'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-offer-deal',
  templateUrl: './offer-deal.component.html',
})
export class OfferDealComponent implements OnInit {
  modalTitle = 'Đàm phán giá'
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataStatus: any[] = this.coreService.convertObjToArray(enumData.BidSupplierFileStatus)
  isChooseAll = false

  dataPrices: any
  listOfData: any[] = []

  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  action: any
  dataObject: any = {
    bidId: '',
    endDate: new Date(),
    isSendDealPrice: true,
    isRequireFilePriceDetail: true,
    isRequireFileTechDetail: false,
  }

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public dialogRef: MatDialogRef<OfferDealComponent>,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private drawerService: NzDrawerService,
    public authenticationService: AuthenticationService
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe((params: any) => {
      this.data = params
    })
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.modalTitle += ` Item [${this.data.itemName}] - [${this.data.bidName}]`

    this.loadData()
  }

  loadData() {
    this.dataObject.bidId = this.data.id
    this.notifyService.showloading()
    this.loading = true
    Promise.all([
      this.apiService.get(this.apiService.OFFER_DEAL.GET_PRICE(this.data.id), {}),
      this.apiService.post(this.apiService.OFFER_DEAL.LOAD_SUPPLIER_DATA, { bidId: this.data.id }),
    ]).then((res) => {
      this.loading = false
      this.dataPrices = res[0]
      this.listOfData = res[1]
      this.notifyService.hideloading()
    })
  }

  // Chọn tất cả
  checkAll() {
    for (const item of this.listOfData) {
      item.isChoose = this.isChooseAll
    }
  }

  onSave() {
    this.notifyService.showloading()
    const lstSupplierChoose = this.listOfData.filter((c: any) => c.isChoose)
    if (lstSupplierChoose.length === 0) {
      this.notifyService.showWarning(`Vui lòng chọn nhà cung cấp tham gia đàm phán.`)
      return
    }

    if (!this.dataObject.endDate) {
      this.notifyService.showWarning('Vui lòng chọn thời điểm kết thúc đàm phán giá!')
      return
    }

    {
      let strErr = ''
      for (const item of this.dataPrices) {
        if (item.number == null || item.number === '') {
          strErr += `Hạng mục [${item.name}] chưa nhập số lượng cần đàm phán<br>`
        } else if (item.number <= 0) {
          strErr += `Hạng mục [${item.name}] cần nhập số lượng cần đàm phán lớn hơn 0<br>`
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showWarning(strErr)
        return
      }
    }

    if (this.dataObject.isSendDealPrice) {
      let strErr = ''
      for (const item of this.dataPrices) {
        if (item.suggestPrice == null) {
          strErr += `Hạng mục [${item.name}] chưa nhập giá mong muốn đàm phán<br>`
        } else if (item.suggestPrice <= 0) {
          strErr += `Hạng mục [${item.name}] cần nhập giá mong muốn đàm phán lớn hơn 0<br>`
        } else if (item.lstChildId && item.lstChildId.length > 0) {
          let totalSuggestPrice = 0
          const lstPrice = this.dataPrices.filter((c: any) => c.parentId == item.id)
          for (const itemChild of lstPrice) {
            const suggestPrice = +itemChild.suggestPrice
            totalSuggestPrice += suggestPrice
          }
          if (item.suggestPrice != totalSuggestPrice) {
            strErr += `Hạng mục [${item.name}] cần nhập giá mong muốn đàm phán bằng tổng các hạng mục con<br>`
          }
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showWarning(strErr)
        return
      }
    }

    const lstPrice: any[] = []
    for (const item of this.dataPrices) {
      lstPrice.push({
        bidPriceId: item.id,
        bestPrice: item.bestPrice?.value,
        suggestPrice: item.suggestPrice,
        maxPrice: item.maxPrice,
        sort: item.sort,
        number: item.number,
      })
    }

    this.apiService
      .post(this.apiService.OFFER_DEAL.SAVE_OFFER_DEAL, {
        ...this.dataObject,
        lstSupplierChoose,
        lstPrice,
      })
      .then((result) => {
        this.notifyService.hideloading()
        if (!result?.error) {
          this.closeDialog(true)
          this.notifyService.showSuccess(result.message)
        }
      })
  }

  onChose(item: any, key: any) {
    item.suggestPrice = key
  }

  onCopy() {
    for (const item of this.dataPrices) {
      item.suggestPrice = item.valueTop1
    }
  }

  closeDialog(flag: boolean) {
    //  queryParams: { id: object.id, name: object.name },
    // queryParamsHandling: 'merge',
    const dataSend = { id: this.data.bidId, name: this.data.bidName }
    this.router.navigate(['/price-quote/list'], {
      queryParams: dataSend,
      queryParamsHandling: 'merge',
    })
  }

  /** Chi tiết nhà cung cấp */
  showDetail(data: { bidId: string; supplierId: string; supplierName: string }) {
    // this.drawerService.create({
    //   nzTitle: (this.language_key?.INTERPRISE_DETAIL || 'Chi tiết nhà cung cấp') + ` ${data.supplierName}`,
    //   nzContent: BidResultDetailComponent,
    //   nzContentParams: data,
    //   nzWidth: '640',
    // })
  }

  /** Lịch sử nộp giá nhà cung cấp */
  showHistory(data: { bidId: string; supplierId: string; supplierName: string }) {
    // this.drawerService.create({
    //   nzTitle: `Lịch sử nộp giá nhà cung cấp ${data.supplierName}`,
    //   nzContent: BidHistoryPriceComponent,
    //   nzContentParams: data,
    //   nzWidth: '640',
    // })
  }

  showModalBidSupplierPriceDetail(bidPriceId: string) {
    // this.drawerService.create({
    //   nzTitle: 'Danh sách giá trị hạng mục theo nhà cung cấp',
    //   nzContent: BidSupplierPriceDetailComponent,
    //   nzContentParams: {
    //     bidPriceId,
    //   },
    //   nzWidth: '640',
    // })
  }

  //#region excel

  clickExportExcel() {
    const tbl = document.getElementById('test-html-table')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Thông tin đàm phán giá.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['id', 'name', 'number', 'valueTop1', 'supplierNameTop1', 'value1', 'value2'],
      })

      for (const item of this.dataPrices) {
        const itemExcel = jsonData.find((c: any) => c.id === item.id)
        if (itemExcel) {
          if (this.dataObject.isSendDealPrice) {
            item.suggestPrice = itemExcel.value1
            item.maxPrice = itemExcel.value2
          } else {
            item.maxPrice = itemExcel.value1
          }
        }
      }
    }
  }

  //#endregion
}
