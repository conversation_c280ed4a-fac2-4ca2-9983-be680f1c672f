<nz-row matDialogTitle>

  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="data?.id">
  <nz-collapse>
    <nz-collapse-panel nzHeader="Chọn nhà cung cấp tham gia" class="ant-bg-antiquewhite" nzActive="true">
      <nz-row class="mt-3">
        <nz-table nz-col nzSpan="24" [nzScroll]="{ x: '1250px' }" [nzData]="listOfData" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th nzLeft nzWidth="50px">
                <label nz-checkbox [(ngModel)]="isChooseAll" (ngModelChange)="checkAll()">
                </label>
              </th>
              <th>{{ language_key?.INTERPRISE_NAME || 'Tên nhà cung cấp' }}</th>
              <th>Tổng điểm</th>
              <th>Tổng điểm HĐXT</th>
              <th>Thứ hạng</th>
              <th>Điểm năng lực</th>
              <th>Điểm HĐXT năng lực</th>
              <th>Điểm báo giá</th>
              <th>Điểm HĐXT báo giá</th>
              <th>Điểm ĐKTM</th>
              <th>Điểm HĐXT ĐKTM</th>
              <th>Trạng thái hồ sơ</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of listOfData">
              <td nzLeft>
                <label nz-checkbox [(ngModel)]="data.isChoose"></label>
              </td>
              <td class="mw-25">
                <span nz-tooltip [nzTooltipTitle]="language_key?.INTERPRISE_DETAIL || 'Chi tiết nhà cung cấp'"
                  (click)="showDetail(data)" nz-icon nzType="info-circle" class="mr-1"></span>
                <span nz-tooltip nzTooltipTitle="Lịch sử nộp giá" (click)="showHistory(data)" nz-icon nzType="history"
                  class="mr-1"></span>
                {{ data.supplierName }}
              </td>
              <td [ngClass]="{'text-right': data.scoreTotal !== -1}">
                {{ coreService.scoreRankABCD(data.scoreTotal) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualTotal !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualTotal) }}
              </td>
              <td class="mw-25">{{ data.rank }}</td>
              <td [ngClass]="{'text-right': data.scoreTech !== -1}">
                {{ coreService.scoreRankABCD(data.scoreTech) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualTech !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualTech) }}
              </td>
              <td [ngClass]="{'text-right': data.scorePrice !== -1}">
                {{ coreService.scoreRankABCD(data.scorePrice) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualPrice !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualPrice) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreTrade !== -1}">
                {{ coreService.scoreRankABCD(data.scoreTrade) }}
              </td>
              <td [ngClass]="{'text-right': data.scoreManualTrade !== -1}">
                {{ coreService.scoreRankABCD(data.scoreManualTrade) }}
              </td>
              <td class="mw-25">{{ data.statusFileName }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse class="mt-3" *ngIf="dataPrices">
    <nz-collapse-panel nzHeader="Thông tin đàm phán giá" class="ant-bg-antiquewhite" nzActive="true">
      <nz-row class="mt-3">
        <nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" nzRequired>
              Chọn thời điểm kết thúc đàm phán giá
            </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Chọn thời điểm kết thúc đàm phán giá">
              <nz-date-picker nzFormat="dd-MM-yyyy HH:mm" nzShowTime [(ngModel)]="dataObject.endDate" name="endDate">
              </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="dataObject.isSendDealPrice" name="isSendDealPrice">
                Gửi giá mong muốn đàm phán cho nhà cung cấp
              </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="dataObject.isRequireFileTechDetail" name="isRequireFileTechDetail">
                Bắt buộc File chi tiết kỹ thuật
              </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
      <nz-row class="mt-3">
        <nz-col nzSpan="24">
          <button class="mr-2" nz-button (click)="clickExportExcel()">
            <span nz-icon nzType="download"></span>Xuất excel</button>
          <input class="hidden" type="file" id="file" (change)="clickImportExcel($event)" onclick="this.value=null"
            accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
          <label for="file" class="label-custom-file">
            <span nz-icon nzType="upload"></span>
            {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
          </label>
        </nz-col>
      </nz-row>
      <nz-row class="mt-3" nzGutter="2">
        <nz-table nz-col nzSpan="24" id="test-html-table" [nzData]="dataPrices" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th rowspan="2" class="hidden">ID</th>
              <th rowspan="2">Hạng mục</th>
              <th rowspan="2">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
              <th colspan="2">Giá tốt nhất</th>
              <th rowspan="2" *ngIf="dataObject.isSendDealPrice">Giá mong muốn đàm phán</th>
              <th rowspan="2">Giá tối đa</th>
            </tr>
            <tr>
              <th>
                <span class="text-left mr-3">Giá</span>
                <span *ngIf="dataObject.isSendDealPrice " class="text-right" (click)="onCopy()" nz-tooltip
                  nzTooltipTitle="Lấy tất cả giá tốt nhất đang chào làm giá mong muốn đàm phán">
                  <span nz-icon nzType="copy"></span>
                </span>
              </th>
              <th>{{ language_key?.INTERPRISE || 'Nhà cung cấp' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of dataPrices">
              <td class="hidden">{{ item.id }} </td>
              <td class="mw-25">{{ item.name }}</td>
              <td>
                <span class="hidden">{{ item.number | number }}</span>
                <input nz-input currencyMask [(ngModel)]="item.number" name="number"
                  [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'" />
              </td>
              <td class="text-right">
                <div>
                  <span (click)="showModalBidSupplierPriceDetail(item.id)">
                    {{ item.valueTop1 > 0 ? (item.valueTop1 | number) : '' }}</span>
                  <span *ngIf="dataObject.isSendDealPrice" class="ml-2" (click)="onChose(item, item.valueTop1)"
                    nz-tooltip nzTooltipTitle="Lấy giá tốt nhất đang chào làm giá mong muốn đàm phán">
                    <span nz-icon nzType="copy"></span>
                  </span>
                </div>
              </td>
              <td>
                <span *ngIf="item.listTop1.length === 1">
                  {{ item.listTop1[0].supplierName }} - {{ item.listTop1[0].supplierScorePrice | number: '1.0-2' }}
                </span>
                <u nz-popover [nzPopoverContent]="contentTemplate" *ngIf="item.listTop1.length > 1"
                  style="color: blue;">
                  {{ item.listTop1.length }} DN
                </u>
                <ng-template #contentTemplate>
                  <div *ngFor="let itemTop1 of item.listTop1">
                    <p>
                      {{ itemTop1.supplierName }} - {{ itemTop1.supplierScorePrice | number: '1.0-2' }}
                    </p>
                  </div>
                </ng-template>
              </td>
              <td *ngIf="dataObject.isSendDealPrice" class="text-right">
                <span class="hidden">{{ item.suggestPrice | number }} </span>
                <input nz-input currencyMask [(ngModel)]="item.suggestPrice" name="suggestPrice"
                  placeholder="Giá mong muốn" />
              </td>
              <td class="text-right">
                <span class="hidden">{{ item.maxPrice | number }}</span>
                <input nz-input currencyMask [(ngModel)]="item.maxPrice" name="maxPrice" placeholder="Giá tối đa" />
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<nz-row matDialogActions *ngIf="data?.id && dataPrices" style="margin-top: 15px">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button class="button-save mr-2" (click)="onSave()">
      <span nz-icon nzType="send" nzTheme="outline"></span>
      Gửi yêu cầu đàm phán giá cho nhà cung cấp
    </button>

    <button nz-button class="mr-2" (click)="closeDialog(true)"
      style="border-color: #e41717 ;color: #e41717 ;border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>

  </nz-col>
</nz-row>