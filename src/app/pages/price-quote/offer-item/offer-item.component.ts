import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs/internal/Subscription'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-offer-item',
  templateUrl: './offer-item.component.html',
})
export class OfferItemComponent implements OnInit {
  modalTitle = 'Đàm phán/ đấu giá'
  total = enumData.Page.total
  pageSize = enumData.Page.pageSize
  pageIndex = enumData.Page.pageIndex
  loading = false
  listOfData: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  action: any
  enumRole009: any
  enumRole010: any

  constructor(
    private coreService: CoreService,
    private notifyService: NotifyService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { id: string; name: string },
    public authenticationService: AuthenticationService
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.route.queryParams.subscribe((params: any) => {
      this.data = params
    })
    this.language_key = this.coreService.getLanguage()
    this.modalTitle += ` gói thầu [${this.data.name}]`
    this.searchData(true)
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    if (reset) this.pageIndex = 1
    const dataSearch: any = {
      where: { bidId: this.data.id },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.OFFER_RATE.ITEM_PAGINATION, dataSearch).then((res) => {
      if (res) {
        this.listOfData = res[0]
        this.total = res[1]
        this.loading = false
      }
    })
  }

  checkStatus(status: string) {
    if (status == enumData.BidStatus.DangDamPhanGia.code) {
      this.notifyService.showWarning(`Item ${enumData.BidStatus.DangDamPhanGia.name}, không thể đàm phán/ đấu giá.`)
      return false
    }
    if (status == enumData.BidStatus.DangDauGia.code) {
      this.notifyService.showWarning(`Item ${enumData.BidStatus.DangDauGia.name}, không thể đàm phán/ đấu giá.`)
      return false
    }
    if (status == enumData.BidStatus.DongDauGia.code) {
      this.notifyService.showWarning(`Item ${enumData.BidStatus.DongDauGia.name}, không thể đàm phán/ đấu giá.`)
      return false
    }
    return true
  }

  bidDeal(item: { id: string; status: string; itemName: string }) {
    if (!this.checkStatus(item.status)) return
    this.router.navigate(['/price-quote/offer-deal'], {
      queryParams: { id: item.id, itemName: item.itemName, bidName: this.data.name, bidId: this.data.id },
      queryParamsHandling: 'merge',
    })
  }

  bidAuction(item: { id: string; status: string; itemName: string }) {
    if (!this.checkStatus(item.status)) return
    this.router.navigate(['/bid/bid-auction'], {
      queryParams: { id: item.id, itemName: item.itemName, bidName: this.data.name, bidId: this.data.id },
      queryParamsHandling: 'merge',
    })
    // this.dialog
    //   .open(BidAuctionComponent, { data: { id: item.id, itemName: item.itemName, bidName: this.data.name } })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     if (res) this.searchData()
    //   })
  }
  closeDialog() {
    this.router.navigate(['/price-quote/list'])
  }
}
