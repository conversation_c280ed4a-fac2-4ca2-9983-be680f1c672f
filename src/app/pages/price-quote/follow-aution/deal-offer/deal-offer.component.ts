import { Component, Inject, OnInit, Optional } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { enumData } from '../../../../core'

@Component({
  selector: 'app-deal-offer',
  templateUrl: './deal-offer.component.html',
})
export class DealOfferComponent implements OnInit {
  modalTitle = 'Chi tiết thông tin đàm phán giá'
  lstData: any = []
  loading = false
  dataStatus: any[] = this.coreService.convertObjToArray(enumData.BidSupplierFileStatus)
  isChooseAll = false
  dataObject: any = {
    bidId: '',
    endDate: new Date(),
    isSendDealPrice: true,
    isRequireFilePriceDetail: true,
    isRequireFileTechDetail: false,
  }
  dataPrices: any
  listOfData: any[] = []
  listSup: any[] = []
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public dialogRef: MatDialogRef<DealOfferComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {}
  async ngOnInit() {
    await this.loadData()
  }

  closeDialog() {
    this.dialogRef.close()
  }

  async loadData() {
    /* tìm ra danh sách item*/
    this.notifyService.showloading()
    this.loading = true
    Promise.all([this.apiService.get(this.apiService.OFFER_DEAL.GET_PRICE(this.data.bidId), {})]).then((res) => {
      this.loading = false
      this.dataPrices = res[0]
      this.notifyService.hideloading()
    })
  }

  onSave() {
    this.notifyService.showloading()
    const lstSupplierChoose = [{ supplierId: this.data.supplierId }]
    if (lstSupplierChoose.length === 0) {
      this.notifyService.showWarning(`Vui lòng chọn nhà cung cấp tham gia đàm phán.`)
      return
    }

    if (!this.dataObject.endDate) {
      this.notifyService.showWarning('Vui lòng chọn thời điểm kết thúc đàm phán giá!')
      return
    }

    {
      let strErr = ''
      for (const item of this.dataPrices) {
        if (item.number == null || item.number === '') {
          strErr += `Hạng mục [${item.name}] chưa nhập số lượng cần đàm phán<br>`
        } else if (item.number <= 0) {
          strErr += `Hạng mục [${item.name}] cần nhập số lượng cần đàm phán lớn hơn 0<br>`
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showWarning(strErr)
        return
      }
    }

    if (this.dataObject.isSendDealPrice) {
      let strErr = ''
      for (const item of this.dataPrices) {
        if (item.suggestPrice == null) {
          strErr += `Hạng mục [${item.name}] chưa nhập giá mong muốn đàm phán<br>`
        } else if (item.suggestPrice <= 0) {
          strErr += `Hạng mục [${item.name}] cần nhập giá mong muốn đàm phán lớn hơn 0<br>`
        } else if (item.lstChildId && item.lstChildId.length > 0) {
          let totalSuggestPrice = 0
          const lstPrice = this.dataPrices.filter((c: any) => c.parentId == item.id)
          for (const itemChild of lstPrice) {
            const suggestPrice = +itemChild.suggestPrice
            totalSuggestPrice += suggestPrice
          }
          if (item.suggestPrice != totalSuggestPrice) {
            strErr += `Hạng mục [${item.name}] cần nhập giá mong muốn đàm phán bằng tổng các hạng mục con<br>`
          }
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showWarning(strErr)
        return
      }
    }

    const lstPrice: any[] = []
    for (const item of this.dataPrices) {
      lstPrice.push({
        bidPriceId: item.id,
        bestPrice: item.bestPrice?.value,
        suggestPrice: item.suggestPrice,
        maxPrice: item.maxPrice,
        sort: item.sort,
        number: item.number,
      })
    }

    this.apiService
      .post(this.apiService.OFFER_DEAL.SAVE_OFFER_DEAL, {
        ...this.dataObject,
        lstSupplierChoose,
        lstPrice,
      })
      .then((result) => {
        this.notifyService.hideloading()
        if (!result?.error) {
          this.closeDialog()
          this.notifyService.showSuccess(result.message)
        }
      })
  }
}
