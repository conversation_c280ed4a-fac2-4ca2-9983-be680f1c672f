<nz-row matDialogTitle>

  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>


<div matDialogContent>

  <nz-row class="mt-3" nzGutter="2">
    <nz-table nz-col nzSpan="24" id="test-html-table" [nzData]="dataPrices" [nzLoading]="loading"
      [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th rowspan="2" class="hidden">ID</th>
          <th rowspan="2"><PERSON><PERSON>ng mục</th>
          <th rowspan="2">{{ 'Số lượng' }}</th>
          <th colspan="2">Gi<PERSON> tốt nhất</th>
          <th rowspan="2">Gi<PERSON> mong muốn đàm phán</th>
          <th rowspan="2">G<PERSON><PERSON> tối đa</th>
        </tr>
        <tr>
          <th>
            <span class="text-left mr-3">Gi<PERSON></span>
            <span>
              <span nz-icon nzType="copy"></span>
            </span>
          </th>
          <th>{{ 'Nhà cung cấp' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of dataPrices">
          <td class="hidden">{{ item.id }} </td>
          <td class="mw-25">{{ item.name }}</td>
          <td>
            <span class="hidden">{{ item.number | number }}</span>
            <input [disabled]="true" nz-input currencyMask [(ngModel)]="item.number" name="number"
              [placeholder]="'Nhập số lượng'" />
          </td>
          <td class="text-right">
            <div>
              <span></span>
              <span></span>
            </div>
          </td>
          <td>
            <span *ngIf="item.listTop1.length === 1">
              {{ item.listTop1[0].supplierName }} - {{ item.listTop1[0].supplierScorePrice | number: '1.0-2' }}
            </span>
            <u nz-popover [nzPopoverContent]="contentTemplate" *ngIf="item.listTop1.length > 1" style="color: blue;">
              {{ item.listTop1.length }} DN
            </u>
            <ng-template #contentTemplate>
              <div *ngFor="let itemTop1 of item.listTop1">
                <p>
                  {{ itemTop1.supplierName }} - {{ itemTop1.supplierScorePrice | number: '1.0-2' }}
                </p>
              </div>
            </ng-template>
          </td>
          <td class="text-right">
            <span class="hidden">{{ item.suggestPrice | number }}</span>
            <input nz-input currencyMask [(ngModel)]="item.suggestPrice" name="suggestPrice"
              placeholder="Giá mong muốn" />
          </td>
          <td class="text-right">
            <span class="hidden">{{ item.maxPrice | number }}</span>
            <input nz-input currencyMask [(ngModel)]="item.maxPrice" name="maxPrice" placeholder="Giá tối đa" />
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>

</div>

<nz-row matDialogActions style="margin-top: 15px">
  <nz-col nzSpan="24" class="text-center">


    <button nz-button class="mr-2" (click)="closeDialog()"
      style="border-color: #e41717 ;color: #e41717 ;border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>

    <button nz-button class="button-save mr-2" (click)="onSave()">
      <span nz-icon nzType="send" nzTheme="outline"></span>
      Gửi yêu cầu đàm phán giá cho nhà cung cấp
    </button>

  </nz-col>
</nz-row>