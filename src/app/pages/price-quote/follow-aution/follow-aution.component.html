<nz-row matDialogTitle>

  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="data?.id">
  <nz-collapse>
    <nz-collapse class="mt-3" *ngIf="dataPrices">
      <nz-collapse-panel nzHeader="Thông tin đàm phán giá" class="ant-bg-antiquewhite" nzActive="true">
        <nz-row class="mt-3" nzGutter="2">
          <nz-table nz-col nzSpan="24" id="test-html-table" [nzData]="dataPrices" [(nzPageSize)]="pageSize"
            [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th rowspan="2" class="hidden">ID</th>
                <th rowspan="2">H<PERSON>ng mụ<PERSON></th>
                <th rowspan="2">{{ language_key?.QUANTITY || '<PERSON><PERSON> lượng' }}</th>
                <th colspan="2"><PERSON><PERSON><PERSON> tốt nhất</th>
                <th rowspan="2" *ngIf="dataObject.isSendDealPrice">Giá mong muốn đàm phán</th>
                <th rowspan="2">Giá tối đa</th>
              </tr>
              <tr>
                <th>
                  <span class="text-left mr-3">Giá</span>
                  <span *ngIf="dataObject.isSendDealPrice" class="text-right" (click)="onCopy()" nz-tooltip
                    nzTooltipTitle="Lấy tất cả giá tốt nhất đang chào làm giá mong muốn đàm phán">
                    <span nz-icon nzType="copy"></span>
                  </span>
                </th>
                <th>{{ language_key?.INTERPRISE || 'Nhà cung cấp' }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of dataPrices">
                <td class="hidden">{{ item.id }} </td>
                <td class="mw-25">{{ item.name }}</td>
                <td>
                  <span class="hidden">{{ item.number | number }}</span>
                  <input [disabled]="true" nz-input currencyMask [(ngModel)]="item.number" name="number"
                    [placeholder]="language_key?.QUANTITY_ENTER || 'Nhập số lượng'" />
                </td>
                <td class="text-right">
                  <div>
                    <span (click)="showModalBidSupplierPriceDetail(item.id)">
                      {{ item.valueTop1 > 0 ? (item.valueTop1 | number) : '' }}</span>
                    <span *ngIf="dataObject.isSendDealPrice" class="ml-2" (click)="onChose(item, item.valueTop1)"
                      nz-tooltip nzTooltipTitle="Lấy giá tốt nhất đang chào làm giá mong muốn đàm phán">
                      <span nz-icon nzType="copy"></span>
                    </span>
                  </div>
                </td>
                <td>
                  <span *ngIf="item.listTop1.length === 1">
                    {{ item.listTop1[0].supplierName }} - {{ item.listTop1[0].supplierScorePrice | number: '1.0-2' }}
                  </span>
                  <u nz-popover [nzPopoverContent]="contentTemplate" *ngIf="item.listTop1.length > 1"
                    style="color: blue;">
                    {{ item.listTop1.length }} DN
                  </u>
                  <ng-template #contentTemplate>
                    <div *ngFor="let itemTop1 of item.listTop1">
                      <p>
                        {{ itemTop1.supplierName }} - {{ itemTop1.supplierScorePrice | number: '1.0-2' }}
                      </p>
                    </div>
                  </ng-template>
                </td>
                <td *ngIf="dataObject.isSendDealPrice" class="text-right">
                  <span class="hidden">{{ item.suggestPrice | number }}</span>
                  <input [disabled]="true" nz-input currencyMask [(ngModel)]="item.suggestPrice" name="suggestPrice"
                    placeholder="Giá mong muốn" />
                </td>
                <td class="text-right">
                  <span class="hidden">{{ item.maxPrice | number }}</span>
                  <input [disabled]="true" nz-input currencyMask [(ngModel)]="item.maxPrice" name="maxPrice"
                    placeholder="Giá tối đa" />
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>

    <nz-collapse-panel nzHeader="Chọn nhà cung cấp tham gia" class="ant-bg-antiquewhite" nzActive="true">
      <nz-row class="mt-3">
        <nz-table nz-col nzSpan="24" [nzScroll]="{ x: '1250px' }" [nzData]="listSup" [(nzPageSize)]="pageSize"
          [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>Nhà cung cấp</th>
              <th>Trạng thái</th>
              <th>Số lần đàm phán</th>
              <th>Chi tiết đàm phán</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of listSup">
              <td class="mw-25">{{data.name}}</td>
              <td class="mw-25">{{data.statusName}}</td>
              <td class="mw-25">{{data.attemp}}</td>
              <td class="mw-25">
                <button nz-button nzShape="circle" (click)="clickView(data)" class="mr-2 mt-2 btn-primary"
                  nzTooltipTitle="Xem chi tiết" nzTooltipPlacement="top" nz-tooltip nzType="primary" nzGhost>
                  <span nz-icon nzType="eye"></span>
                </button>
                <button nz-button nzShape="circle" (click)="clickData(data)" class="mr-2 mt-2 btn-primary"
                  nzTooltipTitle="Đàm phán giá" nzTooltipPlacement="top" nz-tooltip nzType="primary" nzGhost>
                  <span nz-icon nzType="form" nzTheme="outline"></span>
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>


</div>

<nz-row matDialogActions *ngIf="data?.id && dataPrices" style="margin-top: 15px">
  <nz-col nzSpan="24" class="text-center">
    <!-- <button nz-button class="button-save mr-2" (click)="onSave()" >
      <span nz-icon nzType="send" nzTheme="outline"></span>
      Gửi yêu cầu đàm phán giá cho nhà cung cấp
    </button> -->

    <button nz-button class="mr-2" (click)="closeDialog(true)"
      style="border-color: #e41717 ;color: #e41717 ;border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>

  </nz-col>
</nz-row>