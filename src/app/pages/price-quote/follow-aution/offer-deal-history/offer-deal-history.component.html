<nz-row matDialogTitle>

  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>


<div matDialogContent>

  <nz-table nz-col nzSpan="24" [nzScroll]="{ x: '1250px' }" [nzData]="lstData" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th rowspan="2">Nhà cung cấp</th>
        <ng-container *ngFor="let u of this.lstData[0]?.lstOffer; let i = index">
          <th colspan="2">{{'Lần ' +' '+ i+1 }}</th>
        </ng-container>
      </tr>
      <tr>
        <ng-container *ngFor="let u of this.lstData[0]?.lstOffer; let i = index">
          <th>Gia mong muốn</th>
          <th>Giá NCC đề nghị</th>
        </ng-container>
      </tr>

    </thead>
    <tbody>
      <tr *ngFor="let data of this.lstData">
        <td class="mw-25">{{data.name}}</td>
        <ng-container *ngFor="let u of this.lstData[0]?.lstOffer; let i = index">
          <td>{{u.suggestPrice | number}}</td>
          <td>{{u.valueOffer | number}}</td>
        </ng-container>
      </tr>
    </tbody>
  </nz-table>

</div>

<nz-row matDialogActions style="margin-top: 15px">
  <nz-col nzSpan="24" class="text-center">


    <button nz-button class="mr-2" (click)="closeDialog()"
      style="border-color: #e41717 ;color: #e41717 ;border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>

  </nz-col>
</nz-row>