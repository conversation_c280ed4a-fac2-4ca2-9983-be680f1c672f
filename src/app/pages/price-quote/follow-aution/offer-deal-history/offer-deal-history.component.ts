import { Component, Inject, OnInit, Optional } from '@angular/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'

@Component({
  selector: 'app-offer-deal-history',
  templateUrl: './offer-deal-history.component.html',
})
export class OfferDealHistoryComponent implements OnInit {
  modalTitle = 'Chi tiết thông tin đàm phán giá'
  lstData: any = []
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public dialogRef: MatDialogRef<OfferDealHistoryComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public dialog: MatDialog,
    public authenticationService: AuthenticationService
  ) {}

  async ngOnInit() {
    await this.findItem()
  }

  async findItem() {
    await this.apiService
      .post(this.apiService.OFFER_DEAL.LOAD_SUPPLIER_DEAL, { bidId: this.data.bidId, supplierId: this.data.supplierId })
      .then((result) => {
        this.notifyService.hideloading()
        this.lstData = result
      })
  }

  closeDialog() {
    this.dialogRef.close()
  }
}
