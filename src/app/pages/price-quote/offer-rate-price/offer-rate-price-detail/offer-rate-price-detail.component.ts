import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-offer-rate-price-detail',
  templateUrl: './offer-rate-price-detail.component.html',
})
export class OfferRatePriceDetailComponent implements OnInit {
  modalTitle = 'Bảng chi tiết đánh giá kết quả chào giá'
  loading = false
  pageSize = enumData.Page.pageSizeMax
  listOfData: any[] = []
  listOfDataCustom: any[] = []
  bidPriceCol: any[] = []
  bidPriceColId: any[] = []
  bidSupplierPriceColValue: any[] = []
  bidSupplier: any
  mpoType = enumData.ColType.MPO.code
  supType = enumData.ColType.Supplier.code
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<OfferRatePriceDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private drawerService: NzDrawerService
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadBestData()
  }

  loadBestData() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.OFFER_RATE.LOAD_BEST_PRICE_VALUE, {
        bidId: this.data.data.offerId,
        bidSupplierId: this.data.data.id,
      })
      .then((res) => {
        this.listOfData = res[0]
        this.bidPriceCol = res[1] || []
        this.bidSupplierPriceColValue = res[2] || []
        this.bidSupplier = res[3] || {}
        this.listOfDataCustom = res[4] || []
        this.bidPriceColId = this.bidPriceCol.map((c) => c.id)
        this.notifyService.hideloading()
      })
  }

  onValidBidSupplierPrice() {
    this.data.data.isPriceValid = true
    this.closeDialog(this.data)
  }

  onInvalidBidSupplierPrice() {
    this.data.data.isPriceValid = false
    this.closeDialog(this.data)
  }

  // showModalBidSupplierPriceDetail(bidPrice: any) {
  //   this.drawerService.create({
  //     nzTitle: `Bảng xếp hạng đánh giá các NCC hạng mục [${bidPrice.name}]`,
  //     nzContent: BidSupplierPriceDetailComponent,
  //     nzContentParams: { bidPriceId: bidPrice.id },
  //     nzWidth: '640',
  //   })
  // }

  closeDialog(data: any) {
    this.dialogRef.close(data)
  }

  clickExportExcelPrice() {
    setTimeout(() => {
      const tbl = document.getElementById('price-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng đánh giá bảng chào giá của nhà thầu ${this.data.data.supplierName}.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  clickExportExcelCustomPrice() {
    setTimeout(() => {
      const tbl = document.getElementById('custom-price-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng đánh giá thông tin cơ cấu giá của nhà thầu ${
        this.data.data.supplierName
      }.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
