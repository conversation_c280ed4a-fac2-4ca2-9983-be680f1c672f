import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { OfferRatePriceDetailComponent } from './offer-rate-price-detail/offer-rate-price-detail.component'

@Component({
  selector: 'app-offer-rate-price',
  templateUrl: './offer-rate-price.component.html',
})
export class OfferRatePriceComponent implements OnInit {
  modalTitle = 'Thông tin đánh giá bảng chào giá, cơ cấu giá các doanh nghiệp dự thầu'
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataObject: any = {}
  isNeedConfirm = false
  language_key: any
  isNeedApprove = false
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<OfferRatePriceComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.isNeedConfirm =
      (true &&
        (this.data.statusRatePrice === enumData.BidPriceRateStatus.DangTao.code ||
          this.data.statusRatePrice === enumData.BidPriceRateStatus.TuChoi.code)) ||
      this.data.statusRatePrice === null

    this.isNeedApprove = this.data.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code
    this.loadData()
  }

  /** Hàm lấy ds ncc tham gia thầu và tính điểm */
  loadData() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.OFFER_RATE.LOAD_PRICE_RATE(this.data.id), {}).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  viewDetail(item: any, data: any) {
    const object = { data, template: item.lstBidPrice, isNeedConfirm: this.isNeedConfirm }
    this.dialog.open(OfferRatePriceDetailComponent, { disableClose: false, data: object })
  }

  createBidPriceRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER_RATE.CREATE_PRICE_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }

  approveTradeRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER_RATE.APPROVE_TRADE_RATE, this.dataObject).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }

  rejectTradeRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER_RATE.REJECT_TRADE_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }
}
