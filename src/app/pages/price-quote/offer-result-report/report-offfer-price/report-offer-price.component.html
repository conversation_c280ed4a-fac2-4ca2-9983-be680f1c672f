<div id="print-section" *ngIf="dataReport" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150" />
  <div style="text-align: center; font-weight: 700; font-size: 18px">
    <div>BÁO CÁO SO SÁNH TỔNG HỢP BẢNG CHÀO GIÁ</div>
    <div>TÊN GÓI THẦU: {{ dataReport.bidName }}</div>
    <div style="font-size: 15px">Ngày in: {{ todate | date : 'dd/MM/yyyy' }}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px" *ngFor="let bidChild of dataReport.listItem">
    <div style="margin-bottom: 10px">
      Item: <b>{{ bidChild.itemName }}</b>
    </div>
    <table id="price-table" style="margin-bottom: 10px; border-spacing: 0" align="center">
      <thead>
        <tr style="text-align: center">
          <th rowspan="2" style="width: 40px; border: darkgrey 1px solid">&nbsp;{{ language_key?.NO || 'STT' }}&nbsp;</th>
          <th rowspan="2" style="width: 300px; border: darkgrey 1px solid">&nbsp;Tên hạng mục&nbsp;</th>
          <th
            colspan="3"
            style="width: 300px; border: darkgrey 1px solid; cursor: pointer"
            *ngFor="let bidSupplier of bidChild.lstBidSupplier"
            (click)="showDetail(bidSupplier)"
          >
            &nbsp;{{ bidSupplier.supplierName }}&nbsp;
          </th>
        </tr>
        <tr style="text-align: center">
          <ng-container *ngFor="let bidSupplier of bidChild.lstBidSupplier">
            <td style="border: darkgrey 1px solid">&nbsp;Số lượng&nbsp;</td>
            <td style="border: darkgrey 1px solid">&nbsp;Đơn giá&nbsp;</td>
            <td style="border: darkgrey 1px solid">&nbsp;Thành tiền&nbsp;</td>
          </ng-container>
        </tr>
        <tr style="font-weight: 700">
          <td colspan="2" style="border: darkgrey 1px solid; text-align: left">&nbsp;Xếp loại&nbsp;</td>
          <td colspan="3" align="center" style="border: darkgrey 1px solid" *ngFor="let bidSupplier of bidChild.lstBidSupplier; let i = index">
            {{ bidSupplier.rankPrice || i + 1 }}
          </td>
        </tr>
        <tr style="font-weight: 700">
          <td colspan="2" style="border: darkgrey 1px solid; text-align: left">&nbsp;Thành tiền&nbsp;</td>
          <td colspan="3" align="center" style="border: darkgrey 1px solid" *ngFor="let bidSupplier of bidChild.lstBidSupplier">
            {{ bidSupplier.totalPrice | number }}
          </td>
        </tr>
      </thead>
      <tbody *ngIf="bidChild.bidPrices?.length">
        <ng-container *ngFor="let priceLv1 of bidChild.bidPrices">
          <tr>
            <td style="border: darkgrey 1px solid">&nbsp;{{ priceLv1.sort > 0 ? priceLv1.sort : '' }}&nbsp;</td>
            <td style="border: darkgrey 1px solid">&nbsp;{{ priceLv1.name }}&nbsp;</td>
            <ng-container *ngFor="let bidSupplier of bidChild.lstBidSupplier">
              <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv1[bidSupplier.id].number | number : '1.0-2' }}&nbsp;</td>
              <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv1[bidSupplier.id].value | number : '1.0-2' }}&nbsp;</td>
              <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv1[bidSupplier.id].price | number : '1.0-2' }}&nbsp;</td>
            </ng-container>
          </tr>
          <ng-container *ngFor="let priceLv2 of priceLv1.__childs__">
            <tr>
              <td style="border: darkgrey 1px solid">&ensp;&nbsp;{{ priceLv2.sort > 0 ? priceLv2.sort : '' }}&nbsp;</td>
              <td style="border: darkgrey 1px solid">&nbsp;{{ priceLv2.name }}&nbsp;</td>
              <ng-container *ngFor="let bidSupplier of bidChild.lstBidSupplier">
                <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv2[bidSupplier.id].number | number : '1.0-2' }}&nbsp;</td>
                <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv2[bidSupplier.id].value | number : '1.0-2' }}&nbsp;</td>
                <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv2[bidSupplier.id].price | number : '1.0-2' }}&nbsp;</td>
              </ng-container>
            </tr>
            <ng-container *ngFor="let priceLv3 of priceLv2.__childs__">
              <tr>
                <td style="border: darkgrey 1px solid">&emsp;&nbsp;{{ priceLv3.sort > 0 ? priceLv3.sort : '' }}&nbsp;</td>
                <td style="border: darkgrey 1px solid">&nbsp;{{ priceLv3.name }}&nbsp;</td>
                <ng-container *ngFor="let bidSupplier of bidChild.lstBidSupplier">
                  <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv3[bidSupplier.id].number | number : '1.0-2' }}&nbsp;</td>
                  <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv3[bidSupplier.id].value | number : '1.0-2' }}&nbsp;</td>
                  <td align="right" style="border: darkgrey 1px solid">&nbsp;{{ priceLv3[bidSupplier.id].price | number : '1.0-2' }}&nbsp;</td>
                </ng-container>
              </tr>
            </ng-container>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px">
    <div style="text-align: right; font-weight: 700" *ngIf="dataReport.approveChooseSupplierWinDate">
      Ngày {{ dataReport.approveChooseSupplierWinDate | date : 'dd' }} tháng {{ dataReport.approveChooseSupplierWinDate | date : 'MM' }} năm
      {{ dataReport.approveChooseSupplierWinDate | date : 'yyyy' }}
    </div>
    <div style="text-align: right; font-weight: 700" *ngIf="!dataReport.approveChooseSupplierWinDate">Ngày ..... tháng ..... năm .....</div>
    <table style="margin: 10px 0 100px 0">
      <thead>
        <tr style="text-align: center">
          <th style="width: 40px"></th>
          <th style="width: 33%">ĐỀ XUẤT</th>
          <th style="width: 33%">KIỂM TRA</th>
          <th style="width: 33%">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-5 text-center" *ngIf="!dataReport.isHidePrint">
  <!-- <button nz-button (click)="clickExportExcel()" class="mr-2"><span nz-icon nzType="download"></span>Xuất excel</button>
  <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section" ngxPrint>
    <span nz-icon nzType="printer"></span> In báo cáo
  </button> -->
</div>

<nz-modal
  [(nzVisible)]="isVisibleDetail"
  [nzTitle]="titleDetail"
  (nzOnCancel)="handleCancel()"
  [nzWidth]="'70vw'"
  [nzFooter]="null"
  *ngIf="bidSupplier"
>
  <ng-container *nzModalContent>
    <app-offer-history-price [offerId]="bidSupplier.bidId" [supplierId]="bidSupplier.supplierId"></app-offer-history-price>
  </ng-container>
</nz-modal>
