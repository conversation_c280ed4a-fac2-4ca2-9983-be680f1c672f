import { Component, Input, OnInit } from '@angular/core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-report-offer-price',
  templateUrl: './report-offer-price.component.html',
})
export class ReportOfferPriceComponent implements OnInit {
  loading = false
  pageSize = enumData.Page.pageSizeMax
  isVisibleDetail = false
  titleDetail = ''
  bidSupplier: any
  todate = new Date()
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(public coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) { }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankPrice - b.rankPrice)
    }
  }



  showDetail(bidSupplier: any) {
    this.isVisibleDetail = true
    this.titleDetail = `Báo cáo bảng chào giá và lịch sử nộp giá doanh nghiệp ${bidSupplier.supplierName}`
    this.bidSupplier = bidSupplier
  }

  handleCancel() {
    this.isVisibleDetail = false
    this.bidSupplier = null
  }
}
