import { Component, OnInit, Input } from '@angular/core'

import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from 'src/app/services'
@Component({
  selector: 'app-offer-history-price',
  templateUrl: './offer-history-price.component.html',
})
export class OfferHistoryPriceComponent implements OnInit {
  loading = false
  pageSize = enumData.Page.pageSizeMax
  bidSupplier: any
  lstResult: any[] = []
  dataPrice: any
  todate = new Date()
  isShowListBidDeal = false
  bid: any
  @Input()
  public offerId!: string
  @Input()
  public supplierId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDetail()
  }

  loadDetail() {
    if (this.offerId) {
      this.loading = true
      this.apiService.get(this.apiService.OFFER_DETAIL.GET_BID_HISTORY_PRICE(this.offerId), { supplierId: this.supplierId }).then((res) => {
        this.loading = false
        this.bid = res.bid
        this.bidSupplier = res.bidSupplier
        this.lstResult = res.lstResult || []
        if (this.lstResult.length > 0) {
          this.dataPrice = this.lstResult[0]
          this.dataPrice.totalPrice = 0
          for (const data1 of this.dataPrice.lstPrice) {
            const value = +data1.value
            data1.price = data1.number * value
            this.dataPrice.totalPrice += data1.price
            for (const data2 of data1.__childs__) {
              const value = +data2.value
              data2.price = data2.number * value
              for (const data3 of data2.__childs__) {
                const value = +data3.value
                data3.price = data3.number * value
              }
            }
          }
        }
      })
    }
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('price-detail-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng chào giá doanh nghiệp ${this.bidSupplier.supplierName}.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  clickExportExcelByTable(table: string) {
    setTimeout(() => {
      const tbl = document.getElementById(table)
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Lịch sử nộp giá doanh nghiệp ${this.bidSupplier.supplierName}.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
