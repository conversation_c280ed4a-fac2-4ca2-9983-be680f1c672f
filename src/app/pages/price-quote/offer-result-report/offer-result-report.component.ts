import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { Location } from '@angular/common'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({ templateUrl: './offer-result-report.component.html' })
export class OfferReportComponent implements OnInit {
  dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private route: ActivatedRoute,
    private apiService: ApiService,
    private _location: Location,
    @Optional() @Inject(MAT_DIALOG_DATA) public offerId: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    const id = this.route.snapshot.paramMap.get('id')
    this.offerId = id
    if (id) {
      this.notifyService.showloading()
      this.apiService.get(this.apiService.OFFER_RATE.GET_DATA_REPORT(id), {}).then(async (res) => {
        this.notifyService.hideloading()
        this.dataReport = res
      })
    }
  }
  closeDialog() {
    this._location.back()
  }
}
