import { Component, Input, OnInit } from '@angular/core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-report-offer-result',
  templateUrl: './report-offer-result.component.html',
})
export class ReportOfferResultComponent implements OnInit {
  pageSize = enumData.Page.pageSizeMax
  loading = false
  listOfData: any[] = []
  temp = 'Đang đánh giá'
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(private coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) { }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankTotalScore - b.rankTotalScore)
    }
  }


}
