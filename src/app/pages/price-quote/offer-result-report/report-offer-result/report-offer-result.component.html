<nz-row>
  <!-- <button nz-button (click)="clickExportExcel()" class="mr-2"><span nz-icon nzType="download"></span>Xuất excel</button> -->
  <button nz-button class="ant-btn-blue" [useExistingCss]="true" printSectionId="print-section" ngxPrint>
    <span nz-icon nzType="printer"></span>In
  </button>
</nz-row>
<div class="mt-3" id="print-section">
  <nz-row class="mb-3" *ngFor="let bidChild of dataReport.listItem">
    <div class="mb-3">
      Item: <b>{{ bidChild.itemName }}</b>
    </div>
    <nz-table
      nz-col
      nzSpan="24"
      [nzData]="bidChild.lstBidSupplier"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr>
          <th rowspan="2">
            {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệ<PERSON>' }}
          </th>
          <th colspan="4">Điểm hệ thống</th>
          <th colspan="4">Điểm HĐXT chấm</th>
        </tr>
        <tr>
          <th>Tổng điểm</th>
          <th>Điểm kỹ thuật</th>
          <th>Điểm báo giá</th>
          <th>Điểm ĐKTM</th>
          <th>Tổng điểm HĐXT</th>
          <th>Điểm HĐXT kỹ thuật</th>
          <th>Điểm HĐXT báo giá</th>
          <th>Điểm HĐXT ĐKTM</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of bidChild.lstBidSupplier">
          <td class="mw-25">
            {{ data.supplierName }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreTotal !== -1 }">
            {{ data.scoreTotal === -1 ? temp : (data.scoreTotal | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreTech !== -1 }">
            {{ data.scoreTech === -1 ? temp : (data.scoreTech | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scorePrice !== -1 }">
            {{ data.scorePrice === -1 ? temp : (data.scorePrice | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreTrade !== -1 }">
            {{ data.scoreTrade === -1 ? temp : (data.scoreTrade | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreManualTotal !== -1 }">
            {{ data.scoreManualTotal === -1 ? temp : (data.scoreManualTotal | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreManualTech !== -1 }">
            {{ data.scoreManualTech === -1 ? temp : (data.scoreManualTech | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreManualPrice !== -1 }">
            {{ data.scoreManualPrice === -1 ? temp : (data.scoreManualPrice | number : '1.0-2') }}
          </td>
          <td [ngClass]="{ 'text-right': data.scoreManualTrade !== -1 }">
            {{ data.scoreManualTrade === -1 ? temp : (data.scoreManualTrade | number : '1.0-2') }}
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>

  <nz-row class="mb-5">
    <nz-col nzSpan="24">Hội đồng xét thầu</nz-col>
    <nz-col nzSpan="8" class="text-center">MPO</nz-col>
    <nz-col nzSpan="8" class="text-center">PHÒNG KỸ THUẬT</nz-col>
    <nz-col nzSpan="8" class="text-center">BAN GIÁM ĐỐC</nz-col>
  </nz-row>
</div>
