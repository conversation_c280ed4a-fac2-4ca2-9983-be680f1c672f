<div id="print-section" *ngIf="dataReport" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150" />
  <div style="text-align: center; font-weight: 700; font-size: 18px">
    <div>BÁO CÁO SO SÁNH TỔNG ĐIỂM CÁC TIÊU CHÍ</div>
    <div>TÊN GÓI THẦU: {{ dataReport.bidName }}</div>
    <div style="font-size: 15px">Ngày in: {{ todate | date : 'dd/MM/yyyy' }}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px" *ngFor="let bidChild of dataReport.listItem">
    <div style="margin-bottom: 10px">
      Item: <b>{{ bidChild.itemName }}</b>
    </div>
    <table id="total-table" style="margin-bottom: 10px; border-spacing: 0" align="center">
      <thead>
        <tr style="text-align: center">
          <th style="width: 8%; border: darkgrey 1px solid">Xếp hạng tổng</th>
          <th style="width: 20%; border: darkgrey 1px solid">
            {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}
          </th>
          <th style="width: 8%; border: darkgrey 1px solid">Tổng điểm</th>
          <th style="width: 8%; border: darkgrey 1px solid">Xếp hạng năng lực</th>
          <th style="width: 8%; border: darkgrey 1px solid">Điểm năng lực</th>
          <th style="width: 8%; border: darkgrey 1px solid">Xếp hạng kỹ thuật</th>
          <th style="width: 8%; border: darkgrey 1px solid">Điểm kỹ thuật</th>
          <th style="width: 8%; border: darkgrey 1px solid">Xếp hạng ĐKTM</th>
          <th style="width: 8%; border: darkgrey 1px solid">Điểm ĐKTM</th>
          <th style="width: 8%; border: darkgrey 1px solid">Xếp hạng bảng giá</th>
          <th style="width: 8%; border: darkgrey 1px solid">Điểm bảng giá</th>
        </tr>
      </thead>
      <tbody>
        <tr style="text-align: center" *ngFor="let data of bidChild.lstBidSupplier; let i = index">
          <td style="border: darkgrey 1px solid">{{ data.rankTotalScore }}</td>
          <td align="left" style="border: darkgrey 1px solid">&nbsp;{{ data.supplierName }}</td>
          <td style="border: darkgrey 1px solid">
            {{ data.scoreTotal == -1 ? temp : (data.scoreTotal | number : '1.0-2') }}
          </td>
          <td style="border: darkgrey 1px solid">{{ data.rankCapacity }}</td>
          <td style="border: darkgrey 1px solid">
            {{ data.scoreCapacity == -1 ? temp : (data.scoreCapacity | number : '1.0-2') }}
          </td>
          <td style="border: darkgrey 1px solid">{{ data.rankTech }}</td>
          <td style="border: darkgrey 1px solid">
            {{ data.scoreTech == -1 ? temp : (data.scoreTech | number : '1.0-2') }}
          </td>
          <td style="border: darkgrey 1px solid">{{ data.rankTrade }}</td>
          <td style="border: darkgrey 1px solid">
            {{ data.scoreTrade == -1 ? temp : (data.scoreTrade | number : '1.0-2') }}
          </td>
          <td style="border: darkgrey 1px solid">{{ data.rankScorePrice }}</td>
          <td style="border: darkgrey 1px solid">
            {{ data.scorePrice == -1 ? temp : (data.scorePrice | number : '1.0-2') }}
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr style="font-weight: 600">
          <td align="center" style="background-color: yellow">{{ bidChild.lstBidSupplier.length }}</td>
          <td align="left" style="background-color: yellow">Công ty tham gia</td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tfoot>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px">
    <div style="text-align: right; font-weight: 700" *ngIf="dataReport.approveChooseSupplierWinDate">
      Ngày {{ dataReport.approveChooseSupplierWinDate | date : 'dd' }} tháng {{ dataReport.approveChooseSupplierWinDate | date : 'MM' }} năm
      {{ dataReport.approveChooseSupplierWinDate | date : 'yyyy' }}
    </div>
    <div style="text-align: right; font-weight: 700" *ngIf="!dataReport.approveChooseSupplierWinDate">Ngày ..... tháng ..... năm .....</div>
    <table style="margin: 10px 0 100px 0">
      <thead>
        <tr style="text-align: center">
          <th style="width: 5px"></th>
          <th style="width: 25%">BP. MPO</th>
          <th style="width: 25%">BP. YÊU CẦU</th>
          <th style="width: 25%">BP. TÀI CHÍNH</th>
          <th style="width: 25%">BAN GIÁM ĐỐC</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-5 text-center">
  <!-- <button nz-button (click)="clickExportExcel()" class="mr-2">
    <span nz-icon nzType="download"></span>Xuất excel
  </button> -->
  <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section" ngxPrint>
    <span nz-icon nzType="printer"></span> In báo cáo
  </button>
</div>
