import { Component, Input, OnInit } from '@angular/core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-report-offer-tech',
  templateUrl: './report-offer-tech.component.html',
})
export class ReportOfferTechComponent implements OnInit {
  loading = false
  pageSize = enumData.Page.pageSizeMax
  isVisibleDetail = false
  titleDetail = ''
  bidSupplier: any
  todate = new Date()
  temp = 'Đang đánh giá'
  @Input()
  public dataReport: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(public coreService: CoreService, private notifyService: NotifyService, private storageService: StorageService) { }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    for (const child of this.dataReport.listItem) {
      child.lstBidSupplier.sort((a: any, b: any) => a.rankTech - b.rankTech)
    }
  }



  showDetail(bidSupplier: any) {
    this.isVisibleDetail = true
    this.titleDetail = `Báo cáo chi tiết kỹ thuật doanh nghiệp ${bidSupplier.supplierName}`
    this.bidSupplier = bidSupplier
  }

  handleCancel() {
    this.isVisibleDetail = false
    this.bidSupplier = null
  }
}
