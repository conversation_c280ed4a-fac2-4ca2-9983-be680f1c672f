import { Component, OnInit, Input } from '@angular/core'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, StorageService } from 'src/app/services'
@Component({
  selector: 'app-offer-result-trade',
  templateUrl: './offer-result-trade.component.html',
})
export class OfferResultTradeComponent implements OnInit {
  dataObject: any
  loading = false
  listOfData: any[] = []
  dataType = enumData.DataType
  pageSize = enumData.Page.pageSizeMax
  todate = new Date()
  @Input()
  public bidId!: string | undefined
  @Input()
  public supplierId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.loadDataList()
  }

  loadDataList() {
    if (this.bidId) {
      this.loading = true
      this.apiService.get(this.apiService.OFFER_DETAIL.GET_BID_RESULT_TRADE(this.bidId), { supplierId: this.supplierId }).then((data) => {
        if (data) {
          this.loading = false
          this.dataObject = data
        }
      })
    }
  }

  getSupplierScore(bidTradeId: string) {
    const bidTrade = this.dataObject.bidTrades.find((p: any) => p.id === bidTradeId)
    if (bidTrade && bidTrade.percent > 0 && bidTrade.__childs__?.length > 0) {
      let temp = 0
      for (const child of bidTrade.__childs__) {
        const find = this.dataObject.__bidSupplierTradeValue__.find((p: any) => p.bidTradeId === child.id)
        if (find && find.score > 0) {
          temp += find.score
        }
      }
      temp = (temp * bidTrade.percent) / 100
      return temp
    } else if (this.dataObject.__bidSupplierTradeValue__) {
      const find = this.dataObject.__bidSupplierTradeValue__.find((p: any) => p.bidTradeId === bidTradeId)
      if (find) return find.score
    }
    return ''
  }

  getSupplierValue(bidTradeId: string) {
    if (this.dataObject.__bidSupplierTradeValue__) {
      const find = this.dataObject.__bidSupplierTradeValue__.find((p: any) => p.bidTradeId === bidTradeId)
      if (find) return find.value
    }
    return ''
  }

  convertTypeList(bidTrade: any) {
    if (this.dataObject.__bidSupplierTradeValue__) {
      const list = bidTrade.__bidTradeListDetails__
      const find = this.dataObject.__bidSupplierTradeValue__.find((p: any) => p.bidTradeId === bidTrade.id)
      if (find && list) {
        const data = list.find((p: any) => p.id === find.value)
        if (data) return data.name
      }
    }
    return ''
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('trade-detail-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Kết quả đánh giá thông tin ĐKTM doanh nghiệp ${
        this.dataObject.supplierName
      }.xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
