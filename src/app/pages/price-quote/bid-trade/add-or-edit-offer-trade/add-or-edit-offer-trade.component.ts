import { Component, OnInit, Inject, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../../../core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../../services'

@Component({ templateUrl: './add-or-edit-offer-trade.component.html' })
export class AddOrEditOfferTradeComponent implements OnInit {
  modalTitle = 'Thêm mới điều kiện thương mại'
  isShowPercent = false
  dataObject: any = {}
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  dataOfferTrade: any[] = []
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddOrEditOfferTradeComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.loadDataOfferTrade()
    if (this.data && this.data !== null) {
      if (this.data.id) this.modalTitle = 'Chỉnh sửa điều kiện thương mại'
      this.dataObject = { ...this.data }
      this.onCheckShowPercent()
    }
  }

  onCheckShowPercent() {
    if (this.dataObject.type === enumData.DataType.Number.code || this.dataObject.type === enumData.DataType.List.code) {
      this.isShowPercent = true
      this.dataObject.percent = this.dataObject.percent || 0
    } else {
      this.isShowPercent = false
      this.dataObject.percent = null
      this.dataObject.percentRule = null
    }
  }

  async loadDataOfferTrade() {
    if (this.data.offerId)
      await this.apiService.get(this.apiService.OFFER.OFFERTRADE_GET(this.data.offerId), {}).then((result) => {
        this.dataOfferTrade = result
      })
    if (this.data.bidId)
      await this.apiService.get(this.apiService.OFFER.OFFERTRADE_GET(this.data.bidId), {}).then((result) => {
        this.dataOfferTrade = result
      })
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.parentId && this.dataObject.parentId !== '') {
      this.dataObject.level = 2
    } else {
      this.dataObject.level = 1
    }
    if (this.dataObject.type !== this.dataType.Number.code) this.dataObject.percentRule = null
    if (this.dataObject.percentRule !== null && this.dataObject.percentRule <= 0) {
      this.notifyService.showError('Vui lòng nhập giá trị đạt là số lớn hơn 0!')
      return
    }
    if (this.dataObject.percentDownRule !== null && this.dataObject.percentDownRule <= 0) {
      this.notifyService.showError('Vui lòng nhập điều kiện liệt tỉ trọng là số lớn hơn 0!')
      return
    }
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.OFFER.OFFERTRADE_CREATE, this.dataObject).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
      this.closeDialog(true)
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.OFFER.OFFERTRADE_UPDATE, this.dataObject).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.closeDialog(true)
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
