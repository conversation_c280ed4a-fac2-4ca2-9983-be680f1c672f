<nz-row matDialogTitle>

  <nz-col nzSpan="24" class="text-center">
    <strong style="size: 35px"> {{ modalTitle | uppercase }}</strong>
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-form-control nzSpan="24">
    <nz-form-label>Lĩnh vực mua hàng</nz-form-label>
    <nz-select
      [disabled]="true"
      nzShowSearch
      nzAllowClear
      [nzPlaceHolder]="'Chọn Lĩnh vực mua hàng'"
      [(ngModel)]="data.count"
      name="count"
    >
      <nz-option *ngFor="let item of data.listItem" [nzLabel]="item.itemName" [nzValue]="item.count"> </nz-option>
    </nz-select>
  </nz-form-control>

  <div *ngFor="let item of data.listItem; let i = index" class="mt-3">
    <div *ngIf="i === data.count">
      <nz-row nzGutter="8" class="mt-3">
        <nz-col nzSpan="18">
          <button class="mr-2" nz-button (click)="clickAdd(item)" nzType="primary">
            <span nz-icon nzType="plus"></span>
            {{ language_key?.ADD || 'Thêm mới' }}
          </button>

          <button nz-button (click)="loadTrade(item)" class="ant-btn-blue mr-2">Tải điều kiện thương mại từ template</button>

          <button
            class="mr-2"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn xoá tất cả điều kiện thương mại?"
            nzPopconfirmPlacement="bottom"
            (nzOnConfirm)="clickDeleteAll(item)"
            nzDanger
          >
            <span nz-icon nzType="delete" nzTheme="outline"></span>
            Xoá tất cả
          </button>
          <button class="mr-2" style="margin-left: 10px" nz-button (click)="clickExportExcel(item)">
            <span nz-icon nzType="download"></span>Xuất excel
          </button>

          <input
            class="hidden"
            type="file"
            id="file"
            (change)="clickImportExcel($event, item)"
            onclick="this.value=null"
            accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          />
          <label for="file" class="label-custom-file">
            <span nz-icon nzType="upload"></span>
            {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
          </label>
        </nz-col>
      </nz-row>
      <nz-row class="mt-3">
        <span *ngIf="item.sumPercent < 100" class="text-orange">Tổng tỉ trọng đạt {{ item.sumPercent }}%, chưa đủ 100%</span>
        <nz-table nz-col nzSpan="24" [nzData]="item.listTrade" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>{{ language_key?.NO || 'STT' }}</th>
              <th>Tên điều kiện thương mại</th>
              <th>Tỉ trọng(%)</th>
              <th>Giá trị đạt</th>
              <th>Kiểu dữ liệu</th>
              <th>Bắt buộc?</th>
              <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let rowData of item.listTrade">
              <tr>
                <td (click)="clickEdit(rowData)">
                  {{ rowData.sort > 0 ? rowData.sort : '' }}
                </td>
                <td (click)="clickEdit(rowData)" class="mw-25">{{ rowData.name }}</td>
                <td (click)="clickEdit(rowData)" class="text-right">{{ rowData.percent }}</td>
                <td (click)="clickEdit(rowData)" class="text-right">{{ rowData.percentRule | number }}</td>
                <td (click)="clickEdit(rowData)">
                  {{ rowData.type }}
                </td>
                <td (click)="clickEdit(rowData)">
                  {{ rowData.isRequired ? 'Bắt buộc' : 'Không' }}
                </td>
                <td>
                  <button
                    *ngIf="rowData.type === dataType.List.code"
                    nz-tooltip
                    nzTooltipTitle="Cấu hình danh sách"
                    class="mr-3"
                    nz-button
                    [nzType]="rowData.__bidTradeListDetails__?.length > 0 ? 'default' : 'dashed'"
                    (click)="settingList(rowData)"
                  >
                    <span nz-icon nzType="unordered-list"></span>
                  </button>
                  <button
                    *ngIf="data.isMPO && !(data.skipApprove === true && data.status === bidStatus.DangDuyetGoiThau.code)"
                    nz-popconfirm
                    nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?"
                    nzPopconfirmPlacement="bottom"
                    (nzOnConfirm)="clickDelete(rowData)"
                    nz-tooltip
                    nzTooltipTitle="Xoá tiêu chí"
                    nz-button
                    nzDanger
                  >
                    <span nz-icon nzType="delete"></span>
                  </button>
                </td>
              </tr>
              <ng-container>
                <td [nzIndentSize]="10" colspan="8" scope="colgroup" *ngIf="rowData.sumPercent < 100 && rowData.sumPercent >= 0" class="text-orange">
                  Tổng tỉ trọng trong mục này đạt {{ rowData.sumPercent }}%, chưa đủ 100%
                </td>
                <tr *ngFor="let childData of rowData.__childs__">
                  <td (click)="clickEdit(childData)" [nzIndentSize]="10">{{ childData.sort > 0 ? childData.sort : '' }}</td>
                  <td (click)="clickEdit(childData)" class="mw-25">
                    {{ childData.name }}
                  </td>
                  <td (click)="clickEdit(childData)" class="text-right">{{ childData.percent }}</td>
                  <td (click)="clickEdit(childData)" class="text-right">{{ childData.percentRule | number }}</td>
                  <td (click)="clickEdit(childData)">
                    {{ childData.type }}
                  </td>
                  <td (click)="clickEdit(childData)">
                    {{ childData.isRequired ? 'Bắt buộc' : 'Không' }}
                  </td>
                  <td>
                    <button
                      *ngIf="childData.type === dataType.List.code"
                      nz-tooltip
                      nzTooltipTitle="Cấu hình danh sách"
                      class="mr-3"
                      nz-button
                      [nzType]="childData.__bidTradeListDetails__?.length > 0 ? 'default' : 'dashed'"
                      (click)="settingList(childData)"
                    >
                      <span nz-icon nzType="unordered-list"></span>
                    </button>

                    <button
                      nz-popconfirm
                      nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?"
                      nzPopconfirmPlacement="bottom"
                      (nzOnConfirm)="clickDelete(childData)"
                      nz-tooltip
                      nzTooltipTitle="Xoá tiêu chí"
                      nz-button
                      nzDanger
                    >
                      <span nz-icon nzType="delete"></span>
                    </button>

                    <button
                      *ngIf="data.isMPO"
                      nz-popconfirm
                      nzPopconfirmTitle="Bạn có chắc muốn xoá tiêu chí này?"
                      nzPopconfirmPlacement="bottom"
                      (nzOnConfirm)="clickDelete(childData)"
                      nz-tooltip
                      nzTooltipTitle="Xoá tiêu chí"
                      nz-button
                      nzDanger
                    >
                      <span nz-icon nzType="delete"></span>
                    </button>
                  </td>
                </tr>
              </ng-container>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-row>
    </div>
  </div>

  <!-- <nz-row class="mt-3">
    <h4>Người yêu cầu ghi chú:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'" [(ngModel)]="data.noteTrade"
        [disabled]="!data.isShowCreateTrade"></textarea>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <h4>Người duyệt ghi chú:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.noteMPOLeader" [disabled]="true"></textarea>
    </nz-col>
  </nz-row> -->
</div>

<nz-row style="margin-top: 15px">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="createTrade()" class="mr-2 button-save">
      <!-- <button nz-button (click)="createTrade()" class="mr-2 button-save" *ngIf="true "> -->
      <!-- Gửi yêu cầu phê duyệt điều kiện thương mại -->
      <span nz-icon nzType="save" nzTheme="outline"></span>
      {{ language_key?.SAVE || 'Lưu' }}
    </button>

    <button nz-button (click)="sendTrade()" class="mr-2 button-save" *ngIf="data.statusTrade === tradeStatus.DaTao.code">
      <span nz-icon nzType="send" nzTheme="outline"></span>
      <!-- <button nz-button (click)="acceptTrade()" class="mr-2 button-save" *ngIf="true"> -->
      {{ 'Gửi duyệt' }}
    </button>

    <!-- Duyệt yêu cầu phê duyệt điều kiện thương mại -->
    <button nz-button (click)="acceptTrade()" class="mr-2 button-save" *ngIf="data.statusTrade === tradeStatus.GuiDuyet.code && data.canApprove">
      <span nz-icon nzType="save" nzTheme="outline"></span>
      <!-- <button nz-button (click)="acceptTrade()" class="mr-2 button-save" *ngIf="true"> -->
      {{ 'Duyệt' }}
    </button>

    <button nz-button class="mr-2" (click)="closeDialog(false)" style="border-color: #e41717; color: #e41717; border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>
  </nz-col>
</nz-row>
