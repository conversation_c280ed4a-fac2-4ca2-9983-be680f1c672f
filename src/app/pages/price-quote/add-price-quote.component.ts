import { Component, OnInit, Optional, Inject } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { Router } from '@angular/router'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { CreateItemQuoteComponent } from './create-item-pr/create-item-quote.component'

@Component({
  selector: 'app-add-price-quote',
  templateUrl: './add-price-quote.component.html',
  styleUrls: ['./add-price-quote.component.scss'],
})
export class AddPriceQuoteComponent implements OnInit {
  modalTitle = 'Thêm mới yêu cầu báo giá'
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  lstUnit: any[] = []
  loading = false
  language_key: any
  dataObject: any = {}
  dataObjectSearch: any = {}
  enum = enumData
  pageSize = enumData.Page.pageSizeMax

  orderType = enumData.OrderType

  isOrderShipment = false
  lstShipment: any
  lstOrderType = this.coreService.convertObjToArray(enumData.OrderType)

  setOfCheckedId = new Set<any>()
  dataPr: any[] = []
  dataBusiness: any[] = []

  checked = false
  indeterminate = false
  lstStatus = [enumData.SupplierServiceExpertiseStatus.DaThamDinh, enumData.SupplierServiceExpertiseStatus.ChuaThamDinh]
  lstTypeGetData = [
    { code: 1, name: 'Chỉ chọn nhà cung cấp trong LVMH' },
    { code: 2, name: 'Chỉ chọn nhà cung cấp ngoài LVMH' },
  ]
  lstTypeSup: any = {}
  lstType: any = {}
  dateFormat = 'dd/MM/yyyy'
  lstSup: any[] = []
  maxSizeUpload = enumData.maxSizeUpload
  isVisiblePopupChooseAddress = false
  isLoadCity = false
  lstExternalMatGroup: any[] = []
  isEdit = false
  dataCity: any[] = []
  dataDistrict: any[] = []
  dataSearch: any = {}
  dataWard: any[] = []
  dataEmployee: any
  isAllDisplayDataChecked = false
  isIndeterminate = false
  numberOfChecked = 0
  supplier: any
  service: any
  cityId!: string
  districtId!: string
  wardId!: string
  address!: string
  fieldCurrent: any

  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  referenceType: any

  enumRole: any
  dataCurrency: any
  action: any
  objPermission: any = {}
  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private drawerService: NzDrawerService,
    private storageService: StorageService,
    private router: Router,
    private dialogRef: MatDialogRef<AddPriceQuoteComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
  }

  async checkBiddingType() {
    if (this.dataObject.offerTypeCode === this.orderType.SHIPPING.code) {
      this.isOrderShipment = true
      // await this.apiService.post(this.apiService.SHIPMENT.FIND_COMPANY, {}).then((result) => {
      //   this.lstShipment = result
      // })
    } else {
      this.isOrderShipment = false
    }
  }

  async loadShipment() {
    // await this.apiService.post(this.apiService.SHIPMENT_COST.LOAD_DETAIL, { id: this.dataObject.shipmentId }).then((result) => {
    //   this.dataObject.lstPriceShipment = result.lstPrice
    //   this.notifyService.showInfo(`Thực hiện load danh sách bảng giá của Shipment ${result.code} `)
    // })
  }

  loadUnit() {
    this.notifyService.showloading()
    // Promise.all([this.apiService.post(this.apiService.UNIT.DATA_SELECT_BOX, {})]).then(async (res) => {
    //   this.lstUnit = res[0]
    // })
    this.notifyService.hideloading()
  }
  /*  */
  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })

    /* Loại nguồn tham chiếu  */

    this.language_key = this.coreService.getLanguage()
    this.dataObject.supplier = []
    this.dataObject.service1 = []
    this.dataObject.isGetFromPr = true
    this.dataObject.supplier = new Set(this.dataObject.supplier)
    this.supplier = [
      { name: 'Public', id: '0' },
      { name: 'Nhà hàng SeSan', id: '1' },
      { name: 'CÔNG TY BẢO HIỂM PVI GIA ĐỊNH', id: '2' },
      { name: 'Công ty cung cấp phần mềm Citek', id: '3' },
      { name: 'CÔNG TY CỔ PHẦN KIM CƯƠNG', id: '4' },
      { name: 'Công Ty Trách Nhiệm HH 1 Thành Viên TRÙNG KHÁNH ', id: '5' },
      { name: 'Công ty cổ phần giải phải TMA', id: '6' },
      { name: 'Công Ty TNHH 1 Thành viên GALAXY Khánh An', id: '7' },
      { name: 'TỔNG CÔNG TY CP BẢO HIỂM HÀNG KHÔNG', id: '8' },
      { name: 'Công ty Cổ Phần Phần Mềm Bravo', id: '9' },
      { name: 'Nhà hàng SeSan2', id: '10' },
    ]

    this.service = [
      {
        id: '1',
        name: 'Thực phẩm sống',
      },
      {
        id: '2',
        name: 'Thực phẩm lạnh',
      },
      {
        id: '3',
        name: 'Thực phẩm nóng',
      },
      {
        id: '4',
        name: 'Thực phẩm ăn nhanh',
      },
      {
        id: '5',
        name: 'Thực phẩm chưa chế biến',
      },
      {
        id: '6',
        name: 'Thực phẩm đã chế biến',
      },
      {
        id: '7',
        name: 'Thực phẩm đóng gói',
      },
    ]

    if (this.data && this.data.id) {
      this.modalTitle = 'Chỉnh sửa chào giá nhanh'
      this.dataObject = { ...this.data }
      this.isEdit = true
    } else if (history.state.status === 'add') {
    }
    // this.dataObject.listItem = [
    //   { code: 'GAOST24', name: 'Gạo ST24' },
    //   { code: 'GAOST25', name: 'Gạo ST25' },
    // ]
    this.lstType = this.coreService.convertObjToArray(this.enum.QuickPriceType)
    this.referenceType = this.coreService.convertObjToArray(this.enum.ReferenceType)
    this.lstTypeSup = this.coreService.convertObjToArray(this.enum.supplierType)
    this.dataObject.listSupp = []
    this.dataObject.isShowClient = false
    await this.loadData()
    this.loadUnit()
    await this.loadFullEmployee()
    this.dataObject.listItem = []
    //
  }

  async loadFullEmployee() {
    await this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((result) => {
      this.dataEmployee = result
    })
  }

  submitForm() {
    //#endregion
  }

  async findSup() {
    this.dataObjectSearch.exCode = this.dataObject.externalMaterialGroupId

    //     /* Load danh sách nhà cung cấp theo ex-group */{
    //   Promise.all([this.apiService.post(this.apiService.SUPPLIER.FIND_BY_COMPANY, { exMatGrId: this.dataObject.externalMaterialGroupId })]).then((res) => {
    //     this.dataObject.lstSup = res[0]
    //   })
    // }
    // await this.apiService.post(this.apiService.SUPPLIER.FIND_BY_COMPANY, { data: this.dataObjectSearch }).then((res) => {
    //   this.loading = false
    //   this.dataObject.lstSup = res || []
    //   this.refreshStatus()
    //   this.dataObjectSearch = {}
    // })
  }
  async loadData() {
    this.apiService.post(this.apiService.SERVICE.FIND, { isLast: true }).then((result) => {
      this.lstExternalMatGroup = result
      console.log(this.lstExternalMatGroup )
    })
    // await this.apiService.post(this.apiService.SUPPLIER.LOAD_DATA_SELECT, {}).then((res) => {
    //   this.loading = false
    //   this.dataObject.lstSup = res || []
    //   this.refreshStatus()
    // })
    // this.notifyService.showloading()
    // Promise.all([
    //   this.apiService.post(this.apiService.CURRENCY.DATA_SELECT_BOX, {}), //1
    // ]).then(async (res) => {
    //   this.notifyService.hideloading()
    //   this.dataCurrency = res[0]
    // })
    // Promise.all([this.apiService.post(this.apiService.COMPANY.FIND_USER_EXGROUP, { companyId: this.dataObject.companyInvite })]).then(async (res) => {
    //   this.lstExternalMatGroup = res[0]
    // })
  }

  onChangeExternalMaterialGroup() {
    /**Nếu chọn lại exMat thì reset lại danh sách Item & Ds ncc, Load Bid theo exMatGr */
    this.dataObject.lstItem = []

    this.findBusinessPlant()
    if (this.dataObject.externalMaterialGroupId) {
      this.findPrWithExMatGr()
    } else {
      /**Không thì load all bid */
      this.findPr()
    }
    /* nếu là loại Exmat thì item bằng = ex  */
    if (this.dataObject.bidAutionType === enumData.AuctionType.MatGroup.code) {
      for (const item of this.lstExternalMatGroup) {
        if (this.dataObject.externalMaterialGroupId === item.id) this.dataObject.lstItem.push(item)
      }
    }

    /* Load danh sách nhà cung cấp theo ex-group */ {
      // Promise.all([this.apiService.post(this.apiService.SUPPLIER.FIND_BY_COMPANY, { exMatGrId: this.dataObject.externalMaterialGroupId })]).then(
      //   (res) => {
      //     this.dataObject.lstSup = res[0]
      //   }
      // )
    }
  }

  async findBusinessPlant() {
    // Promise.all([this.apiService.post(this.apiService.BUSINESS_PLAN.FIND, {})]).then((res) => {
    //   this.dataBusiness = res[0]
    // })
  }

  async findPrWithExMatGr() {
    // Promise.all([this.apiService.post(this.apiService.PR.FIND_WITH_EX, { exMatGrId: this.dataObject.externalMaterialGroupId })]).then((res) => {
    //   this.dataPr = res[0]
    //   // this.dataSupplierSrc = this.dataSupplier
    // })
  }
  async findPr() {
    Promise.all([this.apiService.post(this.apiService.PR.FIND, {})]).then((res) => {
      this.dataPr = res[0]
    })
  }
  async loadDataSup() {
    const where: any = {}
    if (this.dataSearch.supplierName && this.dataSearch.supplierName !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    if (this.dataSearch.lstStatus && this.dataSearch.lstStatus.length > 0) {
      where.lstStatus = this.dataSearch.lstStatus
    }
    this.dataSearch.typeGetData = this.dataSearch.typeGetData || 1
    where.typeGetData = this.dataSearch.typeGetData

    await this.apiService.post(this.apiService.SUPPLIER.LOAD_OFFER, where).then((res) => {
      this.dataObject.lstSup = res || []
      this.refreshStatus()
    })
  }

  onAddService() {
    if (!this.dataObject.service1) {
      this.notifyService.showWarning('Bạn chưa chọn dịch vụ')
      // return 0
    } else {
      this.dataObject.listItem = [
        ...this.dataObject.listItem,
        {
          id: this.dataObject.service1.id,
          code: this.dataObject.service1.code,
          name: this.dataObject.service1.name,
        },
      ]
    }
  }

  onDeleteBank(index: number) {
    this.dataObject.listItem = this.dataObject.listItem.filter((_expense: any, idx: any) => idx !== index)
  }

  onChangePr() {
    // this.dataSupplier = this.dataSupplierSrc
    this.dataObject.lstSup = []
    this.dataObject.lstItem = []
    if (!this.dataObject.prId) return
    /* Tìm ra danh sách nhà cung cấp của gói thầu được chọn */
    this.loadData()
    /* Tìm ra danh sách item của gói thầu được chọn  */
    this.apiService.post(this.apiService.PR.FIND, { lstId: this.dataObject.prId }).then((res) => {
      this.dataObject.lstItem = res || []
    })
  }
  /* Hàm load item của BusinessPlan */

  onChangeBusiness() {
    // this.dataSupplier = this.dataSupplierSrc
    this.dataObject.lstSup = []
    this.dataObject.lstItem = []
    if (!this.dataObject.businessPlanId) return
    /* Tìm ra danh sách nhà cung cấp của gói thầu được chọn */
    this.loadData()
    /* Tìm ra danh sách item của gói thầu được chọn  */
    // this.apiService.post(this.apiService.BUSINESS_PLAN.FIND_PR_AU, { id: this.dataObject.businessPlanId }).then((res) => {
    //   this.dataObject.lstItem = res || []
    // })
  }

  closeDialog() {
    this.router.navigate(['/price-quote/list'])
  }

  onSave() {
    this.notifyService.showloading()
    this.dataObject.supplier = Array.from(this.dataObject.supplier)
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.OFFER.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.router.navigate(['/price-quote/list'])
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.OFFER.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.router.navigate(['/price-quote/list'])
      }
    })
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }

  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.dataObject.supplier.add(id)
    } else {
      this.dataObject.supplier.delete(id)
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked)
    this.refreshCheckedStatus()
  }

  onAllChecked(value: boolean): void {
    this.dataObject.lstSup.forEach((item: any) => this.updateCheckedSet(item.id, value))
    this.refreshCheckedStatus()
  }

  onCurrentPageDataChange($event: any): void {
    this.dataObject.lstSup = $event
    this.refreshCheckedStatus()
  }

  refreshCheckedStatus(): void {
    this.checked = this.dataObject.lstSup.every((item: any) => this.dataObject.supplier.has(item.id))
    this.indeterminate = this.dataObject.lstSup.some((item: any) => this.dataObject.supplier.has(item.id)) && !this.checked
  }

  checkAll(value: boolean) {
    this.dataObject.lstSup.forEach((item: any) => (item.isChosen = value))
    this.refreshStatus()
  }
  refreshStatus() {
    this.isAllDisplayDataChecked = this.dataObject.lstSup.every((item: any) => item.isChosen)
    this.isIndeterminate = this.dataObject.lstSup.some((item: any) => item.isChosen) && !this.isAllDisplayDataChecked
    this.numberOfChecked = this.dataObject.lstSup.filter((item: any) => item.isChosen).length
  }

  showSupplierInfo(data: any) {
    // this.drawerService.create({
    //   nzTitle: 'Chi tiết nhà cung cấp',
    //   nzContent: BidSupplierDetailComponent,
    //   nzContentParams: data,
    //   nzWidth: '640',
    // })
  }

  clickEdit(obj: any) {
    obj.isEdit = true
    obj.externalMaterialGroupId = this.dataObject.externalMaterialGroupId
    this.dialog
      .open(CreateItemQuoteComponent, { data: obj, disableClose: false })
      .afterClosed()
      .subscribe((data) => {})
  }

  onDelete(index: any) {
    if (this.isEdit === true) {
      const item = this.dataObject.lstItem.find((x: any) => x.index === index)
      item.isChangePrItem = true
    } else {
      let lstItem = this.dataObject.lstItem
      this.dataObject.lstItem.splice(index, 1)
      let i = 1
      for (let item of lstItem) {
        item.index = i
        item.itemNo = i * 10
        i++
      }
    }
  }

  onAdd() {
    if (!this.dataObject.externalMaterialGroupId) {
      this.notifyService.showError('Vui lòng chọn Lĩnh vực mua hàng')
      return
    }
    let length = (this.dataObject.lstItem.length || 0) + 1
    let data = {
      number: length,
      createdDate: this.dataObject.createdAt,
      lstItem: this.dataObject.lstItem,
      externalMaterialGroupId: this.dataObject.externalMaterialGroupId,
    }
    this.notifyService.showloading()
    this.dialog
      .open(CreateItemQuoteComponent, { data, disableClose: false })
      .afterClosed()
      .subscribe((data) => {
        if (data !== true) {
          const len = this.dataObject.lstItem.length
          const item = {
            index: '' + (len + 1),
            materialId: data.materialId,
            materialCode: data?.materialCode,
            quantity: data.quantity,
            shortText: data.shortText,
            deliveryDate: data.deliveryDate,
            unitId: data.unitId,
            unitCode: data.unitCode,
            isNew: true,
            materialGroupCode: data.materialGroupCode,
            materialGroupId: data.materialGroupId,
            materialGroupName: data.materialGroupName,
            plantCode: data.plantCode,
            isChangePrItem: false,
            itemNo: data.itemNo,
            valuationType: data.valuationType,
            category: data.category,
            subNoAset: data.subNoAset,
            trackingNumber: data.trackingNumber,
            itemText: data.itemText,
            itemNote: data.itemNote,
            deliveryText: data.deliveryText,
            materialPoText: data.materialPoText,
            batch: data.batch,
            ounId: data.ounId,
            ounCode: data.ounCode,
            glAccountId: data.glAccountId,
            glAccountCode: data.glAccountCode,
            costCenterId: data.costCenterId,
            costCenterCode: data.costCenterCode,
            valuationPrice: data.valuationPrice,
            orderId: data.orderId,
            orderCode: data.orderCode,
            assetId: data.assetId,
            assetCode: data.assetCode,
            categoryName: data.categoryName,
            purchasingGroupId: data.purchasingGroupId,
            lstItemCompoment: [],
            externalMaterialGroupName: data.externalMaterialGroupName,
            fileList: data.fileList,
            purchasingGroupName: data.purchasingGroupName,
            assetName: data.assetName,
            costCenterName: data.costCenterName,
            glAccountname: data.glAccountname,
            ounName: data.ounName,
            unitName: data.unitName,
            plantName: data.plantName,
          }
          this.dataObject.lstItem = [...this.dataObject.lstItem, item]
        }
      })
    this.notifyService.hideloading()
  }
}
