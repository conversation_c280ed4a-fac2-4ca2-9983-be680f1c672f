import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from '../../core/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { ConfigServiceComponent } from './config-service/config-service.component'
import { Router } from '@angular/router'
import { OfferSupplierTradeRateComponent } from './offer-supplier-trade-rate/offer-supplier-trade-rate.component'
import { OfferRatePriceComponent } from './offer-rate-price/offer-rate-price.component'
import { OfferRankByMinPriceComponent } from './offer-rate/offer-rank-by-min-price/offer-rank-by-min-price.component'
import { OfferRankBySumPriceComponent } from './offer-rate/offer-rank-by-sum-price/offer-rank-by-sum-price.component'
import { OfferEvaluationComponent } from './offer-evaluation/offer-evaluation.component'

@Component({
  selector: 'app-price-quote',
  templateUrl: './price-quote.component.html',
})
export class PriceQuoteComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  enumData = enumData
  biddingType = enumData.BiddingType
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataSearch: any = {}
  dateFormat = 'dd/MM/yyyy'
  dataStatus = this.coreService.convertObjToArray(enumData.SupplierStatus)
  listOfData: any[] = []
  errorString!: string
  isExporting = false
  lstErrorImport: any[] = []
  isVisibleError = false
  isVisibleChangePw = false
  isVisible = false
  supplierChoose: any
  timePeriod = new Date()
  targetId: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  objPermission: any = {}
  bidPriceMinListItem: any
  isVisible1 = false
  isVisible2 = false
  offerChoose: any

  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private router: Router,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.searchData()
    //
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    // this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = { isDeleted: false }
    if (this.dataSearch.supplierCode2 && this.dataSearch.supplierCode2 !== '') {
      where.code = this.dataSearch.supplierCode2
    }

    if (this.dataSearch.supplierCode1 && this.dataSearch.supplierCode1 !== '') {
      where.name = this.dataSearch.supplierCode1
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.supplierCode && this.dataSearch.supplierCode.length !== '') {
      where.supplierCode = this.dataSearch.supplierCode
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    this.apiService.post(this.apiService.OFFER.PAGINATION, dataSearch).then((res) => {
      this.loading = false

      this.total = res[1]
      this.listOfData = res[0]
      for (const item of this.listOfData) {
        if (item.listSupplierService) item.itemName = item.listSupplierService.map((c: any) => c.itemName).join()
      }
    })
  }

  chooseSupplier(object: any) {
    this.router.navigate(['/price-quote/list/offer-choose-supplier', object.id])
    // this.dialog
    //   .open(BidChooseSupplierComponent, { disableClose: false, data: object })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     this.searchData()
    //   })
  }

  bidSupplierTradeRate(object: any) {
    this.dialog
      .open(OfferSupplierTradeRateComponent, { data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  bidDealAuction(object: { id: string; name: string }) {
    this.router.navigate(['/price-quote/offer-item'], {
      queryParams: { id: object.id, name: object.name },
      queryParamsHandling: 'merge',
    })
  }

  bidFollowDealAuction(object: { id: string; name: string }) {
    this.router.navigate(['/price-quote/follow-offer-deal'], {
      queryParams: { id: object.id, name: object.name },
      queryParamsHandling: 'merge',
    })
  }

  bidEvaluation(object: any) {
    const data = {
      bidId: object.id,
      isShowEnd: object.isShowEnd,
      isShowAcceptEnd: object.isShowAcceptEnd,
    }
    this.dialog
      .open(OfferEvaluationComponent, { data })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  bidSupplierPriceRate(object: any) {
    object.isNotConfigTrade = object.isNotConfigTrade
    this.dialog
      .open(OfferRatePriceComponent, { data: object })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  reportRateBid(object: any) {
    this.router.navigate(['price-quote/report', object.id])
  }

  //#region Phân tích giá

  /** Load các hạng mục giá của gói thầu */
  private async loadPrice(bidId: string) {
    this.notifyService.showloading()
    await this.apiService.get(this.apiService.OFFER.GET_PRICE(bidId), {}).then((res) => {
      this.notifyService.hideloading()
      this.bidPriceMinListItem = res
    })
  }

  async rankByMinPrice(object: any) {
    await this.loadPrice(object.id)
    this.isVisible1 = true
    this.offerChoose = object
  }

  /** Xếp hạng theo tổng giá (Giá theo từng NCC) */
  async rankBySumPrice(object: any) {
    await this.loadPrice(object.id)
    this.isVisible2 = true
    this.offerChoose = object
  }

  bidTrade(object: any) {
    this.router.navigate(['/price-quote/list/trade', object.id])
  }
  bidPrice(object: any) {
    this.router.navigate(['/price-quote/list/price', object.id])
  }

  clickAdd() {
    this.router.navigate(['/price-quote/list/add'], {
      state: { status: 'add' },
    })
  }

  clickView(object: any) {
    this.router.navigate(['/price-quote/list/detail', object.id])
  }
  clickEdit(object: any) {
    this.dialog
      .open(ConfigServiceComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.searchData()
      })
  }

  clickShowErrorImport() {
    this.isVisibleError = true
  }

  closeModelError() {
    this.isVisibleError = false
  }

  showModal(id: any): void {
    this.isVisible = true
    this.targetId = id
  }

  async handleOk() {
    this.isVisible = false
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.OFFER.UPDATE_TIME, { targetId: this.targetId, timePeriod: this.timePeriod }).then((res) => {
      this.loading = false
      this.notifyService.hideloading()
      this.searchData()
    })
    this.targetId = ''
  }

  async sendApprove(data: any) {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.OFFER.SEND_APPROVE, { targetId: data.id }).then((res) => {
      this.loading = false
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async approve(data: any) {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.OFFER.APPROVE, { targetId: data.id }).then((res) => {
      this.loading = false
      this.notifyService.hideloading()
      this.searchData()
    })
  }

  async public(data: any) {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.OFFER.PUBLIC, { targetId: data.id }).then((res) => {
      this.loading = false
      this.notifyService.hideloading()
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  handleCancel(): void {
    this.isVisible = false
    this.targetId = ''
  }

  hidePopupAnalysPrice() {
    this.isVisible1 = this.isVisible2 = false
    this.offerChoose = undefined
  }

  analysisData(item: any) {
    let lstBidPriceId = item.listPrice.filter((c: any) => c.isChoose).map((c: any) => c.id)
    if (lstBidPriceId.length == 0) lstBidPriceId = item.listPrice.map((c: any) => c.id)

    if (this.isVisible1) {
      this.dialog.open(OfferRankByMinPriceComponent, {
        disableClose: false,
        data: { bid: item, title: this.offerChoose.title, lstId: lstBidPriceId },
      })
    } else {
      this.dialog.open(OfferRankBySumPriceComponent, {
        disableClose: false,
        data: { bid: item, title: this.offerChoose.title, lstId: lstBidPriceId },
      })
    }

    this.isVisible1 = this.isVisible2 = false
  }

  onChangeChooseAll(item: any) {
    for (const price of item.listPrice) {
      price.isChoose = item.isChooseAll
    }
  }
}
