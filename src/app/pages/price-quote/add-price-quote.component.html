<form nz-form #frmAdd="ngForm">
  <nz-row>
    <nz-col nzSpan="24" class="text-center fs-24 fw-600">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>
  <nz-collapse>
    <nz-collapse-panel [nzHeader]="'Thông tin chung'" [nzActive]="true">
      <nz-row nzGutter="8">
        
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiêu <PERSON></nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tiêu Đ<PERSON> ">
              <input nz-input placeholder="Nhập Tiêu <PERSON>" [(ngModel)]="dataObject.title" name="title" required [disabled]="isEdit" />
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Nhân viên phụ trách -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" nzRequired>Nhân viên phụ trách</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Nhân viên phụ trách!">
              <nz-select
                nzShowSearch
                nzAllowClear
                nzPlaceHolder="Chọn Nhân viên phụ trách"
                [(ngModel)]="dataObject.employeeId"
                name="employeeId"
                required
              >
                <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" nzRequired class="text-left"> Mục đích chào giá </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Mục đích chào giá!">
              <nz-select
                nzShowSearch
                nzAllowClear
                nzPlaceHolder="Chọn Mục đích chào giá"
                [(ngModel)]="dataObject.offerTypeCode"
                name="offerTypeCode"
                (ngModelChange)="checkBiddingType()"
              >
                <nz-option *ngFor="let item of lstOrderType" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6" *ngIf="isOrderShipment">
          <nz-form-item>
            <nz-form-label nzSpan="24" nzFor="shipmentId" nzRequired class="text-left"> Mã Shipment Cost </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Mã shipment Cost!">
              <nz-select
                nzShowSearch
                nzAllowClear
                nzPlaceHolder="Chọn Mã shipment Cost"
                [(ngModel)]="dataObject.shipmentId"
                name="shipmentId"
                (ngModelChange)="loadShipment()"
                [ngModelOptions]="{ standalone: true }"
              >
                <nz-option *ngFor="let item of lstShipment" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <!-- Ngày bắt đầu phụ lục< -->
        <nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" nzRequired>Ngày bắt đầu</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="">
              <nz-date-picker
                [nzFormat]="dateFormat"
                name="effectiveDate"
                placeholder="Ngày bắt đầu"
                [(ngModel)]="dataObject.effectiveDate"
                [ngModelOptions]="{ standalone: true }"
                required
              >
              </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Ngày hết hạn -->
        <nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" nzRequired>Ngày kết thúc</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="">
              <nz-date-picker
                [nzFormat]="dateFormat"
                name="endDate"
                placeholder="Ngày kết thúc"
                [(ngModel)]="dataObject.endDate"
                [ngModelOptions]="{ standalone: true }"
                required
              >
              </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Đơn vị tiền tệ -->
        <nz-col nzSpan="6">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Đơn vị tiền tệ</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập chọn đơn vị tiền tệ">
              <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Chọn đơn vị tiền tệ" [(ngModel)]="dataObject.currency" name="currency">
                <nz-option *ngFor="let item of dataCurrency" [nzLabel]="item.code" [nzValue]="item.code"> </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Thời gian hiệu lực báo giá -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời gian hiệu lực báo giá</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Thời gian hiệu lực báo giá ">
              <input nz-input placeholder="Nhập Thời gian hiệu lực báo giá" [(ngModel)]="dataObject.timePeriod" name="timePeriod" required />
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Điều kiện thanh toán -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left" nzRequired>Điều kiện thanh toán</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Điều kiện thanh toán ">
              <input nz-input placeholder="Nhập Điều kiện thanh toán" [(ngModel)]="dataObject.condition" name="condition" required />
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Địa điểm giao hàng -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Địa điểm giao hàng</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Địa điểm giao hàng ">
              <input nz-input placeholder="Nhập Địa điểm giao hàng" [(ngModel)]="dataObject.address" name="address" />
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- PM Order -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">PM Order</nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập PM Order ">
              <input nz-input placeholder="Nhập PM Order" [(ngModel)]="dataObject.noteTrade" name="noteTrade" />
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <!-- Giá khai báo giá đã bao gồm VAT -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="dataObject.isHaveVat" name="isHaveVat" id="isHaveVat"> Giá khai báo giá đã bao gồm VAT </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <!-- Không cấu hình điều kiện thương mại -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="dataObject.isNotConfigTrade" name="isNotConfigTrade" id="isNotConfigTrade"> Không cấu hình điều kiện thương mại </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <!-- Cho phép hiển thị thông tin ở trường NCC -->
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="dataObject.isShowClient" name="isShowClient" id="isShowClient"> Cho phép hiển thị thông tin ở trường NCC </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" nzFor="fileAttach" class="text-left">File đính kèm</nz-form-label>
            <nz-form-control nzSpan="24" [nzErrorTip]="'Vui lòng upload file đính kèm'">
              <label for="fileAttach" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
              <input
                class="hidden"
                type="file"
                id="fileAttach"
                [(ngModel)]="dataObject.fileAttach"
                name="fileAttach"
                (change)="handleFileInput($event, 'fileAttach')"
              />
              <div class="tooltip" *ngIf="dataObject.fileAttach && dataObject.fileAttach.length > 0">
                <a href="{{ dataObject.fileAttach }}" target="_blank">Xem file</a>
              </div>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <!-- Mô tả -->
        <nz-col nzSpan="24">
          <nz-form-item nzFlex>
            <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
            <nz-form-control nzSpan="24">
              <textarea rows="4" nz-input placeholder="Nhập Mô tả" [(ngModel)]="dataObject.description" name="description"></textarea>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
    <nz-collapse-panel *ngIf="!isOrderShipment" [nzHeader]="'Thông tin Item'" [nzActive]="true">
      <nz-row nzGutter="8">
        <!-- <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-control nzSpan="24">
              <label nz-checkbox [(ngModel)]="dataObject.isGetFromPr" name="isGetFromPr" id="isGetFromPr"> Lấy danh sách Item từ PR </label>
            </nz-form-control>
          </nz-form-item>
        </nz-col> -->

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left">Lĩnh vực mua hàng</nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select
                [disabled]="isEdit"
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataObject.externalMaterialGroupId"
                name="externalMaterialGroupId"
                nzPlaceHolder="Chọn lĩnh vực mua hàng"
                (ngModelChange)="onChangeExternalMaterialGroup()"
              >
                <nz-option *ngFor="let item of lstExternalMatGroup" [nzValue]="item.id" [nzLabel]="item.code + '-' + item.name"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left" nzRequired>Nguồn tham chiếu</nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataObject.refType"
                name="refType"
                nzPlaceHolder="Chọn Nguồn tham chiếu"
                (ngModelChange)="onChangeExternalMaterialGroup()"
              >
                <nz-option *ngFor="let item of referenceType" [nzValue]="item.code" [nzLabel]="item.name"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6" *ngIf="dataObject.refType && dataObject.refType === enum.ReferenceType.PR.code">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" nzRequired>PR </nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataObject.prId"
                name="prId"
                nzPlaceHolder="Chọn PR"
                (ngModelChange)="onChangePr()"
                [disabled]="isEdit"
              >
                <nz-option *ngFor="let item of dataPr" [nzLabel]="item.code" [nzValue]="item.id" required></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="6" *ngIf="dataObject.refType && dataObject.refType === enum.ReferenceType.BusinessPlan.code">
          <nz-form-item>
            <nz-form-label class="text-left" nzSpan="24" nzRequired>Phương án kinh doanh</nz-form-label>
            <nz-form-control nzSpan="24">
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="dataObject.businessPlanId"
                name="businessPlanId"
                nzPlaceHolder="Chọn Phương án kinh doanh"
                (ngModelChange)="onChangeBusiness()"
                [disabled]="isEdit"
              >
                <nz-option *ngFor="let item of dataBusiness" [nzLabel]="item.code" [nzValue]="item.id" required></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-col>

        <nz-col nzSpan="24" class="mt-3 mb-3">
          <button *ngIf="!dataObject.isGetFromPr" class="mr-2" nz-button (click)="onAdd()" nzType="primary">
            <span nz-icon nzType="plus"></span>Tạo mới
          </button>
        </nz-col>
      </nz-row>
      <!-- <nz-table *ngIf="dataObject.isGetFromPr" nz-col nzSpan="24" [nzFrontPagination]="false" [nzData]="['']" nzBordered
        [nzScroll]="{ y: '400px' }" nzBordered>
        <thead>
          <tr>
            <th nzWidth="80px">STT</th>
            <th>LVMH</th>
            <th>Đơn vị tính</th>
            <th>Số lượng</th>
            <th>Giá trần</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dataItem of dataObject.lstItem; let i = index">
            <td>{{ i + 1 }}</td>
            <td>{{ dataItem.name }}</td>
            <td>
              <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataItem.unitId" name="unitId"
                nzPlaceHolder="Chọn lĩnh vực mua hàng">
                <nz-option *ngFor="let item of lstUnit" [nzValue]="item.id"
                  [nzLabel]="item.code  + '-' + item.name"></nz-option>
              </nz-select>
            </td>
            <td><input nz-input currencyMask nz-input name="quantity" [(ngModel)]="dataItem.quantity" /></td>
            <td><input nz-input currencyMask nz-input name="maxPrice" [(ngModel)]="dataItem.maxPrice" /></td>
          </tr>
        </tbody>
      </nz-table> -->

      <nz-table [nzData]="dataObject.lstItem && dataObject.lstItem.length > 0 ? [''] : []" [nzShowPagination]="false" nzBordered>
        <thead>
          <tr class="text-nowrap">
            <th nzWidth="50px">Item line</th>
            <th nzWidth="50px">Category</th>
            <th nzWidth="130px">Material</th>
            <th nzWidth="250px">Short text</th>
            <th nzWidth="50px">Quantity</th>
            <th nzWidth="100px">Delivery Date</th>
            <th nzWidth="100px">Plant</th>
            <th nzWidth="100px">Unit</th>
            <th nzWidth="100px">Material Group</th>
            <th nzRight nzWidth="50px">Tác vụ</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of dataObject.lstItem; let i = index">
            <td class="text-right">{{ data.itemNo | number }}</td>
            <td>{{ data.category }} - {{ data.categoryName }}</td>
            <td>{{ data.materialCode }}</td>
            <td nzBreakWord>{{ data.shortText }}</td>
            <td class="text-right">{{ data.quantity | number }}</td>
            <td>{{ data.deliveryDate | date : 'dd/MM/yyyy' }}</td>
            <td nzBreakWord>{{ data.plantName }}</td>
            <td>{{ data.unitName }}</td>
            <td>{{ data.materialGroupName }}</td>
            <td nzRight class="text-center">
              <button nzShape="circle" nzTrigger="click" nz-button nz-dropdown [nzDropdownMenu]="menu" nzPlacement="bottomCenter" class="btn-primary">
                <span nz-icon nzType="ellipsis"></span>
              </button>

              <nz-dropdown-menu #menu="nzDropdownMenu">
                <ul nz-menu>
                  <li nz-menu-item>
                    <button
                      style="min-width: 190px"
                      [disabled]="
                        data.isChangePrItem === true ||
                        data.itemClosed ||
                        data.isDeleted === true ||
                        (data.lstItemCompoment && data.lstItemCompoment.length > 0)
                      "
                      class="mr-2 mb-2 width-button"
                      nzType="primary"
                      (click)="clickEdit(data)"
                      nz-tooltip
                      [nzTooltipTitle]="'Chỉnh sửa'"
                      nz-button
                      nzPopconfirmPlacement="bottom"
                    >
                      <span nz-icon nzType="edit"></span> Chỉnh sửa
                    </button>
                  </li>
                  <li nz-menu-item>
                    <button
                      style="min-width: 190px"
                      class="mr-2 width-button"
                      (nzOnConfirm)="onDelete(data.index)"
                      [disabled]="data.isChangePrItem || data.isDeleted === true"
                      nz-tooltip
                      [nzTooltipTitle]="language_key?.DELETE || 'Xóa'"
                      nz-button
                      nzDanger
                      nz-popconfirm
                      nzPopconfirmTitle="Bạn có chắc muốn xóa Item ?"
                      nzPopconfirmPlacement="bottom"
                    >
                      <span nz-icon nzType="delete"></span> Xóa Item
                    </button>
                  </li>
                </ul>
              </nz-dropdown-menu>
            </td>
          </tr>
        </tbody>
      </nz-table>
      <!-- Lấy danh sách item từ PR -->
    </nz-collapse-panel>
    <nz-collapse-panel *ngIf="isOrderShipment" [nzHeader]="'Bảng giá'" [nzActive]="true">
      <nz-collapse class="mt-2">
        <!-- Danh sách bảng giá -->
        <nz-row nzGutter="8">
          <nz-col nzSpan="24">
            <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="dataObject.lstPriceShipment" [nzShowPagination]="true" nzBordered>
              <thead>
                <tr>
                  <th>STT</th>
                  <th>Condition type</th>
                  <th>Mô tả</th>
                  <th>Amount</th>
                  <th>Crcy</th>
                  <th>Per</th>
                  <th>Condition value</th>
                  <th>Curr</th>
                  <th>NumCCo</th>
                  <th>CConDe</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of dataObject.lstPriceShipment; let i1 = index">
                  <td>{{ i1 + 1 }}</td>
                  <td class="text-left">{{ data.conditionType }}</td>
                  <td class="text-left">{{ data.description }}</td>
                  <td class="text-left">{{ data.amount }}</td>
                  <td class="text-left">{{ data.crcy }}</td>
                  <td class="text-left">{{ data.per }}</td>
                  <td class="text-left">{{ data.conditionValue }}</td>
                  <td class="text-left">{{ data.curr }}</td>
                  <td class="text-left">{{ data.numCCo }}</td>
                  <td class="text-left">{{ data.cConDe }}</td>
                </tr>
              </tbody>
            </nz-table>
          </nz-col>
        </nz-row>
      </nz-collapse>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-row>
    <nz-col nzSpan="24" class="text-center mt-4">
      <button (click)="closeDialog()" nz-button class="mr-2 btn-cancel btn btn-primary" *ngIf="( !isEdit) || ( && isEdit)">
        <span nz-icon nzType="close"></span>Đóng
      </button>
      <button (click)="onSave()" nz-button [disabled]="!frmAdd.form.valid" nzType="primary">
        {{ language_key?.SAVE || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>
