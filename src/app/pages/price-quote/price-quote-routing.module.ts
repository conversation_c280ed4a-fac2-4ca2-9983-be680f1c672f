import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { PriceQuoteComponent } from './price-quote.component'
import { AddPriceQuoteComponent } from './add-price-quote/add-price-quote.component'
import { OfferTradeComponent } from './bid-trade/offer-trade.component'
import { OfferPriceComponent } from './bid-price/offer-price.component'
import { PriceQuoteDetail2Component } from './price-quote-detail2/price-quote-detail2.component'
import { OfferItemComponent } from './offer-item/offer-item.component'
import { OfferDealComponent } from './offer-item/offer-deal/offer-deal.component'
import { OfferReportComponent } from './offer-result-report/offer-result-report.component'
import { FollowAutionComponent } from './follow-aution/follow-aution.component'
import { OfferChooseSupplierComponent } from './offer-choose-supplier/offer-choose-supplier.component'

const routes: Routes = [
  { path: 'list', component: PriceQuoteComponent },
  { path: 'list/add', component: AddPriceQuoteComponent },
  { path: 'offer-item', component: OfferItemComponent },
  { path: 'offer-deal', component: OfferDealComponent },
  { path: 'follow-offer-deal', component: FollowAutionComponent },
  { path: 'list/trade/:id', component: OfferTradeComponent },
  { path: 'list/price/:id', component: OfferPriceComponent },
  { path: 'list/detail/:id', component: PriceQuoteDetail2Component },
  { path: 'report/:id', component: OfferReportComponent },
  { path: 'list/offer-choose-supplier/:id', component: OfferChooseSupplierComponent },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PriceQuoteRoutingModule {}
