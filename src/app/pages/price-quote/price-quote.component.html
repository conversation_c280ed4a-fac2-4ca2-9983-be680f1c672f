
<nz-collapse>
  <nz-collapse-panel [nzHeader]="'Tì<PERSON> kiếm chào giá nhanh'">
    <nz-row nzGutter="8">
      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ 'Mã chào giá' }}
          </nz-form-label>
          <input nz-input [placeholder]="'Mã chào giá'" [(ngModel)]="dataSearch.supplierCode2" name="supplierCode2" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ 'Tên chào giá' }}
          </nz-form-label>
          <input nz-input [placeholder]="'Tên chào giá'" [(ngModel)]="dataSearch.supplierCode1" name="supplierCode1" />
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="24" class="text-center mt-3">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true, true)" class="mr-2">
          <span nz-icon nzType="redo"></span>Xóa bộ lọc
        </button>
        <button nzShape="round" nz-button (click)="searchData(true)" nzType="primary" nzGhost><span nz-icon nzType="search"></span>Tìm kiếm</button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row nzGutter="8" class="mt-3">
  <nz-col nzSpan="16" style="text-align: left">
    <button nz-button nzType="primary" nzGhost (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ 'Tạo mới chào giá nhanh' }}
    </button>
  
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr class="text-nowrap">
        <th>{{ 'Mã RFQ' }}</th>
        <th>{{ 'Tiêu đề' }}</th>
        <th>{{ 'Ngày bắt đầu' }}</th>
        <th>{{ 'Hết hạn báo giá' }}</th>
        <th>{{ 'Ngày mở báo giá' }}</th>

        <th>{{ 'Số NCC mời' }}</th>
        <th>{{ 'Số NCC tham gia' }}</th>

        <th>{{ 'Ngày tạo' }}</th>
        <th>{{ 'Trạng thái' }}</th>
        <th>{{ 'Xem báo cáo' }}</th>
        <th>{{ 'Phân tích giá' }}</th>

        <th>{{ 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td (click)="clickView(data)">{{ data.code }}</td>
        <td (click)="clickView(data)">{{ data.title }}</td>
        <td (click)="clickView(data)">{{ data.effectiveDate | date : 'dd/MM/YYYY' }}</td>
        <td (click)="clickView(data)">{{ data.endDate | date : 'dd/MM/YYYY' }}</td>
        <td (click)="clickView(data)">{{ data.effectiveDate | date : 'dd/MM/YYYY' }}</td>
        <td (click)="clickView(data)">{{ data.countSp | number }}</td>
        <td (click)="clickView(data)">{{ data.countSp | number }}</td>
        <td (click)="clickView(data)">{{ data.createdAt | date : 'dd/MM/YYYY' }}</td>
        <td (click)="clickView(data)" class="text-center">
          <nz-tag
            [ngStyle]="{
              color: data.statusColor,
              border: '1px solid ' + data.statusBorderColor
            }"
            [nzColor]="data.statusBgColor"
            style="font-weight: 600; border-radius: 30px"
          >
            <div style="display: flex; align-items: center; justify-content: center">
              <div
                [ngStyle]="{
                  background: data.statusColor
                }"
                class="dot"
              ></div>
              <span class="ml-1"> {{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>
        <td class="text-center">
          <button
            nz-button
            nzShape="circle"
            (click)="reportRateBid(data)"
            nzTooltipTitle="Báo cáo kết quả đánh giá"
            nzTooltipPlacement="top"
            nz-tooltip
            style="text-align: center; border: none"
          >
            <i nz-icon nzType="file-text"></i>
          </button>
        </td>
        <td class="text-center">
          <button
            nz-button
            nzShape="circle"
            class="mr-1 text-btn"
            (click)="rankByMinPrice(data)"
            nzTooltipTitle="Xếp hạng theo giá thấp nhất (Giá theo từng hạng mục)"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
          >
            <i nz-icon nzType="deployment-unit"></i>
          </button>

          <button
            nz-button
            nzShape="circle"
            class="mr-1 text-btn"
            (click)="rankBySumPrice(data)"
            nzTooltipTitle="Xếp hạng theo tổng giá (Giá theo từng NCC)"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
          >
            <i nz-icon nzType="fork"></i>
          </button>
        </td>
        <td class="text-center">
          <button
            nz-button
            nzShape="circle"
            (click)="clickView(data)"
            class="mr-2 mt-2 btn-primary"
            nzTooltipTitle="Xem chi tiết"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button nzShape="circle" nzTrigger="click" nz-button nz-dropdown [nzDropdownMenu]="menu" nzPlacement="bottomCenter" class="btn-primary">
            <span nz-icon nzType="ellipsis"></span>
          </button>

          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-item *ngIf="enumData.OfferStatus.HoanTatCauHinh.code === data.status">
                <button
                  *ngIf="enumData.OfferStatus.HoanTatCauHinh.code === data.status"
                  nz-button
                  nzShape="circle"
                  (click)="public(data)"
                  class="mr-1 text-btn"
                  nzTooltipTitle="Duyệt"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon nzType="check" class="text-icon" nzTheme="outline"></i>Công khai
                </button>
              </li>

              <li nz-menu-item *ngIf="enumData.OfferStatus.HoanTatDanhGia.code === data.status">
                <button
                  nz-button
                  nzShape="circle"
                  class="mr-1 text-btn"
                  (click)="bidDealAuction(data)"
                  nzTooltipTitle="Đàm phán / Đấu giá"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon class="text-icon" nzType="dollar"></i>Đàm phán / đấu giá
                </button>
              </li>

              <li nz-menu-item *ngIf="enumData.OfferStatus.DangDamPhanGia.code === data.status">
                <button
                  nz-button
                  nzShape="circle"
                  class="mr-1 text-btn"
                  (click)="bidFollowDealAuction(data)"
                  nzTooltipTitle="Đàm phán / Đấu giá"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon class="text-icon" nzType="dollar"></i>Theo dõi đàm phán giá
                </button>
              </li>

              <li nz-menu-item>
                <button
                  class="mr-1 text-btn"
                  nz-button
                  nzShape="circle"
                  (click)="chooseSupplier(data)"
                  nzTooltipTitle="Thông tin mời thầu"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon nzType="home" class="text-icon"></i>Thông tin mời thầu
                </button>
              </li>

              <!-- <li *ngIf="enumData.OfferStatus.MoiTao.code === data.status && objPermission.isEdit" nz-menu-item>
                <button nz-button nzShape="circle" (click)="clickEdit(data)" class="mr-1 text-btn"
                  nzTooltipTitle="Cấu hình giá" nzTooltipPlacement="top" nz-tooltip>
                  <i nz-icon nzType="setting" class="text-icon"></i>Cấu hình giá
                </button>
              </li> -->

              <!-- <li nz-menu-item> -->
              <li
                nz-menu-item
                *ngIf="
                  (enumData.OfferStatus.DaCongKhai.code === data.status ||
                    enumData.BidTradeRateStatus.DaTao.code === data.statusRateTrade ||
                    data.canApproveTradeRate) &&
                  !data.hiddenTrade
                "
              >
                <button
                  nz-button
                  nzShape="circle"
                  class="mr-1 text-btn"
                  (click)="bidSupplierTradeRate(data)"
                  nzTooltipTitle="Đánh giá điều kiện thương mại"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon class="text-icon" nzType="shopping"></i>Đánh giá DKTM
                </button>
              </li>

              <!-- <li nz-menu-item> -->
              <li
                nz-menu-item
                *ngIf="
                  enumData.OfferStatus.DaCongKhai.code === data.status ||
                  enumData.BidTradeRateStatus.DaTao.code === data.statusRateTrade ||
                  data.canApproveTradeRate
                "
              >
                <button
                  nz-button
                  nzShape="circle"
                  class="mr-1 text-btn"
                  (click)="bidSupplierPriceRate(data)"
                  nzTooltipTitle="Đánh giá bảng chào giá, cơ cấu giá"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon class="text-icon" nzType="dollar"></i>Đánh giá bảng CG, CCG
                </button>
              </li>

              <li
                *ngIf="enumData.OfferStatus.MoiTao.code === data.status || enumData.OfferStatus.DangCauHinhGoiThau.code === data.status"
                nz-menu-item
              >
                <button
                  nz-button
                  *ngIf="data.canAddDate"
                  nzShape="circle"
                  (click)="showModal(data.id)"
                  class="mr-1 text-btn"
                  nzTooltipTitle="Gia hạn "
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon nzType="calendar" class="text-icon"></i>Gia hạn báo giá
                </button>
              </li>
              <li
                *ngIf="
                  (enumData.OfferStatus.MoiTao.code === data.status ||
                    enumData.OfferStatus.DangCauHinhGoiThau.code === data.status ||
                    data.canApproveTrade) &&
                  !data.hiddenTrade
                "
                nz-menu-item
              >
                <button
                  class="mr-1 text-btn"
                  nz-button
                  nzShape="circle"
                  (click)="bidTrade(data)"
                  nzTooltipTitle="Thiết lập điều kiện thương mại"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon nzType="shopping" class="text-icon"></i>Thiết lập DKTM
                </button>
              </li>

              <li *ngIf="data.isShowEnd" nz-menu-item>
                <button
                  *ngIf="data.isShowEnd"
                  nz-button
                  nzShape="circle"
                  class="mr-1 text-btn"
                  (click)="bidEvaluation(data)"
                  nzTooltipTitle="NCC thắng chào giá"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon nzType="audit" class="text-icon"></i>NCC thắng chào giá
                </button>
              </li>

              <li
                *ngIf="
                  (enumData.OfferStatus.MoiTao.code === data.status || enumData.OfferStatus.DangCauHinhGoiThau.code === data.status) &&
                  data.offerTypeCode !== biddingType.SHIPPING.code
                "
                nz-menu-item
              >
                <button
                  class="mr-1 text-btn"
                  nz-button
                  nzShape="circle"
                  (click)="bidPrice(data)"
                  nzTooltipTitle="Thiết lập các hạng mục chào giá, cơ cấu giá"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon nzType="dollar" class="text-icon"></i>Thiết lập các HMCG, CCG
                </button>
              </li>
              <li *ngIf="data.isShowAcceptEnd" nz-menu-item>
                <button
                  *ngIf="data.isShowAcceptEnd"
                  nz-button
                  nzShape="circle"
                  class="mr-1 text-btn"
                  (click)="bidEvaluation(data)"
                  nzTooltipTitle="Duyệt NCC thắng chào giá"
                  nzTooltipPlacement="top"
                  nz-tooltip
                >
                  <i nz-icon class="text-icon" nzType="audit"></i>Dyệt NCC thắng chào giá
                </button>
              </li>
            </ul>
          </nz-dropdown-menu>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" >
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>

<!-- Chọn hạng mục giá cần phân tích -->
<nz-modal
  [nzVisible]="isVisible1 || isVisible2"
  [nzWidth]="'70vw'"
  nzTitle="Chọn hạng mục giá cần phân tích"
  (nzOnCancel)="hidePopupAnalysPrice()"
  *ngIf="(isVisible1 || isVisible2) && offerChoose"
  [nzFooter]="modalFooter"
>
  <ng-container *nzModalContent>
    <nz-collapse *ngFor="let item of bidPriceMinListItem.listItem" class="mt-3">
      <nz-collapse-panel *ngIf="item.isExGr" [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite" [nzExtra]="extraTpl">
        <nz-row class="mt-3">
          <nz-col nzSpan="24">
            <ng-template #extraTpl>
              <button nz-button nzType="primary" (click)="analysisData(item)">Phân tích</button>
            </ng-template>
          </nz-col>
          <nz-table nz-col nzSpan="24" [nzData]="item.listPrice" [nzPageSize]="20" nzBordered [nzShowPagination]="false">
            <thead>
              <tr>
                <th nzWidth="50px">
                  <label nz-checkbox [(ngModel)]="item.isChooseAll" (ngModelChange)="onChangeChooseAll(item)"></label>
                </th>
                <th nzWidth="100px">{{ language_key?.NO || 'STT' }}</th>
                <th nzWidth="300px">Tên hạng mục</th>
                <th nzWidth="130px" *ngFor="let col of item.listPriceCol" class="dynamic-col-mpo">{{ col.name }}</th>
                <th nzWidth="120px">Đơn vị tính</th>
                <th nzWidth="150px">Đơn vị tiền tệ</th>
                <th nzWidth="120px">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                <th nzWidth="120px">Bắt buộc?</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let data1 of item.listPrice">
                <tr>
                  <td>
                    <label nz-checkbox [(ngModel)]="data1.isChoose"></label>
                  </td>
                  <td>{{ data1.sort > 0 ? data1.sort : '' }}</td>
                  <td class="mw-25">{{ data1.name }}</td>
                  <td *ngFor="let col of item.listPriceCol">{{ data1[col.id] }}</td>
                  <td>{{ data1.unit }}</td>
                  <td>{{ data1.currency }}</td>
                  <td class="text-right">{{ data1.number | number }}</td>
                  <td>{{ data1.isRequired ? 'Bắt buộc' : 'Không' }}</td>
                </tr>
              </ng-container>
            </tbody>
          </nz-table>
          <i>* Lưu ý: Khi không chọn hạng mục nào thì mặc định là tất cả</i>
        </nz-row>
      </nz-collapse-panel>
    </nz-collapse>
  </ng-container>
</nz-modal>
<ng-template #modalFooter>
  <nz-row class="mt-3" nzJustify="center">
    <button nz-button (click)="hidePopupAnalysPrice()">Đóng</button>
  </nz-row>
</ng-template>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Gia hạn" (nzOnCancel)="handleCancel()" (nzOnOk)="handleOk()">
  <ng-container *nzModalContent>
    <nz-row nzGutter="24">
      <nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời gian hiệu lực báo giá</nz-form-label>
          <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Thời gian hiệu lực báo giá ">
            <nz-date-picker
              [nzFormat]="dateFormat"
              name="endDate"
              placeholder="Ngày kết thúc"
              [(ngModel)]="timePeriod"
              [ngModelOptions]="{ standalone: true }"
              required
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>
