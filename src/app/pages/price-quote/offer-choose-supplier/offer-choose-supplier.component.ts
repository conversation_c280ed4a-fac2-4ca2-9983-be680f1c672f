import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { Subscription } from 'rxjs'
import { ActivatedRoute, Router } from '@angular/router'
import { Location } from '@angular/common'

@Component({
  selector: 'app-offer-choose-supplier',
  templateUrl: './offer-choose-supplier.component.html',
})
export class OfferChooseSupplierComponent implements OnInit {
  modalTitle = 'Danh sách nhà cung cấp mời thầu'
  pageSize = enumData.Page.pageSizeMax
  lstStatus = [enumData.SupplierServiceExpertiseStatus.DaThamDinh, enumData.SupplierServiceExpertiseStatus.ChuaThamDinh]
  lstTypeGetData = [
    { value: 1, name: 'Chỉ chọn nhà cung cấp trong LVMH' },
    { value: 2, name: 'Chọn cả nhà cung cấp ngoài LVMH' },
  ]
  loading = false
  dataSearch: any = {
    typeGetData: 1,
    supplierName: '',
    lstStatus: [],
  }
  listOfData: any[] = []
  isAllDisplayDataChecked = false
  isIndeterminate = false
  numberOfChecked = 0
  isShowChooseSupplier = false
  isShowSendMPOLeaderCheck = false
  isShowAcceptChooseSupplier = false
  canAcceptSup = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private router: Router,
    private route: ActivatedRoute,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<OfferChooseSupplierComponent>,
    private drawerService: NzDrawerService,
    private _location: Location,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    const id = this.route.snapshot.paramMap.get('id')
    this.data = { listItem: [] }
    if (id) {
      this.data = { id: id }
      this.loadData()
    }
  }

  async loadData() {
    const where: any = { bidId: this.data.id }
    if (this.dataSearch.supplierName && this.dataSearch.supplierName !== '') {
      where.supplierName = this.dataSearch.supplierName
    }
    if (this.dataSearch.lstStatus && this.dataSearch.lstStatus.length > 0) {
      where.lstStatus = this.dataSearch.lstStatus
    }
    this.dataSearch.typeGetData = this.dataSearch.typeGetData || 1
    where.typeGetData = this.dataSearch.typeGetData

    this.loading = true
    this.isShowChooseSupplier = false
    // this.isShowChooseSupplier = true
    this.isShowSendMPOLeaderCheck = false
    this.isShowAcceptChooseSupplier = false

    await this.apiService.post(this.apiService.OFFER.LOAD_SUPPLIER_INVITE, where).then((res) => {
      this.loading = false
      this.modalTitle = `Danh sách nhà cung cấp mời thầu [${res.bidName}]`
      this.listOfData = res.lstData || []
      this.isShowChooseSupplier = res.isShowChooseSupplier
      this.isShowSendMPOLeaderCheck = res.isShowSendMPOLeaderCheck
      this.isShowAcceptChooseSupplier = res.isShowAcceptChooseSupplier
      this.canAcceptSup = res.canAcceptSup

      this.refreshStatus()
    })
  }

  // load lại danh sách hiển thị mới
  refreshStatus() {
    this.isAllDisplayDataChecked = this.listOfData.every((item) => item.isChosen)
    this.isIndeterminate = this.listOfData.some((item) => item.isChosen) && !this.isAllDisplayDataChecked
    this.numberOfChecked = this.listOfData.filter((item) => item.isChosen).length
  }

  // Chọn tất cả
  checkAll(value: boolean) {
    this.listOfData.forEach((item) => (item.isChosen = value))
    this.refreshStatus()
  }

  async chooseSupplier() {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.OFFER.BID_CHOOSE_SUPPLIER, { bidId: this.data.id, lstData: this.listOfData }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Action_Success)
      this.loadData()
    })
  }

  closeDialog(flag: boolean) {
    // this.router.navigate(['/bid/bid-new'])
    this._location.back()
  }
}
