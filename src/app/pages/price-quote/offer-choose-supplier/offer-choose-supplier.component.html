<nz-row matDialogTitle>
 

  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-collapse>
    <nz-collapse-panel [nzHeader]="'Tìm kiếm'" [nzActive]="false">
      <nz-row nzGutter="12">
        <nz-col nzSpan="8">
          <input nz-input placeholder="Tìm theo tên nhà cung cấp" [(ngModel)]="dataSearch.supplierName" />
        </nz-col>
        <nz-col nzSpan="8">
          <nz-select
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataSearch.lstStatus"
            nzMode="multiple"
            [nzPlaceHolder]="language_key?.CHOOSE_STATUS_EXPERTISE || 'Chọn trạng thái thẩm định'"
          >
            <nz-option *ngFor="let item of lstStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col nzSpan="8">
          <nz-select nzShowSearch [(ngModel)]="dataSearch.typeGetData" nzPlaceHolder="Chọn phạm vi lấy">
            <nz-option *ngFor="let item of lstTypeGetData" [nzLabel]="item.name" [nzValue]="item.value"></nz-option>
          </nz-select>
        </nz-col>
        <nz-col style="margin-top: 15px; text-align: center" nzSpan="24">
          <button nz-button (click)="loadData()"><span nz-icon nzType="search"></span>Tìm kiếm</button>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-row class="mt-3">
    <nz-table nz-col nzSpan="24" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th
            *ngIf="isShowChooseSupplier"
            nzShowCheckbox
            [(nzChecked)]="isAllDisplayDataChecked"
            [nzIndeterminate]="isIndeterminate"
            (nzCheckedChange)="checkAll($event)"
            width="20px"
          ></th>
          <th>{{ language_key?.INTERPRISE_CODE || 'Mã nhà cung cấp' }}</th>
          <th>{{ language_key?.INTERPRISE_NAME || 'Tên nhà cung cấp' }}</th>
          <th>Trạng thái NCC</th>
          <th>Ngày đăng ký</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataRow of listOfData">
          <td
            *ngIf="isShowChooseSupplier"
            [ngStyle]="{ 'background-color': dataRow.isChosen ? '#EDF6FF' : '' }"
            nzShowCheckbox
            [(nzChecked)]="dataRow.isChosen"
            (nzCheckedChange)="refreshStatus()"
          ></td>
          <td [ngStyle]="{ 'background-color': dataRow.isChosen ? '#EDF6FF' : '' }">
            <span nz-tooltip nzTooltipTitle="Chi tiết" nz-icon nzType="info-circle"></span>
            {{ dataRow.supplierCode }}
          </td>
          <td class="mw-25" [ngStyle]="{ 'background-color': dataRow.isChosen ? '#EDF6FF' : '' }">{{ dataRow.supplierName }}</td>
          <td [ngStyle]="{ 'background-color': dataRow.isChosen ? '#EDF6FF' : '' }">{{ dataRow.statusName }}</td>
          <td [ngStyle]="{ 'background-color': dataRow.isChosen ? '#EDF6FF' : '' }">{{ dataRow.createdAt | date : 'dd/MM/yyyy' }}</td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>

<nz-row style="margin-top: 15px" matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <!--  -->
    <button
      *ngIf="numberOfChecked > 0 && isShowChooseSupplier"
      nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn mời các nhà cung cấp đã chọn tham gia thầu?"
      nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="chooseSupplier()"
      nz-button
      nzType="primary"
      class="mr-2 button-save"
    >
      <span nz-icon nzType="save" nzTheme="outline"></span>
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
    <!-- -->

    <button nz-button class="mr-2" (click)="closeDialog(false)" style="border-color: #e41717; color: #e41717; border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>
  </nz-col>
</nz-row>
