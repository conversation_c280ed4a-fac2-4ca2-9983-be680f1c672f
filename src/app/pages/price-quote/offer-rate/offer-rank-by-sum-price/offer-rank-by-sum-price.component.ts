import { Component, OnInit, Inject, Optional } from '@angular/core'
import { enumData } from '../../../../core'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './offer-rank-by-sum-price.component.html' })
export class OfferRankBySumPriceComponent implements OnInit {
  listBidSupplier: any[] = []
  isEditableNewValue = false
  bid: any
  pageSize = enumData.Page.pageSizeMax
  listOfData: any[] = []
  bidPriceColTypeMpo: any[] = []
  bidPriceColTypeSup: any[] = []
  numberType = enumData.DataType.Number.code
  todate = new Date()
  isShowPriceDiff = false

  /** 1. Mỗi dòng 1 hạng mục, 2. Mỗi dòng 1 NCC, 3. Dạng 1 nhưng bỏ chi tiết */
  viewType = '1'

  listOfData2: any[] = []
  lstPrice: any
  isLoadViewType2 = false
  numSupplierCol: number = 0
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    public dialogRef: MatDialogRef<OfferRankBySumPriceComponent>,
    public dialog: MatDialog,
    @Optional()
    @Inject(MAT_DIALOG_DATA)
    public data: {
      bid: any
      title: any
      lstId: string[]
    },
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    const dataFilter = {
      bidId: this.data.bid.id,
      lstId: this.data.lstId,
    }
    this.apiService.post(this.apiService.OFFER_RATE.LOAD_RANK_BY_SUM_PRICE, dataFilter).then((res) => {
      this.listOfData = res[0]
      this.bidPriceColTypeMpo = res[1]
      this.bidPriceColTypeSup = res[2]
      this.bid = res[3]
      this.listBidSupplier = res[4]
      // số lượng cột mỗi ncc = 3 cột tĩnh + số cột động
      this.numSupplierCol = 3 + this.bidPriceColTypeSup.length
    })
  }

  searchData2() {
    this.isLoadViewType2 = true
    const dataFilter = {
      bidId: this.data.bid.id,
      lstId: this.data.lstId,
    }
    this.apiService.post(this.apiService.BID_RATE.LOAD_SUPPLIER_RANK_BY_SUM_PRICE, dataFilter).then((res) => {
      this.listOfData2 = res[0]
      this.lstPrice = res[1]
    })
  }

  clickExportExcel() {
    let tbl = document.getElementById('table1')
    if (this.viewType === '2') tbl = document.getElementById('table2')
    if (this.viewType === '3') tbl = document.getElementById('table3')
    const wb = XLSX.utils.table_to_book(tbl)
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng phân tích theo tổng điểm giá các doanh nghiệp tham gia chào giá ${
      this.data.title
    }.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }

  updateValueDynamicCol() {
    if (this.bid.fomular == null || this.bid.fomular.trim() === '') {
      this.notifyService.showError('Gói thầu không thiết lập công thức tính giá, dữ liệu ghi nhận thủ công, không cần tính lại!')
      return
    }
    this.isEditableNewValue = true
  }

  async calValueData() {
    // Tính lại giá
    const numRank = this.listOfData[0].lstRank.length

    // reset tổng cộng
    for (let i = 0; i < numRank; i++) {
      this.listBidSupplier[i].totalPrice = 0
    }

    const setDataCell = (item: any, col: any, newValue: string) => {
      if (item.__bidPriceColValue__ && item.__bidPriceColValue__.length > 0) {
        const cell = item.__bidPriceColValue__.find((c: any) => c.bidPriceColId === col.id)
        if (cell) {
          cell.value = newValue
        }
      }
    }

    // thay đổi giá trị từng dòng
    for (const item of this.listOfData) {
      const lstBidPriceColTypeMpo = this.bidPriceColTypeMpo.filter((c) => c.type === this.numberType)
      for (const col of lstBidPriceColTypeMpo) {
        if (col.newValue != null && col.newValue > 0) {
          setDataCell(item, col, col.newValue)
        }
      }
    }

    // tính lại từng dòng
    let bidPriceCol = [...this.bidPriceColTypeMpo, ...this.bidPriceColTypeSup]
    for (const item of this.listOfData) {
      for (let i = 0; i < numRank; i++) {
        const itemRank = item.lstRank[i]
        if (itemRank) {
          const itemTemp = { ...item }
          const numDynamicCol = bidPriceCol.length
          for (let j = 0; j < numDynamicCol; j++) {
            const dynamicCol = bidPriceCol[j]
            itemTemp[dynamicCol.id] = ''
            if (dynamicCol.colType === enumData.ColType.Supplier.code) {
              const objValue = dynamicCol.__bidSupplierPriceColValue__.find(
                (c: any) => c.bidPriceId === item.id && c.bidSupplierId === itemRank.bidSupplierId
              )
              if (objValue) {
                itemTemp[dynamicCol.id] = objValue.value
              }
            } else {
              if (itemTemp.__bidPriceColValue__ && itemTemp.__bidPriceColValue__.length > 0) {
                const objValue = itemTemp.__bidPriceColValue__.find((c: any) => c.bidPriceColId === dynamicCol.id)
                if (objValue) {
                  itemTemp[dynamicCol.id] = objValue.value
                }
              }
            }
          }

          itemRank.unitPrice = this.coreService.calFomular(this.bid.fomular, bidPriceCol, itemTemp)
          itemRank.price = itemRank.unitPrice * itemTemp.number
          this.listBidSupplier[i].totalPrice += itemRank.price

          // map các giá trị cột động (nếu có cth thì cho tính lại)
          for (let j = 0; j < this.bidPriceColTypeSup.length; j++) {
            const dynamicCol = this.bidPriceColTypeSup[j]
            itemRank[dynamicCol.id] = ''
            if (dynamicCol.fomular && dynamicCol.fomular.length > 0) {
              const temp = this.coreService.calFomular(dynamicCol.fomular, bidPriceCol, itemTemp)
              if (temp) {
                itemRank[dynamicCol.id] = temp
              }
            } else {
              const objValue = dynamicCol.__bidSupplierPriceColValue__.find(
                (c: any) => c.bidPriceId === item.id && c.bidSupplierId === itemRank.bidSupplierId
              )
              if (objValue) {
                itemRank[dynamicCol.id] = objValue.value
              }
            }
          }
        }
      }
    }
  }

  async changeViewType() {
    if (this.viewType === '2') {
      // Nếu chưa lấy data thì lấy data
      if (!this.isLoadViewType2) {
        this.searchData2()
      }
    }
  }
}
