<nz-row>
  <h4>Chọn dạng hiển thị:</h4>
  <nz-radio-group [(ngModel)]="viewType" (ngModelChange)="changeViewType()">
    <label nz-radio nzValue="1">Dạng 1</label>
    <label nz-radio nzValue="2">Dạng 2</label>
    <label nz-radio nzValue="3">Dạng 3</label>
  </nz-radio-group>
</nz-row>
<nz-row class="mt-3" *ngIf="viewType === '1'">
  <h4>Tùy chọn in:</h4>
  <nz-col nzSpan="24">
    <label nz-checkbox [(ngModel)]="isShowPriceDiff"> Lấy giá trị chênh lệch so với Top 1 </label>
  </nz-col>
</nz-row>
<nz-row class="mt-3" *ngIf="viewType === '1'">
  <button *ngIf="!isEditableNewValue" nz-button (click)="updateValueDynamicCol()" class="ant-btn-blue">
    <span nz-icon nzType="edit"></span> Cập nhật giá trị của cột động
  </button>
  <button *ngIf="isEditableNewValue" nz-button (click)="calValueData()" class="ant-btn-orange">
    <span nz-icon nzType="calculator"></span> Tính lại giá
  </button>
</nz-row>

<div *ngIf="viewType === '1'" class="mt-3">
  <div id="print-section-1" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">
    <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150" />
    <div style="text-align: center; font-weight: 700; font-size: 18px">
      <div>BÁO CÁO PHÂN TÍCH SO SÁNH GIÁ XẾP HẠNG THEO TỔNG GIÁ TRỊ (Y)</div>
      <div>TÊN GÓI THẦU: {{ data.title }}</div>
      <div style="font-size: 15px">Ngày in: {{ todate | date : 'dd/MM/yyyy' }}</div>
    </div>
    <!-- Body -->
    <div style="margin-top: 20px">
      <table id="table1" style="margin-bottom: 10px; border: 2px solid black; border-spacing: 0" align="center">
        <thead>
          <tr style="text-align: center">
            <th rowspan="2" style="width: 50px; border: black 1px solid; display: none">ID</th>
            <th rowspan="2" style="width: 30px; border: black 1px solid">{{ language_key?.NO || 'STT' }}</th>
            <th rowspan="2" style="width: 300px; border: black 1px solid">Tên hạng mục</th>
            <th rowspan="2" style="width: 100px; border: black 1px solid" *ngFor="let col of bidPriceColTypeMpo">
              <span>{{ col.name }}</span
              ><br />
              <input
                *ngIf="isEditableNewValue && col.type === numberType"
                nz-input
                currencyMask
                placeholder="Nhập giá trị mới"
                [(ngModel)]="col.newValue"
              />
            </th>
            <th rowspan="2" style="width: 100px; border: black 1px solid">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
            <th rowspan="2" style="width: 100px; border: black 1px solid">Đơn vị tính</th>
            <th rowspan="2" style="width: 100px; border: black 1px solid">Đơn vị tiền tệ</th>
            <th
              [attr.colspan]="numSupplierCol"
              style="width: 300px; border: black 1px solid; background-color: #e3f8ff"
              *ngFor="let bidSupplier of listBidSupplier; let i = index"
              [ngStyle]="{ color: bidSupplier.isNotFillPrice ? 'red' : '' }"
            >
              Top {{ i + 1 }}
            </th>
          </tr>
          <tr style="text-align: center; background-color: #e3f8ff">
            <ng-container *ngFor="let bidSupplier of listBidSupplier">
              <th style="border: black 1px solid" *ngIf="!isShowPriceDiff">&nbsp;Đơn giá&nbsp;</th>
              <th style="border: black 1px solid">&nbsp;Thành tiền&nbsp;</th>
              <th style="border: black 1px solid" *ngIf="isShowPriceDiff">&nbsp;Giá trị chênh lệch so với Top 1&nbsp;</th>
              <th style="border: black 1px solid">&nbsp;{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}&nbsp;</th>
              <th style="border: black 1px solid" *ngFor="let col of bidPriceColTypeSup">{{ col.name }}</th>
            </ng-container>
          </tr>
        </thead>
        <tbody>
          <tr style="text-align: center" *ngFor="let item of listOfData">
            <td style="border: black 1px solid; display: none">
              {{ item.id }}
            </td>
            <td style="border: black 1px solid">
              {{ item.sort > 0 ? item.sort : '' }}
            </td>
            <td align="left" style="border: black 1px solid">&nbsp;{{ item.name }}</td>
            <td style="border: black 1px solid" *ngFor="let col of bidPriceColTypeMpo">
              {{ item[col.id] }}
            </td>
            <td style="border: black 1px solid">
              {{ item.number | number }}
            </td>
            <td style="border: black 1px solid">
              {{ item.unit }}
            </td>
            <td style="border: black 1px solid">
              {{ item.currency }}
            </td>
            <ng-container *ngFor="let rankData of item.lstRank">
              <td style="border: black 1px solid" *ngIf="!isShowPriceDiff">&nbsp;{{ rankData.unitPrice | number }}&nbsp;</td>
              <td align="right" style="border: black 1px solid">&nbsp;{{ rankData.price | number }}&nbsp;</td>
              <td align="right" style="border: black 1px solid" *ngIf="isShowPriceDiff">&nbsp;{{ rankData.priceDiff | number }}&nbsp;</td>
              <td style="border: black 1px solid">&nbsp;{{ rankData.supplierName }}&nbsp;</td>
              <th style="border: black 1px solid" *ngFor="let col of bidPriceColTypeSup">{{ item[col.id] }}</th>
            </ng-container>
          </tr>
        </tbody>
        <tfoot>
          <tr style="text-align: right; font-weight: 600">
            <td style="border: black 1px solid; display: none"></td>
            <td style="border: black 1px solid"></td>
            <td align="left" style="border: black 1px solid">&nbsp;Tổng cộng</td>
            <td style="border: black 1px solid" *ngFor="let col of bidPriceColTypeMpo"></td>
            <td style="border: black 1px solid"></td>
            <td style="border: black 1px solid"></td>
            <td style="border: black 1px solid"></td>

            <ng-container *ngFor="let bidSupplier of listBidSupplier">
              <td style="border: black 1px solid" *ngIf="!isShowPriceDiff"></td>
              <td align="right" style="border: black 1px solid">&nbsp;{{ bidSupplier.totalPrice | number }}&nbsp;</td>
              <td align="right" style="border: black 1px solid" *ngIf="isShowPriceDiff">&nbsp;{{ bidSupplier.totalPriceDiff | number }}&nbsp;</td>
              <td style="border: black 1px solid"></td>
              <th style="border: black 1px solid" *ngFor="let col of bidPriceColTypeSup"></th>
            </ng-container>
          </tr>
        </tfoot>
      </table>
    </div>
    <!-- Footer -->
    <div style="margin-top: 30px; padding: 0px 5px">
      <div style="text-align: right; font-weight: 700" *ngIf="data.bid.approveChooseSupplierWinDate">
        Ngày {{ data.bid.approveChooseSupplierWinDate | date : 'dd' }} tháng {{ data.bid.approveChooseSupplierWinDate | date : 'MM' }} năm
        {{ data.bid.approveChooseSupplierWinDate | date : 'yyyy' }}
      </div>
      <div style="text-align: right; font-weight: 700" *ngIf="!data.bid.approveChooseSupplierWinDate">Ngày ..... tháng ..... năm .....</div>
      <table style="margin: 10px 0 100px 0">
        <thead>
          <tr style="text-align: center">
            <th style="width: 30px"></th>
            <th style="width: 33%">ĐỀ XUẤT</th>
            <th style="width: 33%">KIỂM TRA</th>
            <th style="width: 33%">PHÊ DUYỆT</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="mt-5 text-center">
    <button nz-button (click)="clickExportExcel()" class="mr-2"><span nz-icon nzType="download"></span>Xuất excel</button>
    <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section-1" ngxPrint>In báo cáo</button>
  </div>
</div>

<div *ngIf="viewType === '2'" class="mt-3">
  <div id="print-section-2" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">
    <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150" />
    <div style="text-align: center; font-weight: 700; font-size: 18px">
      <div>BÁO CÁO PHÂN TÍCH SO SÁNH GIÁ XẾP HẠNG THEO TỔNG GIÁ TRỊ (Y)</div>
      <div>TÊN GÓI THẦU: {{ data.title }}</div>
      <div style="font-size: 15px">Ngày in: {{ todate | date : 'dd/MM/yyyy' }}</div>
    </div>
    <!-- Body -->
    <div style="margin-top: 20px">
      <table id="table2" style="margin-bottom: 10px; border: 2px solid black; border-spacing: 0" align="center">
        <thead>
          <tr style="text-align: center">
            <th rowspan="2" style="width: 50px; border: black 1px solid">Xếp hạng</th>
            <th rowspan="2" style="width: 200px; border: black 1px solid">
              {{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}
            </th>
            <th rowspan="2" style="width: 150px; border: black 1px solid">Tổng thành tiền</th>
            <th colspan="2" style="width: 300px; border: black 1px solid; background-color: #e3f8ff" *ngFor="let price of lstPrice">
              {{ price.name }}
            </th>
          </tr>
          <tr style="text-align: center; background-color: #e3f8ff">
            <ng-container *ngFor="let price of lstPrice">
              <th style="border: black 1px solid">&nbsp;Đơn giá&nbsp;</th>
              <th style="border: black 1px solid">&nbsp;Thành tiền&nbsp;</th>
            </ng-container>
          </tr>
        </thead>
        <tbody>
          <tr style="text-align: right" *ngFor="let item of listOfData2; let i = index">
            <td align="center" style="border: black 1px solid" [ngStyle]="{ color: item.isNotFillPrice ? 'red' : '' }">
              {{ i + 1 }}
            </td>
            <td align="left" style="border: black 1px solid">&nbsp;{{ item.supplierName }}&nbsp;</td>
            <td style="border: black 1px solid">&nbsp;{{ item.totalPrice | number }}&nbsp;</td>
            <ng-container *ngFor="let price of lstPrice">
              <td style="border: black 1px solid">&nbsp;{{ item[price.id].unitPrice | number }} {{ price.currency }}&nbsp;</td>
              <td style="border: black 1px solid">&nbsp;{{ item[price.id].totalPrice | number }} {{ price.currency }}&nbsp;</td>
            </ng-container>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- Footer -->
    <div style="margin-top: 30px; padding: 0px 5px">
      <div style="text-align: right; font-weight: 700" *ngIf="data.bid.approveChooseSupplierWinDate">
        Ngày {{ data.bid.approveChooseSupplierWinDate | date : 'dd' }} tháng {{ data.bid.approveChooseSupplierWinDate | date : 'MM' }} năm
        {{ data.bid.approveChooseSupplierWinDate | date : 'yyyy' }}
      </div>
      <div style="text-align: right; font-weight: 700" *ngIf="!data.bid.approveChooseSupplierWinDate">Ngày ..... tháng ..... năm .....</div>
      <table style="margin: 10px 0 100px 0">
        <thead>
          <tr style="text-align: center">
            <th style="width: 30px"></th>
            <th style="width: 33%">ĐỀ XUẤT</th>
            <th style="width: 33%">KIỂM TRA</th>
            <th style="width: 33%">PHÊ DUYỆT</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="mt-5 text-center">
    <button nz-button (click)="clickExportExcel()" class="mr-2"><span nz-icon nzType="download"></span>Xuất excel</button>
    <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section-2" ngxPrint>In báo cáo</button>
  </div>
</div>

<div *ngIf="viewType === '3'" class="mt-3">
  <div id="print-section-3" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">
    <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150" />
    <div style="text-align: center; font-weight: 700; font-size: 18px">
      <div>BÁO CÁO PHÂN TÍCH SO SÁNH GIÁ XẾP HẠNG THEO TỔNG GIÁ TRỊ (Y)</div>
      <div>TÊN GÓI THẦU: {{ data.title }}</div>
      <div style="font-size: 15px">Ngày in: {{ todate | date : 'dd/MM/yyyy' }}</div>
    </div>
    <!-- Body -->
    <div style="margin-top: 20px">
      <table id="table3" style="margin-bottom: 10px; border: 2px solid black; border-spacing: 0" align="center">
        <thead>
          <tr style="text-align: center; background-color: #e3f8ff">
            <th
              colspan="2"
              style="width: 300px; border: black 1px solid"
              *ngFor="let bidSupplier of listBidSupplier; let i = index"
              [ngStyle]="{ color: bidSupplier.isNotFillPrice ? 'red' : '' }"
            >
              Top {{ i + 1 }}
            </th>
          </tr>
          <tr style="text-align: left; background-color: #e3f8ff">
            <ng-container *ngFor="let bidSupplier of listBidSupplier">
              <th style="border: black 1px solid">&nbsp;{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}&nbsp;</th>
              <th style="border: black 1px solid">&nbsp;Tổng thành tiền&nbsp;</th>
            </ng-container>
          </tr>
        </thead>
        <tbody>
          <tr style="text-align: left">
            <ng-container *ngFor="let bidSupplier of listBidSupplier">
              <td style="border: black 1px solid">&nbsp;{{ bidSupplier.supplierName }}&nbsp;</td>
              <td align="right" style="border: black 1px solid">&nbsp;{{ bidSupplier.totalPrice | number }}&nbsp;</td>
            </ng-container>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- Footer -->
    <div style="margin-top: 30px; padding: 0px 5px">
      <div style="text-align: right; font-weight: 700" *ngIf="data.bid.approveChooseSupplierWinDate">
        Ngày {{ data.bid.approveChooseSupplierWinDate | date : 'dd' }} tháng {{ data.bid.approveChooseSupplierWinDate | date : 'MM' }} năm
        {{ data.bid.approveChooseSupplierWinDate | date : 'yyyy' }}
      </div>
      <div style="text-align: right; font-weight: 700" *ngIf="!data.bid.approveChooseSupplierWinDate">Ngày ..... tháng ..... năm .....</div>
      <table style="margin: 10px 0 100px 0">
        <thead>
          <tr style="text-align: center">
            <th style="width: 30px"></th>
            <th style="width: 33%">ĐỀ XUẤT</th>
            <th style="width: 33%">KIỂM TRA</th>
            <th style="width: 33%">PHÊ DUYỆT</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="mt-5 text-center">
    <button nz-button (click)="clickExportExcel()" class="mr-2"><span nz-icon nzType="download"></span>Xuất excel</button>
    <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section-3" ngxPrint>In báo cáo</button>
  </div>
</div>
