import { Component, OnInit, Inject, Optional } from '@angular/core'
import { enumData } from '../../../../core'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiService, AuthenticationService, CoreService, StorageService } from '../../../../services'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'

@Component({ templateUrl: './offer-rank-by-min-price.component.html' })
export class OfferRankByMinPriceComponent implements OnInit {
  lstTitleLv1: any[] = []
  pageSize = enumData.Page.pageSizeMax
  listOfData: any[] = []
  bidPriceCol: any[] = []
  todate = new Date()
  isShowPriceDiff = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    public dialogRef: MatDialogRef<OfferRankByMinPriceComponent>,
    public dialog: MatDialog,
    @Optional()
    @Inject(MAT_DIALOG_DATA)
    public data: {
      bid: any
      title: any
      lstId: string[]
    },
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    const dataFilter = {
      bidId: this.data.bid.id,
      lstId: this.data.lstId,
    }
    this.apiService.post(this.apiService.OFFER_RATE.LOAD_RANK_BY_MIN_PRICE, dataFilter).then((res) => {
      this.listOfData = res[0]
      this.bidPriceCol = res[1]
      this.lstTitleLv1 = res[2]
    })
  }

  clickExportExcel() {
    const tbl = document.getElementById('test-html-table')
    const wb = XLSX.utils.table_to_book(tbl)

    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng phân tích theo giá thấp nhất của từng hạng mục chào giá ${
      this.data.title
    }.xlsx`
    /* save to file */
    XLSX.writeFile(wb, fileName)
  }
}
