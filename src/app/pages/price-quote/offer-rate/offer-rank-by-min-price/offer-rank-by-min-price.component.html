<nz-row>
  <h4>T<PERSON><PERSON> chọn in:</h4>
  <nz-col nzSpan="24">
    <label nz-checkbox [(ngModel)]="isShowPriceDiff"> Lấy gi<PERSON> trị chênh lệch so với Top 1 </label>
  </nz-col>
</nz-row>
<nz-row class="mt-3 mb-5">
  <button nz-button (click)="clickExportExcel()" class="mr-2"><span nz-icon nzType="download"></span>Xuất excel</button>
  <button nz-button nzType="primary" [useExistingCss]="true" printSectionId="print-section" ngxPrint>In báo cáo</button>
</nz-row>

<div id="print-section" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">
  <img src="../../../../../../assets/img/logoform.jpg" alt="logo_masan" height="50" width="150" />
  <div style="text-align: center; font-weight: 700; font-size: 18px">
    <div *ngIf="!isShowPriceDiff">BÁO CÁO PHÂN TÍCH SO SÁNH GIÁ XẾP HẠNG THEO HẠNG MỤC ĐƠN GIÁ (A)</div>
    <div *ngIf="isShowPriceDiff">BÁO CÁO PHÂN TÍCH GIÁ TRỊ HẠNG MỤC CHÊNH LỆCH GIỮA CÁC NCC SO VỚI NCC TOP 1 (A)</div>
    <div>TÊN GÓI THẦU: {{ data.title }}</div>
    <div style="font-size: 15px">Ngày in: {{ todate | date : 'dd/MM/yyyy' }}</div>
  </div>
  <!-- Body -->
  <div style="margin-top: 20px">
    <table id="test-html-table" style="margin-bottom: 10px; border: 2px solid black; border-spacing: 0" align="center">
      <thead>
        <tr style="text-align: center">
          <th rowspan="2" style="width: 50px; border: black 1px solid; display: none">ID</th>
          <th rowspan="2" style="width: 30px; border: black 1px solid">{{ language_key?.NO || 'STT' }}</th>
          <th rowspan="2" style="width: 200px; border: black 1px solid">Tên hạng mục</th>
          <th rowspan="2" style="width: 100px; border: black 1px solid" *ngFor="let col of bidPriceCol">
            {{ col.name }}
          </th>
          <th rowspan="2" style="width: 100px; border: black 1px solid">{{ language_key?.QUANTITY || 'Số lượng' }}</th>
          <th rowspan="2" style="width: 100px; border: black 1px solid">Đơn vị tính</th>
          <th rowspan="2" style="width: 100px; border: black 1px solid">Đơn vị tiền tệ</th>
          <th
            [attr.colspan]="isShowPriceDiff ? 4 : 3"
            style="width: 350px; border: black 1px solid; background-color: #e3f8ff"
            *ngFor="let title of lstTitleLv1; let i = index"
          >
            Top {{ i + 1 }}
          </th>
        </tr>
        <tr style="text-align: center; background-color: #e3f8ff">
          <ng-container *ngFor="let title of lstTitleLv1">
            <th style="border: black 1px solid">&nbsp;Đơn giá&nbsp;</th>
            <th style="border: black 1px solid">&nbsp;Tổng tiền&nbsp;</th>
            <th style="border: black 1px solid" *ngIf="isShowPriceDiff">&nbsp;Giá trị chênh lệch so với Top 1&nbsp;</th>
            <th style="border: black 1px solid">&nbsp;{{ language_key?.INTERPRISE_NAME || 'Tên doanh nghiệp' }}&nbsp;</th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <tr style="text-align: center" *ngFor="let item of listOfData">
          <td style="border: black 1px solid; display: none">
            {{ item.id }}
          </td>
          <td style="border: black 1px solid">
            {{ item.sort > 0 ? item.sort : '' }}
          </td>
          <td align="left" style="border: black 1px solid">&nbsp;{{ item.name }}</td>
          <td style="border: black 1px solid" *ngFor="let col of bidPriceCol">
            {{ item[col.id] }}
          </td>
          <td style="border: black 1px solid">
            {{ item.number | number }}
          </td>
          <td style="border: black 1px solid">
            {{ item.unit }}
          </td>
          <td style="border: black 1px solid">
            {{ item.currency }}
          </td>
          <ng-container *ngFor="let rankData of item.lstRank">
            <td style="border: black 1px solid">&nbsp;{{ rankData.unitPrice | number }}&nbsp;</td>
            <td style="border: black 1px solid">&nbsp;{{ rankData.price | number }}&nbsp;</td>
            <td style="border: black 1px solid" *ngIf="isShowPriceDiff">&nbsp;{{ rankData.priceDiff | number }}&nbsp;</td>
            <td style="border: black 1px solid">
              <span *ngIf="rankData.lstSupplier.length === 1">{{ rankData.lstSupplier[0].supplierName }}</span>
              <u *ngIf="rankData.lstSupplier.length > 1" nz-popover [nzPopoverContent]="contentTemplate" style="color: blue">
                {{ rankData.lstSupplier.length }} DN</u
              >
              <ng-template #contentTemplate>
                <div *ngFor="let itemC of rankData.lstSupplier">
                  <p>{{ itemC.supplierName }} - {{ itemC.supplierScorePrice | number : '1.0-2' }}</p>
                </div>
              </ng-template>
            </td>
          </ng-container>
        </tr>
      </tbody>
      <tfoot>
        <tr style="text-align: right; font-weight: 600">
          <td style="border: black 1px solid; display: none"></td>
          <td style="border: black 1px solid"></td>
          <td align="left" style="border: black 1px solid">&nbsp;Tổng cộng</td>
          <td style="border: black 1px solid" *ngFor="let col of bidPriceCol"></td>
          <td style="border: black 1px solid"></td>
          <td style="border: black 1px solid"></td>
          <td style="border: black 1px solid"></td>

          <ng-container *ngFor="let title of lstTitleLv1">
            <td style="border: black 1px solid"></td>
            <td style="border: black 1px solid">&nbsp;{{ title | number }}&nbsp;</td>
            <td style="border: black 1px solid" *ngIf="isShowPriceDiff"></td>
            <td style="border: black 1px solid"></td>
          </ng-container>
        </tr>
      </tfoot>
    </table>
  </div>
  <!-- Footer -->
  <div style="margin-top: 30px; padding: 0px 5px">
    <div style="text-align: right; font-weight: 700" *ngIf="data.bid.approveChooseSupplierWinDate">
      Ngày {{ data.bid.approveChooseSupplierWinDate | date : 'dd' }} tháng {{ data.bid.approveChooseSupplierWinDate | date : 'MM' }} năm
      {{ data.bid.approveChooseSupplierWinDate | date : 'yyyy' }}
    </div>
    <div style="text-align: right; font-weight: 700" *ngIf="!data.bid.approveChooseSupplierWinDate">Ngày ..... tháng ..... năm .....</div>
    <table style="margin: 10px 0 100px 0">
      <thead>
        <tr style="text-align: center">
          <th style="width: 30px"></th>
          <th style="width: 33%">ĐỀ XUẤT</th>
          <th style="width: 33%">KIỂM TRA</th>
          <th style="width: 33%">PHÊ DUYỆT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
