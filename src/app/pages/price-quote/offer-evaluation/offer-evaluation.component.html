<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent *ngIf="dataObject.id">
  <nz-collapse *ngFor="let item of dataObject.listItem" class="mt-3">
    <nz-collapse-panel [nzHeader]="'Item ' + item.itemName" class="ant-bg-antiquewhite">
      <nz-row nzGutter="8" class="mt-3">
        <nz-table
          nz-col
          nzSpan="24"
          [nzScroll]="{ x: '1250px' }"
          [nzData]="item.lstBidSupplier"
          [(nzPageSize)]="pageSize"
          [nzLoading]="loading"
          [nzShowPagination]="false"
          nzBordered
        >
          <thead>
            <tr>
              <th nzLeft nzWidth="50px">
                <label nz-checkbox [(ngModel)]="item.isChooseAll" [nzDisabled]="dataObject.isShowAcceptEnd" (ngModelChange)="checkAll(item)"> </label>
              </th>
              <th nzWidth="300px">Ghi chú cho NCC thắng chào giá</th>
              <th nzWidth="180px">{{ language_key?.INTERPRISE_NAME || 'Tên nhà cung cấp' }}</th>
              <th nzWidth="150px">Tổng điểm</th>
              <th nzWidth="150px">Tổng điểm HĐXT</th>
              <th nzWidth="100px">Thứ hạng</th>
              <th nzWidth="150px">Điểm năng lực</th>
              <th nzWidth="180px">Điểm HĐXT năng lực</th>
              <th nzWidth="150px">Điểm báo giá</th>
              <th nzWidth="170px">Điểm HĐXT báo giá</th>
              <th nzWidth="150px">Điểm ĐKTM</th>
              <th nzWidth="170px">Điểm HĐXT ĐKTM</th>
              <th nzWidth="150px">Trạng thái hồ sơ</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let rowData of item.lstBidSupplier">
              <td nzLeft>
                <label nz-checkbox [(ngModel)]="rowData.isChoose" [nzDisabled]="dataObject.isShowAcceptEnd"></label>
              </td>
              <td>
                <textarea
                  *ngIf="rowData.isChoose"
                  [disabled]="dataObject.isShowAcceptEnd"
                  nz-input
                  rows="1"
                  auto
                  placeholder="Nhập ghi chú cho NCC thắng chào giá "
                  [(ngModel)]="rowData.noteSuccessBid"
                >
                </textarea>
              </td>
              <td>
                <span nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Chi tiết" (click)="showBidResultDetail(rowData)"></span>
                {{ rowData.supplierName }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scoreTotal !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreTotal) }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scoreManualTotal !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreManualTotal) }}
              </td>
              <td>{{ rowData.rank }}</td>
              <td [ngClass]="{ 'text-right': rowData.scoreTech !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreTech) }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scoreManualTech !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreManualTech) }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scorePrice !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scorePrice) }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scoreManualPrice !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreManualPrice) }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scoreTrade !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreTrade) }}
              </td>
              <td [ngClass]="{ 'text-right': rowData.scoreManualTrade !== -1 }">
                {{ coreService.scoreRankABCD(rowData.scoreManualTrade) }}
              </td>
              <td>{{ rowData.statusFileName }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse class="mt-3">
    <nz-collapse-panel [nzHeader]="'Danh sách đánh giá'" class="ant-bg-antiquewhite">
      <nz-row *ngIf="dataObject.haveProgress" nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="dataObject.approvalProgress" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>Cấp duyệt</th>
                <th>Vị trí duyệt</th>
                <th>Người duyệt(Nếu có)</th>
                <th>Trạng thái duyệt</th>
                <th>Ghi chú người duyệt</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.approvalProgress">
                <td>{{ data.level }}</td>
                <td>{{ data.departmentName }}</td>
                <td>{{ data.employeeName }}</td>
                <td>{{ data.status }}</td>
                <td>{{ data.comment || '' }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>

        <nz-col *ngIf="dataObject.showComment" nzSpan="24">
          <nz-form-item>
            <nz-form-label nzSpan="24" nzRequired class="text-center"> Người duyệt ghi chú </nz-form-label>
            <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập mô tả nội dung mời thầu (1-250 kí tự)!">
              <textarea nz-input id="comment" [(ngModel)]="dataObject.comment" rows="5" auto required></textarea>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
</div>

<nz-row matDialogActions *ngIf="dataObject.id">
  <nz-col nzSpan="24" class="text-center">
    <button
      *ngIf="dataObject.isShowEnd"
      nz-button
      nzType="primary"
      class="mr-2"
      nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn xác nhận nhà cung cấp thắng chào giá ?"
      nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="onAcceptBidSupplier()"
    >
      Xác nhận nhà cung cấp thắng chào giá
    </button>
    <button
      *ngIf="dataObject.isShowAcceptEnd && dataObject.canApprove"
      nz-button
      nzType="primary"
      class="mr-2"
      nz-popconfirm
      nzPopconfirmTitle="Bạn có chắc muốn phê duyệt NCC thắng chào giá ?"
      nzPopconfirmPlacement="bottom"
      (nzOnConfirm)="onAccept()"
    >
      Phê duyệt kết quả
    </button>
    <button
      *ngIf="dataObject.isShowAcceptEnd && dataObject.canApprove"
      nz-button
      nzType="primary"
      nzDanger
      nzPopoverTitle="Thông báo"
      nz-popover
      [(nzPopoverVisible)]="visible"
      nzPopoverTrigger="click"
      [nzPopoverContent]="contentTemplate"
    >
      Yêu cầu kiểm tra lại
    </button>
  </nz-col>
</nz-row>

<ng-template #contentTemplate>
  <nz-row [nzGutter]="16">
    <nz-col nzSpan="24" class="text-center">
      <nz-checkbox-group [(ngModel)]="checkOptionsOne"></nz-checkbox-group>
    </nz-col>
    <nz-col nzSpan="12" class="text-center">
      <button nz-button nzType="default" (click)="clickMe()">Đóng</button>
    </nz-col>
    <nz-col nzSpan="12" class="text-center">
      <button nz-button nzType="primary" (click)="onReject()">Xác nhận</button>
    </nz-col>
  </nz-row>
</ng-template>
