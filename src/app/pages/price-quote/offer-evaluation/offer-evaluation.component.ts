import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { Subscription } from 'rxjs'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { enumData } from '../../../core'

@Component({
  selector: 'app-offer-evaluation',
  templateUrl: './offer-evaluation.component.html',
})
export class OfferEvaluationComponent implements OnInit {
  modalTitle = 'Chọn nhà cung cấp thắng đấu giá'
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataSearch: any = {}
  checkOptionsOne = [
    { label: 'Đánh giá NL, KT', value: 'YCKT', checked: true },
    { label: '<PERSON><PERSON><PERSON> gi<PERSON> bả<PERSON>, <PERSON><PERSON> & <PERSON><PERSON><PERSON> gi<PERSON>K<PERSON>', value: 'CCG', checked: true },
  ]
  language_key: any
  subscriptions: Subscription = new Subscription()
  dataObject: any = {}
  enumProject: any
  visible: boolean = false
  enumRole: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    public dialogRef: MatDialogRef<OfferEvaluationComponent>,
    public dialog: MatDialog,
    @Optional()
    @Inject(MAT_DIALOG_DATA)
    public data: {
      bidId: string
      isShowEnd: boolean
      isShowAcceptEnd: boolean
    },
    private drawerService: NzDrawerService,
    public authenticationService: AuthenticationService
  ) {}

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.searchData()
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    this.apiService.post(this.apiService.OFFER_EVALUATION.LOAD_SUPPLIER_DATA, { bidId: this.data.bidId }).then((res) => {
      this.loading = false
      this.dataObject = res
      this.dataObject.isShowEnd = this.data.isShowEnd || false
      this.dataObject.isShowAcceptEnd = this.data.isShowAcceptEnd || false
    })
  }

  async onAcceptBidSupplier() {
    this.notifyService.showloading()
    for (const item of this.dataObject.listItem) {
      const lstSupplierChoose = item.lstBidSupplier.filter((c: any) => c.isChoose)
      if (lstSupplierChoose.length === 0) {
        this.notifyService.showError(`Vui lòng chọn nhà cung cấp trúng thầu cho Item [${item.itemName}].`)
        return
      }
    }
    this.apiService
      .post(this.apiService.OFFER_EVALUATION.EVALUATION_OFFER_SUPPLIER, {
        bidId: this.dataObject.id,
        listItem: this.dataObject.listItem,
        comment: this.dataObject.noteCloseBidMPO,
      })
      .then((res) => {
        this.notifyService.showSuccess(res.message)
        this.closeDialog(true)
      })
  }

  async onAccept() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.OFFER_EVALUATION.APPROVE_SUPPLIER_WIN_OFFER, {
        bidId: this.dataObject.id,
        comment: this.dataObject.comment,
      })
      .then((data) => {
        this.notifyService.showSuccess(data.message)
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
  }

  async onReject() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.OFFER_EVALUATION.REJECT_SUPPLIER_WIN_OFFER, {
        bidId: this.dataObject.id,
        recheck: this.checkOptionsOne,
        comment: this.dataObject.comment,
      })
      .then((data) => {
        this.notifyService.hideloading()
        this.closeDialog(true)
      })
  }

  // Chọn tất cả
  checkAll(item: any) {
    for (const bidSup of item.lstBidSupplier) {
      bidSup.isChoose = item.isChooseAll
    }
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  showBidResultDetail(data: { bidId: string; supplierId: string }) {
    // this.drawerService.create({
    //   nzTitle: this.language_key?.INTERPRISE_DETAIL || 'Chi tiết nhà cung cấp',
    //   nzContent: BidResultDetailComponent,
    //   nzContentParams: data,
    //   nzWidth: '640',
    // })
  }

  clickMe(): void {
    this.visible = false
  }
}
