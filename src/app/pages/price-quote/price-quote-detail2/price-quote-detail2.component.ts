import { Component, Inject, OnInit, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-price-quote-detail2',
  templateUrl: './price-quote-detail2.component.html',
})
export class PriceQuoteDetail2Component implements OnInit {
  loading = false
  language_key: any
  dataObject: any = {}
  lstExternalMatGroup: any[] = []
  dataEmployee: any
  enum = enumData
  pageSize = enumData.Page.pageSizeMax
  dateFormat = 'dd/MM/yyyy'
  supplier: any
  service: any
  dataPr: any[] = []
  dataCurrency: any
  modalTitle = 'Thông tin chào giá'
  objPermission: any = {}
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    private route: ActivatedRoute,
    private router: Router,
    private dialogRef: MatDialogRef<PriceQuoteDetail2Component>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {}

  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('id')
    if (id) {
      this.data = { id: id }
      this.searchData()
    }
    this.loadFullEmployee()
    this.loadData()
    this.findPr()
    //
  }

  async findPr() {
    Promise.all([this.apiService.post(this.apiService.PR.FIND, {})]).then((res) => {
      this.dataPr = res[0]
    })
  }

  async loadData() {
    this.apiService.post(this.apiService.SERVICE.FIND, { isLast: true }).then((result) => {
      this.service = result
    })
    // await this.apiService.post(this.apiService.SUPPLIER.LOAD_DATA_SELECT, {}).then((res) => {
    //   this.loading = false
    //   this.dataObject.lstSup = res || []
    // })
    this.notifyService.showloading()
    // Promise.all([
    //   this.apiService.post(this.apiService.CURRENCY.DATA_SELECT_BOX, {}), //1
    // ]).then(async (res) => {
    //   this.notifyService.hideloading()
    //   this.dataCurrency = res[0]
    // })
    // Promise.all([this.apiService.post(this.apiService.EXTERNAL_MATERIAL_GROUP.FIND, {})]).then(async (res) => {
    //   this.lstExternalMatGroup = res[0]
    // })
  }

  async loadFullEmployee() {
    await this.apiService.post(this.apiService.EMPLOYEE.FIND, {}).then((result) => {
      this.dataEmployee = result
    })
  }
  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.FIND_ONE, { id: this.data.id }).then((data) => {
      if (data) {
        this.notifyService.hideloading()
        this.dataObject = data
      }
    })
  }

  closeDialog() {
    this.router.navigate(['/price-quote/list'])
  }
}
