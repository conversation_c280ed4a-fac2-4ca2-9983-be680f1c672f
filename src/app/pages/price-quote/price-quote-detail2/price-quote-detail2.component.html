<form nz-form #frmAdd="ngForm">
  <nz-row>
    <nz-col nzSpan="24" class="text-center fs-24 fw-600">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>
  <nz-tabset>
    <nz-tab nzTitle="Thông tin chung">
      <nz-collapse>
        <nz-collapse-panel [nzHeader]="'Thông tin chung'" [nzActive]="true">
          <nz-row nzGutter="8">
            <!-- Mã RFQ -->
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Mã RFQ</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Mã RFQ ">
                  <input nz-input placeholder="Nhập Mã RFQ" [(ngModel)]="dataObject.code" name="code" required [disabled]="true" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Ti<PERSON>u <PERSON> -->
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Tiêu Đề</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Tiêu Đề ">
                  <input nz-input placeholder="Nhập Tiêu Đề" [(ngModel)]="dataObject.title" name="title" required [disabled]="true" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Nhân viên phụ trách -->
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzRequired>Nhân viên phụ trách</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng chọn Nhân viên phụ trách!">
                  <nz-select
                    [disabled]="true"
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Chọn Nhân viên phụ trách"
                    [(ngModel)]="dataObject.employeeId"
                    name="employeeId"
                    required
                  >
                    <nz-option *ngFor="let item of dataEmployee" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Ngày bắt đầu phụ lục< -->
            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" nzRequired>Ngày bắt đầu</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="">
                  <nz-date-picker
                    [disabled]="true"
                    [nzFormat]="dateFormat"
                    name="effectiveDate"
                    placeholder="Ngày bắt đầu"
                    [(ngModel)]="dataObject.effectiveDate"
                    [ngModelOptions]="{ standalone: true }"
                    required
                  >
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Ngày hết hạn -->
            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" nzRequired>Ngày kết thúc</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="">
                  <nz-date-picker
                    [disabled]="true"
                    [nzFormat]="dateFormat"
                    name="endDate"
                    placeholder="Ngày kết thúc"
                    [(ngModel)]="dataObject.endDate"
                    [ngModelOptions]="{ standalone: true }"
                    required
                  >
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Đơn vị tiền tệ -->
            <nz-col nzSpan="8">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Đơn vị tiền tệ</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập chọn đơn vị tiền tệ">
                  <nz-select
                    [disabled]="true"
                    nzShowSearch
                    nzAllowClear
                    nzPlaceHolder="Chọn đơn vị tiền tệ"
                    [(ngModel)]="dataObject.currency"
                    name="currency"
                    required
                  >
                    <nz-option *ngFor="let item of dataCurrency" [nzLabel]="item.code" [nzValue]="item.code"> </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Thời gian hiệu lực báo giá -->
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Thời gian hiệu lực báo giá</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Thời gian hiệu lực báo giá ">
                  <input
                    [disabled]="true"
                    nz-input
                    placeholder="Nhập Thời gian hiệu lực báo giá"
                    [(ngModel)]="dataObject.timePeriod"
                    name="timePeriod"
                    required
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Điều kiện thanh toán -->
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Điều kiện thanh toán</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Điều kiện thanh toán ">
                  <input
                    [disabled]="true"
                    nz-input
                    placeholder="Nhập Điều kiện thanh toán"
                    [(ngModel)]="dataObject.condition"
                    name="condition"
                    required
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Địa điểm giao hàng -->
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Địa điểm giao hàng</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập Địa điểm giao hàng ">
                  <input [disabled]="true" nz-input placeholder="Nhập Địa điểm giao hàng" [(ngModel)]="dataObject.address" name="address" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Giá khai báo giá đã bao gồm VAT -->
            <nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-control nzSpan="24">
                  <label nz-checkbox [(ngModel)]="dataObject.isHaveVat" formControlName="isHaveVat" id="isHaveVat" nzDisabled>
                    Giá khai báo giá đã bao gồm VAT
                  </label>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <!-- Cho phép hiển thị thông tin ở trường NCC -->
            <nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-control nzSpan="24">
                  <label nz-checkbox [(ngModel)]="dataObject.isShowClient" formControlName="isShowClient" id="isShowClient" nzDisabled>
                    Cho phép hiển thị thông tin ở trường NCC
                  </label>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left">PM Order</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng nhập PM Order ">
                  <input nz-input placeholder="Nhập PM Order" [(ngModel)]="dataObject.noteTrade" name="noteTrade" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="fileAttach" class="text-left">File đính kèm</nz-form-label>
                <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file đính kèm ">
                  <div class="tooltip" *ngIf="dataObject.fileAttach">
                    <a href="{{ dataObject.fileAttach }}" target="_blank"> Xem file </a>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <!-- Mô tả -->
            <nz-col nzSpan="24">
              <nz-form-item nzFlex>
                <nz-form-label nzSpan="24" class="text-left">Mô tả</nz-form-label>
                <nz-form-control nzSpan="24">
                  <textarea
                    [disabled]="true"
                    rows="4"
                    nz-input
                    placeholder="Nhập Mô tả"
                    [(ngModel)]="dataObject.description"
                    name="description"
                  ></textarea>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
        </nz-collapse-panel>
        <nz-collapse-panel [nzHeader]="'Thông tin Item'" [nzActive]="true">
          <nz-row nzGutter="8">
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control nzSpan="24">
                  <label nz-checkbox [(ngModel)]="dataObject.isGetFromPr" name="isGetFromPr" id="isGetFromPr" nzDisabled>
                    Lấy danh sách Item từ PR
                  </label>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" class="text-left" nzRequired>Lĩnh vực mua hàng</nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-select
                    [disabled]="true"
                    nzShowSearch
                    nzAllowClear
                    [(ngModel)]="dataObject.externalMaterialGroupId"
                    name="MaterialGroupId"
                    nzPlaceHolder="Chọn lĩnh vực mua hàng"
                  >
                    <nz-option *ngFor="let item of lstExternalMatGroup" [nzValue]="item.id" [nzLabel]="item.code + '-' + item.name"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8" *ngIf="dataObject.isGetFromPr">
              <nz-form-item>
                <nz-form-label class="text-left" nzSpan="24" nzRequired>PR </nz-form-label>
                <nz-form-control nzSpan="24">
                  <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.prId" name="prId" nzPlaceHolder="Chọn PR" [disabled]="true">
                    <nz-option *ngFor="let item of dataPr" [nzLabel]="item.code" [nzValue]="item.id" required></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
          <nz-table [nzData]="dataObject.lstItem && dataObject.lstItem.length > 0 ? [''] : []" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr class="text-nowrap">
                <th nzWidth="50px">Item line</th>
                <th nzWidth="50px">Category</th>
                <th nzWidth="130px">Material</th>
                <th nzWidth="250px">Short text</th>
                <th nzWidth="50px">Quantity</th>
                <th nzWidth="100px">Delivery Date</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstItem; let i = index">
                <td class="text-right">{{ data.itemNo | number }}</td>
                <td>{{ data.category }} - {{ data.categoryName }}</td>
                <td>{{ data.materialCode }}</td>
                <td nzBreakWord>{{ data.shortText }}</td>
                <td class="text-right">{{ data.quantity | number }}</td>
                <td>{{ data.deliveryDate | date : 'dd/MM/yyyy' }}</td>
              </tr>
            </tbody>
          </nz-table>
          <!-- Lấy danh sách item từ PR -->
        </nz-collapse-panel>
        <nz-collapse-panel [nzHeader]="'Thông tin NCC'" [nzActive]="true">
          <div>
            <!-- Tìm kiếm NCC -->
            <nz-row class="mt-3">
              <nz-table
                nz-col
                nzSpan="24"
                [nzData]="dataObject.lstSup"
                [(nzPageSize)]="pageSize"
                [nzLoading]="loading"
                [nzShowPagination]="false"
                nzBordered
              >
                <thead>
                  <tr>
                    <!-- <th width="20px"></th> -->
                    <th>{{ 'Mã nhà cung cấp' }}</th>
                    <th>{{ 'Tên nhà cung cấp' }}</th>
                    <th>Ngày đăng ký</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let dataRow of dataObject.lstSup">
                    <td>
                      <span nz-tooltip nzTooltipTitle="Chi tiết" nz-icon nzType="info-circle"></span>
                      {{ dataRow.supplierCode }}
                    </td>
                    <td class="mw-25">{{ dataRow.supplierName }}</td>
                    <td>{{ dataRow.createdAt | date : 'dd/MM/yyyy' }}</td>
                  </tr>
                </tbody>
              </nz-table>
            </nz-row>
          </div>
        </nz-collapse-panel>
      </nz-collapse>
    </nz-tab>
    <nz-tab nzTitle="Điều kiện thương mại">
      <app-trade-detail [offer]="{ id: data.id }"></app-trade-detail>
    </nz-tab>
    <nz-tab nzTitle="Cấu hình giá">
      <app-price-detail [offer]="{ id: data.id }"></app-price-detail>
    </nz-tab>

    <nz-tab nzTitle="Kết quả đàm phán">
      <ng-template nz-tab>
        <app-offer-result-deal [bidId]="data.id"></app-offer-result-deal>
      </ng-template>
    </nz-tab>
  </nz-tabset>

  <nz-row>
    <nz-col nzSpan="24" class="text-center mt-4">
      <button (click)="closeDialog()" nz-button class="mr-2 btn-cancel btn btn-primary"><span nz-icon nzType="close"></span>Đóng</button>
    </nz-col>
  </nz-row>
</form>
