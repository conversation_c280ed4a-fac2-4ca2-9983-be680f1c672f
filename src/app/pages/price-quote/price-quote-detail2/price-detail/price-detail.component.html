<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-form-control nzSpan="24">
    <nz-form-label>Lĩnh vực mua hàng</nz-form-label>
    <nz-select [disabled]="true" nzShowSearch nzAllowClear [nzPlaceHolder]="'Chọn Lĩnh vực mua hàng'"
      [(ngModel)]="data.count" name="count">
      <nz-option *ngFor="let item of data.listItem" [nzLabel]="item.itemName" [nzValue]="item.count"> </nz-option>
    </nz-select>
  </nz-form-control>

  <div *ngFor="let item of data.listItem; let i = index" class="mt-3">
    <div *ngIf="i === data.count">
      <nz-collapse class="mt-3">
        <nz-collapse-panel nzHeader="Thông tin các hạng mục chào giá" class="ant-bg-lightblue"
          [(nzActive)]="dicActiveCollapsePrice[item.id]">
          <nz-row class="mt-3">
            <nz-table nz-col nzSpan="24" [nzData]="item.listPrice" [(nzPageSize)]="pageSize" [nzLoading]="loading"
              [nzShowPagination]="false" nzBordered>
              <thead>
                <tr>
                  <th style="width: 120px">
                    {{ language_key?.NO || 'STT' }}
                  </th>
                  <th>Tên hạng mục</th>
                  <th *ngFor="let col of item.listPriceCol" class="dynamic-col-mpo">
                    {{ col.name }}
                  </th>
                  <th>Đơn vị tính</th>
                  <th>Đơn vị tiền tệ</th>
                  <th nz-tooltip nzTooltipTitle="Mã cột để làm công thức: [qty]">
                    {{ language_key?.QUANTITY || 'Số lượng' }}
                  </th>
                  <th>Bắt buộc?</th>
                </tr>
              </thead>
              <tbody>
                <!-- level 1 -->
                <ng-container *ngFor="let data1 of item.listPrice">
                  <tr>
                    <td>
                      {{ data1.sort > 0 ? data1.sort : '' }}
                    </td>
                    <td class="mw-25">{{ data1.name }}</td>
                    <td *ngFor="let col of item.listPriceCol">{{ data1[col.id] }}</td>
                    <td>
                      {{ data1.unit }}
                    </td>
                    <td>
                      {{ data1.currency }}
                    </td>
                    <td class="text-right">{{ data1.number | number }}</td>
                    <td>
                      {{ data1.isRequired ? 'Bắt buộc' : 'Không' }}
                    </td>
                  </tr>
                  <!-- level 2 -->
                  <ng-container *ngFor="let data2 of data1.__childs__">
                    <tr>
                      <td [nzIndentSize]="5">{{ data2.sort > 0 ? data2.sort : '' }}</td>
                      <td class="mw-25">
                        {{ data2.name }}
                      </td>
                      <td *ngFor="let col of item.listPriceCol">{{ data2[col.id] }}</td>
                      <td>
                        {{ data2.unit }}
                      </td>
                      <td>
                        {{ data2.currency }}
                      </td>
                      <td class="text-right">
                        {{ data2.number | number }}
                      </td>
                      <td>
                        {{ data2.isRequired ? 'Bắt buộc' : 'Không' }}
                      </td>
                    </tr>
                    <!-- level 3 -->
                    <ng-container *ngFor="let data3 of data2.__childs__">
                      <tr>
                        <td [nzIndentSize]="30">{{ data3.sort > 0 ? data3.sort : '' }}</td>
                        <td class="mw-25">
                          {{ data3.name }}
                        </td>
                        <td *ngFor="let col of item.listPriceCol">{{ data3[col.id] }}</td>
                        <td>
                          {{ data3.unit }}
                        </td>
                        <td>
                          {{ data3.currency }}
                        </td>
                        <td class="text-right">
                          {{ data3.number | number }}
                        </td>
                        <td>
                          {{ data3.isRequired ? 'Bắt buộc' : 'Không' }}
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>

      <nz-collapse class="mt-3">
        <nz-collapse-panel nzHeader="Thông tin các hạng mục cơ cấu giá" class="ant-bg-lightblue"
          [(nzActive)]="dicActiveCollapseCustomPrice[item.id]">
          <nz-row class="mt-3">
            <nz-table nz-col nzSpan="24" [nzData]="item.listCustomPrice" [(nzPageSize)]="pageSize"
              [nzLoading]="loading2" [nzShowPagination]="false" nzBordered>
              <thead>
                <tr>
                  <th>{{ language_key?.NO || 'STT' }}</th>
                  <th>Tên hạng mục</th>
                  <th>Đơn vị tính</th>
                  <th>Đơn vị tiền tệ</th>
                  <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                  <th>Bắt buộc?</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let dataRow of item.listCustomPrice">
                  <tr>
                    <td>
                      {{ dataRow.sort > 0 ? dataRow.sort : '' }}
                    </td>
                    <td class="mw-25">
                      {{ dataRow.name }}
                    </td>
                    <td>
                      {{ dataRow.unit }}
                    </td>
                    <td>
                      {{ dataRow.currency }}
                    </td>
                    <td class="text-right">
                      {{ dataRow.number | number }}
                    </td>
                    <td>
                      {{ dataRow.isRequired ? 'Bắt buộc' : 'Không' }}
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>
    </div>
  </div>

  <nz-row class="mt-3">
    <nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-control nzSpan="24">
          <label nz-checkbox id="isCompleteAll">
            Yêu cầu NCC báo giá đủ
          </label>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="24" class="text-left">
      <h4>Người yêu cầu ghi chú:</h4>
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'" [(ngModel)]="data.notePrice"
        [disabled]="!data.isShowCreatePrice"></textarea>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <nz-col nzSpan="24" class="text-left">
      <h4>Người duyệt ghi chú:</h4>
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.noteMPOLeader" [disabled]="true"></textarea>
    </nz-col>
  </nz-row>
</div>

<!-- Thiết lập công thức tính đơn giá -->

<!-- Thiết lập cách tính điểm giá -->