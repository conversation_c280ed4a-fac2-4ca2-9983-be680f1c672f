import { Component, OnInit, Optional, Inject, Input } from '@angular/core'
import { MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Subscription } from 'rxjs'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-price-detail',
  templateUrl: './price-detail.component.html',
})
export class PriceDetailComponent implements OnInit {
  @Input() offer: any
  loading = false
  loading2 = false
  pageSize = enumData.Page.pageSizeMax
  modalTitle = 'Thông tin thiết lập bảng giá'
  listOfData: any[] = []
  lstUnit: any[] = []
  lstCurrency: any[] = []
  lstDataType = [enumData.DataType.String, enumData.DataType.Address, enumData.DataType.Km, enumData.DataType.Time]
  dataPriceScoreCalculateWay = this.coreService.convertObjToArray(enumData.PriceScoreCalculateWay)

  isVisible = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  itemChoose: any
  dicActiveCollapse: any = {}
  dicActiveCollapsePrice: any = {}
  dicActiveCollapseCustomPrice: any = {}
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  isLoadData = false
  isVisibleSettingWayCalScorePrice = false
  dataSettingWayCalScorePrice: any = {}
  priceStatus = enumData.BidPriceStatus
  bidTechStatus = enumData.BidTechStatus
  bidStatus = enumData.BidStatus

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })

    this.data = { listItem: [] }

    if (this.offer.id) {
      this.data = { id: this.offer.id }
      this.searchData()
    }
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    this.loading2 = true
    this.apiService.get(this.apiService.OFFER.GET_PRICE(this.data.id), {}).then((res) => {
      this.loading = false
      this.loading2 = false
      const countNow = this.data.count
      this.data = res
      this.data.count = countNow
      let count = 0
      const lstEx = []
      for (const item of this.data.listItem) {
        if (item.isExmatgroup == true || item.isExGr) {
          item.count = count
          count++
          lstEx.push(item)
        }
      }
      this.data.listItem = lstEx
      this.data.count = 0
    })
  }

  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
      // this.apiService.post(this.apiService.CURRENCY.DATA_SELECT_BOX, {}),
    ])
    this.lstUnit = res[0]
    // this.lstCurrency = res[1]
    this.isLoadData = true
  }

  async loadPrice(item: any) {
    this.loading = true
    await this.apiService.get(this.apiService.OFFER.LOAD_PRICE(item.id), {}).then(() => {
      this.loading = false
      this.searchData()
    })
  }

  async loadCustomPrice(item: any) {
    this.loading = true
    await this.apiService.get(this.apiService.OFFER.LOAD_CUSTOMPRICE(item.id), {}).then(() => {
      this.loading = false
      this.searchData()
    })
  }

  checkDynamicColRequired(row: any, lstCol: any[]) {
    let strErr = ''
    if (row.__bidPriceColValue__ == null || row.__bidPriceColValue__.length == 0) {
      strErr = `Hạng mục [${row.name}] chưa điền đủ thông tin các cột động bắt buộc!<br>`
    } else {
      for (const col of lstCol) {
        // Nếu chưa nhập thì báo lỗi
        if (!row.__bidPriceColValue__.some((c: any) => c.bidPriceColId === col.id)) {
          strErr = `Hạng mục [${row.name}] chưa điền đủ thông tin các cột động bắt buộc!<br>`
          break
        }
      }
    }
    return strErr
  }

  checkData() {
    let strErr = ''

    for (const item of this.data.listItem) {
      let lstColRequired = item.listPriceCol.filter((c: any) => c.colType === enumData.ColType.MPO.code && c.isRequired === true)
      for (const priceLv1 of item.listPrice) {
        if (priceLv1.__bidPriceListDetails__ && priceLv1.__bidPriceListDetails__.length > 0) {
          for (const z of priceLv1.__bidPriceListDetails__) {
            if (!z.value) {
              strErr += `Hạng mục [${priceLv1.name}] của Item [${item.itemName}] chưa thiết lập đủ thông tin bổ sung!<br>`
              break
            }
          }
        }
        // kiểm tra bắt buộc ở cột động
        if (lstColRequired.length > 0) {
          strErr += this.checkDynamicColRequired(priceLv1, lstColRequired)
          for (const priceLv2 of priceLv1.__childs__) {
            strErr += this.checkDynamicColRequired(priceLv2, lstColRequired)
            for (const priceLv3 of priceLv2.__childs__) {
              strErr += this.checkDynamicColRequired(priceLv3, lstColRequired)
            }
          }
        }
      }
    }

    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      return false
    }
    return true
  }

  async createPrice() {
    this.notifyService.showloading()
    if (this.checkData()) {
      await this.apiService.post(this.apiService.OFFER.CREATE_PRICE(this.data.id), this.data).then((res) => {
        if (res) {
          this.notifyService.showSuccess(res.message)
          this.notifyService.hideloading()
          this.closeDialog(true)
        }
      })
    }
  }

  closeDialog(flag: boolean) {
    this.router.navigate(['/price-quote/list'])
  }

  clickDelete(itemPrice: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.OFFERPRICE_DELETE, { id: itemPrice.id }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData()
    })
  }

  clickDeleteCustomPrice(itemCustomPrice: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.OFFERCUSTOMPRICE_DELETE, { id: itemCustomPrice.id }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData()
    })
  }

  clickDeleteAll(item: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.OFFERPRICE_DELETEALL, { id: item.id }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData()
    })
  }

  clickDeleteAllCustomPrice(item: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.OFFERCUSTOMPRICE_DELETEALL, { id: item.id }).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.searchData()
    })
  }

  clickExportExcelPrice(item: any) {
    this.notifyService.showloading()

    let lstDataExport = []

    //#region header
    const bidPriceColTitle: any = {}
    for (const col of item.listPriceCol) {
      bidPriceColTitle[col.id] = ''
    }
    const title: any = {
      zenId: 'CẤU HÌNH TEMPLATE',
      sort: '',
      name: '',
      ...bidPriceColTitle,
      unit: '',
      currency: '',
      number: '',
      isRequired: '',
      blank: '',
      zenListId: 'CẤU HÌNH CÁC THÔNG TIN MỞ RỘNG',
      nameList: '',
      typeDataList: '',
      valueList: '',
    }
    lstDataExport.push(title)

    const bidPriceColHeader: any = {}
    for (const col of item.listPriceCol) {
      bidPriceColHeader[col.id] = col.name
    }
    const numColTable1 = 3 + item.listPriceCol.length + 4
    const header: any = {
      zenId: 'Cột',
      sort: this.language_key?.NO || 'STT',
      name: 'Tên hạng mục',
      ...bidPriceColHeader,
      unit: 'Đơn vị tính',
      currency: 'Đơn vị tiền tệ',
      number: 'Số lượng',
      isRequired: 'Bắt buộc?',
      blank: '',
      zenListId: 'Cột',
      nameList: 'Tên trường thông tin',
      typeDataList: 'Kiểu dữ liệu',
      valueList: 'Giá trị',
    }
    lstDataExport.push(header)
    //#endregion

    //#region custom data before export
    let i1 = 1
    const lstDataTable1: any[] = []
    const lstDataTable2 = []
    // lv1
    for (const data1 of item.listPrice) {
      const dataTable1Lv1: any = {}
      dataTable1Lv1.zenId = '' + i1
      i1++
      dataTable1Lv1.sort = data1.sort > 0 ? data1.sort : ''
      dataTable1Lv1.name = data1.name
      dataTable1Lv1.unit = data1.unit
      dataTable1Lv1.currency = data1.currency
      dataTable1Lv1.number = data1.number
      dataTable1Lv1.isRequired = data1.isRequired
      for (const col of item.listPriceCol) dataTable1Lv1[col.id] = data1[col.id]
      lstDataTable1.push(dataTable1Lv1)

      if (data1.__bidPriceListDetails__ && data1.__bidPriceListDetails__.length > 0) {
        for (const detail of data1.__bidPriceListDetails__) {
          const dataTable2Lv1: any = {}
          dataTable2Lv1.zenListId = dataTable1Lv1.zenId
          dataTable2Lv1.nameList = detail.name
          dataTable2Lv1.typeDataList = detail.type
          dataTable2Lv1.valueList = detail.value
          lstDataTable2.push(dataTable2Lv1)
        }
      }

      let i2 = 1
      const lstDataLv2 = data1.__childs__
      for (const data2 of lstDataLv2) {
        const dataTable1Lv2: any = {}
        dataTable1Lv2.zenId = dataTable1Lv1.zenId + '.' + i2
        i2++
        dataTable1Lv2.sort = data2.sort > 0 ? data2.sort : ''
        dataTable1Lv2.name = data2.name
        dataTable1Lv2.unit = data2.unit
        dataTable1Lv2.currency = data2.currency
        dataTable1Lv2.number = data2.number
        dataTable1Lv2.isRequired = data2.isRequired
        for (const col of item.listPriceCol) dataTable1Lv2[col.id] = data2[col.id]
        lstDataTable1.push(dataTable1Lv2)

        if (data2.__bidPriceListDetails__ && data2.__bidPriceListDetails__.length > 0) {
          for (const detail of data2.__bidPriceListDetails__) {
            const dataTable2Lv2: any = {}
            dataTable2Lv2.zenListId = dataTable1Lv2.zenId
            dataTable2Lv2.nameList = detail.name
            dataTable2Lv2.typeDataList = detail.type
            dataTable2Lv2.valueList = detail.value
            lstDataTable2.push(dataTable2Lv2)
          }
        }

        let i3 = 1
        const lstDataLv3 = data2.__childs__
        for (const data3 of lstDataLv3) {
          const dataTable1Lv3: any = {}
          dataTable1Lv3.zenId = dataTable1Lv2.zenId + '.' + i3
          i3++
          dataTable1Lv3.sort = data3.sort > 0 ? data3.sort : ''
          dataTable1Lv3.name = data3.name
          dataTable1Lv3.unit = data3.unit
          dataTable1Lv3.currency = data3.currency
          dataTable1Lv3.number = data3.number
          dataTable1Lv3.isRequired = data3.isRequired
          for (const col of item.listPriceCol) dataTable1Lv3[col.id] = data3[col.id]
          lstDataTable1.push(dataTable1Lv3)

          if (data3.__bidPriceListDetails__ && data3.__bidPriceListDetails__.length > 0) {
            for (const detail of data3.__bidPriceListDetails__) {
              const dataTable2Lv3: any = {}
              dataTable2Lv3.zenListId = dataTable1Lv3.zenId
              dataTable2Lv3.nameList = detail.name
              dataTable2Lv3.typeDataList = detail.type
              dataTable2Lv3.valueList = detail.value
              lstDataTable2.push(dataTable2Lv3)
            }
          }
        }
      }
    }

    //#endregion

    let numRowData = lstDataTable1.length > lstDataTable2.length ? lstDataTable1.length : lstDataTable2.length
    for (let i = 0; i < numRowData; i++) {
      const dataTable1 = lstDataTable1[i] || {}
      const dataTable2 = lstDataTable2[i] || {}
      lstDataExport.push({ ...dataTable1, ...dataTable2 })
    }
    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template cấu hình bảng chào giá của Item [${item.itemName}].xlsx`
    const sheetName = 'Bảng chào giá'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)
    wb.Sheets[sheetName]['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: numColTable1 - 1 } } /* A1:G1 */,
      { s: { r: 0, c: numColTable1 + 1 }, e: { r: 0, c: numColTable1 + 4 } } /* I1:L1 */,
    ]

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  async clickImportExcelPrice(ev: any, item: any) {
    if (!this.isLoadData) {
      await this.loadAllList()
    }
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: [
          'zenId',
          'sort',
          'name',
          ...item.listPriceColId,
          'unit',
          'currency',
          'number',
          'isRequired',
          'blank',
          'zenListId',
          'nameList',
          'typeDataList',
          'valueList',
        ],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng header
      let isErr = false
      const header = jsonData.shift()
      // Kiểm tra header
      if (
        header.zenId !== 'Cột' ||
        header.sort !== (this.language_key?.NO || 'STT') ||
        header.name !== 'Tên hạng mục' ||
        header.unit !== 'Đơn vị tính' ||
        header.currency !== 'Đơn vị tiền tệ' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.number !== 'Số lượng' ||
        header.zenListId !== 'Cột' ||
        header.nameList !== 'Tên trường thông tin' ||
        header.typeDataList !== 'Kiểu dữ liệu' ||
        header.valueList !== 'Giá trị'
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError('File không đúng template chào giá Item ' + item.itemName)
        return false
      }

      // Tách và kiểm tra data từng bảng
      const lstDataTable1: any[] = []
      const lstDataTable2 = []
      let strErr = ''
      for (const row of jsonData) {
        // add data table 1
        if (row.zenId != null && row.zenId != '') {
          const dataTable1: any = {}
          dataTable1.zenId = (row.zenId + '').trim()
          dataTable1.sort = row.sort || 0
          dataTable1.name = (row.name || '') + ''
          dataTable1.unit = (row.unit || '') + ''
          dataTable1.currency = (row.currency || '') + ''
          dataTable1.number = row.number || 0
          dataTable1.isRequired = row.isRequired
          if (dataTable1.isRequired == null || dataTable1.isRequired === '' || typeof dataTable1.isRequired !== 'boolean') {
            dataTable1.isRequired = false
          }
          for (const colId of item.listPriceColId) {
            dataTable1[colId] = row[colId]
          }
          const lstId = dataTable1.zenId.split('.')
          dataTable1.level = lstId.length
          if (dataTable1.level < 1 || dataTable1.level > 3) {
            strErr += `Cột [${dataTable1.zenId}] không hợp lệ, không xác định được level nào<br>`
          }
          for (const id of lstId) {
            try {
              let intId = parseInt(id)
              if (intId <= 0) {
                strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số dương<br>`
              }
            } catch {
              strErr += `Cột [${dataTable1.zenId}] có [${id}] không là số<br>`
            }
          }
          if (dataTable1.level == 2) {
            dataTable1.parentZenId = lstId[0] + ''
            if (!lstDataTable1.some((c) => c.zenId == dataTable1.parentZenId)) {
              strErr += `Không tìm thấy cấp cha của cột [${dataTable1.zenId}] ở phía trên của dòng này<br>`
            }
          }
          if (dataTable1.level == 3) {
            dataTable1.parentZenId = lstId[0] + '.' + lstId[1]
            if (!lstDataTable1.some((c) => c.zenId == dataTable1.parentZenId)) {
              strErr += `Không tìm thấy cấp cha của cột [${dataTable1.zenId}] ở phía trên của dòng này<br>`
            }
          }

          if (dataTable1.name.trim() === '') {
            strErr += 'Tên hạng mục không được để trống<br>'
          }
          if (dataTable1.unit.trim().length > 0) {
            if (!this.lstUnit.some((c) => c.code === dataTable1.unit)) {
              strErr += `Đơn vị tính [${dataTable1.unit}] không tồn tại<br>`
            }
          }
          if (dataTable1.currency.trim().length > 0) {
            if (!this.lstCurrency.some((c) => c.code === dataTable1.currency)) {
              strErr += `Đơn vị tiền tệ [${dataTable1.currency}] không tồn tại<br>`
            }
          }
          if (dataTable1.number == null || typeof dataTable1.number !== 'number') {
            strErr += 'Số lượng là số, không được để trống<br>'
          }
          for (const col of item.listPriceCol) {
            if (col.isRequired && (dataTable1[col.id] == null || dataTable1[col.id] === '')) {
              strErr += `${col.name} không được để trống<br>`
            }
            if (
              dataTable1[col.id] != null &&
              dataTable1[col.id] != '' &&
              col.type === enumData.DataType.Number.code &&
              typeof dataTable1[col.id] !== 'number'
            ) {
              strErr += `${col.name} là số, vui lòng điền đúng<br>`
            }
          }

          lstDataTable1.push(dataTable1)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return false
      }

      for (const row of jsonData) {
        // add data table 2
        if (row.zenListId != null && row.zenListId != '') {
          const dataTable2: any = {}
          dataTable2.zenListId = (row.zenListId + '').trim()
          dataTable2.nameList = (row.nameList || '') + ''
          dataTable2.typeDataList = (row.typeDataList || '') + ''
          dataTable2.valueList = (row.valueList || '') + ''
          if (!lstDataTable1.some((c) => c.zenId == dataTable2.zenListId)) {
            strErr += `Cột [${row.zenListId}] không tồn tại, không xác định thuộc hạng mục nào<br>`
          }
          if (dataTable2.nameList.trim() === '') {
            strErr += 'Tên trường thông tin không được để trống<br>'
          }
          if (dataTable2.typeDataList.trim() === '') {
            strErr += 'Kiểu dữ liệu không được để trống<br>'
          }
          if (!this.lstDataType.some((c) => c.code === dataTable2.typeDataList)) {
            strErr += `Kiểu dữ liệu [${dataTable2.typeDataList}] không tồn tại trong [String, Address, Km, Time]<br>`
          }
          if (dataTable2.valueList == null || dataTable2.valueList === '') {
            strErr += 'Giá trị không được để trống<br>'
          }
          if (
            dataTable2.valueList != null &&
            dataTable2.valueList != '' &&
            (dataTable2.typeDataList == enumData.DataType.Km.code || dataTable2.typeDataList == enumData.DataType.Time.code)
          ) {
            try {
              const a = +dataTable2.valueList
              if (typeof a != 'number' || a <= 0 || isNaN(a) || !isFinite(a)) {
                strErr += `Giá trị kiểu ${dataTable2.typeDataList} phải là số dương<br>`
              }
            } catch {
              strErr += `Giá trị kiểu ${dataTable2.typeDataList} phải số dương<br>`
            }
          }

          lstDataTable2.push(dataTable2)
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return false
      }

      this.notifyService.showloading()
      this.apiService.post(this.apiService.OFFER.OFFERPRICE_IMPORT(item.id), { lstDataTable1, lstDataTable2 }).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })

      return
    }
  }

  clickExportExcelCustomPrice(item: any) {
    let lstDataExport = []
    const title: any = { sort: 'CẤU HÌNH TEMPLATE', name: '', unit: '', currency: '', number: '', isRequired: '' }
    lstDataExport.push(title)
    const header: any = {
      sort: this.language_key?.NO || 'STT',
      name: 'Tên hạng mục',
      unit: 'Đơn vị tính',
      currency: 'Đơn vị tiền tệ',
      number: 'Số lượng',
      isRequired: 'Bắt buộc?',
    }
    lstDataExport.push(header)

    for (const itemCustomPrice of item.listCustomPrice) {
      const row: any = {}
      row.sort = itemCustomPrice.sort > 0 ? itemCustomPrice.sort : ''
      row.name = itemCustomPrice.name
      row.unit = itemCustomPrice.unit
      row.currency = itemCustomPrice.currency
      row.number = itemCustomPrice.number
      row.isRequired = itemCustomPrice.isRequired
      lstDataExport.push(row)
    }

    var ws = XLSX.utils.json_to_sheet(lstDataExport, {
      skipHeader: true,
    })
    var wb = XLSX.utils.book_new()
    const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Template cấu hình cơ cấu giá của Item [${item.itemName}].xlsx`
    const sheetName = 'Cơ cấu giá'
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    wb.Sheets[sheetName]['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 5 } } /* A1:F1 */]

    XLSX.writeFile(wb, fileName)

    setTimeout(() => {
      this.notifyService.hideloading()
    }, 100)
  }

  async clickImportExcelCustomPrice(ev: any, item: any) {
    if (!this.isLoadData) {
      await this.loadAllList()
    }
    let workBook = null
    let jsonData: any = null
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      this.notifyService.showloading()
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: ['sort', 'name', 'unit', 'currency', 'number', 'isRequired'],
      })

      // bỏ dòng merge
      jsonData.shift()
      // bỏ dòng đầu
      const header: any = jsonData.shift()
      if (
        header.sort !== (this.language_key?.NO || 'STT') ||
        header.name !== 'Tên hạng mục' ||
        header.isRequired !== 'Bắt buộc?' ||
        header.unit !== 'Đơn vị tính' ||
        header.currency !== 'Đơn vị tiền tệ' ||
        header.number !== 'Số lượng'
      ) {
        this.notifyService.showError(`File không đúng template cơ cấu giá của Item [${item.itemName}]`)
        return false
      }

      // Kiểm tra từng dòng
      let strErr = ''
      for (const row of jsonData) {
        if (row.sort == null || row.sort === '') {
          row.sort = 0
        }
        if (row.isRequired == null || row.isRequired === '' || typeof row.isRequired !== 'boolean') {
          row.isRequired = false
        }
        if (row.name == null || row.name === '') {
          strErr += 'Tên hạng mục không được để trống<br>'
        }
        row.unit = (row.unit || '') + ''
        if (row.unit != null && row.unit.length > 0) {
          if (!this.lstUnit.some((c) => c.code === row.unit)) {
            strErr += `Đơn vị tính [${row.unit}] không tồn tại<br>`
          }
        }
        row.currency = (row.currency || '') + ''
        if (row.currency != null && row.currency.length > 0) {
          const objCurrency = this.lstCurrency.find((c) => c.code === row.currency)
          if (!objCurrency) {
            strErr += `Đơn vị tiền tệ ${row.currency} không tồn tại<br>`
          }
        }
        if (row.number == null || typeof row.number !== 'number') {
          strErr += 'Số lượng là số, không được để trống<br>'
        }
      }

      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return false
      }

      this.apiService.post(this.apiService.OFFER.OFFERCUSTOMPRICE_IMPORT(item.id), { lstData: jsonData }).then(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
        this.searchData()
      })

      return
    }
  }

  //#region Cấu hình công thức tính đơn giá
  settingFomularCalValue(item: any) {
    this.isVisible = true
    this.itemChoose = item
  }
  handleCancel() {
    this.isVisible = false
    this.itemChoose = null
  }
  handleOk() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.SETTING_FOMULAR, this.itemChoose).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisible = false
      this.itemChoose.fomular = res.fomular
      this.itemChoose = null
    })
  }
  //#endregion

  //#region Cấu hình cách tính điểm giá
  settingPriceCalWay(item: any) {
    this.isVisibleSettingWayCalScorePrice = true
    this.itemChoose = item
    this.dataSettingWayCalScorePrice = {
      id: this.itemChoose.id,
      wayCalScorePrice: this.itemChoose.wayCalScorePrice || enumData.PriceScoreCalculateWay.SumScore.code,
    }
  }
  cancelSettingPriceCalWay() {
    this.isVisibleSettingWayCalScorePrice = false
  }
  saveSettingPriceCalWay() {
    this.notifyService.showloading()
    if (!this.dataSettingWayCalScorePrice.wayCalScorePrice) {
      this.notifyService.showWarning(`Vui lòng chọn cách tính điểm giá!`)
      return
    }
    this.apiService.post(this.apiService.OFFER.SETTING_WAY_CAL_SCORE_PRICE, this.dataSettingWayCalScorePrice).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.isVisibleSettingWayCalScorePrice = false
      this.itemChoose.wayCalScorePrice = this.dataSettingWayCalScorePrice.wayCalScorePrice
    })
  }

  async acceptPrice() {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.OFFER.PRICE_ACCEPT(this.data.id), this.data).then((res) => {
      if (res) {
        this.notifyService.showSuccess(res.message)
        this.closeDialog(true)
      }
    })
  }

  //#endregion
}
