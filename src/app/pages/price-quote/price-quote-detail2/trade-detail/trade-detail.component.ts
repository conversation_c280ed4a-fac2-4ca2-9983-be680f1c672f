import { Component, Input, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

@Component({
  selector: 'app-trade-detail',
  templateUrl: './trade-detail.component.html',
})
export class TradeDetailComponent implements OnInit {
  modalTitle = 'Thông tin điều kiện thương mại'
  loading = false
  dataType = enumData.DataType
  lstDataType = [enumData.DataType.String, enumData.DataType.Number, enumData.DataType.List, enumData.DataType.File, enumData.DataType.Date]
  pageSize = enumData.Page.pageSizeMax
  dicActiveCollapse: any = {}
  listOfData: any[] = []
  language_key: any
  data: any
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  tradeStatus = enumData.BidTradeRateStatus
  bidTechStatus = enumData.BidTechStatus
  bidStatus = enumData.BidStatus

  @Input() offer: any

  constructor(
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  async ngOnInit() {
    this.language_key = this.coreService.getLanguage()
    // const id = this.route.snapshot.paramMap.get('id')
    if (this.offer.id) {
      this.data = { id: this.offer.id }
      this.searchData()
    }
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    await this.apiService.get(this.apiService.OFFER.GET_TRADE(this.data.id), {}).then(async (res) => {
      // this.loading = false
      // this.data = res

      this.loading = false
      const countNow = this.data.count || 0
      this.data = res
      this.data.count = countNow
      let count = 0
      const lstEx = []

      for (const item of this.data.listItem) {
        if (item.isExmatgroup == true || item.isExGr) {
          item.count = count
          count++
          lstEx.push(item)
        }
      }
      this.data.listItem = lstEx
      this.data.count = 0
    })
  }

  async loadTrade(item: any) {
    this.loading = true
    await this.apiService.get(this.apiService.OFFER.LOADTRADE(item.id), {}).then(() => {
      this.loading = false
      this.searchData()
    })
  }

  checkData() {
    let strErr = ''
    for (const item of this.data.listItem) {
      let sumPercent = 0
      for (const v of item.listTrade) {
        if (v.type === enumData.DataType.List.code && v.__bidTradeListDetails__.length === 0) {
          strErr += `Tiêu chí [${v.name}] của Item [${item.itemName}] chưa thiết lập danh sách!<br>`
        }

        const lstChildTypeList = v.__childs__.filter((c: any) => c.type === enumData.DataType.List.code && c.__bidTradeListDetails__.length === 0)
        for (const x of lstChildTypeList) {
          strErr += `*** Tiêu chí [${x.name}] thuộc tiêu chí cấp 1 [${v.name}] của Item [${item.itemName}] chưa thiết lập danh sách!<br>`
        }

        const lstChildCalPercent = v.__childs__.filter((c: any) => c.type === enumData.DataType.List.code || c.type === enumData.DataType.Number.code)
        if (lstChildCalPercent.length > 0) {
          v.sumPercent = 0
          for (const x of lstChildCalPercent) {
            if (x.percent > 0) {
              v.sumPercent += x.percent
            }
          }
          if (v.sumPercent < 100) {
            strErr += `Tổng tỉ trọng tiêu chí [${v.name}] của Item [${item.itemName}] chưa đủ 100%!<br>`
          }
          if (v.sumPercent > 100) {
            strErr += `Tổng tỉ trọng tiêu chí [${v.name}] của Item [${item.itemName}] vượt quá 100%!<br>`
          }
        }

        if (v.percent > 0) {
          sumPercent += v.percent
        }
      }

      if (sumPercent > 100) strErr += `Tổng tỉ trọng Item [${item.itemName}] vượt quá 100%!<br>`
    }

    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      return false
    }
    return true
  }

  async createTrade() {
    this.notifyService.showloading()
    if (this.checkData()) {
      await this.apiService.post(this.apiService.OFFER.CREATE_TRADE(this.data.id), this.data).then(async (res) => {
        if (res) {
          this.notifyService.showSuccess(res.message)
          this.notifyService.hideloading()
          this.closeDialog(true)
        }
      })
    }
  }

  closeDialog(flag: boolean) {
    this.router.navigate(['/price-quote/list'])
  }
}
