<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    <strong style="size: 35px"> {{ modalTitle | uppercase }}</strong>
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-form-control nzSpan="24">
    <nz-form-label>Lĩnh vực mua hàng</nz-form-label>
    <nz-select
      [disabled]="true"
      nzShowSearch
      nzAllowClear
      [nzPlaceHolder]="'Chọn Lĩnh vực mua hàng'"
      [(ngModel)]="data.count"
      name="warehouseId"
    >
      <nz-option *ngFor="let item of data.listItem" [nzLabel]="item.itemName" [nzValue]="item.count"> </nz-option>
    </nz-select>
  </nz-form-control>

  <div *ngFor="let item of data.listItem; let i = index" class="mt-3">
    <div *ngIf="i === data.count">
      <nz-row class="mt-3">
        <span *ngIf="item.sumPercent < 100" class="text-orange">Tổng tỉ trọng đạt {{ item.sumPercent }}%, chưa đủ 100%</span>
        <nz-table nz-col nzSpan="24" [nzData]="item.listTrade" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>{{ language_key?.NO || 'STT' }}</th>
              <th>Tên điều kiện thương mại</th>
              <th>Tỉ trọng(%)</th>
              <th>Giá trị đạt</th>
              <th>Kiểu dữ liệu</th>
              <th>Bắt buộc?</th>
              <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let rowData of item.listTrade">
              <tr>
                <td>
                  {{ rowData.sort > 0 ? rowData.sort : '' }}
                </td>
                <td class="mw-25">{{ rowData.name }}</td>
                <td class="text-right">{{ rowData.percent }}</td>
                <td class="text-right">{{ rowData.percentRule | number }}</td>
                <td>
                  {{ rowData.type }}
                </td>
                <td>
                  {{ rowData.isRequired ? 'Bắt buộc' : 'Không' }}
                </td>
              </tr>
              <ng-container>
                <td [nzIndentSize]="10" colspan="8" scope="colgroup" *ngIf="rowData.sumPercent < 100 && rowData.sumPercent >= 0" class="text-orange">
                  Tổng tỉ trọng trong mục này đạt {{ rowData.sumPercent }}%, chưa đủ 100%
                </td>
                <tr *ngFor="let childData of rowData.__childs__">
                  <td [nzIndentSize]="10">{{ childData.sort > 0 ? childData.sort : '' }}</td>
                  <td class="mw-25">
                    {{ childData.name }}
                  </td>
                  <td class="text-right">{{ childData.percent }}</td>
                  <td class="text-right">{{ childData.percentRule | number }}</td>
                  <td>
                    {{ childData.type }}
                  </td>
                  <td>
                    {{ childData.isRequired ? 'Bắt buộc' : 'Không' }}
                  </td>
                </tr>
              </ng-container>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-row>
    </div>
  </div>
</div>

<!-- <nz-row style="margin-top: 15px;">
  <nz-col nzSpan="24" class="text-center ">

    <button nz-button class="mr-2" (click)="closeDialog(false)"
      style="border-color: #e41717 ;color: #e41717 ;border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>
  </nz-col>
</nz-row> -->
