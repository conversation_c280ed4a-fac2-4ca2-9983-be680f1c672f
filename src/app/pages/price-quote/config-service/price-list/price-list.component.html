<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row>
    <nz-col nzSpan="24">
      <button nz-button (click)="searchData()" class="mr-2">Làm mới</button>
      <button nz-button (click)="addRow()" nzType="primary">Thêm</button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-table nz-col nzSpan="24" class="mb-3" [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzShowPagination]="false"
      nzBordered>
      <thead>
        <tr>
          <th>Tên trường thông tin</th>
          <th>Giá trị</th>
          <th>{{ language_key?.OPTION || '<PERSON><PERSON> chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataRow of listOfData">
          <td class="mw-25">
            <ng-container
              *ngIf="this.listOfData.length > 0 && editCache[dataRow.id]?.edit && !editCache[dataRow.id]?.edit; else nameInputTpl">
              {{ dataRow.nameCol }}
            </ng-container>
            <ng-template #nameInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[dataRow.id]?.data.nameCol"
                placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>
          <td class="text-right">
            <ng-container
              *ngIf=" this.listOfData.length > 0 && editCache[dataRow.id]?.edit &&  !editCache[dataRow.id]?.edit; else valueInputTpl">
              {{ dataRow.price }}
            </ng-container>
            <ng-template #valueInputTpl>
              <input nz-input type="number" [(ngModel)]="editCache[dataRow.id]?.data.price"
                placeholder="Nhập số (0-100)" />
            </ng-template>
          </td>
          <td class="text-nowrap">
            <div class="editable-row-operations">
              <ng-container
                *ngIf=" this.listOfData.length > 0 && editCache[dataRow.id]?.edit && !editCache[dataRow.id].edit; else saveTpl">
                <button nz-tooltip nzTooltipTitle="Sửa" class="mr-2" nz-button *ngIf="true "
                  (click)="startEdit(dataRow.id)">
                  <span nz-icon nzType="edit"></span>
                </button>
                <button nz-popconfirm nzPopconfirmTitle="Bạn có chắc muốn xoá?" nzPopconfirmPlacement="bottom"
                  *ngIf="true " (nzOnConfirm)="startDelete(dataRow)" nz-tooltip nzTooltipTitle="Xoá" class="mr-2"
                  nz-button nzDanger>
                  <span nz-icon nzType="delete"></span>
                </button>
              </ng-container>
              <ng-template #saveTpl>
                <button nz-tooltip [nzTooltipTitle]="language_key?.SAVE || 'Lưu'" class="mr-2" nz-button
                  nzType="primary" *ngIf="true " (click)="saveEdit(dataRow.id)">
                  <span nz-icon nzType="save"></span>
                </button>
                <button nz-tooltip [nzTooltipTitle]="language_key?.CANCEL || 'Hủy'" class="mr-2" nz-button nzDanger
                  *ngIf="true " (click)="startDelete(dataRow)">
                  <span nz-icon nzType="close"></span>
                </button>
              </ng-template>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>