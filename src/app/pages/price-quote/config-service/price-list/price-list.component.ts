import { Component, OnInit, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { enumData } from '../../../../core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../../services'
import { Subscription } from 'rxjs'
import * as uuid from 'uuid'

@Component({
  selector: 'app-price-list',
  templateUrl: './price-list.component.html',
})
export class PriceListComponent implements OnInit {
  modalTitle = 'Thiết lập bảng chào giá'
  loading = true
  editCache: any = {}
  listOfData: any[] = []
  pageSize = enumData.Page.pageSizeMax
  lstDataType = [enumData.DataType.String, enumData.DataType.Address, enumData.DataType.Km, enumData.DataType.Time]
  dataType = enumData.DataType
  dataFilterStatus = this.coreService.convertObjToArray(enumData.StatusFilter)
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.modalTitle = 'Thiết lập bảng chào giá '
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    this.searchData()
  }

  startEdit(id: string) {
    this.editCache[id].edit = true
  }

  cancelEdit(id: string) {
    const index = this.listOfData.findIndex((c) => c.id === id)
    if (this.editCache[id].data.isNew !== true) {
      this.editCache[id] = {
        data: { ...this.listOfData[index] },
        edit: false,
      }
    } else {
      this.listOfData = this.listOfData.filter((d) => d.id !== id)
      delete this.editCache[id]
    }
  }

  saveEdit(id: string) {
    this.notifyService.showloading()
    const index = this.listOfData.findIndex((c) => c.id === id)
    if (!this.editCache[id].data.nameCol) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }
    if (this.editCache[id].data.nameCol.length > 250) {
      this.notifyService.showError(enumData.Warnings.TooLong)
      return
    }
    if (this.editCache[id].data.price === null) {
      this.notifyService.showError(enumData.Warnings.Require)
      return
    }
    // if (this.editCache[id].data.price < 0 || this.editCache[id].data.price > 100) {
    //   this.notifyService.showError('Vui lòng nhập số (0-100)')
    //   return
    // }

    let url = this.apiService.OFFER.ITEMTECHLISTDETAIL_UPDATE
    if (this.editCache[id].data.isNew === true) {
      delete this.editCache[id].data.id
      url = this.apiService.OFFER.ITEMTECHLISTDETAIL_CREATE
    }

    this.apiService.post(url, this.editCache[id].data).then((result) => {
      if (result) {
        this.editCache[id].edit = false
        if (!this.editCache[id].data.isNew) {
          Object.assign(this.listOfData[index], this.editCache[id].data)
        } else if (result.id) {
          const item = this.editCache[id].data
          item.id = result.id
          delete item.isNew
          Object.assign(this.listOfData[index], item)

          delete this.editCache[id]
          this.editCache[item.id] = {
            edit: false,
            data: { ...item },
          }
        }
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      }
    })
  }

  updateEditCache() {
    this.listOfData.forEach((item) => {
      this.editCache[item.id] = {
        edit: false,
        data: { ...item },
      }
    })
  }

  addRow() {
    const item = {
      id: uuid.v4(),
      nameCol: '',
      price: 0,
      offerServiceId: this.data.id,
      isNew: true,
    }
    this.listOfData = [...this.listOfData, item]
    this.editCache[item.id] = {
      edit: true,
      data: { ...item },
    }
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    this.apiService.get(this.apiService.OFFER.ITEMTECHLISTDETAIL_LIST(this.data.id), {}).then((res) => {
      this.loading = false
      this.listOfData = res
      this.updateEditCache()
    })
  }

  startDelete(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.ITEMTECHLISTDETAIL_DELETE, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(enumData.Constants.Message_Delete_Success)
      this.listOfData = this.listOfData.filter((d) => d.id !== data.id)
      delete this.editCache[data.id]
    })
  }
}
