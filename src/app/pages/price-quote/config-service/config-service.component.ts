import { Component, OnInit, Optional, Inject } from '@angular/core'
import { enumData } from '../../../core'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from '../../../services'
import { Subscription } from 'rxjs'
import { PriceListComponent } from './price-list/price-list.component'

@Component({
  selector: 'app-config-service',
  templateUrl: './config-service.component.html',
})
export class ConfigServiceComponent implements OnInit {
  loading = false
  loading2 = false
  pageSize = enumData.Page.pageSizeMax
  modalTitle = 'Thông tin bảng chào giá'
  listOfData: any[] = []
  lstUnit: any[] = []
  lstCurrency: any[] = []
  lstDataType = [enumData.DataType.String, enumData.DataType.Address, enumData.DataType.Km, enumData.DataType.Time]
  isVisible = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  itemChoose: any
  dicActiveCollapse: any = {}
  dicActiveCollapsePrice: any = {}
  dicActiveCollapseCustomPrice: any = {}
  enumProject: any
  enumRole: any
  currentUser: any
  dataObj: any = {}
  action: any
  listCustomPrice: any
  isLoadData = false

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<ConfigServiceComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()
    // this.listOfData = [
    //   { name: 'Gạo ST24', money: '1.000.000VND', value: '10' },
    //   { name: 'Gạo ST25', money: '2.000.000VND', value: '10' },
    // ]
    this.searchData()
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    this.loading2 = true
    this.apiService.get(this.apiService.OFFER.GET_PRICE(this.data.id), {}).then((res: any) => {
      this.loading = false
      this.loading2 = false
      this.listOfData = res
    })
  }
  clickAddPrice(data: string) {
    this.dialog
      .open(PriceListComponent, { disableClose: false, data: data })
      .afterClosed()
      .subscribe((res) => {})
  }

  async loadAllList() {
    // const res = await Promise.all([
    //   this.apiService.post(this.apiService.SETTING_STRING.FIND, { type: enumData.SettingStringType.unit }),
    //   this.apiService.post(this.apiService.CURRENCY.DATA_SELECT_BOX, {}),
    // ])
    // this.lstUnit = res[0]
    // this.lstCurrency = res[1]
    // this.isLoadData = true
  }

  async loadPrice(item: any) {
    // this.loading = true
    // await this.apiService.get(this.apiService.BID.LOAD_PRICE(item.id), {}).then(() => {
    //   this.loading = false
    //   this.searchData()
    // })
  }

  async loadCustomPrice(item: any) {
    // this.loading = true
    // await this.apiService.get(this.apiService.BID.LOAD_CUSTOMPRICE(item.id), {}).then(() => {
    //   this.loading = false
    //   this.searchData()
    // })
  }

  checkDynamicColRequired(row: any, lstCol: any[]) {}

  checkData() {}

  async createPrice() {}

  settingExInfo(itemPrice: any) {}

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  clickAdd(item: any) {}

  clickAddCustomPrice(item: any) {}

  clickEdit(itemPrice: any) {}

  clickEditCustomPrice(itemCustomPrice: any) {}

  clickDelete(itemPrice: any) {}

  clickDeleteCustomPrice(itemCustomPrice: any) {}

  clickDeleteAll(item: any) {}

  clickDeleteAllCustomPrice(item: any) {}

  settingPriceCol(item: any) {}

  clickExportExcelPrice(item: any) {}

  async clickImportExcelPrice(ev: any, item: any) {}

  clickExportExcelCustomPrice(item: any) {}

  async clickImportExcelCustomPrice(ev: any, item: any) {}

  settingFomularCalValue(item: any) {}
  handleCancel() {}
  handleOk() {}
}
