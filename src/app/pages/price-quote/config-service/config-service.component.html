<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row nzGutter="24">
    <nz-col nzSpan="24">
      <nz-table nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
        [nzShowPagination]="false" nzBordered>
        <thead>
          <tr class="text-nowrap">
            <th>{{ 'Lĩnh vực mua hàng' }}</th>
            <th>{{ 'Số lượng' }}</th>
            <th>{{ 'Tác vụ' }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of basicTable.data">
            <td class="mw-25">{{ data.serviceName }}</td>
            <td class="mw-25">{{ data.value }}</td>
            <td class="text-nowrap">
              <button (click)="clickAddPrice(data)" nz-tooltip nzTooltipTitle="Thêm" class="mr-2" nz-button
                nzType="primary" nzGhost>
                <span nz-icon nzType="plus-circle"></span>
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-col>

    <!-- <nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label class="text-left" [nzSm]="24" [nzXs]="24" nzRequired>Người duyệt ghi chú</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <input nz-input placeholder="Nhập chào giá nhanh" [(ngModel)]="dataObj.note1" name="code" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label class="text-left" [nzSm]="24" [nzXs]="24" nzRequired>Phụ trách ghi chú</nz-form-label>
        <nz-form-control [nzSm]="24" [nzXs]="24">
          <input nz-input placeholder="Nhập chào giá nhanh" [(ngModel)]="dataObj.note2" name="code" />
        </nz-form-control>
      </nz-form-item>
    </nz-col> -->
  </nz-row>
</div>

<nz-row matDialogActions>
  <nz-col nzSpan="24" class="text-center">
    <button nz-button class="ant-btn-blue">
      {{ 'Lưu' }}
    </button>
  </nz-col>
</nz-row>