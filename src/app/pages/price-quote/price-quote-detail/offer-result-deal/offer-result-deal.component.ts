import { Component, OnInit, Input } from '@angular/core'
import { NzDrawerService } from 'ng-zorro-antd/drawer'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, CoreService, StorageService } from 'src/app/services'
import { OfferResultDealDetailComponent } from './offer-result-deal-detail/offer-result-deal-detail.component'

/** Kết quả đàm phán */
@Component({
  selector: 'app-offer-result-deal',
  templateUrl: './offer-result-deal.component.html',
})
export class OfferResultDealComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = false
  listOfData: any[] = []
  @Input()
  public bidId!: string
  language_key: any
  subscriptions: Subscription = new Subscription()

  constructor(
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private drawerService: NzDrawerService
  ) {}

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.searchData()
  }

  searchData(reset: boolean = false, clearFilter: boolean = false) {
    if (reset) this.pageIndex = 1

    this.loading = true
    const dataSearch: any = {
      where: { bidId: this.bidId },
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.loading = true
    this.apiService.post(this.apiService.OFFER.GET_LIST_OFFER_RESULT_DEAL, dataSearch).then((res) => {
      if (res) {
        this.loading = false
        this.listOfData = res[0]
        this.total = res[1]
      }
    })
  }

  showDetail(bidDealId: string) {
    this.drawerService.create({
      nzTitle: 'Chi tiết kết quả đàm phán',
      nzContent: OfferResultDealDetailComponent,
      nzContentParams: { bidDealId },
      nzWidth: '640',
    })
  }
}
