<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center main-title_detail">
    {{ 'Chi tiết chào giá' | titlecase }}
  </nz-col>
</nz-row>

<nz-tabset matDialogContent>
  <nz-tab nzTitle="Thông tin chào giá nhanh">
    <nz-row>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left title-detail">Mã chào giá nhanh</nz-form-label>
          <nz-form-control nzSpan="24" class="value_title_detail">
            <span>{{ dataObject.code }}</span>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left title-detail">Tên chào giá nhanh</nz-form-label>
          <nz-form-control nzSpan="24" class="value_title_detail">
            <span>{{dataObject.title }}</span>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left title-detail">Lĩnh vực mua hàng</nz-form-label>
          <nz-form-control nzSpan="24" class="value_title_detail">
            <span>{{ dataObject.service }}</span>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left title-detail">Thời gian đăng tải</nz-form-label>
          <nz-form-control nzSpan="24" class="value_title_detail">
            <span>{{ dataObject.effectiveDate |date: 'dd/MM/YYYY' }}</span>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="8">
        <nz-form-item nzFlex>
          <nz-form-label nzSpan="24" class="text-left title-detail">Thời gian kết thúc</nz-form-label>
          <nz-form-control nzSpan="24" class="value_title_detail">
            <span>{{ dataObject.endDate |date: 'dd/MM/YYYY' }}</span>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </nz-tab>
  <nz-tab *ngIf="dataObject.lstOfferService && dataObject.lstOfferService.length > 0" nzTitle="Bảng chào giá">
    <nz-col *ngFor="let data of dataObject.lstOfferService " nzSpan="24">
      <nz-collapse *ngIf="data.__offerPrice__.length > 0" class="mt-3">
        <nz-collapse-panel [nzActive]="true" [nzHeader]="data.serviceName" class="ant-bg-lightblue">
          <br />
          <nz-table *ngIf="data.lstData.length > 0" nz-col nzSpan="24" class="mb-3" #basicTable [nzData]="[{}]"
            [nzShowPagination]="false" nzBordered>
            <thead>
              <tr class="text-nowrap">
                <th>{{ 'NCC' }}</th>
                <ng-container *ngFor="let u of this.data.header; let i = index">
                  <th>{{ u.name }}</th>
                </ng-container>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let dataMap of data.lstData">
                <ng-container *ngIf="dataMap.lstPriceRegis.length > 0">
                  <td class="mw-25">{{ dataMap.name }}</td>
                  <ng-container *ngFor="let x of dataMap.lstPriceRegis; let i = index">
                    <td>{{ x.value}}</td>
                  </ng-container>
                </ng-container>
              </tr>
            </tbody>
          </nz-table>
        </nz-collapse-panel>
      </nz-collapse>
    </nz-col>

  </nz-tab>

  <!-- <nz-tab nzTitle="Kết quả đàm phán" *ngIf="dataObject.isShowDeal"> -->

</nz-tabset>