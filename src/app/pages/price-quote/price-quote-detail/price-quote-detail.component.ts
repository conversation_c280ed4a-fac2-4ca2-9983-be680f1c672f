import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from '../../../services'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'

@Component({
  selector: 'app-price-quote-detail',
  templateUrl: './price-quote-detail.component.html',
})
export class PriceQuoteDetailComponent implements OnInit {
  dataObject: any = {}
  bidId: any
  language_key: any
  listOfData: any
  subscriptions: Subscription = new Subscription()

  constructor(private notifyService: NotifyService, private apiService: ApiService, @Optional() @Inject(MAT_DIALOG_DATA) public data: any) {}

  ngOnInit() {
    this.listOfData = [
      { name: 'Công ty trách nhiệm Long Vũ', value1: '20.000.000 (VND)', value2: '30.000.000 (VND)' },
      { name: 'Công ty trách nhiệm Trần Tài', value1: '30.000.000 (VND)', value2: '50.000.000 (VND)' },
      { name: 'Công ty trách nhiệm Trần Phú', value1: '31.000.000 (VND)', value2: '55.000.000 (VND)' },
    ]
    this.loadDetail()
  }

  loadDetail() {
    if (this.data.id) {
      this.notifyService.showloading()
      this.apiService.post(this.apiService.OFFER.FIND_ONE, { id: this.data.id }).then((data) => {
        if (data) {
          this.notifyService.hideloading()
          this.dataObject = data
        }
      })
    }
  }
}
