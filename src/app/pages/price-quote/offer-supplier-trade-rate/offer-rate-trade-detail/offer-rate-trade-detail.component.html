<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row>
    <nz-col nzSpan="24">
      <button nz-button (click)="clickExportExcel()">
        <span nz-icon nzType="download"></span>Xuất excel
      </button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-table nz-col nzSpan="24" id="trade-table" #expandTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
      [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
      <thead>
        <!-- header gid level 1 -->
        <tr>
          <th>Tên tiêu chí</th>
          <th class="text-right">Tỉ trọng(%)</th>
          <th>Thông tin cung cấp</th>
          <th class="text-right"><PERSON><PERSON><PERSON><PERSON> đạ<PERSON> đ<PERSON> ({{ data.data.scoreTrade | number: '1.0-2' }}/100)</th>
          <th>Xếp hạng</th>
          <th class="text-right">Điểm cao nhất</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let dataRow of expandTable.data">
          <tr>
            <td class="mw-25">{{ dataRow.name }}</td>
            <td class="text-right">{{ dataRow.percent > 0 ? (dataRow.percent | number: '1.0-2') : '' }}</td>
            <td [ngClass]="{ 'text-right': dataRow.type === dataType.Number.code }">
              <span *ngIf="dataRow.type === dataType.String.code">{{ dataRow.value }}</span>
              <span *ngIf="dataRow.type === dataType.Number.code">{{ dataRow.value > 0 ? (dataRow.value | number:
                '1.0-2') : '' }}</span>
              <span *ngIf="dataRow.type === dataType.Date.code">{{ dataRow.value | date: 'dd/MM/yyyy HH:mm' }}</span>
              <span *ngIf="dataRow.type === dataType.List.code">{{ dataRow.value }}</span>
              <span *ngIf="dataRow.type === dataType.File.code && dataRow.value">
                <a href="{{ dataRow.value }}" target="_blank">
                  <span nz-icon nzType="file-text"></span> Xem file đính kèm
                </a>
              </span>
            </td>
            <td class="text-right">{{ dataRow.score > 0 ? (dataRow.score | number: '1.0-2') : '' }}</td>
            <td>{{ dataRow.rank}}</td>
            <td class="text-right">
              <div *ngIf="dataRow.bestScore && dataRow && dataRow.childs.length === 0">
                {{ dataRow.bestScore > 0 ? (dataRow.bestScore | number: '1.0-2') : '' }}
                <span (click)="showModalBidSupplierTradeDetail(dataRow)" nz-popover [nzPopoverContent]="contentTemplate"
                  nz-icon nzType="info-circle"></span>
                <ng-template #contentTemplate>
                  <p>NCC: {{ dataRow.bestSupplierName }}</p>
                  <p>Giá trị: {{ dataRow.bestValue | number: '1.0-2' }}</p>
                </ng-template>
              </div>
            </td>
          </tr>
          <ng-container>
            <tr *ngFor="let item of dataRow.childs">
              <td [nzIndentSize]="10" class="mw-25">{{ item.name }}</td>
              <td class="text-right">{{ item.percent > 0 ? (item.percent | number: '1.0-2') : '' }}</td>
              <td [ngClass]="{ 'text-right': item.type === dataType.Number.code }">
                <span *ngIf="item.type === dataType.String.code">{{ item.value }} </span>
                <span *ngIf="item.type === dataType.Number.code">
                  {{ item.value > 0 ? (item.value | number: '1.0-2') : '' }}</span>
                <span *ngIf="item.type === dataType.Date.code">{{ item.value | date: 'dd/MM/yyyy HH:mm' }} </span>
                <span *ngIf="item.type === dataType.List.code">{{ item.value }} </span>
                <span *ngIf="item.type === dataType.File.code && item.value">
                  <a href="{{ item.value }}" target="_blank">
                    <span nz-icon nzType="file-text"></span> Xem file đính kèm
                  </a>
                </span>
              </td>
              <td class="text-right">{{ item.score > 0 ? (item.score | number: '1.0-2') : '' }}</td>
              <td>{{ item.rank }}</td>
              <td class="text-right">
                <div *ngIf="item.bestScore">
                  {{ item.bestScore > 0 ? (item.bestScore | number: '1.0-2') : '' }}
                  <span (click)="showModalBidSupplierTradeDetail(item)" nz-popover [nzPopoverContent]="contentTemplate"
                    nz-icon nzType="info-circle"></span>
                  <ng-template #contentTemplate>
                    <p>NCC: {{ item.bestSupplierName }}</p>
                    <p>Giá trị: {{ item.bestValue | number: '1.0-2' }}</p>
                  </ng-template>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>

  <nz-row class="mt-3">
    <h4>Điểm HĐXT ĐKTM:</h4>
    <nz-col nzSpan="24" class="text-center">
      <input nz-input placeholder="Nhập điểm HĐXT ĐKTM (0-100)" [(ngModel)]="data.data.scoreManualTrade" type="number"
        min="0" max="100" [disabled]="!data.isNeedConfirm" />
      <div class="text-danger text-left" *ngIf="data.data.scoreManualTrade < 0 || data.data.scoreManualTrade > 100">Vui
        lòng chấm điểm từ 0 đến 100</div>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <h4>Ghi chú từ người đánh giá:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.data.noteTrade" [disabled]="!data.isNeedConfirm"></textarea>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <h4>Ghi chú từ người duyệt:</h4>
    <nz-col nzSpan="24" class="text-center">
      <textarea nz-input rows="2" auto [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.data.noteMPOLeader" [disabled]="!data.isNeedApprove"></textarea>
    </nz-col>
  </nz-row>
</div>

<nz-row matDialogActions *ngIf="data.isNeedConfirm">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button nzType="primary" class="mr-2" (click)="onValidBidSupplierTrade()">
      Xác nhận hợp lệ
    </button>
    <button nz-button nzDanger (click)="onInvalidBidSupplierTrade()">
      Không hợp lệ
    </button>
  </nz-col>
</nz-row>