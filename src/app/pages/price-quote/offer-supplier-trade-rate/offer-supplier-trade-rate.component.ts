import { Component, OnInit, Optional, Inject } from '@angular/core'
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/core'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { OfferRateTradeDetailComponent } from './offer-rate-trade-detail/offer-rate-trade-detail.component'

@Component({
  selector: 'app-offer-supplier-trade-rate',
  templateUrl: './offer-supplier-trade-rate.component.html',
})
export class OfferSupplierTradeRateComponent implements OnInit {
  modalTitle = 'Thông tin đánh giá điều kiện thương mại các doanh nghiệp dự thầu'
  pageSize = enumData.Page.pageSizeMax
  dataObject: any = {}
  loading = false
  isNeedConfirm = false
  isNeedApprove = false
  language_key: any
  subscriptions: Subscription = new Subscription()
  enumProject: any
  enumRole: any
  currentUser: any
  action: any
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<OfferSupplierTradeRateComponent>,
    private dialog: MatDialog,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
  }

  async ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) this.language_key = this.coreService.getLanguage()
    })
    this.language_key = this.coreService.getLanguage()

    this.isNeedConfirm =
      (true &&
        (this.data.statusRateTrade === enumData.BidTradeRateStatus.DangTao.code ||
          this.data.statusRateTrade === enumData.BidTradeRateStatus.TuChoi.code)) ||
      this.data.statusRateTrade === null
    this.isNeedApprove =
      this.data.statusRateTrade === enumData.BidTradeRateStatus.DaTao.code && this.data.statusRatePrice === enumData.BidPriceRateStatus.DaTao.code
    this.loadData()
  }

  /** Hàm lấy ds ncc tham gia thầu và tính điểm */
  loadData() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.OFFER_RATE.LOAD_TRADE_RATE(this.data.id), {}).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }

  closeDialog(flag: boolean) {
    this.dialogRef.close(flag)
  }

  viewDetail(item: any, data: any) {
    const object = {
      data,
      template: item.lstBidTrade,
      isNeedConfirm: this.isNeedConfirm,
      isNeedApprove: this.isNeedApprove,
    }
    this.dialog.open(OfferRateTradeDetailComponent, { disableClose: false, data: object })
  }

  createTradeRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER_RATE.CREATE_TRADE_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }

  approveTradeRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER_RATE.APPROVE_TRADE_RATE, this.dataObject).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }

  rejectTradeRate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER_RATE.REJECT_TRADE_RATE, this.dataObject).then(() => {
      this.notifyService.hideloading()
      this.closeDialog(true)
    })
  }
}
