import { Component, Inject, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { NzUploadFile } from 'ng-zorro-antd/upload'
import { Observable, Observer } from 'rxjs'
import { enumData } from 'src/app/core'
import { User } from 'src/app/models'
import { NotifyService, AuthenticationService, CoreService, ApiService } from 'src/app/services'

@Component({
  templateUrl: './create-item-quote.component.html',
})
export class CreateItemQuoteComponent {
  currentUser!: User
  modalTitle = 'Thêm mới Item'
  dateFormat = 'dd/MM/yyyy'
  dataObject: any = {}
  lstCategory: any = this.coreService.convertObjToArray(enumData.PRCategoryItem)
  lstExternalMatGroup: any = []
  lstMaterial: any = []
  lstGLAccount: any = []
  lstCostCenter: any = []
  lstOrder: any = []
  lstAsset: any = []
  lstUnit: any = []
  lstMatGroup: any = []
  lstPurchasingGroup: any = []
  lstValuationType: any = this.coreService.convertObjToArray(enumData.ValuationType)
  previewImage: string | undefined = ''
  previewVisible = false
  uploadUrl: string = this.apiService.UploadUrl
  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<CreateItemQuoteComponent>,
    private authenticationService: AuthenticationService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public coreService: CoreService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
  }

  ngOnInit() {
    this.loadAllDataSelect()
    if (this.data.isEdit || this.data?.id) {
      this.dataObject = this.data
      if (this.data.category === enumData.PRCategoryItem.ONE.code) {
        this.notifyService.showloading()
        Promise.all([
          // this.apiService.post(this.apiService.GL_ACCOUNT.FIND, {}),
          // this.apiService.post(this.apiService.COST_CENTER.FIND, {}),
          // this.apiService.post(this.apiService.ORDER.FIND, {}),
        ]).then(async (res) => {
          this.lstGLAccount = []
          this.lstCostCenter = []
          this.lstOrder = []
          this.notifyService.hideloading()
        })
      } else if (this.data.category === enumData.PRCategoryItem.TWO.code) {
        this.notifyService.showloading()
        this.apiService.post(this.apiService.ASSET.FIND, {}).then((result) => {
          this.lstAsset = result
          this.notifyService.hideloading()
        })
      }
    } else {
      this.dataObject.itemNo = this.data.number * 10
      this.dataObject.fileList = []
    }

    if (this.data.exMatGroupId) {
      this.notifyService.showloading()
      this.apiService.post(this.apiService.MATERIAL.FIND, { externalMaterialGroupId: this.data.exMatGroupId }).then((result) => {
        this.lstMaterial = result
        if (this.data.isEdit) {
          const item = result.find((x: any) => x.id === this.data.materialId)
          if (item) {
            this.dataObject.materialCode = item.code
            this.dataObject.shortText = item.name
            this.dataObject.unitCode = item.unitCode
            // dataObject.materialId = item.id
            this.dataObject.unitId = item.unitId
            this.dataObject.plantCode = item.plantCode
            this.dataObject.plantName = item.plantName
            this.dataObject.materialGroupCode = item.matGroup
            this.dataObject.materialGroupId = item.materialGroupId
            this.dataObject.valuationType = item.valuationType
            let date = this.data.createdDate || new Date()
            this.dataObject.purchasingGroupCode = item.purchasingGroupCode
            this.dataObject.purchasingGroupId = item.purchasingGroupId
            this.dataObject.deliveryDate = date
          }
        }
        this.notifyService.hideloading()
      })
    }
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      // this.apiService.post(this.apiService.UNIT.DATA_SELECT_BOX, {}),
      // this.apiService.post(this.apiService.MAT_GROUP.FIND_ALL, {}),
      // this.apiService.post(this.apiService.PURCHASING_GROUP.FIND, {}),
    ]).then(async (res) => {
      // this.lstUnit = res[0]
      // this.lstMatGroup = res[1]
      // this.lstPurchasingGroup = res[2]
    })
    this.notifyService.hideloading()
  }

  onChangeCategory(event: any) {
    this.lstGLAccount = this.lstCostCenter = this.lstOrder = this.lstAsset = []
    this.dataObject.glAccountId = this.dataObject.orderId = this.dataObject.costCenterId = null
    if (event === enumData.PRCategoryItem.ONE.code) {
      this.notifyService.showloading()
      Promise.all([
        // this.apiService.post(this.apiService.GL_ACCOUNT.FIND, {}),
        // this.apiService.post(this.apiService.COST_CENTER.FIND, {}),
        // this.apiService.post(this.apiService.ORDER.FIND, {}),
      ]).then(async (res) => {
        // this.lstGLAccount = res[0]
        // this.lstCostCenter = res[1]
        // this.lstOrder = res[2]
        this.notifyService.hideloading()
      })
    } else if (event === enumData.PRCategoryItem.TWO.code) {
      this.notifyService.showloading()
      this.apiService.post(this.apiService.ASSET.FIND, {}).then((result) => {
        this.lstAsset = result
        this.notifyService.hideloading()
      })
    }
  }

  onChangeExternalMatGroup(event: any) {
    this.lstMaterial = []
    this.dataObject.materialId = null
    if (event) {
      this.notifyService.showloading()
      this.apiService.post(this.apiService.MATERIAL.FIND, { externalMaterialGroupId: event }).then((result) => {
        this.lstMaterial = result
        this.notifyService.hideloading()
      })
    }
  }

  closeDialog(flag: any) {
    if (this.data.lstItem && this.data.lstItem.length > 0) {
      const exist = this.data.lstItem.find((s: any) => s.materialId === this.dataObject.materialId)
      if (exist) {
        this.notifyService.showError('Material này đã tồn tại. Vui lòng kiểm tra lại !')
        return
      }
    }

    this.dialogRef.close(flag)
  }

  onSave() {
    if (this.dataObject.ounId) {
      const oun = this.lstUnit.find((x: any) => x.id === this.dataObject.ounId)
      if (oun) {
        this.dataObject.ounCode = oun.code
        this.dataObject.ounName = oun.name
      }
    }
    if (this.dataObject.glAccountId) {
      const glAccount = this.lstGLAccount.find((x: any) => x.id === this.dataObject.glAccountId)
      if (glAccount) {
        this.dataObject.glAccountCode = glAccount.code
        this.dataObject.glAccountname = glAccount.name
      }
    }
    if (this.dataObject.costCenterId) {
      const costCenter = this.lstCostCenter.find((x: any) => x.id === this.dataObject.costCenterId)
      if (costCenter) {
        this.dataObject.costCenterCode = costCenter.code
        this.dataObject.costCenterName = costCenter.name
      }
    }
    if (this.dataObject.category) {
      const category = this.lstCategory.find((x: any) => x.code === this.dataObject.category)
      this.dataObject.categoryName = category?.name
    }
    if (this.dataObject.assetId) {
      const asset = this.lstAsset.find((x: any) => x.id === this.dataObject.assetId)
      if (asset) {
        this.dataObject.assetCode = asset.code
        this.dataObject.assetName = asset.name
      }
    }
    if (this.dataObject.materialGroupId) {
      const materialGroup = this.lstMatGroup.find((x: any) => x.id === this.dataObject.materialGroupId)
      if (materialGroup) {
        this.dataObject.materialGroupCode = materialGroup.code
        this.dataObject.materialGroupName = materialGroup.name
      }
    }
    if (this.dataObject.purchasingGroupId) {
      const purchasingGroup = this.lstPurchasingGroup.find((x: any) => x.id === this.dataObject.purchasingGroupId)
      if (purchasingGroup) {
        this.dataObject.purchasingGroupCode = purchasingGroup.code
        this.dataObject.purchasingGroupName = purchasingGroup.name
      }
    }
    if (this.dataObject.unitId) {
      const unit = this.lstUnit.find((x: any) => x.id === this.dataObject.unitId)
      if (unit) {
        this.dataObject.unitCode = unit.code
        this.dataObject.unitName = unit.name
      }
    }

    this.dialogRef.close(this.dataObject)
  }

  onChangeMaterial(event: any) {
    this.dataObject.shortText = this.dataObject.plantCode = this.dataObject.unitId = this.dataObject.unitCode = null
    if (event) {
      const item = this.lstMaterial.find((x: any) => x.id === event)
      if (item) {
        this.dataObject.materialCode = item.code
        this.dataObject.shortText = item.name
        this.dataObject.unitCode = item.unitCode
        this.dataObject.unitId = item.unitId
        this.dataObject.plantCode = item.plantCode
        this.dataObject.plantName = item.plantName
        this.dataObject.materialGroupCode = item.matGroup
        this.dataObject.materialGroupId = item.materialGroupId
        this.dataObject.valuationType = item.valuationType
        let date = this.data.createdDate || new Date()
        this.dataObject.purchasingGroupCode = item.purchasingGroupCode
        this.dataObject.purchasingGroupId = item.purchasingGroupId
        this.dataObject.deliveryDate = date
      }
    }
  }

  handlePreview = async (file: any) => {
    const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
      new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
      })

    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj!)
    }
    this.previewImage = file.url || file.preview
    this.previewVisible = true
  }

  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      if (file.size! > enumData.maxSizeUpload * 1024 * 1024) {
        this.notifyService.showWarning('Tệp có kích thước quá lớn, vui lòng chọn tệp < 10MB.')
        observer.complete()
        return
      }
      observer.next(true)
      observer.complete()
    })

  handleChange(info: any): void {
    if (info.file.status !== 'uploading') {
    }
    if (info.file.status === 'done') {
      this.dataObject.fileList = info.fileList.map((file: any) => ({
        uid: file.uid,
        name: file.name ?? file.fileName,
        fileUrl: file.response ? file.response[0] : file.response.fileUrl,
        fileName: file.name ?? file.fileName,
        dataType: file.type ?? file.dataType,
      }))
    } else if (info.file.status === 'removed') {
      // Handle file removal
      this.dataObject.fileList = info.fileList.map((file: any) => ({
        uid: file.uid,
        name: file.name ?? file.fileName,
        fileUrl: file.response ? file.response[0] : file.response.fileUrl,
        fileName: file.name ?? file.fileName,
        dataType: file.type ?? file.dataType,
      }))
    } else if (info.file.status === 'error') {
      this.notifyService.showError(`${info.file.name} file upload failed.`)
    }
  }
}
