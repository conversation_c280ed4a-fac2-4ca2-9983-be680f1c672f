<form nz-form class="ant-advanced-search-form" #frmAdd="ngForm">
    <nz-row matDialogTitle>
        <nz-col nzSpan="24" class="text-center">
            {{ modalTitle | uppercase }}
        </nz-col>
    </nz-row>

    <div matDialogContent>
        <nz-row nzGutter="8">
            <nz-col nzSpan="6">
                <nz-form-item>
                    <nz-form-label nzSpan="24" class="text-left" nzRequired>Item line</nz-form-label>
                    <nz-form-control nzSpan="24">
                        <input nz-input placeholder="Item line" [disabled]="true" [(ngModel)]="dataObject.itemNo"
                            name="itemNo" required />
                    </nz-form-control>
                </nz-form-item>
            </nz-col>
            <nz-col nzSpan="6">
                <nz-form-item>
                    <nz-form-label nzSpan="24" class="text-left" nzRequired>Category</nz-form-label>
                    <nz-form-control nzSpan="24">
                        <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.category" name="category"
                            nzPlaceHolder="Chọn Category" required (ngModelChange)="onChangeCategory($event)">
                            <nz-option *ngFor="let option of lstCategory" [nzValue]="option.code"
                                [nzLabel]="option.name"></nz-option>
                        </nz-select>
                    </nz-form-control>
                </nz-form-item>
            </nz-col>

            <ng-container *ngIf="dataObject.category !== '3'">
                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Material</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.materialId" name="materialId"
                                nzPlaceHolder="Chọn Material" required (ngModelChange)="onChangeMaterial($event)">
                                <nz-option *ngFor="let item of lstMaterial" [nzValue]="item.id"
                                    [nzLabel]="item.code"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Short text</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.materialId" name="materialId"
                                nzPlaceHolder="Short text" required (ngModelChange)="onChangeMaterial($event)">
                                <nz-option *ngFor="let item of lstMaterial" [nzValue]="item.id"
                                    [nzLabel]="item.name"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Material Group</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.materialGroupId"
                                name="materialGroupId" nzPlaceHolder="Chọn Material Group" required>
                                <nz-option *ngFor="let item of lstMatGroup" [nzValue]="item.id"
                                    [nzLabel]="item.name"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Unit</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.unitId" name="unitId"
                                nzPlaceHolder="Chọn Unit" required>
                                <nz-option *ngFor="let item of lstUnit" [nzValue]="item.id"
                                    [nzLabel]="item.name"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Quatity</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <input nz-input placeholder="Quantity" [(ngModel)]="dataObject.quantity" name="quantity"
                                currencyMask
                                [options]="{ prefix: '', thousands: '.', precision: '' , allowNegative: false }"
                                required />
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left">Oun</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.ounId" name="ounId"
                                nzPlaceHolder="Chọn Oun">
                                <nz-option *ngFor="let item of lstUnit" [nzValue]="item.id"
                                    [nzLabel]="item.name"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Delivery Date</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-date-picker [nzFormat]="dateFormat" [(ngModel)]="dataObject.deliveryDate"
                                name="deliveryDate" [nzPlaceHolder]="'Delivery Date'" required>
                            </nz-date-picker>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>



            </ng-container>

            <ng-container *ngIf="dataObject.category === '3'">
                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Material</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.materialId" name="materialId"
                                nzPlaceHolder="Chọn Material" required (ngModelChange)="onChangeMaterial($event)">
                                <nz-option *ngFor="let item of lstMaterial" [nzValue]="item.id"
                                    [nzLabel]="item.code"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Short text</nz-form-label>
                        <nz-form-control nzSpan="24">

                            <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataObject.materialId" name="materialId"
                                nzPlaceHolder="Short text" required (ngModelChange)="onChangeMaterial($event)">
                                <nz-option *ngFor="let item of lstMaterial" [nzValue]="item.id"
                                    [nzLabel]="item.name"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

                <nz-col nzSpan="6">
                    <nz-form-item>
                        <nz-form-label nzSpan="24" class="text-left" nzRequired>Quatity</nz-form-label>
                        <nz-form-control nzSpan="24">
                            <input nz-input placeholder="Quantity" [(ngModel)]="dataObject.quantity" name="quantity"
                                currencyMask
                                [options]="{ prefix: '', thousands: '.', precision: '' , allowNegative: false }"
                                required />
                        </nz-form-control>
                    </nz-form-item>
                </nz-col>

            </ng-container>


        </nz-row>
    </div>

    <nz-row matDialogActions>
        <nz-col nzSpan="24" class="text-center">
            <button class="mr-2" nz-button nzType="primary" (click)="closeDialog(true)">
                <span nz-icon nzType="close"></span> Đóng
            </button>

            <button class="mr-2 btn btn-primary" nz-button [disabled]="!frmAdd.form.valid" nzType="primary"
                (click)="onSave()">
                <span nz-icon nzType="save"></span> Lưu
            </button>

        </nz-col>
    </nz-row>
</form>