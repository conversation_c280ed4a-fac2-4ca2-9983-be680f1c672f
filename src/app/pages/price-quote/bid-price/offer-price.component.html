<nz-row matDialogTitle>

  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>


<div>
  <div nz-row>
    <div nz-col class="gutter-row" [nzSpan]="24" class="mr-3">
      <nz-form-label>Lĩnh vực mua hàng</nz-form-label>
      <nz-select
        [disabled]="true"
        nzShowSearch
        nzAllowClear
        [nzPlaceHolder]="'Chọn lĩnh vực mua hàng'"
        [(ngModel)]="data.count"
        name="count"
      >
        <nz-option *ngFor="let item of data.listItem" [nzLabel]="item.itemName" [nzValue]="item.count"> </nz-option>
      </nz-select>
    </div>

    <div nz-col class="gutter-row" [nzSpan]="4">
      <nz-form-label [nzSm]="8" [nzXs]="24"><PERSON><PERSON><PERSON> vị tiền tệ</nz-form-label>
      <nz-select nzShowSearch nzAllowClear [(ngModel)]="currency" name="currency" nzPlaceHolder="Chọn đơn vị tiền tệ">
        <nz-option *ngFor="let item of lstCurrency" [nzLabel]="'(' + item.code + ') ' + item.name" [nzValue]="item.code"></nz-option>
      </nz-select>
    </div>
  </div>

  <div *ngFor="let item of data.listItem; let i = index" class="mt-3">
    <div *ngIf="i === data.count">
      <nz-collapse class="mt-3">
        <nz-collapse-panel nzHeader="Thông tin các hạng mục chào giá" class="ant-bg-lightblue" [(nzActive)]="dicActiveCollapsePrice[item.id]">
          <nz-row *ngIf="data.currency && !data.isLoadFromItem" class="mt-3">
            <nz-col nzSpan="24">
              <button class="mr-2" nz-button (click)="clickAdd(item)" nzType="primary">
                <span nz-icon nzType="plus"></span>
                {{ language_key?.ADD || 'Thêm mới' }}
              </button>
              <button nz-button (click)="settingPriceCol(item)" nzType="primary" nzGhost class="mr-2">
                <span nz-icon nzType="setting"></span>
                Cấu hình cột
              </button>
              <button nz-button (click)="settingFomularCalValue(item)" nzType="primary" nzGhost class="mr-2">
                <span nz-icon nzType="calculator"></span>
                Cấu hình công thức tính đơn giá
              </button>
              <button nz-button (click)="settingPriceCalWay(item)" nzType="primary" nzGhost class="mr-2">
                <span nz-icon nzType="calculator"></span>
                Cấu hình cách tính điểm giá
              </button>

              <button class="button-save mr-2" nz-button (click)="loadPrice(item)">Tải các hạng mục từ cấu hình</button>
              <button class="button-save mr-2" *ngIf="data.isLoadFromPr" nz-button (click)="loadPriceService(item)">Tải các hạng mục từ PR</button>

              <button class="button-save mr-2" *ngIf="data.isLoadFromBusinessPlan" nz-button (click)="loadPriceService(item)">
                Tải các hạng mục từ PAKD
              </button>

              <button class="mr-2" nz-button (click)="clickExportExcelPrice(item)"><span nz-icon nzType="download"></span>Xuất excel</button>
              <input
                class="hidden"
                type="file"
                id="file"
                (change)="clickImportExcelPrice($event, item)"
                onclick="this.value=null"
                accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              />
              <label for="file" class="label-custom-file">
                <span nz-icon nzType="upload"></span>
                {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
              </label>
            </nz-col>
          </nz-row>

          <nz-row class="mt-3">
            <nz-table
              nz-col
              nzSpan="24"
              [nzData]="item.listPrice"
              [(nzPageSize)]="pageSize"
              [nzLoading]="loading"
              [nzShowPagination]="false"
              nzBordered
            >
              <thead>
                <tr>
                  <th style="width: 120px">
                    {{ language_key?.NO || 'STT' }}
                  </th>
                  <th>Tên hạng mục</th>
                  <th *ngFor="let col of item.listPriceCol" class="dynamic-col-mpo">
                    {{ col.name }}
                  </th>
                  <th nz-tooltip nzTooltipTitle="Mã cột để làm công thức: [qty]">
                    {{ language_key?.QUANTITY || 'Số lượng' }}
                  </th>
                  <th>Bắt buộc?</th>
                  <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
                </tr>
              </thead>
              <tbody>
                <!-- level 1 -->
                <ng-container *ngFor="let data1 of item.listPrice">
                  <tr *ngIf="data1.currency === currency">
                    <td (click)="clickEdit(data1)">
                      {{ data1.sort > 0 ? data1.sort : '' }}
                    </td>
                    <td (click)="clickEdit(data1)" class="mw-25">{{ data1.name }}</td>
                    <td *ngFor="let col of item.listPriceCol">{{ data1[col.id] }}</td>
                    <td (click)="clickEdit(data1)" class="text-right">{{ data1.number | number }}</td>
                    <td (click)="clickEdit(data1)">
                      {{ data1.isRequired ? 'Bắt buộc' : 'Không' }}
                    </td>
                    <td>
                      <button
                        nz-popconfirm
                        nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục giá này?"
                        nzPopconfirmPlacement="bottom"
                        (nzOnConfirm)="clickDelete(data1)"
                        nz-tooltip
                        nzTooltipTitle="Xoá hạng mục"
                        class="mr-2"
                        nz-button
                        nzDanger
                      >
                        <span nz-icsettingExInfoon nzType="delete"></span>
                      </button>
                      <button
                        nz-tooltip
                        nzTooltipTitle="Thiết lập các thông tin mở rộng"
                        nz-button
                        [nzType]="data1.__bidPriceListDetails__?.length > 0 ? 'default' : 'dashed'"
                        (click)="settingExInfo(data1)"
                      >
                        <span nz-icon nzType="plus-circle"></span>
                      </button>
                    </td>
                  </tr>
                  <!-- level 2 -->
                  <ng-container *ngFor="let data2 of data1.__childs__">
                    <tr *ngIf="data1.currency === currency">
                      <td (click)="clickEdit(data2)" [nzIndentSize]="5">{{ data2.sort > 0 ? data2.sort : '' }}</td>
                      <td class="mw-25" (click)="clickEdit(data2)">
                        {{ data2.name }}
                      </td>
                      <td *ngFor="let col of item.listPriceCol">{{ data2[col.id] }}</td>
                      <td (click)="clickEdit(data2)">
                        {{ data2.unit }}
                      </td>
                      <td (click)="clickEdit(data2)">
                        {{ data2.currency }}
                      </td>
                      <td class="text-right" (click)="clickEdit(data2)">
                        {{ data2.number | number }}
                      </td>
                      <td (click)="clickEdit(data2)">
                        {{ data2.isRequired ? 'Bắt buộc' : 'Không' }}
                      </td>
                      <td>
                        <button
                          nz-popconfirm
                          nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục giá này?"
                          nzPopconfirmPlacement="bottom"
                          (nzOnConfirm)="clickDelete(data2)"
                          nz-tooltip
                          nzTooltipTitle="Xoá hạng mục"
                          class="mr-2"
                          nz-button
                          nzDanger
                        >
                          <span nz-icon nzType="delete"></span>
                        </button>
                        <button
                          nz-tooltip
                          nzTooltipTitle="Thiết lập các thông tin mở rộng"
                          nz-button
                          [nzType]="data2.__bidPriceListDetails__?.length > 0 ? 'default' : 'dashed'"
                          (click)="settingExInfo(data2)"
                        >
                          <span nz-icon nzType="plus-circle"></span>
                        </button>
                      </td>
                    </tr>
                    <!-- level 3 -->
                    <ng-container *ngFor="let data3 of data2.__childs__">
                      <tr *ngIf="data1.currency === currency">
                        <td (click)="clickEdit(data3)" [nzIndentSize]="30">{{ data3.sort > 0 ? data3.sort : '' }}</td>
                        <td class="mw-25" (click)="clickEdit(data3)">
                          {{ data3.name }}
                        </td>
                        <td *ngFor="let col of item.listPriceCol">{{ data3[col.id] }}</td>
                        <td (click)="clickEdit(data3)">
                          {{ data3.unit }}
                        </td>
                        <td (click)="clickEdit(data3)">
                          {{ data3.currency }}
                        </td>
                        <td class="text-right" (click)="clickEdit(data3)">
                          {{ data3.number | number }}
                        </td>
                        <td (click)="clickEdit(data3)">
                          {{ data3.isRequired ? 'Bắt buộc' : 'Không' }}
                        </td>
                        <td>
                          <button
                            nz-popconfirm
                            nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục giá này?"
                            nzPopconfirmPlacement="bottom"
                            (nzOnConfirm)="clickDelete(data3)"
                            nz-tooltip
                            nzTooltipTitle="Xoá hạng mục"
                            class="mr-2"
                            nz-button
                            nzDanger
                          >
                            <span nz-icon nzType="delete"></span>
                          </button>
                          <button
                            nz-tooltip
                            nzTooltipTitle="Thiết lập các thông tin mở rộng"
                            nz-button
                            [nzType]="data3.__bidPriceListDetails__?.length > 0 ? 'default' : 'dashed'"
                            (click)="settingExInfo(data3)"
                          >
                            <span nz-icon nzType="plus-circle"></span>
                          </button>
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>

      <nz-collapse class="mt-3">
        <nz-collapse-panel nzHeader="Thông tin các hạng mục cơ cấu giá" class="ant-bg-lightblue" [(nzActive)]="dicActiveCollapseCustomPrice[item.id]">
          <nz-row class="mt-3">
            <nz-col nzSpan="24">
              <button class="mr-2" nz-button (click)="clickAddCustomPrice(item)" nzType="primary">
                <span nz-icon nzType="plus"></span>
                Thêm hạng mục cơ cấu giá
              </button>

              <button class="mr-2" nz-button (click)="clickExportExcelCustomPrice(item)"><span nz-icon nzType="download"></span>Xuất excel</button>
              <input
                class="hidden"
                type="file"
                id="fileCustom"
                (change)="clickImportExcelCustomPrice($event, item)"
                onclick="this.value=null"
                accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              />
              <label for="fileCustom" class="label-custom-file">
                <span nz-icon nzType="upload"></span>
                {{ language_key?.IMPORT_EXCEL || 'Nhập excel' }}
              </label>
            </nz-col>
          </nz-row>

          <nz-row class="mt-3">
            <nz-table
              nz-col
              nzSpan="24"
              [nzData]="item.listCustomPrice"
              [(nzPageSize)]="pageSize"
              [nzLoading]="loading2"
              [nzShowPagination]="false"
              nzBordered
            >
              <thead>
                <tr>
                  <th>{{ language_key?.NO || 'STT' }}</th>
                  <th>Tên hạng mục</th>
                  <th>Đơn vị tính</th>
                  <th>Đơn vị tiền tệ</th>
                  <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                  <th>Bắt buộc?</th>
                  <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let dataRow of item.listCustomPrice">
                  <tr *ngIf="dataRow.currency === currency">
                    <td (click)="authenticationService.checkPermission([enumRole], action.Update.code) ? clickEditCustomPrice(dataRow) : ''">
                      {{ dataRow.sort > 0 ? dataRow.sort : '' }}
                    </td>
                    <td
                      (click)="authenticationService.checkPermission([enumRole], action.Update.code) ? clickEditCustomPrice(dataRow) : ''"
                      class="mw-25"
                    >
                      {{ dataRow.name }}
                    </td>
                    <td (click)="authenticationService.checkPermission([enumRole], action.Update.code) ? clickEditCustomPrice(dataRow) : ''">
                      {{ dataRow.unit }}
                    </td>
                    <td (click)="authenticationService.checkPermission([enumRole], action.Update.code) ? clickEditCustomPrice(dataRow) : ''">
                      {{ dataRow.currency }}
                    </td>
                    <td
                      (click)="authenticationService.checkPermission([enumRole], action.Update.code) ? clickEditCustomPrice(dataRow) : ''"
                      class="text-right"
                    >
                      {{ dataRow.number | number }}
                    </td>
                    <td (click)="authenticationService.checkPermission([enumRole], action.Update.code) ? clickEditCustomPrice(dataRow) : ''">
                      {{ dataRow.isRequired ? 'Bắt buộc' : 'Không' }}
                    </td>
                    <td>
                      <button
                        *ngIf="true"
                        nz-popconfirm
                        nzPopconfirmTitle="Bạn có chắc muốn xoá hạng mục cơ cấu giá này?"
                        (nzOnConfirm)="clickDeleteCustomPrice(dataRow)"
                        nz-tooltip
                        nzTooltipTitle="Xoá hạng mục"
                        nz-button
                        nzDanger
                      >
                        <span nz-icon nzType="delete"></span>
                      </button>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>
    </div>
  </div>

  <nz-row class="mt-3">
    <nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-control nzSpan="24">
          <label nz-checkbox id="isCompleteAll"> Yêu cầu NCC báo giá đủ </label>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="24" class="text-left">
      <h4>Người yêu cầu ghi chú:</h4>
      <textarea
        nz-input
        rows="2"
        auto
        [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.notePrice"
        [disabled]="!data.isShowCreatePrice"
      ></textarea>
    </nz-col>
  </nz-row>
  <nz-row class="mt-3">
    <nz-col nzSpan="24" class="text-left">
      <h4>Người duyệt ghi chú:</h4>

      <textarea
        nz-input
        rows="2"
        auto
        [placeholder]="language_key?.NOTE || 'Nhập ghi chú'"
        [(ngModel)]="data.noteMPOLeader"
        [disabled]="true"
      ></textarea>
    </nz-col>
  </nz-row>
</div>

<nz-row matDialogActions>
  <!-- <nz-row matDialogActions *ngIf="objPermission.isEdit"> -->

  <nz-col nzSpan="24" class="text-center">
    <!-- yêu cầu Gởi duyệt thông tin HMCG, CCG -->
    <!-- <button nz-button (click)="createPrice()" class="button-save"
      *ngIf="data.isShowCreatePrice "> -->
    <button nz-button (click)="createPrice()" class="button-save" *ngIf="!(data.statusPrice === priceStatus.DaTao.code)">
      <span nz-icon nzType="save" nzTheme="outline"></span>
      {{ language_key?.SAVE || 'Lưu' }}
    </button>
    <!--Duyệt yêu cầu thông tin HMCG, CCG-->
    <button nz-button (click)="acceptPrice()" class="button-save" *ngIf="data.statusPrice === priceStatus.GuiDuyet.code && data.canApprove">
      <span nz-icon nzType="check" nzTheme="outline"></span>
      {{ language_key?.SAVE || 'Duyệt' }}
    </button>
    <button nz-button class="mr-2" (click)="closeDialog(false)" style="border-color: #e41717; color: #e41717; border-radius: 8px !important">
      <span nz-icon nzType="close" nzTheme="outline"></span>
      Đóng
    </button>
  </nz-col>
</nz-row>

<!-- Thiết lập công thức tính đơn giá -->
<nz-modal
  [(nzVisible)]="isVisible"
  nzTitle="Thiết lập công thức tính đơn giá"
  (nzOnCancel)="handleCancel()"
  [nzFooter]="null"
  *ngIf="itemChoose && isVisible"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Công thức:</h4>
      <nz-col nzSpan="24">
        <input nz-input placeholder="Nhập công thức tính đơn giá" [(ngModel)]="itemChoose.fomular" />
      </nz-col>
    </nz-row>
    <nz-row class="mt-5">
      <nz-col nzSpan="24" class="text-center">
        <button
          nz-popconfirm
          nzPopconfirmTitle="Xác nhận lưu công thức?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="handleOk()"
          nz-button
          class="button-save"
        >
          <span nz-icon nzType="save" nzTheme="outline"></span>
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>

<!-- Thiết lập cách tính điểm giá -->
<nz-modal
  [(nzVisible)]="isVisibleSettingWayCalScorePrice"
  nzTitle="Thiết lập cách tính điểm giá"
  (nzOnCancel)="cancelSettingPriceCalWay()"
  [nzFooter]="null"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <h4>Cách tính điểm giá:</h4>
      <nz-col nzSpan="24">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSettingWayCalScorePrice.wayCalScorePrice"
          name="wayCalScorePrice"
          [nzPlaceHolder]="'Chọn cách tính điểm giá'"
        >
          <nz-option *ngFor="let item of dataPriceScoreCalculateWay" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <nz-row class="mt-5">
      <nz-col nzSpan="24" class="text-center">
        <button
          nz-popconfirm
          nzPopconfirmTitle="Xác nhận lưu cách tính điểm giá?"
          nzPopconfirmPlacement="bottom"
          (nzOnConfirm)="saveSettingPriceCalWay()"
          nz-button
          class="button-save"
        >
          <span nz-icon nzType="save" nzTheme="outline"></span>
          Đồng ý
        </button>
      </nz-col>
    </nz-row>
  </ng-container>
</nz-modal>
