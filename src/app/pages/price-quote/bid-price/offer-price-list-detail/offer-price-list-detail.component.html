<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<div matDialogContent>
  <nz-row>
    <nz-col nzSpan="24">
      <button nz-button (click)="searchData()" class="mr-2">L<PERSON>m mới</button>
      <button nz-button (click)="addRow()" nzType="primary">Thêm</button>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <nz-table
      nz-col
      nzSpan="24"
      class="mb-3"
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      nzBordered
    >
      <thead>
        <tr>
          <th>Tên trường thông tin</th>
          <th>Ki<PERSON>u dữ liệu</th>
          <th><PERSON><PERSON><PERSON> trị</th>
          <th>{{ language_key?.OPTION || '<PERSON><PERSON> chọn' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataRow of listOfData">
          <td class="mw-25">
            <ng-container *ngIf="!editCache[dataRow.id].edit; else nameInputTpl">
              {{ dataRow.name }}
            </ng-container>
            <ng-template #nameInputTpl>
              <input type="text" nz-input [(ngModel)]="editCache[dataRow.id].data.name" placeholder="Nhập 1-250 kí tự" />
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[dataRow.id].edit; else typeInputTpl">
              {{ dataRow.type }}
            </ng-container>
            <ng-template #typeInputTpl>
              <nz-select
                nzShowSearch
                nzAllowClear
                [(ngModel)]="editCache[dataRow.id].data.type"
                name="type"
                required
                nzPlaceHolder="Chọn kiểu dữ liệu"
              >
                <nz-option *ngFor="let item of lstDataType" [nzLabel]="item.code" [nzValue]="item.code"></nz-option>
              </nz-select>
            </ng-template>
          </td>
          <td>
            <ng-container *ngIf="!editCache[dataRow.id].edit; else valueInputTpl">
              <p *ngIf="editCache[dataRow.id].data.type === dataType.String.code || editCache[dataRow.id].data.type === dataType.Address.code">
                {{ dataRow.value }}
              </p>
              <p *ngIf="editCache[dataRow.id].data.type === dataType.Km.code || editCache[dataRow.id].data.type === dataType.Time.code">
                {{ dataRow.value | number : '1.0-2' }}
              </p>
            </ng-container>
            <ng-template #valueInputTpl>
              <div>
                <div *ngIf="editCache[dataRow.id].data.type === dataType.String.code">
                  <input nz-input [(ngModel)]="editCache[dataRow.id].data.value" placeholder="Nhập kí tự" />
                </div>
                <div *ngIf="editCache[dataRow.id].data.type === dataType.Address.code">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButtonSearch">
                    <input type="text" nz-input placeholder="Nhập địa chỉ" [(ngModel)]="editCache[dataRow.id].data.value" />
                  </nz-input-group>
                  <ng-template #suffixIconButtonSearch>
                    <button nz-button nzType="primary" nzSearch>
                      <span nz-icon nzType="search"></span>
                    </button>
                  </ng-template>
                </div>
                <div *ngIf="editCache[dataRow.id].data.type === dataType.Km.code">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButtonCal">
                    <input type="number" nz-input placeholder="Nhập khoảng cách (Km)" [(ngModel)]="editCache[dataRow.id].data.value" />
                  </nz-input-group>
                  <ng-template #suffixIconButtonCal>
                    <button nz-button nzType="primary" nzSearch>
                      <span nz-icon nzType="calculator"></span>
                    </button>
                  </ng-template>
                </div>
                <div *ngIf="editCache[dataRow.id].data.type === dataType.Time.code">
                  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButtonCalTime">
                    <input type="number" nz-input placeholder="Nhập thời gian đi (Giờ)" [(ngModel)]="editCache[dataRow.id].data.value" />
                  </nz-input-group>
                  <ng-template #suffixIconButtonCalTime>
                    <button nz-button nzType="primary" nzSearch>
                      <span nz-icon nzType="calculator"></span>
                    </button>
                  </ng-template>
                </div>
              </div>
            </ng-template>
          </td>
          <td class="text-nowrap" *ngIf="data.isMPO">
            <div class="editable-row-operations">
              <ng-container *ngIf="!editCache[dataRow.id].edit; else saveTpl">
                <button nz-tooltip nzTooltipTitle="Sửa" class="mr-2" nz-button (click)="startEdit(dataRow.id)">
                  <span nz-icon nzType="edit"></span>
                </button>
                <button
                  nz-popconfirm
                  nzPopconfirmTitle="Bạn có chắc muốn xoá?"
                  nzPopconfirmPlacement="bottom"
                  (nzOnConfirm)="startDelete(dataRow)"
                  nz-tooltip
                  nzTooltipTitle="Xoá"
                  class="mr-2"
                  nz-button
                  nzDanger
                >
                  <span nz-icon nzType="delete"></span>
                </button>
              </ng-container>
              <ng-template #saveTpl>
                <button
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.SAVE || 'Lưu'"
                  class="mr-2"
                  nz-button
                  nzType="primary"
                  (click)="saveEdit(dataRow.id)"
                >
                  <span nz-icon nzType="save"></span>
                </button>
                <button nz-tooltip [nzTooltipTitle]="language_key?.CANCEL || 'Hủy'" class="mr-2" nz-button nzDanger (click)="cancelEdit(dataRow.id)">
                  <span nz-icon nzType="close"></span>
                </button>
              </ng-template>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>
