import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { CurrencyMaskModule, CURRENCY_MASK_CONFIG } from 'ng2-currency-mask'
import { MaterialModule } from '../../app.module'
import { DirectivesModule } from '../../directive/directives.module'
import { NzAvatarModule } from 'ng-zorro-antd/avatar'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzCardModule } from 'ng-zorro-antd/card'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions'
import { NzDividerModule } from 'ng-zorro-antd/divider'
import { NzDrawerModule } from 'ng-zorro-antd/drawer'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzMessageModule } from 'ng-zorro-antd/message'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzStatisticModule } from 'ng-zorro-antd/statistic'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzTypographyModule } from 'ng-zorro-antd/typography'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { NzProgressModule } from 'ng-zorro-antd/progress'
import { CustomCurrencyMaskConfig } from '../setting/setting.module'
import { PriceQuoteComponent } from './price-quote.component'
import { PriceQuoteRoutingModule } from './price-quote-routing.module'
import { AddPriceQuoteComponent } from './add-price-quote/add-price-quote.component'
import { ConfigServiceComponent } from './config-service/config-service.component'
import { PriceListComponent } from './config-service/price-list/price-list.component'
import { PriceQuoteDetailComponent } from './price-quote-detail/price-quote-detail.component'
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { CreateItemQuoteComponent } from './add-price-quote/create-item-pr/create-item-quote.component'
import { AddOrEditOfferTradeComponent } from './bid-trade/add-or-edit-offer-trade/add-or-edit-offer-trade.component'
import { OfferTradeListDetailComponent } from './bid-trade/offer-trade-list-detail/offer-trade-list-detail.component'
import { OfferTradeComponent } from './bid-trade/offer-trade.component'
import { OfferPriceComponent } from './bid-price/offer-price.component'
import { OfferPriceListDetailComponent } from './bid-price/offer-price-list-detail/offer-price-list-detail.component'
import { AddOrEditOfferPriceComponent } from './bid-price/add-or-edit-bid-price/add-or-edit-offer-price.component'
import { OfferPriceColComponent } from './bid-price/offer-price-col/offer-price-col.component'
import { PriceQuoteDetail2Component } from './price-quote-detail2/price-quote-detail2.component'
import { TradeDetailComponent } from './price-quote-detail2/trade-detail/trade-detail.component'
import { PriceDetailComponent } from './price-quote-detail2/price-detail/price-detail.component'
import { OfferSupplierTradeRateComponent } from './offer-supplier-trade-rate/offer-supplier-trade-rate.component'
import { OfferRateTradeDetailComponent } from './offer-supplier-trade-rate/offer-rate-trade-detail/offer-rate-trade-detail.component'
import { OfferRatePriceComponent } from './offer-rate-price/offer-rate-price.component'
import { OfferRatePriceDetailComponent } from './offer-rate-price/offer-rate-price-detail/offer-rate-price-detail.component'
import { OfferItemComponent } from './offer-item/offer-item.component'
import { OfferDealComponent } from './offer-item/offer-deal/offer-deal.component'
import { OfferReportComponent } from './offer-result-report/offer-result-report.component'
import { ReportOfferResultComponent } from './offer-result-report/report-offer-result/report-offer-result.component'
import { NgxPrintModule } from 'ngx-print'
import { ReportOfferTotalComponent } from './offer-result-report/report-offer-total/report-offer-total.component'
import { ReportOfferTechComponent } from './offer-result-report/report-offer-tech/report-offer-tech.component'
import { ReportOfferTradeComponent } from './offer-result-report/report-offer-trade/report-offer-trade.component'
import { ReportOfferPriceComponent } from './offer-result-report/report-offfer-price/report-offer-price.component'
import { OfferResultDealComponent } from './price-quote-detail/offer-result-deal/offer-result-deal.component'
import { OfferResultDealDetailComponent } from './price-quote-detail/offer-result-deal/offer-result-deal-detail/offer-result-deal-detail.component'
import { OfferRankByMinPriceComponent } from './offer-rate/offer-rank-by-min-price/offer-rank-by-min-price.component'
import { OfferRankBySumPriceComponent } from './offer-rate/offer-rank-by-sum-price/offer-rank-by-sum-price.component'
import { OfferResultTechComponent } from './offer-result-report/offer-result-tech/offer-result-tech.component'
import { OfferResultTradeComponent } from './offer-result-report/offer-result-trade/offer-result-trade.component'
import { OfferHistoryPriceComponent } from './offer-result-report/offer-history-price/offer-history-price.component'
import { FollowAutionComponent } from './follow-aution/follow-aution.component'
import { OfferDealHistoryComponent } from './follow-aution/offer-deal-history/offer-deal-history.component'
import { DealOfferComponent } from './follow-aution/deal-offer/deal-offer.component'
import { OfferEvaluationComponent } from './offer-evaluation/offer-evaluation.component'
import { OfferChooseSupplierComponent } from './offer-choose-supplier/offer-choose-supplier.component'

@NgModule({
  declarations: [
    PriceQuoteComponent,
    CreateItemQuoteComponent,
    AddPriceQuoteComponent,
    ConfigServiceComponent,
    PriceListComponent,
    PriceQuoteDetailComponent,
    OfferResultDealDetailComponent,
    AddOrEditOfferTradeComponent,
    OfferTradeComponent,
    OfferTradeListDetailComponent,
    OfferPriceComponent,
    PriceQuoteDetail2Component,
    OfferDealComponent,
    OfferResultDealComponent,
    OfferPriceListDetailComponent,
    TradeDetailComponent,
    PriceDetailComponent,
    AddOrEditOfferPriceComponent,
    OfferItemComponent,
    OfferPriceColComponent,
    OfferSupplierTradeRateComponent,
    OfferRateTradeDetailComponent,
    OfferRatePriceDetailComponent,
    OfferRatePriceComponent,
    OfferEvaluationComponent,
    OfferReportComponent,
    ReportOfferResultComponent,
    ReportOfferTotalComponent,
    ReportOfferTechComponent,
    ReportOfferTradeComponent,
    FollowAutionComponent,
    ReportOfferPriceComponent,
    OfferRankByMinPriceComponent,
    OfferRankBySumPriceComponent,
    OfferResultTechComponent,
    OfferDealHistoryComponent,
    OfferChooseSupplierComponent,
    DealOfferComponent,
    OfferResultTradeComponent,
    OfferHistoryPriceComponent,
    CreateItemQuoteComponent,
    AddPriceQuoteComponent,
  ],
  imports: [
    CommonModule,
    PriceQuoteRoutingModule,
    CurrencyMaskModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzTableModule,
    NzDividerModule,
    NzGridModule,
    NzModalModule,
    NzIconModule,
    NzToolTipModule,
    NzInputModule,
    NzFormModule,
    NzSelectModule,
    NzCheckboxModule,
    NzTabsModule,
    NzDescriptionsModule,
    NzListModule,
    NzDatePickerModule,
    NzStatisticModule,
    NzPaginationModule,
    NzCollapseModule,
    NzCascaderModule,
    NzPopoverModule,
    NzTypographyModule,
    MaterialModule,
    NzPopconfirmModule,
    NzDrawerModule,
    NzCardModule,
    NzUploadModule,
    NzProgressModule,
    NzAvatarModule,
    NzMessageModule,
    DirectivesModule,
    NzTagModule,
    NzBreadCrumbModule,
    NzDropDownModule,
    NgxPrintModule,
  ],
  providers: [{ provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig }],
})
export class PriceQuoteModule {}
