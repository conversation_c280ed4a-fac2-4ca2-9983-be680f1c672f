export const enumEmailType = {
  // Quy trình 1: nhà cung cấp đăng ký & MPO xét duyệt việc đăng ký
  TEMP01: {
    code: 'GuiMpoDuyetNccDangKy',
    name: '<PERSON><PERSON> Thống Đấu Thầu APE - Nhà cung cấp Mới cần Phê duyệt',
    description: '1/ Mẫu email gửi nhân viên MPO phụ trách việc xét duyệt đăng ký',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị: {0},</p>
      <br>
      <p>Nhà cung cấp {1} thuộc Nhóm {2} đang yêu cầu phê duyệt bởi Anh/Chị.</p>
      <p>Anh/Chị vui lòng vào Link sau để <b>Phê duyệt</b>: {3}</p>
      <br>
      <p>Trân trọng</p>,
      <p><PERSON><PERSON> Thống Đấu Thầu APE</p>
      <br>
      <p><i><PERSON><PERSON><PERSON> ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP02: {
    code: 'GuiNguoiDuyetDuyetNccDangKy',
    name: 'Hệ Thống Đấu Thầu APE - Nhà cung cấp Mới cần Phê duyệt ',
    description: '2/ Mẫu email gửi người duyệt khi nhân viên phụ trách đã duyệt',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị: {0},</p>
      <br>
      <p>Nhà cung cấp {1} đã được xem xét và phê duyệt bởi Anh/Chị {2}.</p>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP03: {
    code: 'GuiNccNguoiDuyetDuyetDangKy',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Nhà cung cấp Mới được Phê duyệt thành công',
    description: '3/ Mẫu email gửi Doanh nghiệp khi người duyệt đã duyệt và thông tin tài khoản',
    numOfVariable: 3,
    default: `<p>Kính gửi Quý {0},</p>
      <br>
      <p>Chào mừng Quý Công ty đến với Hệ Thống Đấu Thầu APE.</p>
      <p>Thông tin đăng nhập của Quý Công ty như sau:</p>
      <p>- Tên Đăng Nhập: {1}</p>
      <p>- Mật khẩu: {2}</p>
      <p>Quý Công ty vui lòng đổi mật khẩu và lưu thông tin tài khoản trên để sử dụng cho việc đăng nhập.</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP04: {
    code: 'GuiMpoNguoiDuyetDuyetDangKy',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Nhà cung cấp Mới đã Phê duyệt bởi cấp quản lý',
    description: '4/ Mẫu email gửi nhân viên MPO khi người duyệt đã duyệt',
    numOfVariable: 3,
    default: `<p>Kính gửi Anh/Chị: {0}</p>
      <br>
      <p>{1} đã được Phê duyệt bởi Cấp quản lý {2}.</p>
      <p>Vui lòng thông báo cho Doanh nghiệp kiểm tra nếu Doanh nghiệp chưa nhận được thông tin phê duyệt thành công.</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP05: {
    code: 'GuiNccMPOTuChoiDangKy',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Nhà cung cấp bị Từ chối cấp Tài khoản',
    description: '5/ Mẫu email gửi Doanh nghiệp khi người phụ trách reject',
    numOfVariable: 2,
    default: `<p>Kính gửi Quý {0}</p>
      <br>
      <p>Quý Công ty đã bị từ chối cấp tài khoản trên Hệ thống Đấu Thầu APE</p>
      <p>Lý do từ chối: {1}</p>
      <p>Xin cảm ơn quý công ty đã gửi thông tin đến hệ thống của công ty chúng tôi.</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP06: {
    code: 'GuiMpoNguoiDuyetTuChoiDangKy',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Nhà cung cấp Mới đã bị từ chối bởi cấp quản lý',
    description: '6/ Mẫu email gửi nhân viên MPO khi người duyệt reject',
    numOfVariable: 4,
    default: `<p>Kính gửi Anh/Chị: {0},</p>
      <br>
      <p>Nhà cung cấp {1} đã bị từ chối bởi Cấp quản lý {2}.</p>
      <p>Lý do từ chối: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  // Quy trình 2: thẩm định thông tin Doanh nghiệp
  TEMP07: {
    code: 'GuiThongBaoThamDinhNcc',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Việc Thẩm Định Doanh nghiệp',
    description: '7/ Mẫu email gửi nhân viên pháp lý, các thành viên ban thẩm định về yêu cầu thẩm định nhà cung cấp',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Vui lòng vào đường Link sau để Thẩm định thông tin Công ty {0}: {1}</p>
      <br>
      <p>Lý do: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP08: {
    code: 'GuiKetQuaThamDinhPhapLyNcc',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Kết quả Thẩm Định Thông tin Pháp lý Doanh nghiệp',
    description: '8/ Mẫu email gửi các thành viên ban thẩm định, người yêu cầu thẩm định về kết quả thẩm định các thông tin pháp lý',
    numOfVariable: 2,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Thông tin Pháp lý của {0} đã có Kết quả thẩm định.</p>
      <br>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP09: {
    code: 'GuiNccBoSungPhapLy',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Bổ sung Thông tin pháp lý Doanh nghiệp',
    description: '9/ Mẫu email gửi nhà cung cấp kết quả thẩm định thông tin pháp lý khi có yêu cầu điều chỉnh, bổ sung',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0}</p>
      <br>
      <p>Quý Công ty cần bổ sung về Thẩm định Thông tin Pháp lý như sau:</p>
      <p>{1}</p>
      <br>
      <p>Vui lòng vào bổ sung trên hệ thống của chúng tôi theo đường Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP10: {
    code: 'GuiKetQuaThamDinhNangLucNcc',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Kết quả Thẩm Định Thông tin Năng lực Doanh nghiệp',
    description: '10/ Mẫu email gửi các thành viên ban thẩm định kết quả thẩm định về thông tin năng lực và yêu cầu điều chỉnh',
    numOfVariable: 2,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Thông tin Năng lực của {0} đã có Kết quả thẩm định.</p>
      <br>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP11: {
    code: 'GuiNccBoSungNangLuc',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Bổ sung/Điều chỉnh Thông tin Năng lực Doanh nghiệp',
    description: '11/ Mẫu email gửi nhà cung cấp kết quả thẩm định thông tin năng lực và yêu cầu điều chỉnh, bổ sung nếu có',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0}</p>
      <br>
      <p>Quý Công ty cần bổ sung/điều chỉnh về Thông tin Năng lực như sau:</p>
      <p>{1}</p>
      <br>
      <p>Vui lòng vào bổ sung/điều chỉnh trên hệ thống của chúng tôi theo đường Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  // Quy trình 3: đấu thầu
  TEMP12: {
    code: 'ThongBaoDaTaoGoiThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Gói thầu {0}',
    description: '12/ Mẫu email gửi cho người duyệt mua hàng và các thành viên trong hội đồng khi vừa tạo gói thầu',
    numOfVariable: 2,
    default: `
      <p>Kính gửi Các Anh/Chị,</p>
      <br>
      <p>Gói Thầu {0} đã được tạo.</p>
      <p>Vui lòng Thiết lập thông tin Kỹ thuật, thông tin Giá, Điều kiện Thương mại cho Gói thầu.</p>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP13: {
    code: 'GuiTechLeadDuyetKyThuat',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Gói thầu {0} đã Thiết lập Thông tin Kỹ thuật',
    description:
      '13/ Mẫu email gửi cho người duyệt kỹ thuật, cc nhân viên phụ trách mua hàng khi nhân viên phụ trách kỹ thuật đã thiết lập xong yêu cầu kỹ thuật cho gói thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} đã được Thiết lập thông tin kỹ thuật thành công bởi Anh/Chị {2}</p>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP14: {
    code: 'ThongBaoDaDuyetKyThuat',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Gói thầu {0} đã Phê duyệt Thông tin Kỹ thuật',
    description:
      '14/ Mẫu email gửi cho nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật và các thành viên trong hội đồng khi yêu cầu kỹ thuật cho gói thầu đã được duyệt',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Các Anh/Chị,</p>
      <br>
      <p>Gói Thầu {0} đã được Phê duyệt Thông tin Kỹ thuật bởi Anh/Chị {1}.</p>
      <br>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP15: {
    code: 'GuiTechTuChoiKyThuat',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Gói thầu {0} bị Từ chối Phê Duyệt Thông tin Kỹ thuật',
    description: '15/ Mẫu email gửi cho nhân viên phụ trách kỹ thuật, cc người phụ trách mua hàng khi yêu cầu kỹ thuật bị reject',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} bị Từ chối Phê duyệt Thông tin Kỹ thuật.</p>
      <p>Lý do: {2}</p>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP16: {
    code: 'GuiMpoLeadDuyetGia',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo về Gói thầu {0} đã tạo Bảng chào giá, Cơ cấu giá, Điều kiện thương mại, Chọn các doanh nghiệp tham gia',
    description:
      '16/ Mẫu email gửi cho người duyệt mua hàng khi nhân viên phụ trách mua hàng đã thiết lập xong bảng chào giá, cơ cấu giá, điều kiện thương mại, doanh nghiệp tham gia cho gói thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} đã tạo Bảng chào giá, Cơ cấu giá, Điều kiện thương mại và Chọn doanh nghiệp tham gia bởi Anh/Chị {2}.</p>
      <br>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP17: {
    code: 'ThongBaoDaDuyetGia',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Bảng chào giá, Cơ cấu giá, Điều kiện thương mại, Chọn các doanh nghiệp tham gia của Gói thầu {0} đã được Duyệt',
    description:
      '17/ Mẫu email thông báo gửi cho nhân viên phụ trách mua hàng, các thành viên trong hội đồng khi người duyệt đã duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại, doanh nghiệp tham gia cho gói thầu',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói Thầu {0} đã Phê Duyệt Bảng chào giá, Cơ cấu giá, Điều kiện thương mại và Chọn doanh nghiệp tham gia bởi Anh/Chị {1}.</p>
      <br>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP18: {
    code: 'GuiMpoTuChoiGia',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Thông tin giá và Điều kiện thương mại của Gói thầu {0} bị Từ chối Phê Duyệt',
    description:
      '18/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi người duyệt reject bảng chào giá, cơ cấu giá, điều kiện thương mại cho gói thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} bị Từ chối Phê duyệt Thông tin giá và Điều kiện thương mại.</p>
      <p>Lý do: {2}</p>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP19: {
    code: 'GuiNccThongBaoMoiThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Mời tham gia Gói thầu {0}',
    description: '19/ Mẫu email gửi Doanh nghiệp, cc nhân viên phụ trách mua hàng về thư thông báo mời thầu và yêu cầu xác nhận tham gia thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Kính mời Quý Công ty tham gia Gói thầu {1}.</p>
      <p>Đính kèm là Thư Mời Thầu.</p>
      <p>Quý Công ty vui lòng xác nhận Tham gia Gói thầu trước ngày {2} theo đường Link: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP20: {
    code: 'GuiNccChuaXacNhanThamGiaThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Mời tham gia Gói thầu {0} (Remind)',
    description: '20/ Mẫu email đến các Doanh nghiệp, cc nhân viên phụ trách mua hàng chưa xác nhận tham gia thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Gói thầu {1} sẽ hết hạn tham gia vào ngày {2}, Quý Công ty vui lòng xác nhận Tham gia Gói thầu theo đường Link: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP21_1: {
    code: 'GuiMpoNccXacNhanThamGiaThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo V/v Tham gia Gói Thầu {0}',
    description: '21.1/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi có nhà cung cấp xác nhận tham gia thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>{1} đã Xác nhận tham gia gói thầu {2}</p>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP21_2: {
    code: 'GuiMpoNccTuChoiThamGiaThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo V/v Tham gia Gói Thầu {0}',
    description: '21.2/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi có nhà cung cấp từ chối tham gia thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>{1} đã Từ chối tham gia gói thầu {2}</p>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP22: {
    code: 'GuiMpoNccNopHoSoThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo V/v Hoàn tất Nộp Hồ Sơ Gói Thầu {0}',
    description: '22/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi có nhà cung cấp nộp xong hồ sơ thầu',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>{1} đã nộp hồ sơ tham gia gói thầu {2}.</p>
      <br>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP23: {
    code: 'ThongBaoSapMoThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Gói Thầu {0} sắp tới hạn Mở thầu',
    description:
      '23/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng thầu thông báo sắp tới hạn mở thầu',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} sẽ mở thầu vào ngày {1}</p>
      <p>Các anh chị vui lòng tham gia đúng thời gian.</p>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP24: {
    code: 'ThongBaoQuaHanMoThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Gói Thầu {0} Quá hạn Mở thầu',
    description:
      '24/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng thầu thông báo đã quá hạn mở thầu 1 ngày',
    numOfVariable: 2,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} đã quá hạn Mở thầu: 01 ngày.</p>
      <p>Anh chị vui lòng sắp xếp tham gia.</p>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP25: {
    code: 'GuiTechLeadDuyetDanhGiaKyThuat',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Gói Thầu {0} hoàn thành Đánh giá Yêu cầu Kỹ thuật',
    description:
      '25/ Mẫu email gửi cho người duyệt kỹ thuật, cc nhân viên phụ trách mua hàng khi nhân viên phụ trách kỹ thuật đã hoàn thành xong việc đánh giá yêu cầu kỹ thuật cho các nhà cung cấp',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Anh/Chị {1} đã hoàn thành đánh giá yêu cầu kỹ thuật cho các nhà cung cấp thuộc Gói Thầu {2}</p>
      <br>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP26: {
    code: 'ThongBaoDuyetDanhGiaKyThuat',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Gói Thầu {0} đã phê duyệt về Đánh giá Yêu cầu Kỹ thuật ',
    description:
      '26/ Mẫu email gửi cho nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật và các thành viên trong hội đồng khi kết quả đánh giá yêu cầu kỹ thuật cho gói thầu đã được duyệt',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Anh/Chị {0} đã hoàn thành đánh giá yêu cầu kỹ thuật cho các nhà cung cấp thuộc Gói Thầu {1}</p>
      <br>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP27: {
    code: 'GuiTechTuChoiDanhGiaKyThuat',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo V/v Đánh giá Yêu cầu Kỹ thuật của Gói Thầu {0} đã bị Từ chối Duyệt',
    description: '27/ Mẫu email gửi cho nhân viên phụ trách kỹ thuật, nhân viên phụ trách mua hàng khi đánh giá yêu cầu kỹ thuật bị reject',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Anh/Chị {1} đã Từ chối Phê duyệt Yêu cầu kỹ thuật cho các nhà cung cấp thuộc Gói Thầu {2}</p>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP28: {
    code: 'GuiMpoLeadDuyetDanhGiaGia',
    name: 'Hệ Thống Đấu Thầu APE - T/Báo Gói Thầu {0} hoàn thành Đánh giá Điều kiện Thương mại và Thông tin Giá',
    description:
      '28/ Mẫu email gửi cho người duyệt mua hàng khi nhân viên phụ trách mua hàng đã hoàn thành xong việc đánh giá kết quả chào giá, cơ cấu giá, điều kiện thương mại cho các nhà cung cấp',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Anh/Chị {1} đã hoàn thành đánh giá Điều kiện thương mại và Kết quả chào giá, cơ cấu giá cho các nhà cung cấp thuộc Gói Thầu {2}</p>
      <br>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP29: {
    code: 'ThongBaoDuyetDanhGiaGia',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Gói Thầu {0} đã Phê duyệt về Điều kiện Thương mại và Thông tin Giá',
    description:
      '29/ Mẫu email gửi cho nhân viên phụ trách mua hàng, các thành viên trong hội đồng khi kết quả đánh giá kết quả chào giá, cơ cấu giá, điều kiện thương mại cho gói thầu đã được duyệt',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Anh/Chị {0} đã Phê duyệt Điều kiện Thương mại và Thông tin Giá cho các nhà cung cấp thuộc Gói Thầu {1}</p>
      <br>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP30: {
    code: 'GuiMpoTuChoiDanhGiaGia',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Gói Thầu {0} đã Phê duyệt về Điều kiện Thương mại và Thông tin Giá',
    description: '30/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi đánh giá kết quả chào giá, cơ cấu giá, điều kiện thương mại bị reject',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Anh/Chị {1} đã Từ chối Phê duyệt Điều kiện Thương mại và Thông tin Giá cho các nhà cung cấp thuộc Gói Thầu {2}</p>
      <p>Chi tiết xin vào Link sau: {4}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP31: {
    code: 'ThongBaoNccThamGiaDamPhan',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo V/v Tham gia Đàm phán giá cho Gói Thầu',
    description: '31/ Mẫu email gửi Doanh nghiệp, cc nhân viên phụ trách mua hàng về việc tham gia đàm phán giá {0}',
    numOfVariable: 5,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Kính mời Quý Công ty tham gia Đàm phán giá liên quan đến Gói Thầu {1} với Công ty chúng tôi.</p>
      <p>Thời gian hiệu lực: Từ ngày {2} đến ngày {3}</p>
      <p>Chi tiết xin vào Link sau: {4}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP32: {
    code: 'GuiMpoNCCXacNhanDamPhan',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Doanh nghiệp phản hồi về Đàm phán giá cho Gói Thầu {0}',
    description: '32/ Mẫu email gửi nhân viên phụ trách mua hàng về việc Doanh nghiệp xác nhận giữ nguyên giá hoặc gửi giá đàm phán mới',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Quý {1} đã có phản hồi V/v tham gia Đàm phán giá như sau:</p>
      <p>{2}</p>
      <br>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP33: {
    code: 'ThongBaoNccThamGiaDauGia',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Tham gia Đấu Giá cho Gói Thầu {0}',
    description: '33/ Mẫu email gửi Doanh nghiệp, cc nhân viên phụ trách mua hàng về việc tham gia đấu giá',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Kính mời Quý Công ty tham gia Đấu giá liên quan đến Gói Thầu {1} với Công ty chúng tôi.</p>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP34: {
    code: 'GuiMpoDuyetKetQuaDauThau',
    name: 'Hệ Thống Đấu Thầu APE - Duyệt Kết quả Đấu thầu cho Gói Thầu {0}',
    description: '34/ Mẫu email gửi người duyệt mua hàng duyệt kết quả đấu thầu',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} cần phê duyệt Kết quả đầu thầu.</p>
      <br>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP35: {
    code: 'ThongBaoKetQuaDauThauDuocDuyet',
    name: 'Hệ Thống Đấu Thầu APE - Kết quả Đấu thầu cho Gói Thầu {0} đã được Phê duyệt',
    description:
      '35/ Mẫu email gửi nhân viên phụ trách mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng khi kết quả đấu thầu đã được duyệt',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Các Anh/Chị,</p>
      <br>
      <p>Kết quả đầu thầu của Gói Thầu {0} đã được phê duyệt bởi Anh/Chị {1}</p>
      <br>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP36: {
    code: 'ThongBaoKetQuaDauThauBiTuChoi',
    name: 'Hệ Thống Đấu Thầu APE - Từ Chối Duyệt Kết quả Đấu thầu cho Gói Thầu {0}',
    description: '36/ Mẫu email gửi nhân viên phụ trách mua hàng khi kết quả đấu thầu bị reject',
    numOfVariable: 5,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} đã bị Từ chối Phê duyệt Kết quả đầu thầu bởi Anh/Chị {2}.</p>
      <p>Lý do: {3}</p>
      <p>Chi tiết xin vào Link sau: {4}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP37: {
    code: 'ThongBaoTrungThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo Trúng Thầu cho Gói Thầu {0}',
    description: '37/ Mẫu email gửi thông báo trúng thầu đến Doanh nghiệp trúng thầu, cc nhân viên phụ trách mua hàng',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Xin chúc mừng Quý Công ty đã Trúng Gói Thầu {1} của Công ty chúng tôi.</p>
      <p>Danh sách Item trúng thầu:</p>
      <p>{3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP38: {
    code: 'ThuCamOnThamGiaThau',
    name: 'Hệ Thống Đấu Thầu APE - Thư Cảm ơn Gói Thầu {0}',
    description: '38/ Mẫu email gửi thư cảm ơn đến Doanh nghiệp tham gia thầu nhưng không trúng thầu, cc nhân viên phụ trách mua hàng',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Chúng tôi xin thông báo {1} đã không trúng gói thầu {2}.</p>
      <br>
      <p>Xin cảm ơn và hẹn gặp lại Quý Công ty trong những Gói thầu sắp tới của Công ty chúng tôi.</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP39: {
    code: 'ThongBaoMoThau',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo mở Gói Thầu {0}',
    description:
      '39/ Mẫu email gửi nhân viên phụ trách mua hàng, người duyệt mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng khi gói thầu được mở',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} đã mở lúc {1}.</p>
      <br>
      <p>Các anh chị vui lòng kiểm tra lại.</p>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP40: {
    code: 'ThongBaoMoThauNCC',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo mở Gói Thầu {0}',
    description: '40/ Mẫu email gửi Doanh nghiệp khi gói thầu được mở',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Chúng tôi xin thông báo Gói thầu {1} đã mở lúc {2}.</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP41: {
    code: 'ThongBaoHanDuyetKyThuat3Ngay',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo hạn duyệt hồ sơ kỹ thuật Gói Thầu {0}',
    description:
      '41/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, Người tạo kỹ thuật và duyệt kỹ thuật còn 03 ngày là đến hạn duyệt hồ sơ kỹ thuật',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} còn 03 ngày là đến hạn duyệt hồ sơ kỹ thuật.</p>
      <br>
      <p>Các anh chị vui lòng kiểm tra lại.</p>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP42: {
    code: 'ThongBaoHetHanDuyetKyThuat1Ngay',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo hết hạn duyệt hồ sơ kỹ thuật Gói Thầu {0}',
    description:
      '42/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng, Người tạo kỹ thuật và duyệt kỹ thuật Đã Hết thời hạn duyệt hồ sơ kỹ thuật 01 ngày',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} đã hết thời hạn duyệt hồ sơ kỹ thuật 01 ngày.</p>
      <br>
      <p>Các anh chị vui lòng kiểm tra lại.</p>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP43: {
    code: 'ThongBaoHanDuyetChaoGia3Ngay',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo hạn duyệt hồ sơ chào giá Gói Thầu {0}',
    description: '43/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng còn 03 ngày là đến hạn duyệt hồ sơ chào giá',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} còn 03 ngày là đến hạn duyệt hồ sơ chào giá.</p>
      <br>
      <p>Các anh chị vui lòng kiểm tra lại.</p>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP44: {
    code: 'ThongBaoHetHanDuyetChaoGia1Ngay',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo hết hạn duyệt hồ sơ chào giá Gói Thầu {0}',
    description: '44/ Mẫu email gửi các nhân viên phụ trách mua hàng, người duyệt mua hàng Đã Hết thời hạn duyệt hồ sơ chào giá 01 ngày',
    numOfVariable: 3,
    default: `
      <p>Kính gửi các Anh/Chị,</p>
      <br>
      <p>Gói thầu {0} đã hết thời hạn duyệt hồ sơ chào giá 01 ngày.</p>
      <br>
      <p>Các anh chị vui lòng kiểm tra lại.</p>
      <p>Chi tiết xin vào Link sau: {1}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP45: {
    code: 'GuiMPOLeadDuyetGoiThauTam',
    name: 'Hệ Thống Đấu Thầu APE - Yêu cầu duyệt gói thầu tạm {0}',
    description: '45/ Mẫu email gửi cho người duyệt mua hàng khi nhân viên phụ trách mua hàng yêu cầu duyệt gói thầu tạm',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} đã được Thiết lập thông tin chung thành công bởi Anh/Chị {2}</p>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP46: {
    code: 'GuiMPOKiemTraLaiGoiThauTam',
    name: 'Hệ Thống Đấu Thầu APE - Yêu cầu kiểm tra lại gói thầu tạm {0}',
    description: '46/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi người duyệt mua hàng yêu cầu kiểm tra lại gói thầu tạm',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu tạm {1} đã bị yêu cầu kiểm tra lại bởi Anh/Chị {2}</p>
      <p>Anh/Chị vui lòng vào Link sau để kiểm tra lại thông tin gói thầu tạm: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP47: {
    code: 'GuiNCCNopLaiChaoGia',
    name: 'Hệ Thống Đấu Thầu APE - Yêu cầu nộp lại chào giá do việc sửa đổi bảng giá gói thầu {0}',
    description: '47/ Mẫu email gửi Doanh nghiệp yêu cầu nộp lại chào giá do việc sửa đổi bảng giá gói thầu',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Gói Thầu {1} đã được sửa đổi bảng giá mới</p>
      <p>Quý Công ty vui lòng vào Link sau để nộp lại chào giá: {2}</p>
      <p>Thời gian hiệu lực: Từ ngày {3} đến ngày {4}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP48: {
    code: 'GuiMpoNccNopChaoGiaBosung',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo V/v Nộp Chào Giá Bổ Sung Cho Gói Thầu {0}',
    description: '48/ Mẫu email gửi cho nhân viên phụ trách mua hàng khi có nhà cung cấp nộp chào giá bổ sung',
    numOfVariable: 4,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>{1} đã nộp chào giá bổ sung cho gói thầu {2}.</p>
      <br>
      <p>Chi tiết xin vào Link sau: {3}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP49: {
    code: 'GuiMpoDuyetNCCThangThau',
    name: 'Hệ Thống Đấu Thầu APE - Duyệt Doanh nghiệp Thắng Thầu Cho Gói Thầu {0}',
    description: '49/ Mẫu email gửi người duyệt mua hàng duyệt Doanh nghiệp thắng thầu',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} cần phê duyệt Doanh nghiệp thắng thầu.</p>
      <br>
      <p>Anh/Chị vui lòng vào Link sau để Phê duyệt: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP50: {
    code: 'ThongBaoNCCThangThauDuocDuyet',
    name: 'Hệ Thống Đấu Thầu APE - Doanh nghiệp Thắng Gói Thầu {0} đã được Phê duyệt',
    description:
      '50/ Mẫu email gửi nhân viên phụ trách mua hàng, nhân viên phụ trách kỹ thuật, người duyệt kỹ thuật và các thành viên trong hội đồng khi Doanh nghiệp thắng thầu đã được duyệt',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Các Anh/Chị,</p>
      <br>
      <p>Doanh nghiệp thắng thầu của Gói Thầu {0} đã được phê duyệt bởi Anh/Chị {1}</p>
      <br>
      <p>Chi tiết xin vào Link sau: {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP51: {
    code: 'ThongBaoNCCThangThauBiTuChoi',
    name: 'Hệ Thống Đấu Thầu APE - Doanh nghiệp Thắng Gói Thầu {0} đã bị Từ Chối',
    description: '51/ Mẫu email gửi nhân viên phụ trách mua hàng khi Doanh nghiệp thắng thầu bị reject',
    numOfVariable: 5,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} đã bị Từ chối Doanh nghiệp thắng thầu bởi Anh/Chị {2}.</p>
      <p>Lý do: {3}</p>
      <p>Chi tiết xin vào Link sau: {4}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP52: {
    code: 'GuiMPOLeadHuyGoiThau',
    name: 'Hệ Thống Đấu Thầu APE - Yêu cầu hủy gói thầu {0}',
    description: '52/ Mẫu email gửi cho người duyệt mua hàng khi nhân viên phụ trách mua hàng yêu cầu hủy gói thầu',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Anh/Chị {0},</p>
      <br>
      <p>Gói Thầu {1} đã được yêu cầu hủy bởi Anh/Chị {2}</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
  TEMP53: {
    code: 'ThongBaoHuyGoiThauNCC',
    name: 'Hệ Thống Đấu Thầu APE - Thông báo hủy Gói Thầu {0}',
    description: '53/ Mẫu email gửi Doanh nghiệp khi gói thầu bị hủy',
    numOfVariable: 3,
    default: `
      <p>Kính gửi Quý {0},</p>
      <br>
      <p>Chúng tôi xin thông báo Gói thầu {1} đã hủy lúc {2}.</p>
      <br>
      <p>Trân trọng,</p>
      <p>Hệ Thống Đấu Thầu APE</p>
      <br>
      <p><i>Lưu ý: Mail này được gửi tự động từ Hệ Thống Đấu Thầu APE, vui lòng không Reply.</i></p>`,
  },
}
