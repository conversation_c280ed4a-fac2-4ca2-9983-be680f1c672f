{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ape-bidding-admin": {"projectType": "application", "schematics": {"@schematics/angular:component": {"inlineTemplate": false, "style": "scss", "skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["core-js", "@babel", "@babel/runtime/regenerator", "seedrandom", "decimal.js", "raf", "xlsx", "clone-deep", "file-saver", "fraction.js", "complex.js", "pdfmake", "typed-function", "exceljs", "moment", "j<PERSON>y", "escape-latex", "rgbcolor", "javascript-natural-sort", "jsbarcode"], "outputPath": "dist/ape-bidding-admin", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/purple-green.css", "src/styles.scss", "src/styles.less"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 3401, "open": true, "buildTarget": "ape-bidding-admin:build"}, "configurations": {"production": {"buildTarget": "ape-bidding-admin:build:production"}, "development": {"buildTarget": "ape-bidding-admin:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "ape-bidding-admin:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "83ec781f-70e9-4955-a9fa-d34a47f0ebd7"}}